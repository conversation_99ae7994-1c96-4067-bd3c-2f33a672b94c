/**
 * @author: TurboC
 * @date: 2020-12-16 20:18:14
*/
import router from '@/router'
import Vue from 'vue'

router && router.afterEach((to, from) => {
  if (to.path === '/home') {
    const footerDom = document.querySelector('.frame-main-footer')
    if (footerDom && to.path === '/home') {
      footerDom.style.display = 'none'
    }
    sessionStorage.setItem('lylRouterCurrentName', to.path)
    // document.querySelector('.CommonLylConfigMenu2').style.display = 'none'
    // document.querySelector('.CommonLylConfigMenu').style.display = 'block'
  } else {
    /* 显示iframe的页面 */
    const arrinclude = ['/pagedata/indicators', '/pagedata/store', '/pagedata/order']
    const framemaincontent = document.querySelector('.frame-main-content')

    if (arrinclude.includes(to.path)) {
      framemaincontent.style.overflow = 'hidden'
    } else {
      framemaincontent.style.overflow = 'auto'
    }

    const footerDom = document.querySelector('.frame-main-footer')
    if (footerDom && to.path === '/home') {
      footerDom.style.display = 'block'
    }

    sessionStorage.setItem('lylRouterCurrentName', to.path)
    // document.querySelector('.CommonLylConfigMenu').style.display = 'none'
    // document.querySelector('.CommonLylConfigMenu2').style.display = 'block'
  }
})

router && router.beforeEach((to, from, next) => {
  if (to.path === '/home' || to.path === '/frame/framePage') {
    document.querySelector('#app').classList.remove('lylCommonWrapperPadding')
  } else {
    document.querySelector('#app').classList.add('lylCommonWrapperPadding')
  }

  if (to.path) {
    // 记录当前路由导航信息，并同步菜单路由信息--加载页面并更新面包屑
    Vue.prototype.$main_tools.menu.setMenuNavMemoryPath(Vue.prototype, to.path)
  }
  next()
})
