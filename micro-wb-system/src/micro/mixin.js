/**
 * Created on 2019/11/15
 * 页面大量重复的代码，提取至mixin，不可滥用
 */
export default {
  data () {
    return {
      delFromCache: false, // 是否清除当前页面的路由缓存，当为true时清除；
      delToCache: false // 是否清除即将前往的那个页面的路由缓存，当为true时清除；
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      // 路由上的meta里配置keepAlive：true，表示当前页面需要开启路由缓存
      if (to.meta.keepAlive && to.name) {
        vm.$store.commit('keepAlive/pushIncludes', to.name) // 使用本地store做缓存
        // vm.$main_tools.store.commit('keepAlive/pushIncludes', to.name) // 使用公共main的store做缓存
      }
    })
  },
  beforeRouteLeave (to, from, next) {
    // 是否清除当前页面的路由缓存
    if (this.delFromCache) {
      this.$store.commit('keepAlive/popIncludes', from.name) // 使用本地store做缓存
      // this.$main_tools.store.commit('keepAlive/popIncludes', from.name) // 使用公共main的store做缓存
    }
    // 是否清除即将前往的那个页面的路由缓存
    if (this.delToCache) {
      this.$store.commit('keepAlive/popIncludes', to.name) // 使用本地store做缓存
      // this.$main_tools.store.commit('keepAlive/popIncludes', to.name) // 使用公共main的store做缓存
    }
    this.$nextTick(function () {
      next()
    })
  }
}
