/**
 * @author: <PERSON><PERSON>
 * @date: 2020-12-16 20:18:14
 */
<template>
  <div id="app" style="height: 100%">
    <keep-alive :include="computeKeepAliveIncludes">
      <router-view />
    </keep-alive>

    <!-- iframe页面 -->
    <!-- <pagedataindicators
      v-show="$route.path == '/pagedata/indicators'"
    ></pagedataindicators>
    <pagedatastore v-show="$route.path == '/pagedata/store'"></pagedatastore>
    <pagedataorder v-show="$route.path == '/pagedata/order'"></pagedataorder> -->
    <!-- iframe页 -->
    <template v-if="hasOpenComponentsArr && hasOpenComponentsArr.length">
      <component
        v-for="item in hasOpenComponentsArr"
        :key="item.name"
        :is="item.name"
        v-show="$route.path === item.path"
      ></component>
    </template>
  </div>
</template>

<script>
import pagedataindicators from '@/views/pagedata/indicators/index'
import pagedatastore from '@/views/pagedata/store/index'
import pagedataorder from '@/views/pagedata/order/index'
import Vue from 'vue'
// import router from '@/router'
export default {
  name: 'app',
  props: {},
  data() {
    return {
      componentsArr: [] // 含有iframe的页面
    }
  },
  components: {
    pagedataindicators,
    pagedatastore,
    pagedataorder
  },
  watch: {
    $route() {
      // 判断当前路由是否iframe页
      this.isOpenIframePage()
    }
  },
  created() {
    // 设置iframe页的数组对象
    const componentsArr = this.getComponentsArr()
    componentsArr.forEach((item) => {
      Vue.component(item.name, item.component)
    })
    this.componentsArr = componentsArr
    // 判断当前路由是否iframe页
    this.isOpenIframePage()
  },
  computed: {
    computeKeepAliveIncludes () {
      return this.$store.state.keepAlive.includes
    },
    // 实现懒加载，只渲染已经打开过（hasOpen:true）的iframe页
    hasOpenComponentsArr() {
      return this.componentsArr.filter((item) => item.hasOpen)
    }
  },
  mounted() {
  },
  methods: {
    // 根据当前路由设置hasOpen
    isOpenIframePage() {
      const target = this.componentsArr.find((item) => {
        return item.path === this.$route.path
      })
      if (target && !target.hasOpen) {
        target.hasOpen = true
      }
    },
    // 遍历路由的所有页面，把含有iframeComponent标识的收集起来
    getComponentsArr() {
      const routes = this.$router.options.routes
      const iframeArr = routes.filter((item) => item.iframeComponent)
      // console.log(iframeArr)
      return iframeArr.map((item) => {
        const name = item.name || item.path.replace('/', '')
        return {
          name: name,
          path: item.path,
          hasOpen: false, // 是否打开过，默认false
          component: item.iframeComponent // 组件文件的引用
        }
      })
    }
  }
}
</script>

<style lang="scss"></style>
