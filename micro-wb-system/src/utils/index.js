/**
 * Created by TurboC on 16/11/18.
 */
/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}
// arraybuffer转string
export function ab2Json (buf) {
  if (buf !== null && buf.byteLength > 0) {
    const encodedString = String.fromCodePoint.apply(null, [...new Uint8Array(buf)])
    const decodedString = decodeURIComponent(escape(encodedString))
    // console.log(decodedString)
    return JSON.parse(decodedString)
  } else {
    return ''
  }
}
// 导出文件
export function ExportFile (data, name) {
  const date = new Date().valueOf()
  let fileName
  if (data.headers['content-disposition']) {
    fileName = decodeURIComponent( // 获取文件名
      data.headers['content-disposition'].split('=')[1].replace(/\+/g, ' ')
    )
  } else {
    fileName = name + date
  }
  const blob = new Blob([data.body])
  const link = document.createElement('a')
  link.download = fileName
  link.href = URL.createObjectURL(blob)
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  // URL.revokeObjectURL(link.href)
  document.body.removeChild(link)
}
/**
 * 检查图片分辨率
 * @param {File} file
 * @param {String} size
 */
export function checkImage(file, size) {
  return new Promise(resolve => {
    if (!size) {
      resolve(true)
    }
    if (typeof size === 'string') {
      size = size.split('x')
    }
    let img = new Image()
    img.onload = function () {
      if (this.width.toString() !== size[0] || this.height.toString() !== size[1]) {
        resolve(false)
      }
      resolve(true)
      img = null
    }
    img.src = URL.createObjectURL(file)
  })
}
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 * index:parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}