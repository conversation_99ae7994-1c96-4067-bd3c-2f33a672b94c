/*
 * @User: JOJO
 * @FilePath: \netshoposmweb\micro-wb-system\src\utils\rewriteSessionStorage.js
 */

// const localStorageMock = () => {
//   var storage = window.sessionStorage
//   return {
//     setItem: function (key, value) {
//       var setItemEvent = new Event('setItemEvent')
//       var oldValue = storage[key]
//       setItemEvent.key = key
//       // 新旧值深度判断，派发监听事件
//       if (oldValue !== value) {
//         setItemEvent.newValue = value
//         setItemEvent.oldValue = oldValue
//         window.dispatchEvent(setItemEvent)
//         storage[key] = value
//         return true
//       }
//       return false
//     },
//     getItem: function (key) {
//       return storage[key]
//     },
//     removeItem: function (key) {
//       storage[key] = null
//       return true
//     },
//     clear: function () {
//       storage.clear()
//       return true
//     },
//     key: function (index) {
//       return storage.key(index)
//     }
//   }
// }

// Object.defineProperty(window, 'sessionStorage', {
//   value: localStorageMock,
//   writable: true
// })

export default {
  install(vue, options) {},
  dispatchEventStroage() {
    const signSetItem = sessionStorage.setItem
    sessionStorage.setItem = function (key, val) {
      const setEvent = new Event('setItemEvent')
      setEvent.key = key
      setEvent.newValue = val
      window.dispatchEvent(setEvent)
      signSetItem.apply(this, arguments)
    }
  },
  dispatchEventStroage2() {
    const signSetItem = sessionStorage.setItem
    sessionStorage.setItem = function (key, val) {
      const setEvent = new Event('setItemEventRouterName')
      setEvent.key = key
      setEvent.newValue = val
      window.dispatchEvent(setEvent)
      signSetItem.apply(this, arguments)
    }
  }
}
