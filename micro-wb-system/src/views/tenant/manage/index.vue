/** * 租户管理 */
<template>
  <div class="webbas">
    <template>
      <div class="list-page-content-css">
        <div class="query-area-content-css">
          <el-form ref="searchForm"
                   :inline="true"
                   :model="table.searchForm"
                   @submit.native.prevent>
            <el-row>
              <el-col :span="6">
                <el-form-item prop="name"
                              label="租户名称：">
                  <el-input v-model.trim="table.searchForm.name"
                            placeholder=""
                            name="name"
                            @keyup.enter.native="search()">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="code"
                              label="租户编码：">
                  <el-input v-model.trim="table.searchForm.code"
                            placeholder=""
                            name="code"
                            @keyup.enter.native="search()">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="status"
                              label="状态：">
                  <asp-select-all v-model="table.searchForm.status"
                                  :code-list="tenantStatusList"></asp-select-all>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="query-area-btn-css">
                  <asp-btn-solid name="查询"
                                 icon="el-icon-search"
                                 @click="search()"></asp-btn-solid>
                  <asp-btn-hollow icon="el-icon-refresh"
                                  name="重置"
                                  @click="reset()"></asp-btn-hollow>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <asp-table ref="table"
                   :url="table.url"
                   :param="table.searchForm"
                   :prefix="table.prefix"
                   type="">
          <template slot="header">
            <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_tenantManage02' })"
                           name="新增"
                           icon="el-icon-plus"
                           @click="addRole"></asp-btn-solid>
            <!-- <asp-btn-hollow v-hasAuth="doAuth({ btnCode: 'wb_tenantManage06' })"
                            name="导出"
                            icon="el-icon-download"
                            @click="exportList"></asp-btn-hollow> -->
          </template>
          <asp-table-column prop="code"
                            min-width="90"
                            label="租户编码"></asp-table-column>
          <asp-table-column prop="name"
                            min-width="110"
                            label="租户名称"></asp-table-column>
          <asp-table-column prop="description"
                            min-width="120"
                            show-overflow-tooltip
                            label="租户描述"></asp-table-column>
          <asp-table-column prop="contactName"
                            width="120"
                            show-overflow-tooltip
                            label="联系人"></asp-table-column>
          <asp-table-column prop="contactMobile"
                            width="115"
                            label="联系人手机号"></asp-table-column>
          <asp-table-column prop="contactEmail"
                            width="139"
                            label="联系人邮箱"></asp-table-column>
          <asp-table-column prop="expireDate"
                            width="100"
                            label="租户有效期"></asp-table-column>
          <!-- dictCodeName(row.status,tenantStatusList) -->
          <asp-table-column :formatter="dictTenantStatus"
                            prop="status"
                            width="55"
                            label="状态"></asp-table-column>
          <asp-table-column :width="this.$aspFontSize.asp_ColButtonSize([2, 2, 2, 4])"
                            label="操作"
                            fixed="right">
            <template slot-scope="{ scope }">
              <asp-btn-text name="查看"
                            @click="detailTenant(scope.$index, scope.row)">
              </asp-btn-text>
              <!-- <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_040105' })" -->
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_tenantManage03' })"
                            v-if="scope.row.status == '1'"
                            name="修改"
                            @click="editTenant(scope.$index, scope.row)">
              </asp-btn-text>
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_tenantManage04' })"
                            name="删除"
                            @click="delTenant(scope.row)">
              </asp-btn-text>
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_tenantManage05' })"
                            v-if="scope.row.status == '1'"
                            name="分配权限"
                            @click="authResourceEvent(scope.$index, scope.row)">
              </asp-btn-text>
            </template>
          </asp-table-column>
        </asp-table>
      </div>
      <!-- 新增/修改租户 -->
      <editTenant ref="editTenant"
                  :dialog-param="tenantModelParam"
                  :operation="tenantModelParam.oper || 'edit'"
                  @search="search"></editTenant>
    </template>
  </div>
</template>

<script>
import editTenant from './editTenant.vue'
export default {
  name: 'TenantManage',
  components: { editTenant },
  data () {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/tenant/listPage',
        searchForm: {
          name: '',
          code: '',
          status: ''
        }
      },
      tenantModelParam: {}
    }
  },
  computed: {
    // 租户状态
    tenantStatusList () {
      const list = this.$aspUtils.getCodeValueByType(this, 'TENANT_STATUS') || []
      return list
    }
  },
  beforeMount () { },
  mounted () {
    // this.$refs.table.paginationStatus = true
    this.$refs.table.listData = this.testData
  },
  methods: {
    /**
     * 按钮权限
     * @method doAuth
     * @param {object} opt
     * @return {object}
     */
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    /**
     * 导出excel
     * @method exportList
     */
    exportList (pageRestart) {
      // console.log(pageRestart)
      let params = ''
      const obj = this.table.searchForm
      for (const key in obj) {
        params += key + '=' + (obj[key] === undefined ? '' : obj[key]) + '&'
      }
      params = params.substring(0, params.length - 1)
      let url = this.$apiConfig.managerPathPrefix + '/tenant/export?' + params
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    search () {
      this.$nextTick(() => {
        this.$refs.table.asp_search()
      })
    },
    reset () {
      this.$refs.table.asp_reset()
    },
    // 新增租户
    addRole () {
      this.tenantModelParam = {
        title: '新增租户',
        id: '',
        modelVisible: true
      }
    },
    // 修改租户
    editTenant (index, row) {
      this.tenantModelParam = {
        title: '修改租户',
        id: row.id,
        modelVisible: true
      }
    },
    // 查看租户
    detailTenant (index, row) {
      this.tenantModelParam = {
        title: '租户详情',
        oper: 'detail',
        id: row.id,
        modelVisible: true
      }
    },
    // 删除租户
    delTenant (row) {
      this.$aspMsgbox.confirm(this, '确认删除此租户？', function (arg, instance) {
        // console.log(arg, instance)
        const url = this.$apiConfig.managerPathPrefix + '/tenant/delete'
        this.$aspHttps.asp_Post(url, { id: row.id }).then(response => {
          if (this.$reponseStatus(response)) {
            this.$message.success('删除成功!')
            this.search()
          }
        })
      })
    },
    // 分配权限
    authResourceEvent (index, row) {
      this.$router.push({
        path: '/tenant/newAuthResource',
        query: {
          id: row.roleId,
          name: row.name
        }
        // query: { row: row }
      })
    },
    dictCodeName (code, list) {
      const item = list.find(val => val.code === code)
      return item ? item.name : code
    },
    dictTenantStatus (row) {
      const item = this.tenantStatusList.find(val => val.code === row.status)
      return item ? item.name : ''
    }
  }
}
</script>
