/**
* Created by yuxuan date on 2024/09/14
* 租户管理权限授权页面（复用角色授权页面）
* 改动点：1、即可全部替换；2、屏蔽数据权限功能；3、请求参数微调；
*/
<template>
  <div class="webbas">
    <div class="background-css">
      <div class="webbas-tree-css">
        <div style="height:30px;padding: 5px 15px;border-bottom: 1px solid #dee8f8;">
          <div class="tree-title"
               style="float:left;font-size: 16px;font-weight: bold;">
            给角色<span>【{{ $route.query.name }}】</span>分配权限</div>
          <div style="color:white; float:right;">
            <asp-btn-solid v-loading="submitStatus"
                           :disabled="tabloading || submitStatus"
                           icon="el-icon-check"
                           name="保存"
                           @click="authResourceSubmit">
            </asp-btn-solid>
            <asp-btn-hollow icon="el-icon-close"
                            name="取消"
                            @click="back()">
            </asp-btn-hollow>
          </div>
        </div>
        <div style="background-color:#fcf8e3;padding: 2px 10px; margin-bottom: 5px; font-size:12px;">
          备注：置灰的权限当前账号无权修改，如需修改请联系管理员。
        </div>
        <el-tabs v-if="tabFlag"
                 :before-leave="beforeLeaveTab"
                 type="card"
                 @tab-click="(tab, event) => handleTabsClick(tab, event)"
                 v-loading="tabloading"
                 empty-text="loding....."
                 v-model="tabsValue">
          <template v-for="(roucesCell, roucesKey) in tabList">
            <el-tab-pane :name="roucesCell.id"
                         :key="roucesKey"
                         style="">
              <span :key="roucesKey"
                    slot="label">{{roucesCell.title}}
              </span>
              <newAuthResourceCell :ref="`authResourceCell${roucesCell.id}`"
                                   :tabCell="true"
                                   :bodyShow="tabsValue === roucesCell.id"
                                   :dependency="dependency"
                                   :authResourceCheckData="authResourceCheckData"
                                   :authResourceDataContent="roucesCell.authResourceDataContent"
                                   :relationObject="relationObject"
                                   :dataResourceList="dataResourceList"
                                   @updateAuthResources="updateAuthResources"></newAuthResourceCell>
            </el-tab-pane>
          </template>
        </el-tabs>
        <newAuthResourceCell v-else
                             ref="authResourceCell"
                             :dependency="dependency"
                             :authResourceCheckData="authResourceCheckData"
                             :authResourceDataContent="authResourceDataContent"
                             :relationObject="relationObject"
                             :dataResourceList="dataResourceList"
                             :oldData="oldData"
                             @updateAuthResources="updateAuthResources"></newAuthResourceCell>
      </div>
    </div>
  </div>
</template>

<script>
// import Vue from 'vue'
import newAuthResourceCell from './components/newAuthResourceCell.vue'
const { projectConfig } = require('../../../../../config.json')
export default {
  name: 'NewTenantAuthResource',
  components: { newAuthResourceCell },
  props: {},
  data () {
    return {
      tabFlag: projectConfig.multiMenu,
      tabList: [],
      relationObject: {},
      tabsValue: '',
      tabloading: false,
      submitStatus: false,
      dependency: {}, // 捕获所以被relation关联的id
      dataResourceList: [], // 数据权限资源
      authResourceData: [],
      authResourceCheckData: [], // 获取已选数据
      allCheckedAuthResourceList: [],
      authResourceDataContent: [], // 资源数组
      oldData: [], // 不可点击的选中数据
      isBack: true // 是否直接返回
    }
  },
  computed: {
  },
  watch: {},
  created () {
    this.init(this.$route.query)
  },
  mounted () { },
  methods: {
    init (val) {
      // 初始化树形结构
      this.authResourceDataContent = []
      // 提交按钮 loading 状态
      this.submitStatus = false
      this.tabloading = true
      if (!val.id) {
        this.$message.warning('没有角色id，请检查参数')
        return
      }
      const listRoleResUrl = this.$apiConfig.managerPathPrefix + '/roleTenant/listRoleResourceTree'
      this.$aspHttps.asp_Post(listRoleResUrl, { roleId: val.id }).then(response => {
        if (this.$reponseStatus(response)) {
          if (!response.data.treeNodes || response.data.treeNodes.length === 0) {
            this.treeEmptyText = '无可分配的权限'
            response.data.treeNodes = []
          }
          this.authResourceData = response.data.treeNodes
          this.authResourceDataContent = response.data.treeNodes
          this.relationObject = response.data.rightExtends
          this.authResourceCheckData = []
          this.oldData = []
          for (let i = 0; i < this.authResourceDataContent.length; i++) {
            // 初始化树结构数据，筛选出数据权限
            this.initDataResource(this.authResourceDataContent[i])
          }
          for (let i = 0; i < this.authResourceData.length; i++) {
            // 初始化授权数据
            this.initAuthResource(this.authResourceData[i])
          }
          // console.log('this.dependency', this.authResourceCheckData)
          // 先分页签
          if (this.tabFlag) {
            this.authResourceDataContent.forEach(item => {
              this.tabList.push({
                id: item.id,
                title: item.text,
                authResourceDataContent: item.children || []
              })
            })
          }
          // 默认值
          this.tabsValue = this.tabList && this.tabList[0] && this.tabList[0].id
        }
        this.tabloading = false
      })
    },
    // 初始化树结构数据，筛选出数据权限
    initDataResource (data) {
      // 当leaf是false,说明存在children，这里id为String，只选父级无效
      if (
        data.type === '3' &&
        data.children &&
        data.children.length !== 0 &&
        data.children.every(item => item.type === '6')
      ) {
        data.children.forEach(item => {
          if (item.checked === true || item.checked === 'true') {
            this.dataResourceList.push(item.id)
          }
        })
        data.dataRights = data.children || []
        data.children = []
      } else if (Object.prototype.hasOwnProperty.call(data, 'children') && data.children) {
        for (let i = 0; i < data.children.length; i++) {
          this.initDataResource(data.children[i])
        }
      }
    },
    // 初始化授权数据
    initAuthResource (data) {
      // 判断是否默认角色 0不是 1是 默认角色不可修改权限
      if (data.roleType === '1') {
        data.disabled = true
      }
      if (data.type === '3' && (data.checked === true || data.checked === 'true')) {
        this.authResourceCheckData.push(data.id)
        // 同步更新已选数据
        this.allCheckedAuthResourceList.push(data)
      }
      // 当leaf是false,说明存在children，这里id为String，只选父级无效
      // 注：当button上绑定了数据权限时leaf会设置为false，数据权限放在dataRights中传给前端，所以判断是否叶子节点时还需判断数据权限的button
      if (data.leaf.toString() === 'true' || (data.dataRights && data.children.length === 0)) {
        if (data.checked === true || data.checked === 'true') {
          this.authResourceCheckData.push(data.id)
          // 同步更新已选数据
          this.allCheckedAuthResourceList.push(data)
          // 存储不可点击的选中数据
          if (data.disabled) {
            this.oldData.push(data)
          }
        }
        // 设置保存节点依赖关系
        data.relation && (data => {
          // 捕获所以被relation关联的id
          data.relation.split(',').forEach(e => {
            this.dependency[e] = this.dependency[e] ? this.dependency[e] : []
            this.dependency[e].push(data.id)
          })
        })(data)
        // 去重
        this.authResourceCheckData = Array.from(new Set(this.authResourceCheckData))
      } else {
        for (let i = 0; i < data.children.length; i++) {
          this.initAuthResource(data.children[i])
        }
      }
    },
    /**
     * 更新已选数据
     */
    updateAuthResources ({ checkedList = [], relationObject = {}, dataResourceList = [] }) {
      this.relationObject = relationObject
      this.dataResourceList = dataResourceList
    },
    authResourceSubmit () {
      // 判断是否默认角色 0不是 1是 默认角色不可修改权限
      if (this.$route.query.roleType === '1') {
        this.$message.error('角色对应权限不能修改!')
        return false
      }
      const checkedNodesList = this.getAllCheckedNodeList()
      let resourceIds = []
      checkedNodesList.forEach(function (item) {
        if (item) resourceIds.push(item.id)
      })
      // 去重
      resourceIds = Array.from(new Set(resourceIds))
      const rightExtends = this.relationObject

      const authResourceParams = {
        roleId: this.$route.query.id,
        listResource: (resourceIds = resourceIds.concat(this.dataResourceList)),
        rightExtends: rightExtends
      }
      // console.log('authResourceParams', authResourceParams)
      this.submitStatus = true
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/roleTenant/updateRoleResource', authResourceParams).then(response => {
        this.submitStatus = false
        if (this.$reponseStatus(response)) {
          this.$message.success('已成功分配！')
          this.delFromCache = true // 是否清除当前页面的路由缓存，当为true时清除；
          this.delToCache = true // 是否清除即将前往的那个页面的路由缓存，当为true时清除；
          this.back()
        }
      })
    },
    // 获取所以已选数据
    getAllCheckedNodeList () {
      const _this = this
      let allCheckedList = []
      // 更新已选数据
      if (this.tabFlag) {
        this.tabList.forEach(item => {
          const refsObj = _this.$refs[`authResourceCell${item.id}`]
          if (item.id && refsObj) {
            const list = JSON.parse(JSON.stringify(refsObj[0] && refsObj[0].getCheckedData()))
            // 补充虚拟根级节点
            list && list.length > 0 && list.push(item)
            allCheckedList = allCheckedList.concat(list)
          }
        })
      } else {
        allCheckedList = JSON.parse(JSON.stringify(this.$refs.authResourceCell.getCheckedData()))
      }
      return allCheckedList
    },
    // 返回
    back () {
      this.$router.back()
    },
    beforeLeaveTab (activeName, oldActiveName) {
      // console.log('beforeLeaveTab', activeName, oldActiveName)
    },
    handleTabsClick (tab, event) {
      // console.log('handleTabsClick', tab, event)
    },
    handleClick (event) {
      // console.log('handleClick', event)
    }
  }
}
</script>
<style lang="scss" scoped>
.webbas {
  .webbas-tree-css {
    height: 100%;
    // ::v-deep .el-tabs__content {
    //   height: 470px;
    //   overflow: hidden;
    //   .el-tab-pane {
    //     height: 490px;
    //     overflow: scroll;
    //   }
    // }
  }
  ::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    border-bottom: 2px solid #409eff;
    color: #303133;
    font-weight: 600;
  }
  ::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item:hover {
    color: #409eff;
  }
}
</style>
