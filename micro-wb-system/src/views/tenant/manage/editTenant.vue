<template>
  <asp-dialog v-model="dialogParam.modelVisible"
              :visible.sync="dialogParam.modelVisible"
              :title="dialogParam.title"
              width="60%">
    <template>
      <el-form ref="editTenat"
               :model="editTenat"
               :rules="rules">
        <el-row class="el-collapse-130">
          <el-col :span="12">
            <el-form-item label="租户名称:"
                          prop="name">
              <el-input v-if='showEdit'
                        v-model="editTenat.name"
                        placeholder="不能超过40个字符"></el-input>
              <span v-else> {{editTenat.name}} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="租户编码:"
                          prop="code">
              <el-input v-if='showEdit'
                        v-model="editTenat.code"
                        readonly
                        disabled
                        placeholder="系统自动生成"></el-input>
              <span v-else> {{editTenat.code}} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-130">
          <el-col :span="12">
            <el-form-item label="租户联系人:"
                          prop="contactName">
              <el-input v-if='showEdit'
                        v-model="editTenat.contactName"
                        placeholder="不能超过20个字符"></el-input>
              <span v-else> {{editTenat.contactName}} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人手机号:"
                          prop="contactMobile">
              <el-input v-if='showEdit'
                        v-model="editTenat.contactMobile"
                        maxlength="11"
                        show-word-limit
                        placeholder="请输入联系人手机号"></el-input>
              <span v-else> {{editTenat.contactMobile}} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-130">
          <el-col :span="12">
            <el-form-item label="联系人邮箱:"
                          prop="contactEmail">
              <el-input v-if='showEdit'
                        v-model="editTenat.contactEmail"
                        placeholder="请输入联系人邮箱"></el-input>
              <span v-else> {{editTenat.contactEmail}} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="租户有效期:"
                          prop="expireDate">
              <el-date-picker v-if='showEdit'
                              v-model="editTenat.expireDate"
                              :picker-options="pickerOptions"
                              type="date"
                              align="right"
                              placeholder
                              value-format="yyyy-MM-dd"
                              class="width-100-css"></el-date-picker>
              <span v-else> {{editTenat.expireDate}} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-130">
          <el-col :span="24">
            <el-form-item label="租户描述:"
                          prop="description">
              <el-input v-if='showEdit'
                        v-model="editTenat.description"
                        rows="6"
                        type="textarea"
                        show-word-limit
                        maxlength="500"
                        placeholder="不能超过500个字符"></el-input>
              <span v-else> {{editTenat.description}} </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow :name="showEdit ? '取消' : '关闭'"
                      icon="el-icon-close"
                      @click="dialogParam.modelVisible = false"></asp-btn-hollow>
      <asp-btn-solid v-if='showEdit'
                     name="确认"
                     icon="el-icon-check"
                     @click="editTenatSubmit('editTenat')"></asp-btn-solid>
    </template>
  </asp-dialog>
</template>

<script>
export default {
  name: 'EditTenant',
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    },
    operation: {
      type: String,
      defalut: 'edit'
    }
  },
  data () {
    var validateLoginName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('名称不能为空'))
      } else {
        var checkLoginNameUrl = this.$apiConfig.managerPathPrefix + '/tenant/checkTenant'
        const param = {
          type: '1', // 1:租户名称，2:租户编码
          value: value,
          id: this.editTenat.id
        }
        this.$aspHttps.asp_Post(checkLoginNameUrl, param).then(response => {
          // response.data校验结果，true-表示不重复，false-表示重复
          if (this.$reponseStatus(response) && response.data) {
            callback()
          } else {
            callback(new Error('名称已存在'))
          }
        })
      }
    }
    var validatePhone = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('手机号不能为空'))
      } else {
        var checkPhoneUrl =
          this.$apiConfig.managerPathPrefix + '/user/checkMobile'
        const param = {
          userId: this.editTenat.id, // 有Id值的不用校验自己的
          mobile: this.editTenat.mobile
        }
        this.$aspHttps.asp_Post(checkPhoneUrl, param).then(response => {
          if (this.$reponseStatus(response) && response.data) {
            callback()
          } else {
            callback(new Error('手机号已被使用'))
          }
        })
      }
    }
    var validateEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('邮箱不能为空'))
      } else {
        var checkEmailUrl =
          this.$apiConfig.managerPathPrefix + '/user/checkEmail'
        const param = {
          userId: this.editTenat.id,
          email: value
        }
        this.$aspHttps.asp_Post(checkEmailUrl, param).then(response => {
          if (this.$reponseStatus(response) && response.data) {
            callback()
          } else {
            callback(new Error('邮箱已被使用'))
          }
        })
      }
    }
    return {
      editTenat: {
        id: '',
        code: '',
        name: '',
        contactName: '',
        contactMobile: '',
        contactEmail: '',
        expireDate: '',
        description: ''
      },
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      rules: {
        name: [
          { required: true, message: '租户名称不能为空', trigger: 'blur' },
          { max: 40, message: '输入不能超过40个字符', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' },
          { validator: validateLoginName, trigger: 'blur' }
        ],
        contactName: [
          { required: true, message: '租户联系人不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' }
        ],
        contactMobile: [
          { required: true, message: '联系人手机号不能为空', trigger: 'blur' },
          { validator: this.$main_tools.validation.mobile, trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        contactEmail: [
          { required: true, message: '联系人邮箱不能为空', trigger: 'blur' },
          { max: 100, message: '输入不能超过100个字符', trigger: 'blur' },
          { validator: this.$main_tools.validation.email, trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' }
        ],
        expireDate: [
          { required: true, message: '租户有效期不能为空', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '租户描述不能为空', trigger: 'blur' },
          { max: 500, message: '输入不能超过500个字符', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    showEdit: function () {
      return this.operation === 'edit'
    }
  },
  watch: {
    'dialogParam.modelVisible' (val) {
      // 清空表单数据-弹框开关是form表单节点渲染不同步，会报错
      // this.$refs.editTenat.resetFields()
      this.editTenat = {
        id: '',
        code: '',
        name: '',
        contactName: '',
        contactMobile: '',
        contactEmail: '',
        expireDate: '',
        description: ''
      }
      if (val) {
        this.initDetail(this.dialogParam.id)
      }
    }
  },
  created () { },
  methods: {
    initDetail (id) {
      if (!id) return
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/tenant/get', { id }).then(res => {
        if (this.$reponseStatus(res) && res.data) {
          this.editTenat = res.data
        }
      })
    },
    editTenatSubmit () {
      this.$refs.editTenat.validate(valid => {
        if (valid) {
          console.log('submit', this.editTenat)
          const url = this.editTenat.id ? '/tenant/update' : '/tenant/insert'
          this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + url, this.editTenat).then(res => {
            if (this.$reponseStatus(res, false)) {
              this.$message.success({ message: '操作成功', appendToBody: true, customClass: 'message-box-tips' })
              this.dialogParam.modelVisible = false // 关闭弹窗
              this.$emit('search') // 刷新列表
            } else {
              this.$message.error({ message: res.message || '操作失败', appendToBody: true, customClass: 'message-box-tips' })
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.message-box-tips {
  z-index: 9999;
}
</style>
