<template>
  <asp-dialog v-model="dialogParam.modelVisible"
              :visible.sync="dialogParam.modelVisible"
              :title="dialogParam.title"
              width="60%">
    <template>
      <el-form ref="editTenatAdmin"
               autocomplete="off"
               :model="editTenatAdmin"
               :rules="rulesFunc">
        <el-row class="el-collapse-130">
          <el-col :span="12">
            <el-form-item label="归属域:"
                          prop="domain">
              <asp-select-all v-if='showEdit'
                              v-model="editTenatAdmin.domain"
                              :isShowAll="false"
                              :disabled="showDisabled"
                              :readonly="showDisabled"
                              :code-list="tenantDomainList"></asp-select-all>
              <span v-else> {{userDomainName(editTenatAdmin.domain)}} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if='!showEdit'
                          label="账号状态:"
                          prop="">
              <span> {{ userStatusName(editTenatAdmin.userStatus) }} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-130">
          <el-col :span="12">
            <el-form-item label="归属租户:"
                          prop="tenantId">
              <!-- 支持模糊搜索 -->
              <asp-select-all v-if='showEdit'
                              v-model="editTenatAdmin.tenantId"
                              :isShowAll="false"
                              :filterable="true"
                              :disabled="showDisabled"
                              :readonly="showDisabled"
                              :code-list="tenantList"></asp-select-all>
              <!-- 需要转换成Name -->
              <span v-else> {{editTenatAdmin.tenantName}} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号有效期:"
                          prop="userExpireDate">
              <el-date-picker v-if='showEdit'
                              v-model="editTenatAdmin.userExpireDate"
                              :picker-options="pickerOptions"
                              type="date"
                              align="right"
                              placeholder
                              value-format="yyyy-MM-dd"
                              class="width-100-css"></el-date-picker>
              <span v-else> {{editTenatAdmin.userExpireDate}} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-130">
          <el-col :span="12">
            <el-form-item label="用户名:"
                          prop="userName">
              <el-input v-if='showEdit'
                        v-model="editTenatAdmin.userName"
                        :disabled="showDisabled"
                        :readonly="showDisabled"
                        placeholder="不能超过20个字符"></el-input>
              <span v-else> {{editTenatAdmin.userName}} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名:"
                          prop="realName">
              <el-input v-if='showEdit'
                        v-model="editTenatAdmin.realName"
                        placeholder="不能超过20个字符"></el-input>
              <span v-else> {{editTenatAdmin.realName}} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-130">
          <el-col :span="12">
            <el-form-item label="手机号:"
                          prop="mobile">
              <el-input v-if='showEdit'
                        v-model="editTenatAdmin.mobile"
                        maxlength="11"
                        show-word-limit
                        placeholder="请输入联系人手机号"></el-input>
              <span v-else> {{editTenatAdmin.mobile}} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱:"
                          prop="email">
              <el-input v-if='showEdit'
                        v-model="editTenatAdmin.email"
                        auto-complete="off"
                        placeholder="请输入联系人邮箱"></el-input>
              <span v-else> {{editTenatAdmin.email}} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="showEdit"
                class="el-collapse-130">
          <el-col :span="12">
            <el-form-item :label="operation === 'add' ? '初始密码:' : '重置密码:'"
                          prop="ppwwdd">
              <el-input v-model="editTenatAdmin.ppwwdd"
                        auto-complete="new-password"
                        type="password"
                        show-password
                        placeholder=""
                        @blur="pdBlur('ppwwddConfirm')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码:"
                          prop="ppwwddConfirm">
              <el-input v-model="editTenatAdmin.ppwwddConfirm"
                        auto-complete="new-password"
                        type="password"
                        show-password
                        placeholder=""
                        @blur="pdBlur('ppwwdd')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow name="取消"
                      icon="el-icon-close"
                      @click="dialogParam.modelVisible = false"></asp-btn-hollow>
      <asp-btn-solid v-if='showEdit'
                     name="确认"
                     icon="el-icon-check"
                     @click="editTenatAdminSubmit('editTenatAdmin')"></asp-btn-solid>
    </template>
  </asp-dialog>
</template>

<script>
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
export default {
  components: {},
  name: 'EditTenantAdmin',
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    },
    operation: {
      type: String,
      defalut: 'add'
    }
  },
  data () {
    return {
      editTenatAdmin: {
        userId: '',
        domain: '',
        tenantId: '',
        userExpireDate: '',
        userName: '',
        realName: '',
        mobile: '',
        email: '',
        ppwwdd: '',
        ppwwddConfirm: ''
      },
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      tenantList: [] // 租户列表
    }
  },
  computed: {
    showEdit: function () {
      return ['edit', 'add'].includes(this.operation)
    },
    showDisabled: function () {
      return this.operation === 'edit'
    },
    // 租户归属域
    tenantDomainList () {
      const list = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME') || []
      return list
    },
    userStatusName () {
      return function (value) {
        const list = this.$aspUtils.getCodeValueByType(this, 'USER_STATUS') || []
        const item = list.find(val => val.code === value)
        return item ? item.name : ''
      }
    },
    userDomainName () {
      return function (value) {
        const item = this.tenantDomainList.find(val => val.code === value)
        return item ? item.name : ''
      }
    },
    rulesFunc () {
      const _this = this
      const rules = {
        domain: [
          { required: true, message: '归属域不能为空', trigger: 'change' }
        ],
        tenantId: [
          { required: true, message: '归属租户不能为空', trigger: 'change' }
        ],
        userExpireDate: [
          { required: true, message: '账号有效期不能为空', trigger: 'blur' }
        ],
        userName: [
          { required: true, message: '用户名不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' },
          { validator: _this.$main_tools.validation.checkSpecial, trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error('名称不能为空'))
              } else {
                const param = {
                  type: '1', // 1:用户名称，2:租户编码
                  value: value,
                  id: _this.editTenatAdmin.userId
                }
                _this.$aspHttps.asp_Post(_this.$apiConfig.managerPathPrefix + '/user/checkUserName', param).then(response => {
                  // response.data校验结果，true-表示不重复，false-表示重复
                  if (_this.$reponseStatus(response) && response.data) {
                    callback()
                  } else {
                    callback(new Error('名称已存在'))
                  }
                })
              }
            },
            trigger: 'blur'
          }
        ],
        realName: [
          { required: true, message: '真实姓名不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' },
          { validator: _this.$main_tools.validation.checkSpecial, trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          { validator: _this.$main_tools.validation.mobile, trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error('手机号不能为空'))
              } else {
                const param = {
                  userId: _this.editTenatAdmin.userId, // 有Id值的不用校验自己的
                  mobile: _this.editTenatAdmin.mobile
                }
                _this.$aspHttps.asp_Post(_this.$apiConfig.managerPathPrefix + '/user/checkMobile', param).then(response => {
                  if (this.$reponseStatus(response) && response.data) {
                    callback()
                  } else {
                    callback(new Error('手机号已被使用'))
                  }
                })
              }
            },
            trigger: 'blur'
          }
        ],
        email: [
          { required: true, message: '邮箱不能为空', trigger: 'blur' },
          { max: 100, message: '输入不能超过100个字符', trigger: 'blur' },
          { validator: _this.$main_tools.validation.email, trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error('邮箱不能为空'))
              } else {
                const param = {
                  userId: this.editTenatAdmin.userId,
                  email: value
                }
                this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/user/checkEmail', param).then(response => {
                  if (this.$reponseStatus(response) && response.data) {
                    callback()
                  } else {
                    callback(new Error('邮箱已被使用'))
                  }
                })
              }
            },
            trigger: 'blur'
          }
        ],
        ppwwdd: [
          { required: _this.operation === 'add', message: '密码不能为空', trigger: 'blur' },
          { validator: _this.$main_tools.validation.checkPassword, trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && value !== _this.editTenatAdmin.ppwwddConfirm) {
                callback(new Error('两次输入密码不一致'))
              } else {
                // this.$refs.editTenatAdmin.validateField('ppwwddConfirm')
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        ppwwddConfirm: [
          { required: _this.operation === 'add', message: '确认密码不能为空', trigger: 'blur' },
          { validator: _this.$main_tools.validation.checkPassword, trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && value !== _this.editTenatAdmin.ppwwdd) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
      return rules
    }
  },
  watch: {
    'dialogParam.modelVisible' (val) {
      // 清空表单数据-弹框开关是form表单节点渲染不同步，会报错
      // this.$refs.editTenatAdmin.resetFields()
      this.editTenatAdmin = {
        userId: '',
        domain: '',
        tenantId: '',
        userExpireDate: '',
        userName: '',
        realName: '',
        mobile: '',
        email: '',
        ppwwdd: '',
        ppwwddConfirm: ''
      }
      if (val) {
        this.initTenantList()
        this.initDetail(this.dialogParam.id)
      }
    }
  },
  methods: {
    pdBlur (prop) {
      this.$refs.editTenatAdmin.validateField(prop)
    },
    /**
     * 初始化获取租户管理员详情
     * @param id 租户管理员ID
     */
    initDetail (id) {
      if (!id) return
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/tenantAdmin/get', { userId: id }).then(res => {
        if (this.$reponseStatus(res) && res.data) {
          this.editTenatAdmin = res.data
        }
      })
    },
    /**
     * 初始化租户列表
     */
    initTenantList () {
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/tenant/listForCombo', {}).then(res => {
        if (this.$reponseStatus(res) && res.data) {
          this.tenantList = res.data.map(item => Object.assign(item, { code: item.id }))
        }
      })
    },
    addRSA (ppwwdd, modulus, exponent) {
      // RSA
      asp_RSAKey.RSASetPublic(modulus, exponent)
      return asp_RSAKey.RSAEncrypt(ppwwdd)
    },
    /**
     * 提交租户管理员
     */
    editTenatAdminSubmit () {
      this.$refs.editTenatAdmin.validate(valid => {
        if (valid) {
          console.log('submit', this.editTenatAdmin)
          const url = this.editTenatAdmin.userId ? '/tenantAdmin/update' : '/tenantAdmin/insert'
          // 处理密码
          let ppwwdd = ''
          if (this.editTenatAdmin.ppwwdd) {
            const { platformConfig } = this.$aspUtils.getListAllObject(this)
            const modulus = platformConfig ? platformConfig.modulus : ''
            const exponent = platformConfig ? platformConfig.exponent : ''
            ppwwdd = this.editTenatAdmin.ppwwdd
            this.editTenatAdmin.password = this.addRSA(ppwwdd, modulus, exponent)
            delete this.editTenatAdmin.ppwwdd
            delete this.editTenatAdmin.ppwwddConfirm
          }
          this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + url, this.editTenatAdmin).then(res => {
            this.editTenatAdmin.ppwwdd = ppwwdd
            this.editTenatAdmin.ppwwddConfirm = ppwwdd
            if (this.$reponseStatus(res, false)) {
              this.$message.success({ message: '操作成功', appendToBody: true, customClass: 'message-box-tips' })
              this.dialogParam.modelVisible = false // 关闭弹窗
              this.$emit('search') // 刷新列表
            } else {
              this.$message.error({ message: res.message || '操作失败', appendToBody: true, customClass: 'message-box-tips' })
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.message-box-tips {
  z-index: 9999;
}
</style>
