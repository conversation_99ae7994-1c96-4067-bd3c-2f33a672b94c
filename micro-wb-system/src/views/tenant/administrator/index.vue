/** * 租户管理 */
<template>
  <div class="webbas">
    <template>
      <div class="list-page-content-css">
        <div class="query-area-content-css">
          <el-form ref="searchForm"
                   :inline="true"
                   :model="table.searchForm"
                   @submit.native.prevent>
            <el-row>
              <el-col :span="6">
                <el-form-item prop="name"
                              label="关键字：">
                  <el-input v-model.trim="table.searchForm.name"
                            placeholder="用户名或真实姓名"
                            name="name"
                            @keyup.enter.native="search()">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="mobile"
                              label="手机：">
                  <el-input v-model.trim="table.searchForm.mobile"
                            placeholder="手机号码"
                            name="mobile"
                            @keyup.enter.native="search()">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="tenantId"
                              label="归属租户：">
                  <asp-select-all v-model="table.searchForm.tenantId"
                                  :filterable="true"
                                  :code-list="tenantList"></asp-select-all>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="status"
                              label="账号状态：">
                  <asp-select-all v-model="table.searchForm.status"
                                  :code-list="tenantStatusList"></asp-select-all>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item class="query-area-btn-css">
                  <asp-btn-solid name="查询"
                                 icon="el-icon-search"
                                 @click="search()"></asp-btn-solid>
                  <asp-btn-hollow icon="el-icon-refresh"
                                  name="重置"
                                  @click="reset()"></asp-btn-hollow>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <asp-table ref="table"
                   :url="table.url"
                   :param="table.searchForm"
                   :prefix="table.prefix"
                   type="">
          <template slot="header">
            <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_tenantAdmin02' })"
                           name="新增"
                           icon="el-icon-plus"
                           @click="addRole"></asp-btn-solid>
            <!-- <asp-btn-hollow v-hasAuth="doAuth({ btnCode: 'wb_tenantManage06' })"
                            name="导出"
                            icon="el-icon-download"
                            @click="exportList"></asp-btn-hollow> -->
          </template>
          <asp-table-column prop="userName"
                            min-width="90"
                            label="用户名称"></asp-table-column>
          <asp-table-column prop="realName"
                            min-width="90"
                            label="真实姓名"></asp-table-column>
          <asp-table-column prop="mobile"
                            width="110"
                            show-overflow-tooltip
                            label="手机号"></asp-table-column>
          <asp-table-column prop="email"
                            width="139"
                            show-overflow-tooltip
                            label="邮箱"></asp-table-column>
          <asp-table-column prop="tenantName"
                            min-width="90"
                            label="归属租户"></asp-table-column>
          <asp-table-column :formatter="dictDomainStatus"
                            prop="domain"
                            width="90"
                            label="归属域"></asp-table-column>
          <!-- dictCodeName(row.status,tenantStatusList) -->
          <asp-table-column :formatter="dictTenantStatus"
                            prop="userStatus"
                            width="90"
                            label="账号状态"></asp-table-column>
          <asp-table-column :width="this.$aspFontSize.asp_ColButtonSize([2, 2, 4])"
                            label="操作"
                            fixed="right">
            <template slot-scope="{ scope }">
              <asp-btn-text name="查看"
                            @click="detailTenant(scope.$index, scope.row)">
              </asp-btn-text>
              <!-- <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_040105' })" -->
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_tenantAdmin03' })"
                            name="修改"
                            @click="editTenant(scope.$index, scope.row)">
              </asp-btn-text>
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_tenantAdmin04' })"
                            name="删除"
                            @click="delTenant(scope.row)">
              </asp-btn-text>
            </template>
          </asp-table-column>
        </asp-table>
      </div>
      <!-- 新增/修改租户 -->
      <editTenantAdmin ref="editTenantAdmin"
                       :dialog-param="tenantModelParam"
                       :operation="tenantModelParam.oper"
                       @search="search"></editTenantAdmin>
    </template>
  </div>
</template>

<script>
import editTenantAdmin from './editTenantAdmin.vue'
export default {
  name: 'administrator',
  components: { editTenantAdmin },
  data () {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/tenantAdmin/listPage',
        searchForm: {
          name: '',
          mobile: '',
          tenantId: '',
          status: ''
        }
      },
      tenantModelParam: {},
      tenantList: [] // 租户列表
    }
  },
  computed: {
    // 租户状态
    tenantStatusList () {
      const list = this.$aspUtils.getCodeValueByType(this, 'USER_STATUS') || []
      return list
    },
    // 租户归属域
    tenantDomainList () {
      const list = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME') || []
      return list
    }
  },
  beforeMount () { },
  created () {
    this.initTenantList()
  },
  mounted () { },
  methods: {
    /**
     * 按钮权限
     * @method doAuth
     * @param {object} opt
     * @return {object}
     */
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    /**
     * 导出excel
     * @method exportList
     */
    exportList (pageRestart) {
      // console.log(pageRestart)
      let params = ''
      const obj = this.table.searchForm
      for (const key in obj) {
        params += key + '=' + (obj[key] === undefined ? '' : obj[key]) + '&'
      }
      params = params.substring(0, params.length - 1)
      let url = this.$apiConfig.managerPathPrefix + '/tenantAdmin/export?' + params
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    /**
     * 初始化租户列表
     */
    initTenantList () {
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/tenant/listForCombo', {}).then(res => {
        if (this.$reponseStatus(res) && res.data) {
          this.tenantList = res.data.map(item => Object.assign(item, { code: item.id }))
        }
      })
    },
    search () {
      this.$nextTick(() => {
        this.$refs.table.asp_search()
      })
    },
    reset () {
      this.$refs.table.asp_reset()
    },
    // 新增租户管理员
    addRole () {
      this.tenantModelParam = {
        title: '新增租户管理员',
        oper: 'add',
        modelVisible: true
      }
    },
    // 修改租户管理员
    editTenant (index, row) {
      this.tenantModelParam = {
        title: '修改租户管理员',
        oper: 'edit',
        id: row.userId,
        modelVisible: true
      }
    },
    // 查看租户管理员
    detailTenant (index, row) {
      this.tenantModelParam = {
        title: '租户管理员详情',
        oper: 'detail',
        id: row.userId,
        modelVisible: true
      }
    },
    // 删除租户
    delTenant (row) {
      this.$aspMsgbox.confirm(this, '确认删除此租户管理员？', function (arg, instance) {
        // console.log(arg, instance)
        const url = this.$apiConfig.managerPathPrefix + '/tenantAdmin/delete'
        this.$aspHttps.asp_Post(url, { id: row.userId }).then(response => {
          if (this.$reponseStatus(response)) {
            this.$message.success('删除成功!')
            this.search()
          }
        })
      })
    },
    dictCodeName (code, list) {
      const item = list.find(val => val.code === code)
      return item ? item.name : code
    },
    dictTenantStatus (row) {
      const item = this.tenantStatusList.find(val => val.code === row.userStatus)
      return item ? item.name : ''
    },
    dictDomainStatus (row) {
      const item = this.tenantDomainList.find(val => val.code === row.domain)
      return item ? item.name : ''
    }
  }
}
</script>
