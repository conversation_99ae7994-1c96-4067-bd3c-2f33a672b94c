<template>
   <div class="webbas" style="position: relative">
    <div
      class="home"
      style="position: absolute; left: 0; right: 0; top: 0; bottom: 0"
    >
      <iframe
        v-if="smartDataPage"
        ref="iframeSmartDataPage"
        style="width: 100%; height: 100%"
        :src="smartDataPage"
        frameborder="0"
      ></iframe>
    </div>

    <!-- 修改店铺订单量弹窗 -->
    <modificationOrderQuantity
      :dialogVisible="dialogVisible"
      @configSuccess="configSuccess"
      @changeShow="(val) => dialogVisible = val"
    />
  </div>
</template>

<script>
import { mixins } from '../pagedata_mixin'
export default {
  mixins: [mixins],
  created() {
    this.isGroup().then((isgroup) => {
      this.initSmartDataPage('ORDER').then(res => {
        this.smartDataPage = res += `?pageVarOpt=[{"value":${isgroup ? '0' : '1'},"id":"isGroup"}]`
      })
    })
  }
}
</script>

<style>

</style>
