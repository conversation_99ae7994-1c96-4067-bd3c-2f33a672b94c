
import modificationOrderQuantity from '../../components/modificationOrderQuantity/index.vue'

export const mixins = {
  data() {
    return {
      smartDataPage: null,
      dialogVisible: false,
      vloading: false,
      vloading2: false
    }
  },
  components: {
    modificationOrderQuantity
  },
  computed: {},
  created() {
  },
  mounted() {
    if (!window.WindowGetMsgFromIframe) {
      window.WindowGetMsgFromIframe = true
      window.addEventListener('message', this.getMsgFromIframe, false)
    }
  },
  destroyed() {
    // window.removeEventListener('message', this.getMsgFromIframe, false)
  },
  methods: {
    /* 是否是集团账户 */
    isGroup() {
      return new Promise((resolve) => {
        this.$aspHttps
          .asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/getCurrent')
          .then((response) => {
            if (this.$reponseStatus(response)) {
              // 判断用户来源地区
              sessionStorage.setItem('currentCode', response.data.divisions[0])
              if (response.data.departmentId) {
                response.data.departmentId = response.data.departmentId.replace(/\s*/g, '')
              }

              if (response.data.departmentId) {
                resolve(true)
              } else {
                resolve(false)
              }
            } else {
              resolve(false)
            }
          })
          .catch(() => {
            resolve(false)
          })
      })
    },

    /* 向iframe发送消息 */
    configSuccess() {
      const iframeSmartDataPage = this.$refs.iframeSmartDataPage
      // console.log('发送给iframe')
      // iframeSmartDataPage.contentWindow.postMessage({ updateStore: true }, window.location.origin)
      iframeSmartDataPage.contentWindow.location.reload()
    },

    /* 接收iframe传来的消息 */
    getMsgFromIframe(e) {
      console.log(e)

      if (!e.data) {
        return
      }

      // 通过Iframe控制框架进行菜单跳转的，可传递menuId进行控制对应菜单跳转
      if (JSON.stringify(e.data).indexOf('{') === -1) {
        if (this.$main_tools.menuTreeUtil.openMenuPageByIdOrUrl) {
          this.$main_tools.menuTreeUtil.openMenuPageByIdOrUrl(e.data)
        }
        return
      }

      const data = JSON.parse(JSON.stringify(e.data))

      // 接收Iframe子应用项目---接收修改订单量操作
      if (data && data.changeStore === true) {
        this.dialogVisible = true
      }

      // 接收Iframe子应用项目---接收导出店铺表格操作
      if (data && data.ExportShopDataReq) {
        if (!this.ExportShopDataReq) {
          this.vloading = true
          this.ExportShopDataReq = true
          this.$aspHttps.asp_FileDownload(
            this.$apiConfig.level1cloudstorePathPreFix +
            '/databoard/export/exportShopData',
            {
              ...data.ExportShopDataReq
            },
            '导出表格'
          ).then(() => {
            this.ExportShopDataReq = false
            this.vloading = false
          }).catch(() => {
            this.ExportShopDataReq = false
            this.vloading = false
          })
        }
      }

      // 接收Iframe子应用项目---接收导出订单表格操作
      if (data && data.ExportOrderDataReq) {
        if (!this.ExportOrderDataReq) {
          this.ExportOrderDataReq = true
          this.vloading2 = true
          this.$aspHttps.asp_FileDownload(
            this.$apiConfig.level1cloudstorePathPreFix +
              '/databoard/export/exportOrderData',
            {
              ...data.ExportOrderDataReq
            },
            '导出表格'
          ).then(() => {
            this.ExportOrderDataReq = false
            this.vloading2 = false
          }).catch(() => {
            this.ExportOrderDataReq = false
            this.vloading2 = false
          })
        }
      }
    },

    /* 获取iframe链接 */
    initSmartDataPage(type = null) {
      return new Promise(resolve => {
        // 省公司
        this.$aspHttps
          .asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix +
      '/databoard/page/getPageId',
            {
              type
            }
          )
          .then((res) => {
            if (res.status === '200') {
              const { pageId } = res.data
              this.smartDataPage = this.$projectConfig.smartDataPage.replace(
                '{code}',
                pageId
              )
              resolve(this.smartDataPage)
            } else {
              this.$message(res.message)
              resolve(null)
            }
          })
      })
    }
  }
}
