{"list": [{"type": "form", "label": "表单容器", "columnName": "form_search", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_search_one", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 12, "align": "left", "list": [{"type": "input", "label": "缓存键：", "columnName": "keyword", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "搜索Keys", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 60, "bpmFlowImageCloumnName": "", "oldColumnName": "input_1645603707775"}]}, {"span": 12, "align": "right", "list": [{"type": "button", "label": "查询", "columnName": "search", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-search"}, "class": "solid-with-icon-btn ", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "button_1645604232718", "tableId": "redisCacheTable", "formId": "form_search", "event": "submit"}, {"type": "button", "label": "重置", "columnName": "reset", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-refresh"}, "class": "hollow-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "oldColumnName": "button_1645604234400", "tableId": "redisCacheTable", "formId": "form_search", "event": "reset-submit"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false}, {"type": "empty", "label": "空容器", "columnName": "empty_table", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "form", "label": "表单容器", "columnName": "form_1646040766028", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1646040774921", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 24, "align": "left", "list": [{"label": "", "type": "text", "columnName": "totalTip", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "classify": "form", "isLabelWidth": false, "span": 24, "width": "100%", "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": "", "customLabelWidth": true, "oldColumnName": "totalTip1", "class": "total-tip-css"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false, "hasParentForm": false, "labelWidth": 100}, {"type": "table", "label": "Redis缓存管理表格", "columnName": "redisCacheTable", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc", "statusList": []}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [{"timeStamp": 1645604474099, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true}, {"label": "类型", "columnName": "dataType", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "缓存键", "columnName": "redisKey", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "过期时间", "columnName": "expire", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "元素总数", "columnName": "elCount", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}], "operation": [], "toolList": [{"type": "text", "timestamp": 1645604617600, "columnName": "deleteBtn", "label": "删除", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "confirmationSwitch": true, "confirmationMessage": "确认删除缓存$redisKey$？", "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/proxy_webbas/web/support/v1/cacheManage/redis/delete", "apiSuccessTip": false, "apiFailTip": false, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"key\":\"$redisKey$\"}"}, {"type": "text", "timestamp": 1645604618265, "columnName": "detailBtn", "label": "详情", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "dialog-form", "dialogConfig": {"bindName": "redisDetailForm", "title": "详情页面", "subTitle": "", "titleClass": "dialog-title-default", "width": "1200px", "height": "auto", "toolList": []}}], "operation-width": "100", "pagination": {"background": false, "layout": "total", "pageSizes": [20], "currentPage": 1, "pageSize": 20, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/proxy_webbas/web/support/v1/cacheManage/redis/query"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1645604474099, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true}, {"label": "类型", "columnName": "dataType", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "缓存键", "columnName": "redisKey", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "过期时间", "columnName": "expire", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "元素总数", "columnName": "elCount", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}], "hasParentForm": false, "labelWidth": 100, "formId": "form_search", "router": {}, "show-operation-status": []}], "classify": "empty", "hidden": false}], "model": {"keyword": "", "totalTip": ""}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "redis-cache", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "mini", "class": "", "layoutType": "flex", "statusList": [], "localProxy": "/proxy_webbas", "nigxProxy": "/proxy_webbas", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": ""}, "titleName": "Redis缓存管理"}}