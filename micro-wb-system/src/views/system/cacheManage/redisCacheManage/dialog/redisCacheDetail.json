{"formList": [{"label": "栅格布局", "type": "row", "columnName": "row_one", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 12, "childList": [{"label": "类型", "type": "input", "columnName": "dataType", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": false, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 100, "operations": [{"status": ["detail"], "attr": "label", "formatter": ""}]}]}, {"span": 12, "childList": [{"label": "键", "type": "input", "columnName": "redisKey", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": false, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 100, "operations": [{"status": ["detail"], "attr": "label", "formatter": ""}]}]}], "name": "layout", "labelWidth": 160}, {"label": "栅格布局", "type": "row", "columnName": "row_two", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 12, "childList": [{"label": "过期时间", "type": "input", "columnName": "expire", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": false, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 100, "operations": [{"status": ["detail"], "attr": "label", "formatter": ""}]}]}, {"span": 12, "childList": [{"label": "元素总数", "type": "input", "columnName": "elCount", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": false, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 100, "operations": [{"status": ["detail"], "attr": "label", "formatter": ""}]}]}], "name": "layout", "labelWidth": 160}, {"label": "栅格布局", "type": "row", "columnName": "row_three", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 24, "childList": [{"label": "值", "type": "textarea", "columnName": "values", "defaultValue": "", "isModel": true, "required": false, "requiredCutomizeTips": "", "rules": [], "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": "", "shortcut": false, "shortcutOptions": [{"label": "option1", "value": "0"}], "class": "", "operation": [], "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "wordLimitStyle": "transparent", "clearable": true, "disabled": false, "readonly": false, "rows": "5", "text": ""}, "span": 24, "width": "100%", "isCompare": false, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "", "labelWidth": 100, "operations": [{"status": ["detail"], "attr": "label", "formatter": ""}]}]}], "name": "layout", "labelWidth": 160}, {"label": "", "type": "buttonGroup", "isLabelWidth": false, "columnName": "buttonGroup_one", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "close", "type": "primary", "icon": "el-icon-close", "label": "关闭", "interactive": "", "validateProp": "", "apiName": "", "class": "hollow-with-icon-btn", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_close_dialog"}], "name": "layout", "labelWidth": 160, "isDialogFooterBtns": true}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "starPostion": "left", "class": "", "exportName": "redisCacheDetail", "defaultClass": "webbas", "size": "mini", "statusList": ["detail"], "serverProps": {"localProxy": "/proxy_webbas", "nigxProxy": "/proxy_webbas", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "titleName": "Redis缓存详情页面"}, "dataConfig": {}, "virtual_model": {}, "model": {"dataType": "", "elCount": "", "expire": "", "redisKey": "", "values": ""}}