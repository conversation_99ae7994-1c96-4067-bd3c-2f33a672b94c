<template>
    <div class="webbas">
        <div class="list-page-content-css">
            <asp-table ref="table"
                       :url="table.url"
                       :param="table.searchForm"
                       :prefix="table.prefix"
                       :show-page="false"
                       index-switch
                       type=""
            >
                <asp-table-column prop="code" min-width="200" align="center" label="正常值">
                    <template slot-scope="{scope}">
                        <center>
                            <div :style="'background: ' + scope.row.code" style="width: 21px; height: 21px; border-radius: 10px;"></div>
                        </center>
                    </template>
                </asp-table-column>
                <asp-table-column prop="useNum" min-width="200" align="center" show-overflow-tooltip label="当前使用人数">
                </asp-table-column>
                <asp-table-column prop="defaultFlag" min-width="150" align="center" label="系统默认色系">
                    <template slot-scope="{scope}">
                        <el-switch
                            v-model="scope.row.defaultFlag"
                            active-color="#13ce66"
                            inactive-color="#cccccc"
                            @change="changeSwitch( $event, scope.row, scope.$index )"
                        >
                        </el-switch>
                    </template>
                </asp-table-column>
                <asp-table-column prop="status" min-width="150" align="center" label="隐藏此主题">
                    <template slot-scope="{scope}">
                        <el-checkbox v-model="scope.row.status" @change="hiddenTheme(scope)"></el-checkbox>
                    </template>
                </asp-table-column>
            </asp-table>
        </div>
    </div>
</template>

<script>
export default {
  name: 'ThemeList',
  data() {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/userSettings/listSystemSkin',
        searchForm: {}
      }
    }
  },
  mounted () {},
  methods: {
    // 系统默认色系切换
    changeSwitch (data, row, index) {
      const url = this.$apiConfig.managerPathPrefix + '/userSettings/saveDefaultSkin'
      const params = {
        code: row.code
      }
      this.$aspHttps.asp_Post(url, params).then((res) => {
        if (this.$aspUtils.asp_ReponseStatus(res)) {
          this.$refs.table.asp_search()
          this.$message.success(res.message)
        } else {
          const newRow = row
          newRow.defaultFlag = !newRow.defaultFlag
          this.$refs.table.listData[index] = newRow
        }
      })
    },
    // 隐藏此主题切换
    hiddenTheme (scope) {
      const url = scope.row.status ? '/userSettings/inactiveSkin' : '/userSettings/activeSkin'
      const param = {
        code: scope.row.code
      }
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + url, param).then((res) => {
        if (this.$aspUtils.asp_ReponseStatus(res)) {
          this.$refs.table.asp_search()
          this.$message.success(res.message)
        } else {
          const newRow = scope.row
          newRow.status = !newRow.status
          this.$refs.table.listData[scope.index] = newRow
        }
      })
    }
  }
}
</script>