/** * Created by wang<PERSON><PERSON> on 2020/01/03 * 字典表维护菜单详情页面 */
<template>
  <asp-dialog
    :visible.sync="dialogParam.modalVisible"
    :title="'详情信息'"
    width="70%"
  >
    <template>
      <el-form
        ref="commonForm"
        :model="commonForm"
        :inline="true"
        label-width="100px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="类型编码：">
              <span>{{ commonForm.code }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型名称：">
              <span>{{ commonForm.name }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="描述：">
              <span>{{ commonForm.description }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="枚举值：">
              <el-table :data.sync="commonForm.dicts" border>
                <el-table-column
                  prop="code"
                  label="枚举值编码"
                  min-width="60px"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="name"
                  label="枚举值"
                  min-width="60px"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="orderKey"
                  label="枚举值次序号"
                  min-width="60px"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="status"
                  label="状态"
                  min-width="60px"
                  align="center"
                >
                  <template slot-scope="scoped">
                    {{ scoped.row.status === "1" ? "有效" : "无效" }}
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow
        name="返回"
        icon="el-icon-close"
        @click="dialogParam.modalVisible = false"
      ></asp-btn-hollow>
    </template>
  </asp-dialog>
</template>

<script>
export default {
  name: 'DictionaryDetailModal',
  components: {},
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      commonForm: {
        dicts: []
      }
    }
  },
  watch: {
    'dialogParam.modalVisible'(val) {
      if (val) {
        this.commonForm.code = this.dialogParam.rowData.code
        this.commonForm.name = this.dialogParam.rowData.name
        this.commonForm.description = this.dialogParam.rowData.description
        this.commonForm.dicts = this.dialogParam.rowData.dicts
      }
    }
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>
