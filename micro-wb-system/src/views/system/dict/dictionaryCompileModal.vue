/** * Created by wang<PERSON>ping on 2020/01/03 * 字典表维护菜单修改弹窗 */
<template>
  <asp-dialog :visible.sync="dialogParam.modalVisible"
              :title="'修改'"
              width="70%">
    <template>
      <el-form ref="commonForm"
               :model="commonForm"
               :rules="rules"
               :inline="true">
        <el-row>
          <el-col :span="12"
                  class="el-collapse-90">
            <el-form-item label="类型编码："
                          prop="code">
              <el-input v-model="commonForm.code"
                        disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12"
                  class="el-collapse-90">
            <el-form-item label="类型名称："
                          prop="name">
              <el-input v-model="commonForm.name"
                        disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24"
                  class="el-collapse-90">
            <el-form-item label="描述："
                          prop="description">
              <el-input v-model="commonForm.description"
                        rows="3"
                        type="textarea"
                        placeholder="不超过300个字符"
                        maxlength="300"
                        show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-table :data.sync="commonForm.dicts"
                      border>
              <el-table-column label="枚举值编码"
                               min-width="60px"
                               align="center">
                <template slot-scope="scoped">
                  <el-form-item :ref="'dicts.' + scoped.$index + '.code'"
                                :prop="'dicts.' + scoped.$index + '.code'"
                                :rules="[
                      {
                        validator: (rule, value, callback) => {
                          checkEnum(
                            rule,
                            value,
                            callback,
                            scoped.$index,
                            'code',
                            '编码值不能为空',
                            '编码值不能重复'
                          );
                        },
                        trigger: 'blur'
                      }
                    ]">
                    <el-input v-model="scoped.row.code"
                              placeholder="不超过20个字符"
                              maxlength="20"
                              @blur="inputChange('code')"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="枚举值"
                               min-width="60px"
                               align="center">
                <template slot-scope="scoped">
                  <el-form-item :ref="'dicts.' + scoped.$index + '.name'"
                                :prop="'dicts.' + scoped.$index + '.name'"
                                :rules="[
                      {
                        validator: (rule, value, callback) => {
                          checkEnum(
                            rule,
                            value,
                            callback,
                            scoped.$index,
                            'name',
                            '枚举值不能为空',
                            '枚举值不能重复'
                          );
                        },
                        trigger: 'blur'
                      }
                    ]">
                    <el-input v-model="scoped.row.name"
                              placeholder="不超过20个字符"
                              maxlength="20"
                              @blur="inputChange('name')"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="枚举值序号"
                               min-width="60px"
                               align="center">
                <template slot-scope="scoped">
                  <el-form-item :ref="'dicts.' + scoped.$index + '.orderKey'"
                                :prop="'dicts.' + scoped.$index + '.orderKey'"
                                :rules="[
                      {
                        pattern: /^[0-9]+$/,
                        message: '只能输入数字',
                        trigger: 'blur'
                      },
                      {
                        validator: (rule, value, callback) => {
                          checkEnum(
                            rule,
                            value,
                            callback,
                            scoped.$index,
                            'orderKey',
                            '次序号不能为空',
                            '次序号不能重复'
                          );
                        },
                        trigger: 'blur'
                      }
                    ]">
                    <el-input v-model.number="scoped.row.orderKey"
                              placeholder="不超过9个字符"
                              maxlength="9"
                              @blur="inputChange('orderKey')"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="状态"
                               min-width="40px"
                               align="center">
                <template slot-scope="scoped">
                  <el-form-item :ref="'dicts.' + scoped.$index + '.status'"
                                :prop="'dicts.' + scoped.$index + '.status'">
                    <el-select v-model="scoped.row.status"
                               size="mini">
                      <el-option v-for="item in dropDownObject"
                                 :key="item.codeKey"
                                 :label="item.codeValue"
                                 :value="item.codeKey"></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column :render-header="renderOperation"
                               label="操作"
                               min-width="30px"
                               align="center">
                <template slot-scope="scoped">
                  <el-button size="mini"
                             type="text"
                             icon="el-icon-minus operation-cursor"
                             @click="deleteLine(scoped.$index)"></el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow name="取消"
                      icon="el-icon-close"
                      @click="dialogParam.modalVisible = false"></asp-btn-hollow>
      <asp-btn-solid v-loading="loading"
                     name="保存"
                     icon="el-icon-check"
                     @click="buttonSubmit"></asp-btn-solid>
    </template>
  </asp-dialog>
</template>

<script>
export default {
  name: 'DictionaryCompileModal',
  components: {},
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      loading: false,
      dropDownObject: [
        { codeKey: '1', codeValue: '有效' },
        { codeKey: '0', codeValue: '无效' }
      ],
      commonForm: {
        code: '',
        name: '',
        description: '',
        dicts: [
          {
            code: '',
            name: '',
            orderKey: '',
            status: '1'
          }
        ]
      },
      rules: {
        name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入类型编码', trigger: 'blur' }]
      }
    }
  },
  watch: {
    'dialogParam.modalVisible' (val) {
      if (val) {
        this.commonForm.code = this.dialogParam.rowData.code
        this.commonForm.name = this.dialogParam.rowData.name
        this.commonForm.description = this.dialogParam.rowData.description
        this.commonForm.dicts = this.dialogParam.rowData.dicts
        if (this.$aspUtils.isEmptyArray(this.commonForm.dicts)) {
          this.commonForm.dicts = [
            {
              code: '',
              name: '',
              orderKey: '',
              status: '1'
            }
          ]
        }
      } else {
        this.$refs.commonForm.resetFields()
        this.commonForm.dicts = [
          {
            code: '',
            name: '',
            orderKey: '',
            status: '1'
          }
        ]
      }
    }
  },
  created () { },
  mounted () { },
  methods: {
    // 设置操作新增按钮
    renderOperation (h, { column }) {
      return h('div', [
        h('span', column.label + ' '),
        h('i', {
          class: 'el-icon-plus operation-cursor',
          on: {
            click: this.addLine
          }
        })
      ])
    },
    // 保存
    buttonSubmit () {
      this.$refs.commonForm.validate(valid => {
        if (valid) {
          const param = {
            ...this.commonForm
          }
          this.loading = true
          this.$aspHttps
            .asp_Post(
              this.$apiConfig.supportPathPrefix + '/dict/updateType',
              param
            )
            .then(response => {
              if (this.$reponseStatus(response)) {
                this.$message.success('保存成功')
                this.dialogParam.modalVisible = false
                this.$emit('search')
                // 字典表
                const url = this.$apiConfig.supportPathPrefix + '/dict/listAll'
                const param = { domain: this.$projectConfig.domain }
                this.$aspHttps.asp_PostForm(url, param).then((response) => {
                  if (this.$reponseStatus(response)) {
                    this.$aspUtils.saveSStorage(this, 'codeData', response.data)
                  }
                })
              }
              this.loading = false
            })
        }
      })
    },
    // 表格校验
    checkEnum (
      rule,
      value,
      callback,
      index,
      field,
      emptyMessage,
      repeatMessage
    ) {
      if (value !== '') {
        this.equalCheck(index, value, field)
          ? callback(repeatMessage)
          : callback()
      } else {
        callback(emptyMessage)
      }
    },
    // input校验重复
    inputChange (str) {
      for (let i = 0; i < this.commonForm.dicts.length; i++) {
        this.$refs.commonForm.validateField('dicts.' + i + '.' + str)
      }
    },
    // table字段重复判断
    equalCheck (index, value, field) {
      index = parseInt(index)
      return this.commonForm.dicts.some((val, i) => {
        return i === index ? false : val[field] === value
      })
    },
    // 增加行
    addLine () {
      this.commonForm.dicts.push({
        code: '',
        name: '',
        orderKey: '',
        status: '1'
      })
    },
    // 删除行
    deleteLine (index) {
      this.commonForm.dicts.splice(index, 1)
      this.$nextTick(() => {
        this.$refs.commonForm.validate()
      })
    }
  }
}
</script>
