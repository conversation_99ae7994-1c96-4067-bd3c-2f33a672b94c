/** * 字典表维护菜单列表 */
<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form ref="searchForm"
                 :inline="true"
                 :model="table.searchForm">
          <el-row>
            <el-col :span="6">
              <el-form-item label="类型编码："
                            prop="code">
                <el-input v-model="table.searchForm.code"
                          placeholder="请输入类型编码">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="类型名称："
                            prop="name">
                <el-input v-model="table.searchForm.name"
                          placeholder="请输入类型名称">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="query-area-btn-css">
                <asp-btn-solid icon="el-icon-search"
                               name="查询"
                               @click="search()"></asp-btn-solid>
                <asp-btn-hollow icon="el-icon-refresh"
                                name="重置"
                                @click="reset()"></asp-btn-hollow>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <asp-table ref="table"
                 :url="table.url"
                 :param="table.searchForm"
                 :prefix="table.prefix"
                 :index-switch="false"
                 :return-list-data="true"
                 type=""
                 @renderListData="renderListData">
        <asp-table-column prop="code"
                          min-width="110"
                          show-overflow-tooltip
                          label="类型编码">
        </asp-table-column>
        <asp-table-column prop="name"
                          min-width="100"
                          show-overflow-tooltip
                          label="类型名称">
        </asp-table-column>
        <asp-table-column prop="description"
                          min-width="150"
                          label="描述"
                          show-overflow-tooltip>
        </asp-table-column>
        <asp-table-column prop="enums"
                          min-width="150"
                          show-overflow-tooltip
                          label="枚举值">
        </asp-table-column>
        <asp-table-column :width="this.$aspFontSize.asp_ColButtonSize([2, 2])"
                          label="操作">
          <template slot-scope="{ scope }">
            <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_120102' })"
                          name="查看"
                          @click="detail(scope.row)"></asp-btn-text>
            <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_120104' })"
                          name="修改"
                          @click="edit(scope.row)"></asp-btn-text>
          </template>
        </asp-table-column>
      </asp-table>
    </div>
    <!-- 字典值修改 -->
    <dictionaryCompileModal :dialog-param="dialogParam"
                            @search="search"></dictionaryCompileModal>
    <!-- 字典值详情 -->
    <dictionaryDetailModal :dialog-param="detailParam"></dictionaryDetailModal>
  </div>
</template>

<script>
import dictionaryCompileModal from './dictionaryCompileModal'
import dictionaryDetailModal from './dictionaryDetailModal'
export default {
  name: 'Dictionary',
  components: {
    dictionaryCompileModal,
    dictionaryDetailModal
  },
  data () {
    return {
      table: {
        prefix: this.$apiConfig.supportPathPrefix,
        url: '/dict/listType',
        searchForm: {
          code: '',
          name: ''
        }
      },
      dialogParam: {},
      detailParam: {}
    }
  },
  computed: {},
  watch: {},
  created () { },
  methods: {
    /**
     * 按钮权限
     * @method doAuth
     * @param {object} opt
     * @return {object}
     */
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    edit (row) {
      row.dicts = this.$aspUtils.getCodeValueByType(this, row.code)
      this.$nextTick(() => {
        this.dialogParam = {
          modalVisible: true,
          rowData: row
        }
      })
    },
    detail (row) {
      row.dicts = this.$aspUtils.getCodeValueByType(this, row.code)
      this.$nextTick(() => {
        this.detailParam = {
          modalVisible: true,
          rowData: row
        }
      })
    },
    // 组装枚举值列
    renderListData (val) {
      for (let i = 0; i < val.length; i++) {
        if (val[i].dicts.length !== 0) {
          let str = ''
          for (let j = 0; j < val[i].dicts.length; j++) {
            // val[i].dicts[j].orderKey = val[i].dicts[j].orderKey || ''
            if (j === val[i].dicts.length - 1) {
              str +=
                '(' +
                val[i].dicts[j].orderKey +
                ')' +
                val[i].dicts[j].code +
                '-' +
                val[i].dicts[j].name
            } else {
              str +=
                '(' +
                val[i].dicts[j].orderKey +
                ')' +
                val[i].dicts[j].code +
                '-' +
                val[i].dicts[j].name +
                '，'
            }
          }
          val[i].enums = str
        }
      }
    },
    // 列表查询
    search () {
      this.$refs.table.asp_search()
    },
    reset () {
      this.$refs.table.asp_reset()
    }
  }
}
</script>
