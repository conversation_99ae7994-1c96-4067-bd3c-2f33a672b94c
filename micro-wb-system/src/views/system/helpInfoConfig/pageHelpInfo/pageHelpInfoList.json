{"list": [{"type": "form", "label": "表单容器", "columnName": "form_1650968502961", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1650968520289", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 8, "align": "left", "list": [{"type": "input", "label": "页面ID：", "columnName": "pageId", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": false, "classify": "form", "class": "", "props": {"placeholder": "请输入", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "id", "isTrim": true}]}, {"span": 8, "align": "left", "list": [{"type": "input", "label": "页面名称：", "columnName": "pageName", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": false, "classify": "form", "class": "", "props": {"placeholder": "请输入", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "input_1650968563491", "isTrim": true}]}, {"span": 8, "align": "left", "list": [{"type": "input", "label": "更新人：", "columnName": "updateUserName", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": false, "classify": "form", "class": "", "props": {"placeholder": "请输入", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 100, "oldColumnName": "updateUser", "isTrim": true, "bpmFlowImageCloumnName": ""}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}, {"type": "grid", "label": "栅格布局", "columnName": "grid_1650968516415", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 8, "align": "left", "list": [{"label": "更新时间：", "type": "AspDateRange", "required": false, "rules": [], "classify": "form", "class": "", "columnName": "lastUpdateDateStart", "targetName": "lastUpdateDateEnd", "asideIcon": "iconfont iconriqi", "customLabelWidth": false, "icon": "", "operation": [], "hidden": false, "dateRangeType": 0, "props": {"type": "date", "format": "yyyy-MM-dd", "value-format": "yyyy-MM-dd", "range-separator": "至", "start-placeholder": "", "end-placeholder": "", "clearable": false, "disabled": false, "readonly": false, "picker-options": {}}, "width": "100%", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "lastUpdateDateEnd", "oldTargetName": "lastUpdateDateStart"}]}, {"span": 8, "align": "left", "list": []}, {"span": 8, "align": "right", "list": [{"type": "button", "label": "查询", "columnName": "cmmdtSearch", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-search"}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "button_1650968874920", "event": "submit", "tableId": "table_pageHelpInfoList", "formId": "form_1650968502961"}, {"type": "button", "label": "重置", "columnName": "cmmdtClear", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-refresh"}, "class": " hollow-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "oldColumnName": "button_1650968877139", "event": "reset-submit", "tableId": "table_pageHelpInfoList", "formId": "form_1650968502961"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false}, {"type": "empty", "label": "空容器", "columnName": "empty_1652767406148", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "button-group", "label": "按钮容器", "columnName": "button-group_1652766950530", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON>", "width": "100%", "list": [{"type": "button", "label": "导入", "columnName": "export", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "importfieldHelpInfo", "title": "字段帮助信息导入", "subTitle": "", "titleClass": "dialog-title-default", "width": "1100px", "height": "auto", "toolList": []}, "props": {"icon": "el-icon-upload2"}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "button_1652767020192", "event": "dialog-table", "tableId": "table_pageHelpInfoList", "formId": "form_1650968502961"}, {"type": "button", "label": "导出", "columnName": "export", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-download"}, "class": "hollow-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "button_1652767270344", "tableId": "table_pageHelpInfoList", "formId": "form_1650968502961", "event": "export", "exportType": "download", "exportUrl": "/web/support/v1/prompt/export", "apiParam": {"pageId": "$pageId$", "pageName": "$pageName$", "updateUserName": "$updateUserName$", "lastUpdateDateStart": "$lastUpdateDateStart$", "lastUpdateDateEnd": "$lastUpdateDateEnd$"}, "localProxy": "/proxy_webbas"}], "position": "left", "class": "", "isDialogFooterBtns": false, "classify": "layout", "hidden": false, "hasParentForm": true}, {"type": "table", "label": "表格", "columnName": "table_pageHelpInfoList", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": false, "show-pagination": true, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "highlight-current-row": false, "show-header": true, "default-sort": ""}, "btnGroupDynamic": [], "list": [{"timeStamp": 1650968640707, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true}, {"label": "页面ID", "columnName": "pageId", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "页面名称", "columnName": "pageName", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "180", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "有帮助信息的字段数", "columnName": "countField", "type": "link", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "160", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "activeType": "table_row_router", "apiStatusList": [], "requestBeforeRouter": false, "apiCloseDialog": false, "apiDyParam": {}, "beforeRouteApiParam": {}, "sfApiParam": {}, "apiUrlName": "", "apiMethodType": "", "apiMethod": "path", "apiName": "/system/fieldHelpInfoList", "apiParamType": "query", "sf_routerId": "", "sf_pageType": "", "nullHidde": false, "apiParam": "{\"id\":\"$id$\",\"pageId\":\"$pageId$\"}"}, {"label": "更新时间", "columnName": "lastUpdateDate", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "180", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "formatter": "yyyymmddhhmmss"}, {"label": "更新人", "columnName": "updateUserName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "ID", "columnName": "id", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true}], "operation": [], "toolList": [{"type": "text", "timestamp": 1652066899399, "columnName": "delete", "label": "删除", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "table_row_api", "apiMethod": "post+json", "apiName": "/proxy_webbas/web/support/v1/prompt/deleteByPageId", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "refreshTableData": true, "apiParam": "{\"pageId\":\"$pageId$\"}", "confirmationSwitch": true, "confirmationMessage": "确认删除？", "_hidden_state": false}], "operation-width": "100", "pagination": {"layout": "total, sizes, prev, pager, next, jumpe", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 20, "total": 0, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/proxy_webbas/web/support/v1/prompt/listPage"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1650968640707, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true}, {"label": "页面ID", "columnName": "pageId", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "页面名称", "columnName": "pageName", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "180", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "有帮助信息的字段数", "columnName": "countField", "type": "link", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "160", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "activeType": "table_row_router", "apiStatusList": [], "requestBeforeRouter": false, "apiCloseDialog": false, "apiDyParam": {}, "beforeRouteApiParam": {}, "sfApiParam": {}, "apiUrlName": "", "apiMethodType": "", "apiMethod": "path", "apiName": "/system/fieldHelpInfoList", "apiParamType": "query", "sf_routerId": "", "sf_pageType": "", "nullHidde": false, "apiParam": "{\"id\":\"$id$\",\"pageId\":\"$pageId$\"}"}, {"label": "更新时间", "columnName": "lastUpdateDate", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "180", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "formatter": "yyyymmddhhmmss"}, {"label": "更新人", "columnName": "updateUserName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "ID", "columnName": "id", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true}], "router": {}, "multipleSelectionStatus": [], "show-operation-status": [], "formId": "form_1650968502961", "dynamic": {"single_dy_query_area_list": []}}], "classify": "empty", "hidden": false}], "model": {"datePicker_1650968693608": "", "pageId": "", "pageName": "", "updateUserName": "", "lastUpdateDateStart": ""}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "pageHelpInfoList", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "mini", "class": "", "layoutType": "flex", "statusList": [], "localProxy": "", "nigxProxy": "", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": ""}, "titleName": "页面查询列表"}, "dataConfig": {}}