<!--
 * @Author: hezheng
 * @Date: 2022-04-26 15:42:22
 * @Description: 页面帮助信息查询列表
-->
<template>
  <div style="height: 100%">
    <asp-smart-table ref="aspSmartTable"
                     v-model="model"
                     :table-json="tableJson"
                     :size-change="sizeChange"
                     :current-change="currentChange"
                     :before-http="beforeHttp"
                     :after-http="afterHttp"
                     :before-router="beforeRouter"
                     :dialog-config="dialogConfig"
                     @on="onbind">
    </asp-smart-table>
  </div>
</template>

<script>
export default {
  name: 'PageHelpInfoList',
  data () {
    return {
      tableJson: null,
      model: {},
      testData: [],
      dialogConfig: {
        // 导入帮助信息文件
        importfieldHelpInfo: {
          name: 'importfieldHelpInfo',
          _this: this,
          fileId: '',
          pccImport: '',
          hasRightData: false, // 有无正确数据
          setup (data) { }, // 数据安装后执行函数
          /* VUE生命周期api */
          mounted: async (currModelData) => {
            const dialogData = JSON.parse(JSON.stringify(require('./comm/importfieldHelpInfo.json')))
            if (dialogData) {
              const tableJson = dialogData
              const model = tableJson.model
              return {
                tableJson, model, status: ''
              }
            }
          },
          beforeCreate () { },
          created () { },
          beforeMount () { },
          beforeUpdate () { },
          updated () { },
          beforeDestroy () { },
          destroyed () { },
          activated () { },
          deactivated () { },
          /* 智能表格生命周期api */
          onbind (data) {
            console.log(data)
            const { item, $_smartTable, $_this } = data
            const _t = this._this
            switch (item.columnName) {
              case 'ignoreError': // 忽略错误
                if (!this.hasRightData) {
                  $_this.$message.warning('没有正确数据！')
                  return false
                }
                this.pccImport = 'showRight'
                $_smartTable.asp_setHidden('ignoreError', true)
                $_smartTable.asp_setHidden('exportError', true)
                $_smartTable.asp_setHidden('cancelImport', false)
                $_smartTable.asp_setHidden('confirmUpload', false)
                _t.$nextTick(() => {
                  $_smartTable.asp_refreshTableList('imptTable', true)
                  $_smartTable.asp_setHidden('cancelImport', false)
                  $_smartTable.asp_setHidden('confirmUpload', false)
                })
                break
              case 'exportError': // 导出错误
                break
              case 'cancelImport': // 放弃
                break
              case 'confirmUpload': // 确认
                break
              default:
                break
            }
          },
          beforeHttp ({ tableItem, params, httpMethod, $_smartTable, row }) {
            console.log(tableItem, params, httpMethod, row)
            if (tableItem.columnName === 'imptTable') {
              params.fileId = this.fileId
              const pageType = this.pccImport === 'showRight' ? 'pageRight' : 'pageError'
              httpMethod.url = `/proxy_webbas/web/support/v1/excelimport/${pageType}/prompt`
            }
          },
          afterHttp (args) {
            const { $_close, item, tableItem, $_smartTable, response, responseBody } = args
            const _t = this._this
            // 获取fileId
            if (item && item.columnName === 'submitBtn' && _t.$reponseStatus(response) && response.data) {
              this.fileId = response.data.fileId
              $_smartTable.asp_setHidden('imptTable', false)
              const showHidden = response.data.errorNumber > 0 // 有错误数据
              this.hasRightData = response.data.rightNumber > 0 // 有无正确数据
              this.pccImport = showHidden ? '' : 'showRight'// 有错误数据
              $_smartTable.asp_setHidden('ignoreError', !showHidden)
              $_smartTable.asp_setHidden('exportError', !showHidden)
              $_smartTable.asp_setHidden('cancelImport', showHidden)
              $_smartTable.asp_setHidden('confirmUpload', showHidden)
              _t.$nextTick(() => {
                $_smartTable.asp_refreshTableList('imptTable', true)
              })
            }
            if (tableItem && tableItem.columnName === 'imptTable' && _t.$reponseStatus(responseBody) && responseBody.data) { // 导入按钮
              const responseData = responseBody.data
              responseBody.data = []
              responseData.forEach((item) => {
                const obj = JSON.parse(item.jsonData)
                if (item.errorMsg) {
                  obj.errorMsg = item.errorMsg
                }
                responseBody.data.push(obj)
              })
              this.pccImport === 'showRight' && $_smartTable.asp_setHidden('confirmUpload', false)
              this.pccImport === 'showRight' && $_smartTable.asp_setHidden('cancelImport', false)
            } else if (tableItem && tableItem.columnName === 'confirmUpload') { // 导入按钮
              _t.$message.success('导入成功')
              _t.$nextTick(() => {
                _t.$refs.aspSmartTable && _t.$refs.aspSmartTable.asp_refreshTableList('table_pageHelpInfoList')
              })
              // 刷新页面帮助信息缓存列表
              _t.$main_tools.menu.getAllPageHelpInfoList(_t)
              $_close()
            } else if (tableItem && tableItem.columnName === 'cancelImport') { // 取消导入
              _t.$message.success('取消导入成功')
              $_close()
            }
          },
          beforeButton ({ item, rowObj, next }) {
            next(item, rowObj)
          },
          beforeRouter ({ item, row, routerObj, next }) {
            next(routerObj)
          },
          beforeTableRender ({ tableName, tableData, columnItem, scope }, callBack) {
            // callBack('preferentialList', 'commodityName', [], { content: '查看' })
          },
          beforeTableRowRender ({ item, tableData, row, rowClassName }, callback) {
            // callback(rowClassName)
          },
          sizeChange ({ tableItem, pageSize, next }) {
            // console.log(pageSize)
            next(true)
          },
          currentChange ({ tableItem, currentPage, next }) {
            // console.log({ tableItem, currentPage, next })
            next(true)
          }
        }
      }
    }
  },
  async mounted () {
    this.tableJson = JSON.parse(
      JSON.stringify(require('./pageHelpInfoList.json'))
    )
    this.model = this.tableJson.model
  },
  methods: {
    /**
     * 智能表格监听所有组件的交互事件操作：监听、捕捉事件响应
     * @param item 响应组件对象属性集（类型、组件Id，控件内元数属性），columnName每个组件单元的唯一码（组件单元Id）
     * @param type 事件类型（click/blur/onblur等）
     * @param index 当是表格组件时，返回组件的行号
     * @param model 查询区域表单数据模型
     * @param tableModel 表格组件数据模型
     * @param row 表格行数据
     * @param multipleSelection 表格多选数据（当出现列表复选时才有，包括跨页数据，整行数据）
     * @param sortProps 排序属性
     * @returns {Promise<void>}
     */
    async onbind ({ item, type, index, model, tableModel, row, subFormSelectData, sortProps }) { },
    /**
     * 智能表格页面路由跳转的前置操作
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param item 响应组件对象属性集
     * @param row 当响应组件为表格操作列中的按钮时，此参数返回表格行数据，其它返回undefined
     * @param routerObj.routerType: 路由类型
     * @param routerObj.routerParamType 路由参数类型
     * @param routerObj.routerUrl 路由地址或名称
     * @param routerObj.routerParamValue 路由参数
     * @param next 回调函数
    */
    beforeRouter ({ item, row, routerObj, next }) {
      if (item.columnName === 'countField' && row.countField === 0) { // 点击有帮助信息的字段数链接,大于0时才跳转
        return false
      }
      next(routerObj)
    },
    /**
     * 智能表格页面所有请求前的前置操作
     * 例如：修改请求参数、修改请求方式、修改请求URL、或者请求条件不满足不给发送请求
     * @param tableItem 组件对象属性集
     * @param params 请求参数body，数据格式如下(字段格式不一致的需要自行转换)如下:
     *                                         {
     *                                             page：1， // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                             rows: 10，// 分页属性(页大小)，数字类型 （不是分页接口，没有这个字段）
     *                                             .......   // 业务属性字段
     *                                          }
     * @param httpMethod.url 请求地址URL
     * @param httpMethod.type 请求方式，目前主要六种：'post+json', 'post+form', 'get'，'put+json'，'delete+json'，'patch+json'
     * @param row 当组件为表格并且是表格操作列触发的请求，此参数返回表格行数据，其它返回undefined
     */
    beforeHttp ({ tableItem, params, httpMethod, row }) { },
    /**
     * 智能表格页面所有请求后置操作
     * 例如：请求后的数据包体需要做二次处理
     * @param tableItem 组件对象属性集
     * @param responseBody 响应数据body, 数据包格式(字段格式不一致的需要自行转换)如下：
     *                                              {
     *                                                status: "200", // 业务状态码，字符串类型，成功返回"200"，失败返回其它数据
     *                                                message: "",   // 业务提示语，字符串类型，给业务的提示语属性
     *                                                page：1，      // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                                total: 53，    // 分页属性(总记录大小)，数字类型 （不是分页接口，没有这个字段）
     *                                                data: {}或者[] // 业务数据区，对象或数组类型，用于各业务逻辑处理
     *                                               }
     */
    afterHttp ({ tableItem, responseBody }) {
      if (tableItem.columnName === 'delete' && responseBody.status === '200') {
        this.$message.success('删除成功')
        // 刷新页面帮助信息缓存列表
        this.$main_tools.menu.getAllPageHelpInfoList(this)
      }
    },
    /**
     * 表格页码大小发生变化时触发的前置事件
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param pageSize 表格页码大小
     * @param next 回调函数
     */
    sizeChange ({ tableItem, pageSize, next }) {
      const isNext = true
      next(isNext) // 允许继续运行传true, 否则传false  // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 表格当前页发生变化时触发的前置事件，包括点翻页、上一页、下一页、刷新页、重置页
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param currentPage 当前页码号
     * @param next 回调函数
     */
    currentChange ({ tableItem, currentPage, next }) {
      const isNext = true
      next(isNext) // 允许继续运行传true, 否则传false // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    }
  }
}
</script>

