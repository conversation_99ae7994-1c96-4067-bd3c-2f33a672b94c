<!--
 * @Author: hezheng
 * @Date: 2022-04-26 15:30:22
 * @Description: 页面字段帮助信息查询列表
-->
<template>
  <div style="height: 100%">
    <asp-smart-table
      ref="aspSmartTable"
      v-model="model"
      :table-json="tableJson"
      :size-change="sizeChange"
      :current-change="currentChange"
      :before-http="beforeHttp"
      :after-http="afterHttp"
      :dialog-config="dialogConfig"
      :before-table-render="beforeTableRender"
      @on="onbind"
    >
    </asp-smart-table>
  </div>
</template>

<script>
export default {
  name: 'FieldHelpInfoList',
  data () {
    return {
      tableJson: null,
      model: {},
      rowData: {},
      pageId: this.$route.query.pageId || '', // 页面ID
      dialogConfig: {
        // 导入帮助信息文件
        importfieldHelpInfo: {
          name: 'importfieldHelpInfo',
          _this: this,
          setup (data) {}, // 数据安装后执行函数
          /* VUE生命周期api */
          mounted: async (currModelData) => {
            const dialogData = JSON.parse(JSON.stringify(require('./importfieldHelpInfo.json')))
            if (dialogData) {
              const tableJson = dialogData
              const model = tableJson.model
              return {
                tableJson, model, status: ''
              }
            }
          },
          beforeCreate () {},
          created () {},
          beforeMount () {},
          beforeUpdate () {},
          updated () {},
          beforeDestroy () {},
          destroyed () {},
          activated () {},
          deactivated () {},
          /* 智能表格生命周期api */
          onbind (data) { },
          beforeHttp ({ tableItem, params, httpMethod, row }) {
            // console.log(tableItem, params, httpMethod, row)
          },
          afterHttp (args) {
            if (args.tableItem && ['cancelImport', 'confirmUpload'].indexOf(args.tableItem.columnName) > -1) { // 确认或者取消导入按钮后
              args && args.$_close()
              this._this.$nextTick(() => {
                this._this.$refs.aspSmartTable && this._this.$refs.aspSmartTable.asp_refreshTableList('fieldHelpInfoTable')
              })
            }
          },
          beforeButton ({ item, rowObj, next }) {
            next(item, rowObj)
          },
          beforeRouter ({ item, row, routerObj, next }) {
            next(routerObj)
          },
          beforeTableRender ({ tableName, tableData, columnItem, scope }, callBack) {
            // callBack('preferentialList', 'commodityName', [], { content: '查看' })
          },
          beforeTableRowRender ({ item, tableData, row, rowClassName }, callback) {
            // callback(rowClassName)
          },
          sizeChange ({ tableItem, pageSize, next }) {
            // console.log(pageSize)
            next(true)
          },
          currentChange ({ tableItem, currentPage, next }) {
            // console.log({ tableItem, currentPage, next })
            next(true)
          }
        },
        // 变更
        helpInfoUpdate: { // 新增、修改业务弹框
          _this: this,
          name: 'helpInfoUpdate',
          setup (data) { // 数据安装后执行函数
          },
          /* VUE生命周期api */
          mounted: async (data) => {
            // const _this = this
            const dialogData = JSON.parse(JSON.stringify(require('./helpInfoUpdate.json')))
            if (dialogData) {
              const modelHtml = dialogData
              // dialogData.model = JSON.parse(JSON.stringify(_this.rowData))
              const modelData = dialogData.model
              return {
                modelHtml, modelData, status: 'modify'
              }
            }
          },
          beforeCreate () {},
          created () {},
          beforeMount () {},
          beforeUpdate () {},
          updated () {},
          beforeDestroy () {},
          destroyed () {},
          activated () {},
          deactivated () {},
          /* 智能表单生命周期api */
          onbind (data) {},
          beforeHttpPro (data, httpObject, callback) {
            callback(httpObject)
          },
          afterHttpPro (data, responseBody, callback) {
            const { item } = data
            const preThis = this._this
            if (item.columnName === 'submit' && preThis.$aspUtils.asp_ReponseStatus(responseBody, false)) {
              preThis.$message.success('提交成功')
              // 刷新列表
              preThis.$nextTick(() => {
                preThis.$refs.aspSmartTable && preThis.$refs.aspSmartTable.asp_refreshTableList('fieldHelpInfoTable')
              })
              data.$_close()
              // 刷新页面帮助信息缓存列表
              preThis.$main_tools.menu.getAllPageHelpInfoList(preThis)
            }
            callback(responseBody)
          },
          compDataChangePro (data, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            const { item, model } = data
            const isContinue = true
            if (item.columnName === 'submit') {
              // 对比可编辑字段是否被产生修改
              const prompt = this._this.rowData.prompt
              if (model.prompt === prompt) { // 比较新老数据是否产生修改
                this._this.$message.error('界面未做任何修改，无需提交 。')
                return false
              }
            }
            callback(isContinue, data.rowData, data.$_dialogData)
          },
          afterButtonPro (data, callback) {
            const isContinue = true
            callback(isContinue, data.rowData, data.$_dialogData)
          },
          beforeAuthPro (data, callback) {
            const isHasAuth = true
            const isContinue = true
            callback(isHasAuth, isContinue)
          },
          beforeRouterPro (data) {
            data.next(data.routerObj)
          },
          beforeColumnValidatePro (data, callback) {
            const isValidateFail = false
            const isContinueValidate = true
            callback(isContinueValidate, isValidateFail, undefined)
          },
          beforeDrawTableRowPro (data, callback) {
            callback(data.rowClassName)
          },
          beforeLoadingHttpPro ({ item, $_dialogData }, httpObject, callback) {
            // 获取sid业务详情接口
            if (item.apiId === 'getDetail') {
              Object.assign(httpObject.httpBody, {
                id: this._this.rowData.id
              })
            }
            callback(httpObject)
          },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) }
        }
      }
    }
  },
  async mounted () {
    this.tableJson = JSON.parse(
      JSON.stringify(require('./fieldHelpInfoList.json'))
    )
    this.model = this.tableJson.model
  },
  methods: {
    /**
     * 智能表格监听所有组件的交互事件操作：监听、捕捉事件响应
     * @param item 响应组件对象属性集（类型、组件Id，控件内元数属性），columnName每个组件单元的唯一码（组件单元Id）
     * @param type 事件类型（click/blur/onblur等）
     * @param index 当是表格组件时，返回组件的行号
     * @param model 查询区域表单数据模型
     * @param tableModel 表格组件数据模型
     * @param row 表格行数据
     * @param multipleSelection 表格多选数据（当出现列表复选时才有，包括跨页数据，整行数据）
     * @param sortProps 排序属性
     * @returns {Promise<void>}
     */
    async onbind ({ item, type, index, model, tableModel, row, subFormSelectData, sortProps }) {
      switch (item.columnName) {
        case 'back':
          this.$router.back()
          break
        case 'update':
          this.rowData = JSON.parse(JSON.stringify(row))
          break
        default:
      }
    },
    /**
     * 智能表格页面所有请求前的前置操作
     * 例如：修改请求参数、修改请求方式、修改请求URL、或者请求条件不满足不给发送请求
     * @param tableItem 组件对象属性集
     * @param params 请求参数body，数据格式如下(字段格式不一致的需要自行转换)如下:
     *                                         {
     *                                             page：1， // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                             rows: 10，// 分页属性(页大小)，数字类型 （不是分页接口，没有这个字段）
     *                                             .......   // 业务属性字段
     *                                          }
     * @param httpMethod.url 请求地址URL
     * @param httpMethod.type 请求方式，目前主要六种：'post+json', 'post+form', 'get'，'put+json'，'delete+json'，'patch+json'
     * @param row 当组件为表格并且是表格操作列触发的请求，此参数返回表格行数据，其它返回undefined
     */
    beforeHttp ({ tableItem, params, httpMethod, row }) {
      // 查询时传递id参数
      if (tableItem.columnName === 'fieldHelpInfoTable') Object.assign(params, { pageId: this.pageId })
    },
    /**
     * 智能表格页面所有请求后置操作
     * 例如：请求后的数据包体需要做二次处理
     * @param tableItem 组件对象属性集
     * @param responseBody 响应数据body, 数据包格式(字段格式不一致的需要自行转换)如下：
     *                                              {
     *                                                status: "200", // 业务状态码，字符串类型，成功返回"200"，失败返回其它数据
     *                                                message: "",   // 业务提示语，字符串类型，给业务的提示语属性
     *                                                page：1，      // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                                total: 53，    // 分页属性(总记录大小)，数字类型 （不是分页接口，没有这个字段）
     *                                                data: {}或者[] // 业务数据区，对象或数组类型，用于各业务逻辑处理
     *                                               }
     */
    afterHttp ({ tableItem, responseBody }) {
      if (tableItem.columnName === 'delete' && responseBody.status === '200') {
        this.$message.success('删除成功')
        // 刷新页面帮助信息缓存列表
        this.$main_tools.menu.getAllPageHelpInfoList(this)
      }
    },
    /**
     * 表格内容渲染之前的前置动作，
     * @param tableName 当前表格名称
     * @param tableData 表格当页的数据
     * @param columnItem 表格当前列的信息
     * @param scope 表格行信息包含属性 $index row等
     * @param callback 回调事件，用于改变指定列的显示内容
     * @param callback 参数说明如下
     * 参数一：指定修改的表格名称
     * 参数二：指定修改的列名
     * 参数三：指定索引集合，整列生效则传空数组[],指定某几行生效则传索引集合[1,3]
     * 参数四：显示内容{ content: 可以是文本也可以是html代码片段 }
     */
    beforeTableRender ({ tableName, tableData, columnItem, scope }, callBack) {
      // 帮助信息列，需要展示输入的编辑的空格，换行排列
      callBack('fieldHelpInfoTable', 'prompt', [], { content: '<span style="word-break: break-all; white-space: pre-wrap;">' + scope.row.prompt + '</span>' })
    },
    /**
     * 表格页码大小发生变化时触发的前置事件
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param pageSize 表格页码大小
     * @param next 回调函数
     */
    sizeChange ({ tableItem, pageSize, next }) {
      const isNext = true
      next(isNext) // 允许继续运行传true, 否则传false  // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 表格当前页发生变化时触发的前置事件，包括点翻页、上一页、下一页、刷新页、重置页
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param currentPage 当前页码号
     * @param next 回调函数
     */
    currentChange ({ tableItem, currentPage, next }) {
      const isNext = true
      next(isNext) // 允许继续运行传true, 否则传false // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    }
  }
}
</script>

