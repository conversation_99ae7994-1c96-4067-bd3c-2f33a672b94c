{"list": [{"label": "导入组件", "type": "AspImportForPcc", "columnName": "pccImport", "classify": "layout", "icon-dev": "el-icon-film", "props": {}, "uploadCfg": {"columnName": "uploadBtn", "type": "", "url": "", "label": "选择文件", "icon": "el-icon-folder-add", "isShowFileList": true, "limit": "1", "limitNumByHttp": false, "limitHttp": {"apiName": "/web/business/v1/base/commonImport/getMaxImpCount.ajax", "apiParam": "{bizType: \"sidCodeApply\"}"}, "fileList": [], "isShowDesc": false, "desc": "只能上传文件扩展名为xls或xlsx，一次最多可导入500条记录！", "limitSize": "10M", "limitNum": "0", "fileType": "xls,xlsx"}, "downloadBtn": {"columnName": "downloadBtn", "label": "下载模板", "icon": "el-icon-download", "url": "/web/support/v1/excelimport/download/prompt"}, "actionButton": {"columnName": "submitBtn", "label": "导入", "icon": "el-icon-upload2", "url": "/web/support/v1/excelimport/importFileTemp/prompt", "apiParam": "{\"bizType\":\"prompt\"}"}, "dynamic": {"single_dy_query_area_list": [{"source": {"label": "导入组件", "columnName": "pccImport"}, "target": [{"columnName": "tableRight", "label": " tableRight", "props": {"border": true, "stripe": true, "highlight-current-row": false, "show-header": true, "default-sort": ""}, "type": "table", "children": []}], "condition": [{"columnName": "pccImport", "condition": "⊆", "valueType": "value", "compareValueType": "array", "columnValue": "[showError,showRight]", "type": ["hidden"], "result": false, "value": "", "status": []}, {"columnName": "pccImport", "condition": "!⊆", "valueType": "value", "compareValueType": "array", "columnValue": "[showError,showRight]", "type": ["hidden"], "result": true, "value": "", "status": []}]}, {"source": {"label": "导入组件", "columnName": "pccImport"}, "target": [{"columnName": "ignoreError", "label": "忽略错误 ignoreError", "props": {}, "type": "button", "children": []}, {"columnName": "exportError", "label": "导出错误 exportError", "props": {}, "type": "button", "children": []}], "condition": [{"columnName": "pccImport", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "showError", "type": ["hidden"], "result": false, "value": "", "status": []}, {"columnName": "pccImport", "condition": "!=", "valueType": "value", "compareValueType": "string", "columnValue": "showError", "type": ["hidden"], "result": true, "value": "", "status": []}]}, {"source": {"label": "导入组件", "columnName": "pccImport"}, "target": [{"columnName": "cancelImport", "label": "放弃 cancelImport", "props": {}, "type": "button", "children": []}, {"columnName": "confirmUpload", "label": "确认 confirmUpload", "props": {}, "type": "button", "children": []}], "condition": [{"columnName": "pccImport", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "showRight", "type": ["hidden"], "result": false, "value": "", "status": []}, {"columnName": "pccImport", "condition": "!=", "valueType": "value", "compareValueType": "string", "columnValue": "showRight", "type": ["hidden"], "result": true, "value": "", "status": []}]}], "single_subform_columm_list": [{"source": {"label": "导入组件", "columnName": "pccImport"}, "subForm": {"label": " tableRight : tableRight", "columnName": "tableRight"}, "target": [{"columnName": "cooperateDesc", "label": "错误原因 cooperateDesc"}], "condition": [{"columnName": "pccImport", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "showError", "type": ["hidden"], "result": false, "value": "", "status": []}, {"columnName": "pccImport", "condition": "!=", "valueType": "value", "compareValueType": "string", "columnValue": "showError", "type": ["hidden"], "result": true, "value": "", "status": []}]}]}, "project": "fpl", "searchErrorData": {"url": "/web/support/v1/excelimport/pageError/prompt", "apiParam": {"fileId": "$fileId$"}}, "searchRightData": {"url": "/web/support/v1/excelimport/pageRight/prompt", "apiParam": {"fileId": "$fileId$"}}}, {"type": "table", "label": "表格", "columnName": "imptTable", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": true, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [{"timeStamp": 1685502079749, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true}, {"label": "错误原因", "columnName": "errorMsg", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "页面ID", "columnName": "pageId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "页面名称", "columnName": "pageName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "字段ID", "columnName": "fieldId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "字段名称", "columnName": "fieldName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}, {"label": "字段帮助内容", "columnName": "prompt", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true}], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": false, "type": "post", "methods": "/web/support/v1/excelimport/pageError/prompt", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading", "paramDataName": false}, "hidden": true, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [], "paginationStatus": [], "router": {}, "hiddenStatus": []}, {"type": "button-group", "label": "按钮容器", "columnName": "button-group_1606102917618", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON>", "width": "100%", "list": [{"type": "button", "label": "忽略错误", "columnName": "ignoreError", "icon": "icon-input", "width": "100%", "required": false, "hidden": true, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-close"}, "class": "hollow-with-icon-btn", "classify": "button", "hasParentForm": false, "labelWidth": 100, "event": "", "actionName": "doIgnoreErrorAction", "oldColumnName": "button_1606102920200", "hiddenStatus": [], "isEncrypt": false, "isVerifyTableRow": false, "bpmFlowImageCloumnName": ""}, {"type": "button", "label": "导出错误", "columnName": "exportError", "icon": "icon-input", "width": "100%", "required": false, "hidden": true, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-check"}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": false, "labelWidth": 100, "event": "importBusiness", "actionName": "doExportErrorAction", "apiName": "/web/support/v1/excelimport/exportError/prompt", "oldColumnName": "button_1606102921484", "hiddenStatus": [], "apiParam": "{bizType: \"prompt\"}", "isEncrypt": false, "isVerifyTableRow": false, "bpmFlowImageCloumnName": ""}, {"type": "button", "label": "放弃", "columnName": "cancelImport", "icon": "icon-input", "width": "100%", "required": false, "hidden": true, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-close"}, "class": "hollow-with-icon-btn", "classify": "button", "hasParentForm": false, "labelWidth": 100, "event": "importBusiness", "actionName": "doCancelAction", "apiName": "/web/support/v1/excelimport/cancel/prompt", "oldColumnName": "button_1606102922764", "hiddenStatus": [], "apiParam": "{\"bizType\":\"prompt\",\"otherParams\": {}}", "isEncrypt": false, "isVerifyTableRow": false, "bpmFlowImageCloumnName": "", "changeTypeSwitch": true, "apiMethod": "post+form"}, {"type": "button", "label": "确认", "columnName": "confirmUpload", "icon": "icon-input", "width": "100%", "required": false, "hidden": true, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-check"}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": false, "labelWidth": 100, "event": "importBusiness", "actionName": "doConfirmlAction", "apiName": "/web/support/v1/excelimport/importOffical/prompt", "oldColumnName": "button_1606102924084", "hiddenStatus": [], "apiParam": "{\"bizType\":\"prompt\",\"otherParams\": {}}", "bpmFlowImageCloumnName": "", "isEncrypt": false, "isVerifyTableRow": false}], "classify": "layout", "hidden": false, "position": "center", "class": "importfieldHelpInfoBtns", "isDialogFooterBtns": true}], "model": {}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "importfieldHelpInfo", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "mini", "class": "", "layoutType": "flex", "statusList": [], "nigxProxy": "", "localProxy": "/proxy_webbas", "titleName": "字段帮助信息导入", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": ""}, "routerInfo": {}}, "dataConfig": {}}