{"list": [{"type": "form", "label": "表单容器", "columnName": "form_1650968502961", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1650968520289", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 8, "align": "left", "list": [{"type": "input", "label": "字段ID：", "columnName": "fieldId", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": false, "classify": "form", "class": "", "props": {"placeholder": "请输入", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "id", "isTrim": true}]}, {"span": 8, "align": "left", "list": [{"type": "input", "label": "字段名称：", "columnName": "fieldName", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": false, "classify": "form", "class": "", "props": {"placeholder": "请输入", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "input_1650969261894", "isTrim": true}]}, {"span": 8, "align": "right", "list": [{"type": "button", "label": "查询", "columnName": "button_1650969270356", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-search"}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "button_1650968874920", "event": "submit", "tableId": "fieldHelpInfoTable", "formId": "form_1650968502961"}, {"type": "button", "label": "重置", "columnName": "button_1650969272771", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-refresh"}, "class": " hollow-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "oldColumnName": "button_1650968877139", "event": "reset-submit", "tableId": "fieldHelpInfoTable", "formId": "form_1650968502961"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false}, {"type": "table", "label": "表格", "columnName": "fieldHelpInfoTable", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "highlight-current-row": false, "show-header": true, "default-sort": ""}, "btnGroupDynamic": [], "list": [{"timeStamp": 1650968640707, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "字段ID", "columnName": "fieldId", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "字段名称", "columnName": "fieldName", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "150", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "字段帮助信息", "columnName": "prompt", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "400", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "更新时间", "columnName": "lastUpdateDate", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "150", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "canSortConvert": false, "formatter": "yyyymmddhhmmss", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "更新人", "columnName": "updateUserName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "ID", "columnName": "id", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [{"type": "text", "timestamp": 1651053976435, "columnName": "update", "label": "变更", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "dialog-form", "dialogConfig": {"bindName": "helpInfoUpdate", "title": "帮助信息变更", "subTitle": "", "titleClass": "dialog-title-default", "width": "1100px", "height": "auto", "toolList": []}, "_hidden_state": false}, {"type": "text", "timestamp": 1651053977250, "columnName": "delete", "label": "删除", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "confirmationSwitch": true, "confirmationMessage": "确认删除？", "activeType": "table_row_api", "apiMethod": "post+json", "apiName": "/proxy_webbas/web/support/v1/prompt/deleteById", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"id\":\"$id$\"}", "refreshTableData": true, "_hidden_state": false}], "operation-width": "100", "pagination": {"layout": "total, sizes, prev, pager, next, jumpe", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/proxy_webbas/web/support/v1/prompt/listFieldByPageId", "httpParam": "{}"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1650968640707, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "字段ID", "columnName": "fieldId", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "字段名称", "columnName": "fieldName", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "150", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "字段帮助信息", "columnName": "prompt", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "400", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "更新时间", "columnName": "lastUpdateDate", "type": "text", "sort": true, "fixed": false, "classify": "table", "width": "", "min-width": "150", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "canSortConvert": false, "formatter": "yyyymmddhhmmss", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "更新人", "columnName": "updateUserName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "ID", "columnName": "id", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "router": {}, "multipleSelectionStatus": [], "show-operation-status": [], "formId": "form_1650968502961", "showExpand": false, "slotName": "expand"}, {"type": "button-group", "label": "按钮容器", "columnName": "button-group_1651822044752", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON>", "width": "100%", "list": [{"type": "button", "label": "返回", "columnName": "back", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-back"}, "class": " hollow-with-icon-btn", "classify": "button", "hasParentForm": false, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "button_1651822047355"}], "position": "center", "class": "", "isDialogFooterBtns": false, "classify": "layout", "hidden": false}], "model": {"fieldId": "", "fieldName": ""}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "fieldHelpInfoList", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "mini", "class": "", "layoutType": "flex", "statusList": [], "localProxy": "", "nigxProxy": "", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": ""}, "titleName": "字段帮助信息查询列表", "routerInfo": {}}}