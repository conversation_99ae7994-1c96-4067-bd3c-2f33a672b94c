{"formList": [{"label": "栅格布局", "type": "row", "columnName": "row_1651212292854", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 12, "childList": [{"label": "字段ID", "type": "input", "columnName": "fieldId", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "operations": [{"status": ["modify"], "attr": "label", "formatter": ""}]}]}, {"span": 12, "childList": [{"label": "字段名称", "type": "input", "columnName": "fieldName", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "operations": [{"status": ["modify"], "attr": "label", "formatter": ""}]}]}], "name": "layout", "labelWidth": 160}, {"label": "栅格布局", "type": "row", "columnName": "row_1651212311835", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 24, "childList": [{"label": "字段帮助信息", "type": "textarea", "columnName": "prompt", "defaultValue": "", "isModel": true, "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "字段帮助信息不能为空", "trigger": "blur"}], "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": "", "shortcut": false, "shortcutOptions": [{"label": "option1", "value": "0"}], "class": "", "operation": [], "hidden": false, "props": {"placeholder": "不超过1000个字符", "maxlength": "1000", "show-word-limit": true, "wordLimitStyle": "transparent", "clearable": true, "disabled": false, "readonly": false, "rows": "5", "text": ""}, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": ""}]}], "name": "layout", "labelWidth": 160}, {"label": "栅格布局", "type": "row", "columnName": "row_1651212313769", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 12, "childList": [{"label": "更新时间", "type": "text", "columnName": "lastUpdateDate", "defaultValue": "", "isModel": true, "icon-dev": "iconfont icontext", "isLabelWidth": false, "span": 24, "width": "100%", "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": true, "isNumberType": false, "name": "basic", "labelWidth": 160, "arrayDetaSwitch": false, "dateDetaSwitch": true, "dateDateFormat": "yyyy-MM-dd hh:mm:ss", "copyNewVal": ""}]}, {"span": 12, "childList": [{"label": "更新人", "type": "input", "columnName": "updateUserName", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "operations": [{"status": ["modify"], "attr": "label", "formatter": ""}]}]}], "name": "layout", "labelWidth": 160}, {"label": "", "type": "buttonGroup", "isLabelWidth": false, "columnName": "buttonGroup_1651212316152", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "el-icon-check", "label": "保存", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/web/support/v1/prompt/updateField", "class": "solid-with-icon-btn", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": ""}, {"columnName": "cacel", "type": "warning", "icon": "el-icon-close", "label": "取消", "interactive": "", "validateProp": "", "apiName": "", "class": " hollow-with-icon-btn", "default": "show", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_close_dialog", "apiCloseDialog": false}], "name": "layout", "labelWidth": 160}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "starPostion": "left", "class": "", "exportName": "helpInfoUpdate", "defaultClass": "webbas", "size": "small", "statusList": ["modify"], "serverProps": {"localProxy": "/proxy_webbas", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "isOpenPublicTips": false, "tipStarPostion": "right", "tipIcon": "el-icon-question", "placement": "buttom", "compareStatusList": [], "compareDescript": "变更前：", "titleName": "帮助信息变更", "isOpenHelpTipSwitch": true, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "webbas0002", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": "400"}}, "dataConfig": {}, "virtual_model": {}, "model": {"fieldId": "", "fieldName": "", "prompt": "", "lastUpdateDate": "", "updateUserName": ""}, "pre_loading_request_list": [{"apiId": "getDetail", "isAuto": 1, "apiType": "post+json", "sendTime": "mounted", "apiName": "/web/support/v1/prompt/get", "apiParam": "", "status": [], "responseParam": "{mapRules:\"data@jsonData\"}"}]}