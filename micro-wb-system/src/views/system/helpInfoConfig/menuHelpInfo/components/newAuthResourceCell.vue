<!--
 * @Author: yuxuan
 * @Date: 2022-04-29 10:49:18
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-18 10:27:39
 * @Description: 菜单帮助信息查询 - 菜单树
 * @Version: V1.0
 * @FilePath: /webbas/vue-ui/micro-wb-system/src/views/system/helpInfoConfig/menuHelpInfo/components/newAuthResourceCell.vue
-->
<template>
  <div class="webbas"
      v-loading="!bodyShow"
      :class="tabCell ? 'tabCellHeight' : 'singleCellHeight'"
      style="overflow: hidden;overflow-y: scroll;border:1px solid #dee8f8;">
    <div v-loading="false" :empty-text="treeEmptyText" class="background-css" style="border: none;">
      <div class="webbas-tree-css">
        <el-tree
          ref="tree"
          :data="authResourceDataContent"
          :empty-text="treeEmptyText"
          :props="{ children: 'children', label: 'text' }"
          :default-checked-keys="authResourceCheckData"
          :render-content="renderContent"
          node-key="id"
          default-expand-all
          highlight-current
          @node-click="resourceNode"
        >
        </el-tree>
      </div>
    </div>
    <addMenuHelpInfoDialog
      :dialogParam="dialogParam"
      @updateDataSources="updateDataSources"
    ></addMenuHelpInfoDialog>
  </div>
</template>

<script>
// import Vue from 'vue'
import addMenuHelpInfoDialog from './addMenuHelpInfoDialog'
export default {
  name: 'NewAuthResource',
  components: { addMenuHelpInfoDialog },
  props: {
    tabCell: {
      type: Boolean,
      default: false
    },
    bodyShow: {
      type: Boolean,
      default: true
    },
    relationObject: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dependency: {
      type: Object,
      default: () => {
        return {}
      }
    },
    authResourceCheckData: {
      type: Array,
      default: () => {
        return []
      }
    },
    authResourceDataContent: {
      type: Array,
      default: () => {
        return []
      }
    },
    dataResourceList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      dataResourceListCell: this.dataResourceList,
      dataResourceCheckboxList: [],
      // dependency: {},
      currentRow: null,
      treeEmptyText: '加载中···',
      authResourceData: [],
      // authResourceCheckData: [],
      relationObjectCell: {},
      name: '',
      oldData: [], // 不可点击的选中数据
      dialogParam: {
        visible: false
      }
    }
  },
  computed: {},
  watch: {
    authResourceDataContent() {
      this.initLebelStyle()
    }
  },
  created() {},
  mounted() {
    this.initLebelStyle()
  },
  methods: {
    initLebelStyle () {
      this.$nextTick(() => {
        // 叶子元素的div添加样式
        // treeNodeLeaf是上面的最底层节点的class属性的名字 previousSibling
        var treeNodeLeaf = document.getElementsByClassName('treeNodeLeaf')
        setTimeout(function () {
          // 等到树都加载完了再去执行的这里
          for (var i = 0; i < treeNodeLeaf.length; i++) {
            // 点击节点时会重写className改为手动设置内联样式
            treeNodeLeaf[i].parentElement.parentElement.style.float = 'left'
            treeNodeLeaf[i].parentElement.parentElement.style.width = '180px'
          }
        }, 0)
      })
    },
    /**
     * @description: 新增菜单帮助信息附件弹窗（child为空时代表不是叶子节点，不显示弹窗）
     * @param {*} nodeObj 节点信息
     * @param {*} nodeData
     * @param {*} node
     * @return {*}
     */
    resourceNode (nodeObj, nodeData, node) {
      const child = nodeObj.children || ''
      if (!child) {
        this.dataResourceCheckboxList = nodeObj.dataRights || []
        this.dialogParam = {
          visible: true,
          resourceId: nodeObj.id, // 菜单id
          categoryName: nodeObj.text, // 菜单名称
          attachGroupId: nodeObj.attachGroupId // 是否有附件组ID
        }
      }
    },
    /**
     * @description: 给有附件的菜单添加特定的样式
     * data: {
     *    type：1-menu菜单；3-button按钮
     * }
     */
    renderContent (h, { node, data, store }) {
      const inner_span = h('span', {
        domProps: {
          innerHTML: node.label,
          title: node.label
        }
      })
      const flag = data.children === undefined && data.attachGroupId !== undefined
      const file_icon = h('i', {
        class: flag ? 'el-icon-download' : '',
        style: flag ? { 'font-size': '14px', 'margin-left': '2px' } : ''
      })
      // 这里不需要剔除掉首页
      const out_span = node.isLeaf === true ? h('span', { class: 'levelname treeNodeLeaf', style: { color: flag ? '#70B603' : '' } }, [file_icon, inner_span]) : h('span', { class: 'levelname treeNodeOthers' }, [file_icon, inner_span])
      return out_span
    },
    updateDataSources ({ relationObject, dataResourceList }) {
      if (relationObject) this.relationObjectCell = relationObject
      this.dataResourceListCell = dataResourceList
      this.updatecheckedData(this.relationObjectCell)
    },
    getCheckedData () {
      const checkedNodesList = JSON.parse(
        JSON.stringify(this.$refs.tree.getCheckedNodes(false, true))
      ) || []
      return checkedNodesList
    },
    updatecheckedData (relationObject) {
      const checkedList = this.getCheckedData()
      this.$emit('updateAuthResources', { checkedList, relationObject, dataResourceList: this.dataResourceListCell })
    }
  }
}
</script>