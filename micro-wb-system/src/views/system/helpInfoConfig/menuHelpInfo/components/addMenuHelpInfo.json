{"formList": [{"label": "栅格布局", "type": "row", "columnName": "row_1650879875200", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 24, "childList": [{"label": "菜单ID：", "type": "input", "columnName": "resourceId", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "菜单ID不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": true, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "operations": [{"status": ["detail"], "attr": "label", "formatter": ""}], "isBreak": false}]}], "name": "layout", "labelWidth": 160}, {"label": "栅格布局", "type": "row", "columnName": "row_1650879886692", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 24, "childList": [{"label": "菜单帮助信息：", "type": "webbase-upload", "columnName": "attachGroupId", "targetName": "fileIds_1651224197580", "defaultValue": "", "defaultContent": "", "isModel": true, "isLabelWidth": false, "icon-dev": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>", "span": 24, "width": "100%", "operation": [], "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "菜单帮助信息不能为空", "trigger": "blur", "selectStatusList": ["create", "edit"]}], "hidden": false, "props": {"action": "https://jsonplaceholder.typicode.com/photos/", "tip": "", "limit": 1, "text": "", "wordLimitStyle": "transparent"}, "icon": "el-icon-upload2", "text": "选择文件", "wbProps": {"prefix": "/web/support/v1", "attachmentListUrl": "/attachment/list", "proxy": "", "oldFileUrl": "", "attachType": "prompt", "multiple": false, "unitType": "M", "isEffect": true, "showFileLoading": false}, "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "business", "copyNewVal": "", "labelWidth": 160, "copyOldVal": [], "operations": [{"status": ["detail"], "attr": "label", "formatter": ""}], "dynamic": {}, "localDelete": false, "isHelpTipText": ""}]}], "name": "layout", "labelWidth": 160}, {"label": "栅格布局", "type": "row", "columnName": "row_1652840310110", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": true, "classify": "layout", "rowTypeFlex": true, "formFields": [{"span": 24, "childList": [{"label": "菜单名称：", "classify": "basic", "type": "input", "columnName": "categoryName", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160}]}], "name": "layout", "labelWidth": 160, "hiddenListStatus": []}, {"label": "", "type": "buttonGroup", "isLabelWidth": false, "columnName": "buttonGroup_1651029234264", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "el-icon-check", "label": "提交", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/web/support/v1/prompt/workbook/save", "class": "solid-with-icon-btn", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {"attachGroupId": "$attachGroupId$", "invalid": "1", "resourceId": "$resourceId$"}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": "", "detail": "hidden", "edit": "hidden", "create": "show", "actionWithPrePageList": []}, {"is-table-column": false, "columnName": "submit", "label": "提交", "type": "primary", "class": "solid-with-icon-btn", "icon": "el-icon-check", "bpmFlowImageCloumnName": "", "default": "hidden", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiMethod": "post+json", "apiName": "/web/support/v1/prompt/workbook/update", "apiCloseDialogWithResposne": false, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "detail": "hidden", "edit": "show", "create": "hidden", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "activeType": "button_group_form_interface", "apiParam": {"attachGroupId": "$attachGroupId$", "invalid": "1", "resourceId": "$resourceId$"}, "apiIsRefresh": "", "apiIsReturn": ""}, {"is-table-column": false, "columnName": "cancel", "label": "取消", "type": "primary", "class": "hollow-with-icon-btn", "icon": "el-icon-close", "bpmFlowImageCloumnName": "", "default": "show", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "", "validateProp": "", "apiMethod": "", "apiName": "", "apiCloseDialogWithResposne": false, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "detail": "show", "edit": "show", "create": "show", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "activeType": ""}, {"is-table-column": false, "columnName": "close", "label": "关闭", "type": "primary", "class": "hollow-with-icon-btn", "icon": "el-icon-close", "bpmFlowImageCloumnName": "", "default": "hidden", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "", "validateProp": "", "apiMethod": "", "apiName": "", "apiCloseDialogWithResposne": false, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "detail": "hidden", "edit": "hidden", "create": "hidden", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": []}, {"is-table-column": false, "columnName": "delete", "label": "删除", "type": "primary", "class": "solid-with-icon-btn", "icon": "el-icon-delete", "bpmFlowImageCloumnName": "", "default": "show", "authSwitch": false, "authId": "", "confirmationSwitch": true, "confirmationMessage": "确认删除$categoryName$的帮助信息？", "interactive": "button_custom_submit_part_validate", "validateProp": ["resourceId"], "apiMethod": "post+json", "apiName": "/web/support/v1/prompt/workbook/deleteById", "apiCloseDialogWithResposne": false, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "detail": "hidden", "edit": "show", "create": "hidden", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": ["edit"], "activeType": "button_group_form_interface", "apiParam": {"resourceId": "$resourceId$"}, "apiIsRefresh": "", "apiIsReturn": ""}], "name": "layout", "labelWidth": 160, "copyOldVal": ""}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "addMenuHelpInfo", "defaultClass": "webbas", "size": "small", "statusList": ["detail", "edit", "create"], "serverProps": {"localProxy": "/proxy_webbas", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "titleName": "新增菜单帮助信息", "isOpenPublicTips": true, "isOpenHelpTipSwitch": true, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "webbas0001", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": "400"}}, "dataConfig": {}, "virtual_model": {}, "model": {"resourceId": "", "attachGroupId": "", "fileIds_1651224197580": "", "categoryName": "", "127845dssad": "", "127845ds77": ""}}