<!--
 * @Author: <PERSON>
 * @Date: 2022-04-26 17:26:56
 * @LastEditors: yuxuan
 * @LastEditTime: 2023-04-18 18:08:48
 * @Description: 菜单帮助信息查询
 * @Version: V1.0
 * @FilePath: /webbas/vue-ui/micro-wb-system/src/views/system/helpInfoConfig/menuHelpInfo/index.vue
-->
<template>
  <div class="webbas">
    <div class="background-css">
      <div class="webbas-tree-css">
        <el-tabs v-if="tabFlag"
                 :before-leave="beforeLeaveTab"
                 type="card"
                 @tab-click="(tab, event) => handleTabsClick(tab, event)"
                 v-loading="tabloading"
                 empty-text="loding....."
                 v-model="tabsValue">
          <template v-for="(roucesCell, roucesKey) in tabList">
            <el-tab-pane :name="roucesCell.id"
                         :key="roucesKey"
                         style="">
              <span :key="roucesKey"
                    slot="label">{{roucesCell.title}}
              </span>
              <newAuthResourceCell :ref="`authResourceCell${roucesCell.id}`"
                                   :tabCell="true"
                                   :bodyShow="tabsValue === roucesCell.id"
                                   :dependency="dependency"
                                   :authResourceCheckData="authResourceCheckData"
                                   :authResourceDataContent="roucesCell.authResourceDataContent"
                                   :relationObject="relationObject"
                                   :dataResourceList="dataResourceList"
                                   @updateAuthResources="updateAuthResources"></newAuthResourceCell>
            </el-tab-pane>
          </template>
        </el-tabs>
        <newAuthResourceCell v-else
                             ref="authResourceCell"
                             :dependency="dependency"
                             :authResourceCheckData="authResourceCheckData"
                             :authResourceDataContent="authResourceDataContent"
                             :relationObject="relationObject"
                             :dataResourceList="dataResourceList"
                             @updateAuthResources="updateAuthResources"></newAuthResourceCell>
      </div>
    </div>
  </div>
</template>

<script>
// import Vue from 'vue'
import newAuthResourceCell from './components/newAuthResourceCell'
export default {
  name: 'NewAuthResourceTab',
  components: { newAuthResourceCell },
  props: {},
  data () {
    return {
      tabFlag: this.$projectConfig.multiMenu,
      tabList: [],
      relationObject: {},
      tabsValue: '',
      tabloading: false,
      dependency: {}, // 捕获所以被relation关联的id
      dataResourceList: [], // 数据权限资源
      authResourceData: [],
      authResourceCheckData: [], // 获取已选数据
      allCheckedAuthResourceList: [],
      authResourceDataContent: [], // 资源数组
      isBack: true // 是否直接返回
    }
  },
  watch: {},
  created () {
    this.init()
  },
  mounted () { },
  methods: {
    /**
     * @description: 初始化页面
     * @param {*}
     * @return {*}
     */
    init () {
      // 初始化树形结构
      this.authResourceDataContent = []
      this.tabloading = true
      const listRoleResUrl = this.$apiConfig.supportPathPrefix + '/prompt/workbook/listRoleResourceTree'
      this.$aspHttps.asp_PostForm(listRoleResUrl).then(response => {
        if (this.$reponseStatus(response)) {
          this.authResourceData = response.data.treeNodes
          this.authResourceDataContent = response.data.treeNodes
          this.relationObject = response.data.rightExtends
          this.authResourceCheckData = []
          this.oldData = []
          // 默认值
          this.tabsValue = this.tabList && this.tabList[0] && this.tabList[0].id
        }
        this.tabloading = false
      })
    },
    /**
     * @description: 弹窗保存刷新菜单树数据
     * @return {*}
     */
    updateAuthResources ({ checkedList = [], relationObject = {}, dataResourceList = [] }) {
      this.init()
    },
    beforeLeaveTab (activeName, oldActiveName) {
      // console.log('beforeLeaveTab', activeName, oldActiveName)
    },
    handleTabsClick (tab, event) {
      // console.log('handleTabsClick', tab, event)
    },
    handleClick (event) {
      // console.log('handleClick', event)
    }
  }
}
</script>
<style lang="scss" scoped>
.webbas {
  .background-css {
    height: 100%;
    box-sizing: border-box;
  }
  .webbas-tree-css {
    height: 100%;
  }
  ::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    border-bottom: 2px solid #409eff;
    color: #303133;
    font-weight: 600;
  }
  ::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item:hover {
    color: #409eff;
  }
}
</style>
