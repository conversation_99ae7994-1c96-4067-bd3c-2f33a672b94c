/** * 日志管理 */
<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form ref="searchForm"
                 :inline="true"
                 :model="table.searchForm">
          <el-row>
            <el-col :span="6"
                    style="padding-left: 15px">
              <el-form-item prop="firstType"
                            label="类型一级：">
                <el-select v-model="firstType"
                           class="el-select"
                           clearable
                           placeholder
                           @change="handlerFirstType">
                  <el-option v-for="(item, index) in logTypeDataFirst"
                             :key="index"
                             :label="item.name"
                             :value="item.type"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="secondType"
                            label="类型二级：">
                <el-select v-model="secondType"
                           class="el-select"
                           clearable
                           placeholder
                           @change="handlerSecondType">
                  <el-option v-for="(item, index) in logTypeDataSecond"
                             :key="index"
                             :label="item.name"
                             :value="item.type"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="thirdType"
                            label="类型三级：">
                <el-select v-model="thirdType"
                           class="el-select"
                           clearable
                           placeholder
                           @change="handlerThirdType">
                  <el-option v-for="(item, index) in logTypeDataThird"
                             :key="index"
                             :label="item.name"
                             :value="item.type"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="operatorName"
                            label="操作员：">
                <el-input v-model.trim="table.searchForm.operatorName"
                          placeholder
                          name="operatorName"
                          @keyup.enter.native="search(true)"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12"
                    style="padding-left: 15px">
              <el-form-item label="操作时间：">
                <asp-date-range :labelWith="85"
                                :start-date.sync="table.searchForm.operationDateStart"
                                :end-date.sync="table.searchForm.operationDateEnd" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="description"
                            label="操作说明：">
                <el-input v-model.trim="table.searchForm.description"
                          placeholder
                          name="description"
                          @keyup.enter.native="search(true)"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="query-area-btn-css">
                <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_050103' })"
                               icon="el-icon-search"
                               name="查询"
                               @click="search()"></asp-btn-solid>
                <asp-btn-hollow icon="el-icon-refresh"
                                name="重置"
                                @click="reset()"></asp-btn-hollow>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <asp-table ref="table"
                 :url="table.url"
                 :param="table.searchForm"
                 :prefix="table.prefix"
                 :index-switch="false"
                 type="">
        <template slot="header">
          <asp-btn-hollow v-hasAuth="doAuth({ btnCode: 'wb_050102' })"
                          name="导出"
                          icon="el-icon-download"
                          @click="exportList"></asp-btn-hollow>
        </template>
        <asp-table-column prop="logTypeName"
                          min-width="110"
                          show-overflow-tooltip
                          label="日志类型">
        </asp-table-column>
        <asp-table-column prop="operatorName"
                          min-width="100"
                          show-overflow-tooltip
                          label="操作员">
        </asp-table-column>
        <asp-table-column prop="clientIp"
                          label="客户端IP"
                          min-width="100"
                          show-overflow-tooltip>
        </asp-table-column>
        <asp-table-column prop="businessName"
                          min-width="200"
                          show-overflow-tooltip
                          label="操作对象">
        </asp-table-column>
        <asp-table-column prop="description"
                          min-width="250"
                          show-overflow-tooltip
                          label="操作说明">
        </asp-table-column>
        <asp-table-column prop="operationResult"
                          width="100"
                          label="操作结果"
                          show-overflow-tooltip>
        </asp-table-column>
        <asp-table-column prop="operationDateStr"
                          width="155"
                          label="操作时间"
                          sort-key="operationDate">
        </asp-table-column>
      </asp-table>
    </div>
  </div>
</template>

<script>
import AspDateRange from '../../../components/aspire/asp-control/asp-date/asp-date-range'
export default {
  name: 'Log',
  components: { AspDateRange },
  data () {
    return {
      logTypeDataFirst: [],
      logTypeDataSecond: [],
      logTypeDataThird: [],
      firstType: '',
      secondType: '',
      thirdType: '',
      table: {
        prefix: this.$apiConfig.supportPathPrefix,
        url: '/component/log/listLog',
        searchForm: {
          logType: '',
          operatorName: '',
          operationDomain: '',
          operationDateStart: '',
          operationDateEnd: '',
          description: ''
        }
      }
    }
  },
  computed: {
    // TODO 面包屑中获取当前页标题 暂时屏蔽
    // title () {
    //   let currentPath = this.$main_tools.store.state.app.currentPath
    //   return currentPath[currentPath.length - 1].title
    // }
  },
  watch: {},
  created () {
    this.init()
  },
  methods: {
    /**
     * 按钮权限
     * @method doAuth
     * @param {object} opt
     * @return {object}
     */
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    // 字段排序
    handleSortChange (column, prop, order) {
      // console.log(prop, order)
      if (column.prop === 'operationDateStr') {
        this.table.searchForm.sortName = 'OPERATION_DATE'
      }
      if (column.order === 'ascending') {
        this.table.searchForm.order = 'asc'
      } else if (column.order === 'descending') {
        this.table.searchForm.order = 'desc'
      } else {
        this.table.searchForm.sortName = ''
        this.table.searchForm.order = ''
      }
      this.search()
    },
    init () {
      this.getlogType(this.firstType, 'first')
      // this.search()
    },
    // 获取日志类型
    getlogType (val, dataSide) {
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.supportPathPrefix + '/component/log/listForCombo',
          { parentType: val }
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            if (dataSide === 'first') {
              this.firstType = ''
              this.logTypeDataFirst = []
              this.secondType = ''
              this.logTypeDataSecond = []
              this.thirdType = ''
              this.logTypeDataThird = []
              this.logTypeDataFirst = response.data
            } else if (dataSide === 'second') {
              this.secondType = ''
              this.logTypeDataSecond = []
              this.thirdType = ''
              this.logTypeDataThird = []
              this.logTypeDataSecond = response.data
            } else {
              this.thirdType = ''
              this.logTypeDataThird = []
              this.logTypeDataThird = response.data
            }
          }
        })
    },
    handlerFirstType (val) {
      this.firstType = val
      if (val) {
        this.getlogType(this.firstType, 'second')
      } else {
        this.secondType = ''
        this.logTypeDataSecond = []
        this.thirdType = ''
        this.logTypeDataThird = []
      }
      this.getRealLogType()
    },
    handlerSecondType (val) {
      this.secondType = val
      if (val) {
        this.getlogType(this.secondType, 'third')
      } else {
        this.thirdType = ''
        this.logTypeDataThird = []
      }
      // 等待this.getlogType()后再执行
      setTimeout(() => {
        this.getRealLogType()
      }, 100)
    },
    handlerThirdType (val) {
      this.thirdType = val
      this.getRealLogType()
    },
    getRealLogType () {
      if (this.thirdType !== '') {
        this.table.searchForm.logType = this.thirdType
      } else if (this.secondType !== '') {
        this.table.searchForm.logType = this.secondType
      } else if (this.firstType !== '') {
        this.table.searchForm.logType = this.firstType
      } else {
        this.table.searchForm.logType = ''
      }
    },
    /**
     * 导出excel
     * @method exportList
     */
    exportList (pageRestart) {
      // console.log(pageRestart)
      let params = ''
      const obj = this.table.searchForm
      for (const key in obj) {
        params += key + '=' + (obj[key] === undefined ? '' : obj[key]) + '&'
      }
      params = params.substring(0, params.length - 1)
      let url = this.$apiConfig.supportPathPrefix + '/component/log/exportLog?' + params
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGetOpen(url)
    },
    // 列表查询
    search () {
      this.$refs.table.asp_search()
    },
    reset () {
      this.firstType = ''
      this.secondType = ''
      this.thirdType = ''
      this.logTypeDataSecond = []
      this.logTypeDataThird = []
      this.$refs.table.asp_reset()
    },
    goBack () {
      this.$router.back()
    }
  }
}
</script>
