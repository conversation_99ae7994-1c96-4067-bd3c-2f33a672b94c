<template>
  <div class="asp-transfer"
       :style="{width: width}">
    <asp-transfer-panel ref="leftTransfer"
                        v-model="leftCheckedData"
                        :checkedAll="checkedAlls[0]"
                        :props="props"
                        :data="leftData"
                        :showCount="showCounts[0]"
                        :height="height"
                        :titleText="titleTexts[0]">
      <slot name="left-header"
            slot="header" />
      <slot name="left-header-label"
            slot="header-label" />
      <slot name="left-search"
            slot="search" />
      <slot name="left-body"
            slot="body" />
      <slot name="left-footer"
            slot="footer" />
    </asp-transfer-panel>
    <div class="asp-transfer-buttons">
      <el-button class="asp-transfer-button-right"
                 :class="buttonClasses(0)"
                 :disabled="disabledLeftButton"
                 @click.native="addToRight">
        <span class="button-text">{{ buttonTexts[0] }}</span>
        <i class="el-icon-arrow-right"></i>
      </el-button>
      <el-button class="asp-transfer-button-left"
                 :class="buttonClasses(1)"
                 :disabled="disabledRightButton"
                 @click.native="addToLeft">
        <i class="el-icon-arrow-left"></i>
        <span class="button-text">{{ buttonTexts[1] }}</span>
      </el-button>
    </div>
    <asp-transfer-panel ref="rightTransfer"
                        v-model="rightCheckedData"
                        :checkedAll="checkedAlls[1]"
                        :props="props"
                        :data="rightData"
                        :showCount="showCounts[1]"
                        :height="height"
                        :titleText="titleTexts[1]">
      <slot name="right-header"
            slot="header" />
      <slot name="right-header-label"
            slot="header-label" />
      <slot name="right-search"
            slot="search" />
      <slot name="right-body"
            slot="body" />
      <slot name="right-footer"
            slot="footer" />
    </asp-transfer-panel>
  </div>
</template>

<script>
import aspTransferPanel from './asp-transfer-panel.vue'
export default {
  components: { aspTransferPanel },
  name: 'aspTransfer',
  props: {
    props: {
      type: Object,
      default: () => {
        return {
          key: 'key',
          label: 'label',
          disabled: 'disabled'
        }
      }
    },
    isDefaultModel: { // 是否是默认模型结构
      type: Boolean,
      default: true
    },
    value: { // 已选数据
      type: Array,
      default () {
        return []
      }
    },
    data: { // 待选数据
      type: Array,
      default () {
        return []
      }
    },
    showCounts: { // 是否显示已选项
      type: Array,
      default () {
        return []
      }
    },
    checkedAlls: { // 是否展示全部选择
      type: Array,
      default () {
        return []
      }
    },
    // 显示条件查询
    showQuery: {
      type: Boolean,
      default: true
    },
    // 标题文本
    titleTexts: {
      type: Array,
      default () {
        return ['待选项', '已选项']
      }
    },
    // 按钮文本
    buttonTexts: {
      type: Array,
      default () {
        return []
      }
    },
    // 按钮类名
    buttonclassNames: {
      type: Array,
      default () {
        return []
      }
    },
    height: { // 表格高度
      type: String,
      default: '300px'
    },
    width: {
      type: String,
      default: '600px'
    }
  },
  data () {
    return {
      // leftData: this.data || [], // 待选数据
      // rightData: this.value || [], // 已选数据
      leftCheckedData: {}, // 左侧已选数据
      rightCheckedData: {} // 右侧已选数据
    }
  },
  created () { },
  computed: {
    hasButtonTexts () {
      return this.buttonTexts.length === 2
    },
    buttonClasses () {
      return function (index = 0) {
        const className = this.buttonclassNames[index] || ''
        return ['asp-transfer-button', { 'is-with-texts': this.hasButtonTexts }, className]
      }
    },
    disabledLeftButton () {
      return !(this.leftCheckedData.keys && this.leftCheckedData.keys.length > 0)
    },
    disabledRightButton () {
      return !(this.rightCheckedData.keys && this.rightCheckedData.keys.length > 0)
    },
    leftData () {
      let keys = this.value
      if (this.value.length === 0) return this.data
      if (!this.isDefaultModel) keys = this.value.map(item => { return item[this.props.key] })
      const list = this.data.filter(item => { return (!keys.includes(item[this.props.key])) })
      return list
    },
    rightData () {
      const list = []
      if (this.isDefaultModel) {
        this.data.forEach(item => {
          if (this.value.includes(item[this.props.key])) {
            list.push(item)
          }
        })
        return list
      }
      return this.value
    }
  },
  methods: {
    /** 添加 */
    addToRight () {
      const value = Object.assign({}, this.leftCheckedData)
      let results = []
      if (this.isDefaultModel) {
        results = [...value.keys, ...this.value]
      } else {
        results = [...value.data, ...this.value]
      }
      this.$emit('input', results)
      // 清除状态
      this.$refs.leftTransfer.allChecked = false
      this.leftCheckedData = {}
      this.onBind({ alias: 'addToRight', name: this.buttonTexts[0], type: 'buttonClick', data: { checkedData: value, value: results, data: this.leftData } })
    },
    /** 删除 */
    addToLeft () {
      const value = Object.assign({}, this.rightCheckedData)
      let results = []
      // this.value.filter(item => { return !(value.keys.includes(item[this.props.key]))})
      if (this.isDefaultModel) {
        results = this.value.filter(key => { return !value.keys.includes(key) })
      } else {
        results = this.value.filter(item => { return !(value.keys.includes(item[this.props.key])) })
      }
      this.$emit('input', results)
      // 清除状态
      this.$refs.rightTransfer.allChecked = false
      this.rightCheckedData = {}
      this.onBind({ alias: 'addToLeft', name: this.buttonTexts[0], type: 'buttonClick', data: { checkedData: value, value: results, data: this.rightData } })
    },
    onBind ({ alias, name, type, data }) {
      this.$emit('buttonClick', { alias, name, type, data })
    }
  }
}
</script>

<style scoped lang="scss">
@import './asp-transfer.scss';
</style>
