.asp-transfer {
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.el-icon-arrow-right,
.el-icon-arrow-left {
  font-size: 40px;
  cursor: pointer;
}

.asp-transfer-buttons {
  display: inline-block;
  vertical-align: middle;
  width: 100px;
}

.asp-transfer-button {
  display: block;
  margin: 0 auto;
  padding: 10px;
  border-radius: 4px;
  color: #fff;
  background-color: #409eff;
  font-size: 0;
}

.asp-transfer-button .button-text {
  margin-left: 2px;
  margin-right: 2px;
}

.asp-transfer-button.is-with-texts {
  border-radius: 4px;
}

.asp-transfer-button.is-disabled,
.asp-transfer-button.is-disabled:hover {
  border: 1px solid #dcdfe6;
  background-color: #f5f7fa;
  // color: #c0c4cc;
}

.asp-transfer-button:first-child {
  margin-bottom: 10px;
}

.asp-transfer-button:nth-child(2) {
  margin: 0 auto;
}

.asp-transfer-button i,
.asp-transfer-button span {
  font-size: 14px;
}
.solid-with-icon-btn {
  font-size: 14px;
  padding: 7px 15.5px;
  border-radius: 4px;
  background: linear-gradient(360deg, rgb(69, 118, 228) 0%, rgb(111, 158, 240) 100%);
  color: rgb(255, 255, 255);
}
.hollow-with-icon-btn {
  font-size: 14px;
  padding: 7px 15.5px;
  color: rgb(70, 118, 229);
  border-color: #419fff;
  background-color: #f5fafd;
}
.asp-transfer-panel {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  display: inline-block;
  width: calc((100% - 100px) / 2);
  max-height: 100%;
  box-sizing: border-box;
  position: relative;

  .asp-transfer-panel-header {
    height: 40px;
    line-height: 40px;
    background: #f5f7fa;
    margin: 0;
    padding-left: 15px;
    border-bottom: 1px solid #ebeef5;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #000;

    span:last-child {
      position: absolute;
      right: 15px;
    }
  }
  .asp-transfer-search {
    .el-input {
      margin: 15px;
      width: calc(100% - 30px);
      .el-input__inner {
        border-radius: 16px;
      }
    }
    .el-input--prefix .el-input__inner {
      border-radius: 16px;
    }
  }
}
