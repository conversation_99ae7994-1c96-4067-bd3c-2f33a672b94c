<!--
 * @Author: yuxuan <EMAIL>
 * @Date: 2023-03-10 19:40:15
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-04-03 19:49:23
 * @Description: file content
-->
<template>
  <div class="asp-transfer-panel">
    <p class="asp-transfer-panel-header">
      <slot name="header">
        <el-checkbox v-if="checkedAll"
                     v-model="allChecked"
                     :indeterminate="isChecked"
                     @change="handleAllCheckedChange">
        </el-checkbox>
        <span :style="checkedAll ? {} : { marginLeft: '20px' }">
          <slot name="header-label">
            {{ titleText }}
          </slot>
        </span>
        <span v-show="showCount">{{leftSelection.length}}/{{data.length}}</span>
      </slot>
    </p>
    <div class="asp-transfer-search">
      <slot name="search">
        <el-input v-if="filterable"
                  v-model="query"
                  size="default"
                  :placeholder="placeholder"
                  prefix-icon="el-icon-search"
                  clearable />
      </slot>
    </div>
    <div :style="bodyStyle"
         style="padding: 0 15px 10px 15px">
      <slot name="body"
            :data="{data, value}">
        <el-checkbox-group v-model="leftSelection"
                           @change="changeSelect">
          <el-checkbox :key="index"
                       v-for="(item, index) in groupList"
                       class="el-transfer-panel__item"
                       :label="item[props.key]"
                       :value="item[props.key]"
                       :disabled="item[props.disabled]">{{item[props.label]}}</el-checkbox>
        </el-checkbox-group>
      </slot>
    </div>
    <div class="asp-transfer-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'aspTransferPanel',
  props: {
    props: {
      type: Object,
      default: () => {
        return {
          key: 'key',
          label: 'label',
          disabled: 'disabled'
        }
      }
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    value: { // 选中的值
      type: Object,
      default () {
        return {}
      }
    },
    checkedAll: { // 是否展示全部选择
      type: Boolean,
      default: true
    },
    placeholder: { // 搜索框占位符
      type: String,
      default: '请输入搜索内容00'
    },
    filterable: { // 是否展示搜索框
      type: Boolean,
      default: true
    },
    showCount: { // 是否展示已选项
      type: Boolean,
      default: true
    },
    titleText: { // 标题文本leftCountFlag
      type: String,
      required: true
    },
    height: { // 表格高度
      type: String,
      default: '300px'
    }
  },
  data () {
    return {
      allChecked: false,
      leftData: [],
      leftSelection: [],
      checkedData: {
        keys: [],
        data: []
      },
      query: ''
    }
  },
  created () { },
  computed: {
    isChecked () {
      if (this.leftSelection && this.leftSelection.length > 0 && this.leftSelection.length !== this.data.length) {
        return true
      }
      return false
    },
    bodyStyle () {
      return {
        height: this.height,
        'overflow-y': 'scroll'
      }
    },
    groupList () {
      const list = []
      if (!this.query) return this.data
      this.data.forEach(item => {
        if (item[this.props.key].indexOf(this.query) >= 0 || item[this.props.label].indexOf(this.query) >= 0) {
          list.push(item)
        }
      })
      return list
    }
  },
  watch: {
    value: {
      handler (val, oldVal) {
        if (val === oldVal) return
        this.leftSelection = [].concat(val.keys || [])
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleAllCheckedChange (val) {
      this.allChecked = val
      this.leftSelection = []
      this.checkedData.keys = []
      this.checkedData.data = []
      if (val) {
        this.data.forEach(item => {
          this.leftSelection.push(item[this.props.key])
          this.checkedData.keys.push(item[this.props.key])
          this.checkedData.data.push(item)
        })
      }
      this.$emit('input', this.checkedData)
    },
    changeSelect (data) {
      this.leftSelection = data
      this.checkedData.keys = data
      this.checkedData.data = []
      this.data.forEach(item => {
        if (data.includes(item[this.props.key])) {
          this.checkedData.data.push(item)
        }
      })
      this.allChecked = this.leftSelection.length === this.data.length
      this.$emit('input', this.checkedData)
    }
  }
}
</script>

<style scoped lang="scss">
@import './asp-transfer.scss';
</style>