<!--
 * @Author: yuxuan <EMAIL>
 * @Date: 2023-03-15 19:56:35
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-04-03 17:27:58
 * @Description: file content
-->
<template>
  <div class="asp-left-tab-panel">
    <el-radio-group v-model="radioValue"
                    size="small"
                    @change="radioChange">
      <el-radio-button v-for="(item, index) in data"
                       :key="index"
                       :disabled="radioDisabled(item.id)"
                       :value="item.id"
                       :label="item.id">{{item.text}}</el-radio-button>
    </el-radio-group>
  </div>
</template>

<script>
export default {
  name: 'leftTabPanel',
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    index: {
      type: Number
    },
    value: {
      type: String,
      default: ''
    },
    checkedData: {
      type: Array,
      default: () => {
        return []
      }
    },
    disabledAll: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      radioValue: '', // 当前单选数据id
      currentData: {} // 当前单选数据
    }
  },
  computed: {},
  created () { },
  mounted () { },
  methods: {
    radioDisabled (id) {
      if (this.disabledAll) return true
      const keys = this.checkedData.map(item => { return item.id })
      if (keys.includes(id)) {
        return true
      }
      return false
    },
    radioChange (val) {
      this.$emit('input', val)
      this.data.forEach(item => {
        if (item.id === val) {
          this.$emit('updateParent', { data: item, index: this.index })
          this.currentData = { data: item, index: this.index }
        }
      })
    },
    resetData () {
      this.radioValue = '' // 当前单选数据id
      this.currentData = {} // 当前单选数据
    }
  }
}
</script>

<style scoped lang="scss">
</style>