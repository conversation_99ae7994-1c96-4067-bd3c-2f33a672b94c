<!--
 * @Author: yuxuan <EMAIL>
 * @Date: 2023-03-15 19:56:35
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-04-10 11:48:20
 * @Description: file content
-->
<template>
  <div class="asp-left-tab">
    <el-tabs :before-leave="beforeLeaveTab"
             type="card"
             @tab-click="(tab, event) => handleTabsClick(tab, event)"
             v-model="tabsValue">
      <template v-for="(tabItem, index) in tabList">
        <el-tab-pane :name="tabItem.id"
                     :key="index"
                     style="">
          <span :key="index"
                slot="label">{{tabItem.text}}
            <i v-if="tabsValue === tabItem.id && tabsValue !== `none${index}`"
               class="el-icon-close"
               style="width: 14px;margin-left:0"
               @click="clearTabValue(tabItem.id, index)"></i>
            <i v-else
               class="el-icon-arrow-down"></i>
          </span>
          <left-tab-panel :ref="`leftTabPanel${index}`"
                          v-model="tabItem.id"
                          :key="tabItem.pId"
                          :index="index"
                          :disabledAll="disabledAll"
                          :checkedData="checkedData"
                          :data="tabItem.data"
                          @updateParent="updateTabs"></left-tab-panel>
        </el-tab-pane>
      </template>
    </el-tabs>
  </div>
</template>

<script>
import leftTabPanel from './leftTabPanel.vue'
export default {
  components: { leftTabPanel },
  name: 'leftTab',
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    checkedData: {
      type: Array,
      default: () => {
        return []
      }
    },
    disabledAll: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      tabsValue: 'none0',
      tabList: [
        {
          id: 'none0',
          text: '请选择',
          pId: '000',
          hasCheckedNums: 0, // 下一层级已选个数
          data: this.data
        }
      ]
    }
  },
  created () { },
  computed: {
  },
  watch: {
    data (val) {
      console.log('leftTab000', val)
      if (val.length > 0) {
        this.tabList[0].data = val
      }
    }
  },
  methods: {
    beforeLeaveTab (activeName, oldActiveName) {
      // console.log('beforeLeaveTab', activeName, oldActiveName)
    },
    /** tab清除事件 */
    clearTabValue (tab, tabIndex) {
      this.tabsValue = `none${tabIndex}`
      this.tabList[tabIndex].id = `none${tabIndex}`
      this.tabList[tabIndex].text = '请选择'
      if (this.tabList[tabIndex + 1]) {
        this.tabList.splice(tabIndex + 1, this.tabList.length)
      }
      const refsObjNext = this.$refs[`leftTabPanel${tabIndex}`]
      refsObjNext && refsObjNext[0].resetData()
      if (tabIndex > 0) { // 同步上层数据动作，边界场景除外
        const refsObj = this.$refs[`leftTabPanel${tabIndex - 1}`]
        const data = (refsObj && refsObj[0].currentData) || {}
        data.tabIndex = data.index
        this.$emit('change', { ...data }) // 重新设置选中数据
      } else {
        this.$emit('change', { alias: 'clearCheck' }) // 重新设置选中数据
      }
    },
    handleTabsClick (tab, event) {
      if (this.tabsValue === 'none0' && this.tabList[0].data.length === 0) {
        this.tabList[0].data = [...this.data]
      }
    },
    async updateTabs ({ data, index = 0 }) {
      this.tabList[index].text = data.text
      if (this.tabList[index + 1]) {
        this.tabList.splice(index + 1, this.tabList.length)
      }
      this.tabsValue = data.id
      // 2024-4 针对数据精确到街道乡镇，数量级过大，改为接口轮询逐级查询下级数据
      // 当children为空时优先走接口加载数据
      if ((!data.children || data.children.length === 0) && data.id) {
        const url = this.$apiConfig.supportPathPrefix + '/division/listChildrenDivisionTree'
        const childrenResp = await this.$aspHttps.asp_PostForm(url, { code: data.id })
        if (this.$reponseStatus(childrenResp) &&
          Object.prototype.hasOwnProperty.call(childrenResp, 'data') &&
          Object.prototype.hasOwnProperty.call(childrenResp.data[0], 'children')) {
          const children = childrenResp.data[0].children
          this.$set(data, 'children', children)
        }
      }
      if (data.children && data.children.length > 0 && data.id !== '000') {
        // 边界场景，中央除外
        this.tabsValue = `none${index + 1}`
        this.tabList.push(
          {
            id: `none${index + 1}`,
            pId: data.id,
            text: '请选择',
            hasCheckedNums: 0,
            data: data.children
          }
        )
      }
      this.$emit('change', { data, tabIndex: index })
      // 同步处理选中记录状态--标记已选回填个数
      this.markCheckedOnTree(index)
    },
    /** 当左侧数据确定添加后，数据重置动作 */
    updateLeftTabVal ({ tabIndex }) {
      if (tabIndex === undefined) return
      // 初始化tablList和tabsValue
      this.tabsValue = `none${tabIndex}`
      this.tabList[tabIndex].id = `none${tabIndex}`
      this.tabList[tabIndex].text = '请选择'
      this.tabList[tabIndex].hasCheckedNums = this.tabList[tabIndex].hasCheckedNums + 1
      const refsObjNext = this.$refs[`leftTabPanel${tabIndex}`]
      refsObjNext && refsObjNext[0].resetData()
      this.tabList.splice(tabIndex + 1, this.tabList.length)
      if (tabIndex > 0) { // 同步上层数据动作，边界场景除外
        const refsObj = this.$refs[`leftTabPanel${tabIndex - 1}`]
        const data = (refsObj && refsObj[0].currentData) || {}
        data.tabIndex = data.index
        this.$emit('change', { ...data }) // 重新设置选中数据
      }
      // 判断是否合并成上层数据：当子集全部选中时，自动触发父级数据同步选中操作(边界场景除外-当前即是最上层)
      if (tabIndex !== 0 && this.tabList[tabIndex].hasCheckedNums === this.tabList[tabIndex].data.length) {
        this.$emit('change', { alias: 'addToRight' })
      }
    },
    markCheckedOnTree (tabIndex) {
      if (!(this.tabList[tabIndex] && this.tabList[tabIndex].hasCheckedNums === 0)) return // tab结构不存在无需处理
      if (this.checkedData.length === 0) return // 已选数据为空无需处理
      const keys = this.checkedData.map(item => { return item.id })
      const checkedKeys = this.tabList[tabIndex].data.filter(item => { return keys.includes(item.id) })
      this.tabList[tabIndex].hasCheckedNums = checkedKeys.length
    },
    reset () {
      this.tabsValue = 'none0'
      this.tabList = [
        {
          id: 'none0',
          text: '请选择',
          hasCheckedNums: 0, // 下一层级已选个数
          data: this.data
        }
      ]
      const refsObjNext = this.$refs[`leftTabPanel${0}`]
      refsObjNext && refsObjNext[0].resetData()
    }
  }
}
</script>

<style scoped lang="scss">
</style>