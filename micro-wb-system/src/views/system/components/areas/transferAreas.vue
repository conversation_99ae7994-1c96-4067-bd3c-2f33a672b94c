<!--
 * @Author: <PERSON>
 * @Date: 2021-11-10 13:48:43
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-04-03 19:38:32
 * @Description: 关联归属区域 弹框（系统成员管理-新增成员-归属区域）
 * @Version: V1.1
 * @FilePath: /WEB2/micro-wb-system/src/views/system/components/areas/checkAreas.vue
-->
<template>
  <asp-dialog v-model="dialogParam.modelVisible"
              :title="dialogParam.title"
              :close-on-click-modal="false"
              :visible.sync="dialogParam.areaModelVisible"
              :lock-scroll="true"
              :append-to-body="true"
              class="webbas"
              width="1050px">
    <template>
      <div class="">
        <asp-transfer ref="aspTransfer"
                      :props="defaultProps"
                      width="1000px"
                      :isDefaultModel="false"
                      :titleTexts="['待关联归属区域', '已关联归属区域']"
                      :checkedAlls="[false, true]"
                      :showCounts="[false, true]"
                      :buttonTexts="['添加','删除']"
                      :buttonclassNames="['solid-with-icon-btn','hollow-with-icon-btn']"
                      :data="areaTreeData"
                      :value="checkedData"
                      @buttonClick="transferClick"
                      v-model="checkedData">
          <template slot="left-search">
            <el-input v-model="leftCurrentTitle"
                      size="default"
                      readOnly />
          </template>
          <template slot="left-body">
            <leftTab ref="leftTab"
                     :disabledAll="disabledAll"
                     :data="areaTreeData"
                     :checkedData="checkedData"
                     @change="leftTabChange"></leftTab>
          </template>
        </asp-transfer>
      </div>
    </template>
    <template slot="footer-center">
      <asp-btn-solid name="确定"
                     @click.native.prevent="operAreas('save')">
      </asp-btn-solid>
      <asp-btn-hollow name="清空"
                      @click="operAreas('clear')"></asp-btn-hollow>
      <asp-btn-hollow name="取消"
                      @click="operAreas('cancel')"></asp-btn-hollow>
    </template>
  </asp-dialog>
</template>

<script>
import leftTab from './components/leftTab.vue'
import aspTransfer from '../transfer/asp-transfer.vue'
export default {
  components: { aspTransfer, leftTab },
  name: 'transferAreas',
  props: {
    dialogParam: {
      type: Object,
      default: null
    },
    multiple: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      isSave: false, // 是否能提交
      defaultProps: { // 穿梭框配置属性
        key: 'id',
        label: 'pathName',
        disabled: 'disabled'
      },
      areaTreeData: [], // 地域树形节点数据
      checkedData: [], // 当前右侧区域展示的已选数据
      leftCurrentTitle: '', // 左侧输入框展示的当前已选数据
      disabledAll: false, // 禁止选择所以地域
      allCheckedIds: [], // 所有被选中的节点Id
      areasCheckedNodes: [] // 选中的地域的节点数据
    }
  },
  watch: {
    checkedData (val) {
      // console.log('checkedData', val)
    },
    'dialogParam.areaModelVisible' (val) {
      if (val) {
        // 获取地域
        this.getAreasData()
      }
    }
  },
  mounted () { },
  methods: {
    /** 穿梭框按钮点击事件 */
    transferClick ({ alias, name, type, data }) {
      const { checkedData = {} } = data
      // 若是单选事件，直接更新已选数据
      if (alias === 'addToRight' && !this.multiple) {
        this.checkedData = checkedData.data
        // 清空leftCurrentTitle
        if (checkedData.tabIndex === 0) this.leftCurrentTitle = ''
      } else if (alias === 'addToRight') {
        this.$refs.leftTab.updateLeftTabVal(checkedData)
        // 过滤需要合并的数据
        this.filterCheckedData(checkedData)
        // 清空leftCurrentTitle
        if (checkedData.tabIndex === 0) this.leftCurrentTitle = ''
      } else if (alias === 'addToLeft') {
        if (checkedData.data[0].id === '000') {
          this.disabledAll = false // 取消选中中央后，取消禁用所有
        }
      }
    },
    /** tab页签内数据变化 */
    leftTabChange ({ alias, data, tabIndex }) {
      if (alias === 'addToRight') {
        this.$refs.aspTransfer.addToRight()
      } else if (alias === 'clearCheck') {
        this.$refs.aspTransfer.leftCheckedData = {}
        this.leftCurrentTitle = ''
      } else {
        this.$refs.aspTransfer.leftCheckedData = { keys: [data.id], data: [data], tabIndex }
        this.leftCurrentTitle = data.pathName
      }
    },
    /** 初始化待选地域数据 */
    async getAreasData () {
      // const areaData = this.$main_tools.areas.getDownJsonById('200', localStorage.baseAreas)
      if (!this.dialogParam.parentDivision) {
        this.$message.warning('地域数据异常请联系管理员！')
        return
      } else if (this.dialogParam.parentDivision === '000') {
        const areaData = this.$main_tools.areas.getDownJsonById(
          this.dialogParam.parentDivision,
          localStorage.baseAreas
        )
        if (areaData === null) {
          this.$message.warning('地域数据异常请联系管理员！')
          return
        }
        this.areaTreeData = []
        if (areaData.id === '000') {
          this.areaTreeData = areaData.children
        }
        this.areaTreeData.unshift(areaData) // 首行塞值
      } else {
        const url = this.$apiConfig.supportPathPrefix + '/division/listChildrenDivisionTree'
        const divisions = await this.$aspHttps.asp_PostForm(url, { code: this.dialogParam.parentDivision })
        if (this.$reponseStatus(divisions) &&
          Object.prototype.hasOwnProperty.call(divisions, 'data')) {
          this.areaTreeData = divisions.data
        }
      }
      this.checkedData = this.dialogParam.checkedDatas || []
      // 特殊场景判断（中央，禁用所有待选数据）
      if (this.checkedData[0] && this.checkedData[0].id === '000') {
        this.disabledAll = true // 选中中央后，禁用所有
      }
      this.leftCurrentTitle = ''
    },
    // 过滤需要合并的数据
    filterCheckedData ({ data }) {
      // 获取当前需要合并的数据下的所有children的ids
      if (!data) return
      // 边界场景处理：中央--直接合并所有
      if (data[0].id === '000') {
        this.checkedData = data
        this.disabledAll = true // 选中中央后，禁用所有
        return
      }
      const keys = []
      data.forEach(item => {
        getChildRenKeys(item.children)
      })
      // 过滤
      this.checkedData = this.checkedData.filter(item => { return !keys.includes(item.id) })
      function getChildRenKeys (list = []) {
        list.forEach(item => {
          item.id && keys.push(item.id)
          if (item.children && item.children.length > 0) {
            getChildRenKeys(item.children)
          }
        })
      }
    },
    // 提交选中的地域节点id
    operAreas (val) {
      // 关闭弹窗 初始化信息
      const resetDialog = () => {
        this.dialogParam.areaModelVisible = false
      }
      switch (val) {
        case 'save': {
          this.getCheckedDatas()
          if (this.allCheckedIds.length === 0) {
            this.$message.warning('请至少选择一个地区！')
            this.isSave = false
            return
          }
          this.$emit('updateAreaData', {
            Nodes: this.checkedData,
            Ids: this.allCheckedIds,
            labels: this.allCheckedLabels
          })
          this.operAreas('clear')
          resetDialog()
          break
        }
        case 'cancel': {
          this.operAreas('clear')
          resetDialog()
          break
        }
        case 'clear': {
          this.checkedData = []
          this.leftCurrentTitle = ''
          this.disabledAll = false // 选中中央后，禁用所有
          this.$refs.leftTab.reset()
          break
        }
      }
    },
    // 获取选择的节点的id/key
    getCheckedDatas () {
      this.allCheckedIds = this.checkedData.map(item => { return item.id })
      this.allCheckedLabels = this.checkedData.map(item => { return item.pathName })
    },
    // 保存选中的地域数据
    reorganizeCheckedDatas () {
      this.getCheckedKeys()
      if (this.allCheckedIds.length === 0) {
        this.$message.warning('请至少选择一个地区！')
        this.isSave = false
        return
      }
      this.isSave = true
      const data = this.$main_tools.areas.handlerCheckedData(
        JSON.stringify(this.areasCheckedNodes),
        localStorage.baseAreas,
        false
      )
      this.$emit('updateAreaData', data)
    },
    renderContent (h, { node, data, store }) {
      const inner_span = h('span', {
        domProps: {
          innerHTML: node.label
        }
      })
      const out_span =
        node.isLeaf === true
          ? h('span', { class: 'levelname' }, [inner_span])
          : h('span', {}, [inner_span])
      return out_span
    }
  }
}
</script>

<style lang="scss" scoped>
/* #check-Areas .levelname span {
        width: 70px;
        display: block;
        line-height: 28px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    } */
</style>
