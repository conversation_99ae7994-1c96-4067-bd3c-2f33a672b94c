/** * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/03/10. * 虚拟组管理 */
<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form
          ref="searchForm"
          :inline="true"
          :model="table.searchForm"
          @submit.native.prevent
        >
          <el-row>
            <el-col :span="8" class="el-collapse-90">
              <el-form-item prop="groupName" label="虚拟组名称：">
                <el-input v-model.trim="table.searchForm.groupName"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="userName" label="成员名称：">
                <el-input v-model.trim="table.searchForm.userName"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="query-area-btn-css">
                <asp-btn-solid
                  v-hasAuth="doAuth({ btnCode: 'wb_130102' })"
                  name="查询"
                  icon="el-icon-search"
                  @click="search()"
                ></asp-btn-solid>
                <asp-btn-hollow
                  icon="el-icon-refresh"
                  name="重置"
                  @click="reset()"
                ></asp-btn-hollow>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <asp-table
        ref="table"
        :url="table.url"
        :param="table.searchForm"
        :prefix="table.prefix"
        type=""
      >
        <template slot="header">
          <asp-btn-solid
            v-hasAuth="doAuth({ btnCode: 'wb_130103' })"
            name="新增"
            icon="el-icon-plus"
            @click="addVirtualGroup"
          ></asp-btn-solid>
          <asp-btn-hollow
            v-hasAuth="doAuth({ btnCode: 'wb_130105' })"
            name="导出"
            icon="el-icon-download"
            @click="exportList"
          ></asp-btn-hollow>
        </template>
        <asp-table-column
          prop="groupName"
          min-width="200"
          show-overflow-tooltip
          label="虚拟组名称"
          sort-key="groupName"
        >
        </asp-table-column>
        <asp-table-column
          prop="groupDesc"
          min-width="200"
          show-overflow-tooltip
          label="虚拟组简述"
        >
        </asp-table-column>
        <asp-table-column
          :time-format="true"
          prop="updateDate"
          label="更新时间"
        >
        </asp-table-column>
        <asp-table-column
          :width="this.$aspFontSize.asp_ColButtonSize([2, 2, 2])"
          label="操作"
        >
          <template slot-scope="{ scope }">
            <asp-btn-text name="查看" @click="detailView(scope)"></asp-btn-text>
            <asp-btn-text
              v-hasAuth="doAuth({ btnCode: 'wb_130106' })"
              name="修改"
              @click="editVirtualGroup(scope)"
            ></asp-btn-text>
            <asp-btn-text
              v-hasAuth="doAuth({ btnCode: 'wb_130104' })"
              name="删除"
              @click="delVirtualGroup(scope)"
            ></asp-btn-text>
          </template>
        </asp-table-column>
      </asp-table>
    </div>
    <!-- 虚拟组详情 -->
    <detailVirtualGroup
      ref="detailVirtualGroup"
      :dialog-param="detailModelParam"
    ></detailVirtualGroup>
  </div>
</template>

<script>
// import { mapState } from 'vuex'
import detailVirtualGroup from './detailVirtualGroup'
export default {
  name: 'VirtualGroup',
  components: { detailVirtualGroup },
  data() {
    return {
      domainList: [],
      dynamicComponentParams: {},
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/virtual/listPageVirtualGroup',
        searchForm: {
          order: 'desc', // 顺序/倒序排列
          sortName: 'virtualGroup.group_name', // 排序名称
          groupName: '', // 虚拟组名称
          userName: '' // 成员姓名
        }
      },
      detailModelParam: {
        title: '虚拟组详情',
        modelVisible: false,
        groupName: '',
        groupDesc: '',
        domainList: []
      }
    }
  },
  computed: {
    dictDomain: function() {
      const res = {}
      if (this.domainList.length === 0) return res
      this.domainList.map(item => {
        res[item.code] = item.name
      })
      return res
    }
  },
  mounted() {
    this.getTypes()
  },
  methods: {
    // 按钮权限
    doAuth(opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    search() {
      this.$refs.table.asp_search()
    },
    reset() {
      this.$refs.table.asp_reset()
    },
    getTypes() {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    // 导出excel
    exportList() {
      const listParams = {
        groupName: this.table.searchForm.groupName, // 虚拟组名称
        userName: this.table.searchForm.userName // 成员姓名
      }
      const param = Object.keys(listParams)
        .map(key => {
          return key + '=' + listParams[key]
        })
        .join('&')
      let url =
        this.$apiConfig.managerPathPrefix +
        '/virtual/exportVirtualGroup?' +
        param
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    // 新增虚拟组
    addVirtualGroup() {
      const params = {
        row: {
          id: '',
          groupName: '',
          groupDesc: ''
        },
        oper: 'add'
      }
      this.$router.push({
        path: '/system/virtualGroupEdit',
        query: params
      })
    },
    // 修改虚拟组
    editVirtualGroup(scope) {
      this.$router.push({
        path: '/system/virtualGroupEdit',
        query: { row: scope.row, oper: 'edit' }
      })
    },
    // 查看虚拟组
    detailView(scope) {
      this.detailModelParam = {
        title: '虚拟组详情',
        modelVisible: true,
        groupName: scope.row.groupName,
        groupDesc: scope.row.groupDesc,
        virtualGroupId: scope.row.id,
        domainList: this.dictDomain
      }
    },
    // 删除虚拟组
    delVirtualGroup(scope) {
      const showStr = '请确认是否删除虚拟组"' + scope.row.groupName + '"？'
      this.$aspMsgbox.confirm(this, showStr, function(arg, instance) {
        // console.log(arg, instance)
        // instance.confirmButtonLoading = true
        const url =
          this.$apiConfig.managerPathPrefix + '/virtual/deleteVirtualGroup'
        this.$aspHttps
          .asp_PostForm(
            url,
            { virtualGroupId: scope.row.id }
          )
          .then(response => {
            if (this.$reponseStatus(response)) {
              this.search()
              // instance.confirmButtonLoading = false
            } else {
              // instance.confirmButtonLoading = false
            }
          })
      })
    }
  }
}
</script>
