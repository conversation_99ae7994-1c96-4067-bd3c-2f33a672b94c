/** * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/03/11 */
<template>
  <div class="webbas">
    <div class="background-css">
      <el-form ref="editVirtualGroup"
               :model="editForm"
               :rules="editVirtualGroupRules"
               label-position="left">
        <el-collapse v-model="activeName">
          <el-collapse-item name="1">
            <span slot="title"> <i class="el-icon-tickets"></i>基本信息 </span>
            <el-row>
              <el-col :span="24"
                      class="el-collapse-100">
                <el-form-item label="虚拟组名称："
                              prop="groupName">
                  <el-input v-model.trim="editForm.groupName"
                            placeholder="不超过100个字符"
                            maxlength="100"
                            auto-complete="off">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24"
                      class="el-collapse-100">
                <el-form-item label="虚拟组简述："
                              prop="groupDesc">
                  <el-input v-model="editForm.groupDesc"
                            rows="2"
                            type="textarea"
                            placeholder="不超过1000个字符"
                            maxlength="1000"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12"
                      class="el-collapse-100">
                <el-form-item prop="checkAdmin"
                              label="归属域：">
                  <el-select v-model="checkAdmin"
                             placeholder=""
                             @change="getDomain">
                    <el-option v-for="item in checkAdminData"
                               :key="item.code"
                               :label="item.name"
                               :value="item.code">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12"
                      class="el-collapse-100">
                <el-form-item prop="organizationId"
                              label="机构：">
                  <el-select v-model="organizationId"
                             placeholder=""
                             @change="getDepartmentTreeData">
                    <el-option v-for="item in organizationData"
                               :key="item.code"
                               :label="item.label"
                               :value="item.code">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="2">
            <span slot="title">
              <i class="el-icon-tickets"></i>添加成员信息
            </span>
            <el-row>
              <el-col :span="6">
                <div class="overflow-div-css">
                  <el-tree id="tree-change"
                           ref="treeLeft"
                           :data="treeData"
                           :props="defaultProps"
                           :default-checked-keys="[treeShowId]"
                           :default-expanded-keys="[treeShowId]"
                           :expand-on-click-node="false"
                           :check-strictly="true"
                           :render-content="renderTreeFunc"
                           node-key="id"
                           default-expand-all
                           highlight-current
                           @node-click="handleClickNode">
                  </el-tree>
                </div>
              </el-col>
              <el-col :span="18">
                <el-transfer v-model="editForm.userIds"
                             :data="baseData"
                             :render-content="renderFunc"
                             :titles="['待选', '已选']"
                             :button-texts="['删除', '添加']"
                             filterable
                             filter-placeholder="请输入关键字" />
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <div class="center_button">
        <asp-btn-solid :loading="submitStatus"
                       name="提交"
                       icon="el-icon-check"
                       @click="editVirtualGroupSubmit('editVirtualGroup')"></asp-btn-solid>
        <asp-btn-hollow icon="el-icon-caret-left"
                        name="返回"
                        @click="back()"></asp-btn-hollow>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EditVirtualGroup',
  components: {},
  data () {
    return {
      editForm: {
        id: '',
        groupName: '',
        groupDesc: '',
        checkedList: [],
        userIds: []
      },
      searchForm: {
        order: 'asc', // 顺序/倒序排列
        sortName: 'wbUser.user_name' // 排序名称【默认用户名顺序排列】
      },
      editVirtualGroupStatus: '',
      submitStatus: false,
      treeShowId: '', // 选中节点Id
      checkAdmin: '', // 选择域
      checkAdminData: [], // 发布对象 选择域元数据
      organizationId: '', // 机构
      organizationData: [],
      emptyText: '加载中··',
      treeData: [], // 机构树
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      baseData: [],
      renderFunc (h, option) {
        return <span>{option.label}</span>
      },
      activeName: ['1', '2']
    }
  },
  computed: {
    editVirtualGroupRules: function () {
      const rules = {
        groupName: [
          { required: true, message: '该字段不能为空', trigger: 'blur' },
          { max: 100, message: '输入不能超过100个字符', trigger: 'blur' },
          { validator: this.$main_tools.checkSpecial, trigger: 'blur' }
        ],
        groupDesc: [
          { max: 1000, message: '输入不能超过1000个字符', trigger: 'blur' },
          { validator: this.$main_tools.checkSpecial, trigger: 'blur' }
        ]
      }
      return rules
    }
  },
  watch: {},
  created () { },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      const row = this.$route.query.row
      this.editForm.id = row.id
      this.editForm.groupName = row.groupName
      this.editForm.groupDesc = row.groupDesc
      this.editVirtualGroupStatus = this.$route.query.oper
      this.getTypes()
      if (this.editVirtualGroupStatus === 'edit') {
        this.getVirtualGroupUserIds()
      }
    },
    renderTreeFunc (h, { node, data, store }) {
      // console.log(data, store)
      return (
        <el-tooltip
          content={node.label}
          class="item"
          effect="dark"
          placement="right"
        >
          <span class="singleRow">{node.label}</span>
        </el-tooltip>
      )
    },
    getTypes () {
      const domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
      const arr = []
      domainList.forEach(item => {
        arr.push({ code: item.code, name: item.name })
      })
      this.checkAdminData = arr
      this.checkAdmin = domainList[0].code
      this.organizationList()
    },
    editVirtualGroupSubmit (formName) {
      // console.log(formName)
      this.$refs.editVirtualGroup.validate(valid => {
        if (valid) {
          const param = {
            groupName: this.editForm.groupName,
            groupDesc: this.editForm.groupDesc,
            userIds: this.editForm.userIds
          }
          let updateUrl = this.$apiConfig.managerPathPrefix
          let messageText = ''
          if (this.editVirtualGroupStatus === 'add') {
            updateUrl += '/virtual/insertVirtualGroup'
            messageText = '新增'
          } else {
            updateUrl += '/virtual/updateVirtualGroup'
            messageText = '修改'
            param.id = this.editForm.id
          }
          this.confirmSubmit(
            updateUrl,
            messageText,
            this.editForm.groupName,
            param
          )
        }
      })
    },
    confirmSubmit (updateUrl, messageText, groupName, param) {
      this.$confirm(
        '确认要' + messageText + '虚拟组"' + groupName + '"？',
        '提示',
        {
          closeOnClickModal: false,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              // 请求体
              this.$aspHttps.asp_Post(updateUrl, param).then(response => {
                if (this.$reponseStatus(response)) {
                  instance.confirmButtonLoading = false
                  this.$message.success(messageText + '成功!')
                  this.$router.push('/system/virtualGroup')
                  done() // 回调
                } else {
                  instance.confirmButtonLoading = false
                  done()
                }
              })
            }
            if (action === 'cancel') {
              if (instance.confirmButtonLoading === true) {
                this.$message({ type: 'warning', message: '请求中•••' })
              } else {
                done()
              }
            }
          }
        }
      )
        .then(result => {
          if (result) return false
        })
        .catch(() => {
          this.$message({ type: 'info', message: '已取消' + messageText })
        })
    },
    /**
     * 树形菜单选中节点
     * @param data 该节点对应的对象
     * @param node 节点组件本身
     */
    handleClickNode (data, node) {
      let organizationIdTmp = ''
      let departmentIdTmp = ''
      // 穿梭框中被选中的人员信息保留在baseData中
      let baseData_tmp = []
      const userIds_tmp = this.editForm.userIds
      if (userIds_tmp.length > 0) {
        baseData_tmp = this.baseData.filter(function (item) {
          return userIds_tmp.indexOf(item.key) > -1
        })
      }
      this.baseData = baseData_tmp
      // 出现在机构列表中的id是机构id
      const b = this.organizationData.some(function (item, index, array) {
        // console.log(index, array)
        return item.id === node.key
      })
      if (b) {
        organizationIdTmp = node.key
        departmentIdTmp = ''
      } else {
        organizationIdTmp = ''
        departmentIdTmp = node.key
      }
      const param = {
        sortName: this.searchForm.sortName,
        order: this.searchForm.order,
        departmentId: departmentIdTmp,
        organizationId: organizationIdTmp,
        domain: this.editForm.checkAdmin
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix +
          '/user/queryOrganizationUserListForCombo',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            const arrData = response.data
            for (let i = 0; i < arrData.length; i++) {
              const item = arrData[i]
              let b = true
              if (this.baseData.length > 0) {
                b = !this.baseData.some(function (it, index, array) {
                  // console.log(index, array)
                  return item.id === it.key
                })
              }
              if (b) {
                this.baseData.push({
                  key: item.id,
                  label:
                    item.realName +
                    ',' +
                    (b ? item.organizationName : item.departmentName)
                })
              }
            }
          }
        })
    },
    back () {
      this.$router.go(-1)
    },
    // 归属域下拉change事件
    getDomain () {
      this.organizationList()
    },
    // 机构选择下拉change事件
    getDepartmentTreeData () {
      this.queryDeptTreeData()
    },
    // 获取组织机构
    organizationList () {
      const param = {
        domain: this.checkAdmin
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/organization/listForCombo',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            const arrData = response.data
            const arr = []
            for (let i = 0; i < arrData.length; i++) {
              const item = arrData[i]
              arr.push({
                id: item.id,
                code: item.id,
                label: item.name
              })
            }
            this.organizationData = arr
            // 重新加载机构列表后,清除已选结构
            this.organizationId = ''
          }
        })
    },
    // 修改时,获取虚拟组已有的用户列表
    getVirtualGroupUserIds () {
      // let param = {
      //   virtualGroupId: this.editForm.id
      // };
      this.$aspHttps
        .asp_PostForm(
          this.$apiConfig.managerPathPrefix + '/virtual/listVirtualGroupUser',
          { virtualGroupId: this.editForm.id }
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            const arrData = response.data
            const arr = []
            const baseDataTmp = []
            for (let i = 0; i < arrData.length; i++) {
              const item = arrData[i]
              let t = ''
              if (item.departmentName) {
                t = ',' + item.departmentName
              } else {
                t = ',' + item.organizationName
              }
              arr.push(item.userId)
              baseDataTmp.push({
                key: item.userId,
                label: item.realName + t
              })
            }
            this.editForm.userIds = arr
            this.baseData = baseDataTmp
          }
        })
    },
    // 加载机构下的部门树
    queryDeptTreeData () {
      // 先清空tree数据
      const organizationDataTmp = []
      this.treeData = []
      if (this.organizationId && this.organizationData) {
        const t = this.organizationData.find(
          item => item.code === this.organizationId
        )
        organizationDataTmp.push(t)
      }

      for (let index = 0; index < organizationDataTmp.length; index++) {
        const items = organizationDataTmp[index]
        this.$aspHttps
          .asp_PostForm(
            this.$apiConfig.managerPathPrefix + '/department/listDepartmentTree',
            { id: items.id }
          )
          .then(response => {
            if (this.$reponseStatus(response)) {
              if (response.data) {
                const arrData = response.data
                const arr = []
                for (let i = 0; i < arrData.length; i++) {
                  const item1 = arrData[i]
                  arr.push({
                    id: item1.id,
                    label: item1.text,
                    layer: '1',
                    children: []
                  })

                  if (item1.children) {
                    for (let j = 0; j < item1.children.length; j++) {
                      const item2 = item1.children[j]
                      arr[i].children.push({
                        id: item2.id,
                        label: item2.text,
                        upId: item2.parentId,
                        layer: '2',
                        children: []
                      })

                      if (item2.children) {
                        for (let k = 0; k < item2.children.length; k++) {
                          const item3 = item2.children[k]
                          arr[i].children[j].children.push({
                            id: item3.id,
                            label: item3.text,
                            upId: item3.parentId,
                            layer: '3',
                            children: []
                          })

                          if (item3.children) {
                            for (let l = 0; l < item3.children.length; l++) {
                              const item4 = item3.children[l]
                              arr[i].children[j].children[k].children.push({
                                id: item4.id,
                                label: item4.text,
                                upId: item4.parentId,
                                layer: '4',
                                children: []
                              })

                              if (item4.children) {
                                for (
                                  let m = 0;
                                  m < item4.children.length;
                                  m++
                                ) {
                                  const item5 = item4.children[m]
                                  arr[i].children[j].children[k].children.push({
                                    id: item5.id,
                                    label: item5.text,
                                    upId: item5.parentId,
                                    layer: '5',
                                    children: []
                                  })
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
                items.children = arr
                this.treeData.push(items)
              }
            }
          })
      }
    }
  }
}
</script>
