/** * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/03/10 * 虚拟组详情页面 */
<template>
  <asp-dialog
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    title="详情信息"
    width="70%"
  >
    <template>
      <el-form ref="vgDetail" :model="table" :inline="true">
        <el-row>
          <el-col :span="24">
            <el-form-item label="虚拟组名称：">{{
              dialogParam.groupName
            }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="虚拟组简述：">{{
              dialogParam.groupDesc
            }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="虚拟组成员：">
              <asp-table
                ref="table"
                :url="table.url"
                :param="table.searchForm"
                :prefix="table.prefix"
                type=""
              >
                <asp-table-column
                  prop="realName"
                  label="成员姓名"
                  min-width="60px"
                >
                </asp-table-column>
                <asp-table-column
                  :width="this.$aspFontSize.asp_ColFontSize(2)"
                  prop="sex"
                  label="性别"
                >
                  <template slot-scope="{ scope }">
                    {{ getDictSex(scope.row.sex) }}
                  </template>
                </asp-table-column>
                <asp-table-column
                  :width="this.$aspFontSize.asp_ColFontSize(11)"
                  prop="mobile"
                  label="手机"
                >
                </asp-table-column>
                <asp-table-column prop="email" label="邮箱" min-width="100px">
                </asp-table-column>
                <asp-table-column
                  :width="this.$aspFontSize.asp_ColFontSize(5)"
                  prop="domain"
                  label="归属域"
                >
                  <template slot-scope="{ scope }">
                    {{ getDictDomain(scope.row.domain) }}
                  </template>
                </asp-table-column>
                <asp-table-column
                  prop="organizationName"
                  label="归属机构"
                  min-width="105px"
                >
                </asp-table-column>
                <asp-table-column
                  prop="departmentName"
                  label="归属部门"
                  min-width="105px"
                >
                </asp-table-column>
              </asp-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-solid
        name="关闭"
        icon="el-icon-close"
        @click="cancel()"
      ></asp-btn-solid>
    </template>
  </asp-dialog>
</template>

<script>
export default {
  name: 'DetailVirtualGroup',
  components: {},
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/virtual/listPageVirtualGroupUser',
        searchForm: {
          order: 'asc', // 顺序/倒序排列
          sortName: 'name', // 排序名称
          virtualGroupId: ''
        }
      }
    }
  },
  computed: {},
  watch: {
    'dialogParam.modelVisible'(val) {
      if (val) {
        this.table.searchForm.virtualGroupId = this.dialogParam.virtualGroupId
      } else {
        this.$refs.vgDetail.resetFields()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    search() {
      this.$refs.table.asp_search()
    },
    reset() {
      this.$refs.table.asp_reset()
    },
    cancel() {
      this.dialogParam.modelVisible = false
    },
    getDictSex(val) {
      const dic = { 1: '男', 2: '女' }
      return dic[val]
    },
    getDictDomain(domain) {
      if (this.dialogParam.domainList[domain]) {
        return this.dialogParam.domainList[domain]
      }
      return domain
    }
  }
}
</script>
