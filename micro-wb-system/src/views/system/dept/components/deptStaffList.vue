<!--
机构详情组件
-->
<template>
  <div>
    <section class="list-area-css">
      <el-form :inline="true"
               :model="table.searchForm"
               class="list-top-btn"
               @submit.native.prevent>
        <el-row>
          <el-col :span="14"
                  class="el-collapse-98">
            <el-button v-hasAuth="doAuth({ btnCode: 'wb_090120' })"
                       v-if="addAccountFlag"
                       icon="el-icon-plus"
                       size="small"
                       type="primary"
                       class="solid-with-icon-btn"
                       @click="handleAddStaff">新增
            </el-button>
            <el-button v-hasAuth="doAuth({ btnCode: 'wb_090108' })"
                       icon="iconfont el-icon-addpeople mini-font"
                       size="small"
                       type="primary"
                       class="hollow-no-icon-btn-white"
                       @click="handleAuthorizationStaff">给成员分配角色
            </el-button>
          </el-col>
          <el-col :span="10"
                  class="el-collapse-98">
            <el-form-item label="真实姓名："
                          class="query-area-btn-css">
              <el-input v-model.trim="table.searchForm.realName"
                        clearable>
                <el-button slot="append"
                           icon="el-icon-search"
                           @click="queryStaffList"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="list-table-css">
        <asp-table ref="staffTable"
                   :url="table.url"
                   :param="table.searchForm"
                   :prefix="table.prefix"
                   :index-switch="false"
                   :preLoading="false"
                   type=""
                   stripe
                   @select="tableSelect"
                   @select-all="tableSelectAll">
          <asp-table-column type="selection"
                            width="55"
                            align="center"></asp-table-column>
          <asp-table-column prop="userName"
                            width="85"
                            label="登录名"
                            sort-key="userName">
            <template slot-scope="{ scope }">
              <template v-if="isGoStaff">
                <el-button type="text"
                           @click="handleGoStaff(scope.row)">{{ scope.row.userName }}</el-button>
              </template>
              <template v-else>{{ scope.row.userName }}</template>
            </template>
          </asp-table-column>
          <asp-table-column prop="realName"
                            label="真实姓名"
                            width="105"
                            sort-key="realName">
          </asp-table-column>
          <asp-table-column prop="mobile"
                            label="手机"
                            width="110"
                            sort-key="mobile">
          </asp-table-column>
          <asp-table-column prop="email"
                            label="邮箱"
                            min-width="80">
          </asp-table-column>
          <asp-table-column prop="sex"
                            width="80"
                            label="性别">
            <template slot-scope="{ scope }">{{ formataSexLabel(scope.row.sex) }}</template>
          </asp-table-column>
          <asp-table-column label="状态"
                            width="95">
            <template slot-scope="{ scope }">
              <template v-if="scope.row.status === 'NORMAL'">
                <el-tag type="success">正常</el-tag>
              </template>
              <template v-else-if="scope.row.status === 'INACTIVE'">
                <el-tag type="warning">禁用</el-tag>
              </template>
              <template v-else-if="scope.row.status === 'PASSWORD_EXPIRED'">
                <el-tag type="primary">密码过期</el-tag>
              </template>
              <template v-else-if="scope.row.status === 'EXPIRED'">
                <el-tag type="danger">账号过期</el-tag>
              </template>
              <template v-else-if="scope.row.status === 'LOCKED'">
                <el-tag type="danger">锁定</el-tag>
              </template>
            </template>
          </asp-table-column>
        </asp-table>
      </div>
    </section>
    <!-- 当前机构添加用户 -->
    <editStaff :dialog-param="addModelParam"
               @search="queryStaffList"></editStaff>
    <!-- 当前机构用户授权 -->
    <authorizationStaff :dialog-param="authorizationModelParam"
                        @updateAuthorStaff="updateAuthorList"></authorizationStaff>

  </div>
</template>

<script>
import editStaff from '../../staff/editStaff'
import authorizationStaff from '../authorizationStaff'
const VAR_INIT_STR = ''
export default {
  name: 'deptStaffList',
  components: {
    editStaff,
    authorizationStaff
  },
  props: {
    roleList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/userOrganization/queryOrganizationUserList',
        searchForm: {
          departmentId: '',
          organizationId: '',
          domain: '', // 当前机构所属域
          realName: '' // 真实姓名
        }
      },
      currentNodeData: {}, // 当前节点对象
      orgDeptDetail: {}, // 当前机构/部门节点信息
      addAccountFlag: true,
      isGoStaff: false, // 登录名是否展示为链接
      organizationName: '', // 机构名称
      organizationDomain: '', // 当前机构所属域
      dataDivision: '', // 机构/部门地域Id
      addModelParam: {},
      authorizationModelParam: {},
      multipleSelection: [],
      staffList: []
    }
  },
  computed: {},
  watch: {},
  created () { },
  mounted () {
    // 初始化 是否展示新增成员 按钮
    const domain = this.$aspUtils.getDomainObject(this)
    const userDomain = domain.userInfo.domain
    this.addAccountFlag = userDomain !== 'channel'
    // 初始化成员列表--不可初始化，参数不完整
    // this.queryStaffList()
  },
  methods: {
    // 按钮权限
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    // 按钮权限
    doAuthArr (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCodeArr: opt.btnCodeArr }
    },
    /** 是否展示新增成员 按钮 */
    updateAccountFlag (data = {}) {
      if (data.id) this.currentNodeData = Object.assign({}, data)
      const _t = this
      let userDomain = ['channel', 'supplier'].filter(e => {
        return _t.$main_tools.sessionStorage.getItem(e) && true
      })[0]
      userDomain = !userDomain ? 'admin' : userDomain
      this.addAccountFlag = userDomain === 'channel' ? data.level === undefined : true
    },
    /** 转换性别Name */
    formataSexLabel (sex) {
      const sexList = this.$aspUtils.getCodeValueByType(this, 'USER_SEX')
      let sexName = sex
      sexList.forEach(item => {
        if (item.code === sex) {
          sexName = item.name
        }
      })
      return sexName || '-'
    },
    /** 查询成员列表 */
    queryStaffList (data = {}) {
      if (data.id) {
        this.orgDeptDetail = Object.assign({}, data)
      }
      const { id, isOrg } = this.orgDeptDetail
      // departmentId = organizationId 是机构，departmentId置空
      this.table.searchForm.organizationId = ''
      this.table.searchForm.departmentId = ''
      if (isOrg) {
        this.table.searchForm.organizationId = id
      } else {
        this.table.searchForm.departmentId = id
        // 其他都是部门，organizationId置空
      }
      this.$refs.staffTable.asp_search()
    },
    // 新增成员
    handleAddStaff () {
      if (!this.currentNodeData.id) {
        this.$message.warning('请点击左边一个部门再添加')
        return
      }
      const { division, domain, isOrg, id, name, organizationId, organizationName, belongOrganization } = this.currentNodeData
      this.addModelParam = {
        currentData: this.currentNodeData,
        deptDivision: division, // 部门或机构的地域Id
        title: `给【${name}】添加用户`,
        staffModelVisible: true,
        editStaff: {
          id: '',
          userName: '',
          realName: '',
          domain: domain,
          sex: '',
          mobile: '',
          telephone: '',
          email: '',
          organizationId: isOrg ? id : organizationId,
          organizationName: organizationName || belongOrganization,
          departmentId: isOrg ? '' : id,
          departmentName: isOrg ? '' : name,
          password: VAR_INIT_STR,
          pwdConfirm: VAR_INIT_STR
        },
        dataFrom: 'deptForDept'
      }
    },
    // 分配权限
    handleAuthorizationStaff () {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请勾选需要授权的用户')
        return false
      }
      const currentId = this.currentNodeData.id || this.orgDeptDetail.id
      const title = this.currentNodeData.name || this.orgDeptDetail.organizationName
      this.authorizationModelParam = {
        domain: this.orgDeptDetail.domain,
        currentId: currentId,
        title: `给【${title}】内用户授权`,
        authorizationModelVisible: true,
        roleList: this.roleList,
        staffList: this.multipleSelection,
        organizationId: this.orgDeptDetail.organizationId
      }
    },
    // 复选成员信息
    tableSelect (selection, row) {
      this.tableSelectAll(selection)
    },
    tableSelectAll (selection) {
      this.multipleSelection = selection
    },
    /**
     * 用户授权后更新列表
     * @param data
     */
    updateAuthorList (id) {
      // 判断是否为用户列表过来授权完成后，返回成员列表
      if (this.$route.query.dataFrom === 'staff') {
        this.$router.back()
      } else {
        // 授权成功后清除选中项
        this.multipleSelection = []
        this.queryStaffList()
      }
    },
    // 成员名字点击
    handleGoStaff (data) {
      // TODO 可能要让成员管理那边弹出详情
      this.$router.push({
        path: '/system/staffForDept',
        query: {
          realName: data.realName,
          dataFrom: 'deptForDept'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.list-area-css {
  height: auto;
}
</style>
