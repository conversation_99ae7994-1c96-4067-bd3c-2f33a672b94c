<!--
机构详情组件
-->
<template>
  <div>
    <el-card class="box-card box-card-tree"
             shadow="hover">
      <div slot="header"
           class="clearfix">
        <span>部门列表</span>
      </div>
      <section class="tree">
        <el-tree ref="deptTree"
                 :empty-text="deptTreeCfg.emptyText"
                 :data="deptTreeCfg.deptTreeData"
                 :props="deptTreeCfg.defaultProps"
                 :default-checked-keys="[treeShowId]"
                 :expand-on-click-node="false"
                 :default-expand-all="true"
                 :render-content="renderTree"
                 class="filter-tree"
                 icon-class="el-icon-caret-right"
                 node-key="id"
                 label="text"
                 show-checkbox></el-tree>
      </section>
    </el-card>
    <!-- 新增，编辑机构 -->
    <editDept :dialog-param="detpModelParam"
              @uploadDept="queryDeptTree"></editDept>
  </div>
</template>

<script>
import editDept from '../deptTreeEdit'
import { asp_Debounce } from 'asp-smart-ui/lib/utils/common'
export default {
  name: 'DeptTree',
  components: {
    editDept
  },
  props: {
    orgDeptDetail: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      treeShowId: '', // 选中节点Id
      checkData: {}, // 选中的节点--机构管理树形部门选择的节点部门数据
      detpModelParam: {}, // 机构管理操作传递参数
      currentOrgDetail: {},
      deptTreeCfg: {
        emptyText: '加载中··',
        deptTreeData: [], // 机构管理树形节点
        defaultProps: {
          // 树形组件配置参数
          children: 'children',
          label: 'text'
        }
      }
    }
  },
  computed: {
  },
  watch: {},
  created () { },
  mounted () {
    // 初始化机构部门树
    // this.initOrganizationTree()
  },
  methods: {
    /** 初始化机构详情 */
    initOrganizationTree (data) {
      // 初始化节点
      this.checkData = data
      // 获取当前用户所属机构ID
      const domain = this.$aspUtils.getDomainObject(this)
      const id = domain.userInfo.organizationId
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/organization/get', { id }).then(response => {
        if (this.$reponseStatus(response)) {
          Object.assign(this.currentOrgDetail, response.data)
          // 机构节点是否可编辑，当机构详情没有organizationId时，默认是机构节点。或者organizationId和id相同时，也为机构节点
          const orgCanEdit = !data.organizationId || data.id === data.organizationId
          const respData = {
            children: [],
            division: response.data.division,
            expanded: false,
            domain: response.data.domain,
            id: response.data.id,
            level: 1,
            canEdit: orgCanEdit, // 是否可修改
            leaf: false,
            text: response.data.name
          }
          // 重构机构部门树数据
          this.deptTreeCfg.deptTreeData = []
          this.deptTreeCfg.deptTreeData.push(respData)
          this.queryDeptTreeData(id)
        }
      })
    },
    /**
     * queryDeptTreeData {String}
     * 树形菜单 查询数据入口
     * 页面页面所有请求，数据来源都与此方法相关
     * 这里初始化了全局数据
     **/
    queryDeptTreeData (id) {
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/department/listDepartmentTreeByUser', { id }).then(response => {
        if (this.$reponseStatus(response)) {
          // 重构机构部门树数据
          this.deptTreeCfg.deptTreeData[0].children = []
          response.data.forEach(item => {
            item.parentId = this.currentOrgDetail.id // 不中parent节点（主要是机构id）
            this.deptTreeCfg.deptTreeData[0].children.push(item)
          })
        }
      })
    },
    // 渲染树节点内容，绑定操作函数
    renderTree (h, { node, data, store }) {
      const _t = this
      const deptName = h(
        'span',
        {
          class: 'deptName',
          attrs: {
            'data-key': data.id,
            title: data.text.length > 8 ? data.text : ''
          }
          // on: { click: () => { this.handleClickNode(node, data) } }
        },
        data.text.length > 8 ? data.text.substring(0, 8) + '...' : data.text
      )
      // 修改部门按钮
      const edit = h('i', {
        class: 'el-icon-edit',
        on: { click: () => { this.operateDept('edit', data, node) } }
      })
      // 删除部门按钮
      const del = h('i', {
        class: 'el-icon-remove-outline',
        on: { click: () => { this.operateDept('delete', node, data) } }
      })
      // 新增部门按钮
      const add = h('i', {
        class: 'el-icon-circle-plus-outline',
        on: { click: () => { this.operateDept('add', data, node) } }
      })
      // 空占位符不做任何操作
      const noth = h('i', { class: 'el-icon-noth iconfont' }, '')
      const deptTreeOperate = h('span', { class: 'deptTreeOperate' }, getData(data.level))
      const className = `custom-tree-node ${currentClassName(data)}`
      return h('div', { class: className, on: { click: () => { this.handleClickNode(node, data) } } }, [deptName, deptTreeOperate])

      // data.level === undefined ? [add, edit, del] : [add, noth, noth])
      function getData (level) {
        const arrys = ['channel', 'supplier']
        let userDomain = arrys.filter(e => {
          // return _t.$aspUtils.getSStorage(_t, e) && true // 判断有问题
          return _t.$main_tools.sessionStorage.getItem(e) && true
        })[0]
        userDomain = !userDomain ? 'admin' : userDomain
        let showAdd = noth
        let showEdit = noth
        let showDel = noth
        const domain = _t.$aspUtils.getDomainObject(_t)
        const buttonRight = domain ? domain.authInfo : []
        const deptAuthInfo = (buttonRight.wb_020101 || []).concat(buttonRight.wb_090101 || [])
        if (deptAuthInfo.find(function (e) { return e === 'wb_020117' }) ||
          deptAuthInfo.find(function (e) { return e === 'wb_090117' })) {
          showAdd = add
        }
        if (deptAuthInfo.find(function (e) { return e === 'wb_020118' }) ||
          deptAuthInfo.find(function (e) { return e === 'wb_090118' })) {
          showEdit = edit
        }
        if (deptAuthInfo.find(function (e) { return e === 'wb_020119' }) ||
          deptAuthInfo.find(function (e) { return e === 'wb_090119' })) {
          showDel = del
        }
        if (data.canEdit) {
          if (userDomain === 'channel') showAdd = noth
          return level === undefined ? [showAdd, showEdit, showDel] : [showAdd, noth, noth]
        } else {
          return [noth, noth, noth]
        }
      }
      function currentClassName (data) {
        // console.log('currentClassName', data, _t.checkData)
        if (data.canEdit && data.id === _t.checkData.id) {
          return 'tree-node-can-edit'
        }
        return ''
      }
    },
    // 树形菜单选中节点
    handleClickNode (node, data) {
      // 当前节点不可修改时(data.canEdit:false)，点击无效
      if (!data.canEdit) {
        return
      }
      // 加上防抖事件--只触发最后一次点击事件
      const that = this
      const updateDept = asp_Debounce(() => {
        // 更新部门其信息--获取对应节点 用户，角色数据
        that.checkData = data
        that.$emit('updateDept', data)
      }, 500)
      updateDept()
    },
    /**
     * operateDept (type{String}, data{Object}, node{Object})
     * 新增/编辑/删除机构
     * */
    operateDept (type, data, node) {
      if (node.parent && node.parent.data && node.parent.data.id) {
        data.parentId = node.parent.data.id
        data.parentName = node.parent.data.text
      }
      const dataIsFirst = !data.parentId
      switch (type) {
        case 'add':
          this.detpModelParam = {
            dataIsFirst: dataIsFirst, // true则为机构新增一级部门
            organizationId: this.currentOrgDetail.organizationId || this.currentOrgDetail.id, // 无organizationId时id就是机构id
            domain: this.currentOrgDetail.domain,
            currentData: data,
            title: '新增部门',
            status: 'add',
            orgType: this.currentOrgDetail.type,
            orgTypeDisabled: true,
            orgDivision: this.currentOrgDetail.division,
            deptModelVisible: true
          }
          break
        case 'edit':
          this.detpModelParam = {
            dataIsFirst: dataIsFirst, // true则为一级部门
            organizationId: this.currentOrgDetail.organizationId || this.currentOrgDetail.id, // 无organizationId时id就是机构id
            domain: this.currentOrgDetail.domain,
            currentData: data,
            title: '修改部门',
            status: 'edit',
            orgType: this.currentOrgDetail.type,
            orgTypeDisabled: true,
            orgDivision: this.currentOrgDetail.division,
            deptModelVisible: true
          }
          break
        case 'delete':
          this.$confirm('是否要删除部门【' + node.text + '】？', '提示', {
            closeOnClickModal: false,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            beforeClose: (action, instance, done) => {
              if (action === 'confirm') {
                instance.confirmButtonLoading = true
                this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/department/delete', { id: node.id }).then(response => {
                  if (this.$reponseStatus(response)) {
                    this.checkData = {}
                    this.queryDeptTree(this.currentOrgDetail.organizationId || this.currentOrgDetail.id)
                    instance.confirmButtonLoading = false
                    done()
                  } else {
                    instance.confirmButtonLoading = false
                  }
                })
                // done() 回调
                // instance.confirmButtonLoading = false 初始化确认按钮状态
              }
              if (action === 'cancel') {
                if (instance.confirmButtonLoading === true) {
                  this.$message({ type: 'warning', message: '请求中•••' })
                } else {
                  done()
                }
              }
            }
          })
            .then(() => {
              this.$message({ type: 'success', message: '删除成功!' })
            })
            .catch(() => {
              this.$message({ type: 'info', message: '已取消删除' })
            })
          break
      }
    },
    queryDeptTree (id) {
      this.queryDeptTreeData(id)
      this.$emit('updateDept')
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
