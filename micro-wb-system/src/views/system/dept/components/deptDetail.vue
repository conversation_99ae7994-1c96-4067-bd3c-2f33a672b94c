<!--
机构/部门详情组件
-->
<template>
  <section class="query-area-content-css">
    <el-form ref="deptInfo"
             :model="deptInfo">
      <el-row>
        <el-col :span="12"
                class="el-collapse-120">
          <el-form-item label="机构/部门名称：">
            <span>{{ deptInfo.name }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12"
                class="el-collapse-120">
          <el-form-item label="归属域：">
            <span>{{ getDomainName(deptInfo.domain) || deptInfo.domain }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12"
                class="el-collapse-120">
          <el-form-item label="归属机构：">
            <span>{{ deptInfo.belongOrganization || '-' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12"
                class="el-collapse-120">
          <el-form-item label="机构/部门描述：">
            <span>{{ deptInfo.description }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24"
                class="el-collapse-120">
          <el-form-item label="归属区域：">
            <span>{{ deptInfo.divisionName }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24"
                class="el-collapse-120">
          <el-form-item label="机构所属角色：">
            <div>{{ formatInfoRoles }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
export default {
  name: 'DeptDetail',
  components: {},
  props: {},
  data () {
    return {
      // 机构/部门权限列表
      roleList: [],
      // 机构/部门基本信息
      deptInfo: {
        id: '',
        name: '',
        domain: '',
        organizationId: '',
        description: '',
        division: '',
        divisionName: ''
      }
    }
  },
  computed: {
    formatInfoRoles () {
      const arr = []
      this.roleList.map(item => {
        arr.push(item.name)
      })
      return arr.join(',') || '无'
    }
  },
  watch: {},
  created () { },
  mounted () { },
  methods: {
    // 重置结构详情
    resetInfo () {
      this.deptInfo = {
        id: '',
        name: '',
        domain: '',
        organizationId: '',
        description: '',
        division: '',
        divisionName: ''
      }
    },
    /** 获取机构/部门详情 */
    getDeptDetial (id = '', type = 'organization') {
      if (!id) return
      const url = type === 'organization' ? '/organization/get' : '/department/get'
      this.resetInfo()
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + url, { id }).then(response => {
        if (this.$reponseStatus(response)) {
          this.deptInfo = Object.assign({}, response.data)
          // 标记机构/部门类型
          this.deptInfo.isOrg = type === 'organization'
        }
        // 目前默认只查机构所属角色
        id = type === 'organization' ? id : this.deptInfo.organizationId
        this.queryRoleByData(id, 'organization')
      })
    },
    // 获取机构（机构）的用户及角色信息
    queryRoleByData (id = '', type = 'organization') {
      if (!id) return
      // 初始化this.roleList
      this.roleList = []
      const param = {}
      param.id = id
      let url = '/department/getDepartmentContext'
      let hander = this.$aspHttps.asp_PostForm
      if (type === 'organization') {
        url = '/organization/getOrganizationContext'
        param.domain = this.deptInfo.domain
        hander = this.$aspHttps.asp_Post
      }
      hander(this.$apiConfig.managerPathPrefix + url, param).then(response => {
        if (this.$reponseStatus(response)) {
          // 保存机构的角色列表
          const { listRoleVo, listRoleDo } = response.data
          this.roleList = listRoleVo || listRoleDo
        }
        // 同步机构/部门详情信息+所属角色
        this.$emit('updateOrgDeptDetail', { deptInfo: this.deptInfo, roleList: this.roleList })
      })
    },
    // 获取所属域
    getDomainName (code) {
      const domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
      let domainName = code
      domainList.forEach(item => {
        if (item.code === code) {
          domainName = item.name
        }
      })
      return domainName
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
