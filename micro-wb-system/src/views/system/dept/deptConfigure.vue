<!--
 * @Author: yuxuan <EMAIL>
 * @Date: 2022-07-16 19:13:18
 * @LastEditors: yuxuan
 * @LastEditTime: 2023-03-01 19:55:54
 * @Description: 机构详情/部门管理组件
-->
<template>
  <div class="webbas">
    <div class="background-css">
      <div class="list-page-content-css">
        <div v-if="showBack"
             class="query-area-content-css">
          <el-form>
            <el-row>
              <el-col>
                <el-form-item class="query-area-btn-css">
                  <asp-btn-hollow icon="el-icon-caret-left"
                                  name="返回"
                                  @click="componentBack()"></asp-btn-hollow>
                  <!-- {{ userDomain }} 不确定此代码有何用处-->
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="background-color-css dept-config">
          <el-row :gutter="8">
            <el-col :span="7">
              <deptTree ref="deptTree"
                        :orgDeptDetail="orgDeptDetail"
                        @updateDept="updateDept"></deptTree>
            </el-col>
            <el-col :span="17">
              <div class="list-page-content-css">
                <deptDetail ref="deptDetail"
                            @updateOrgDeptDetail="updateOrgDeptDetail"></deptDetail>
                <deptStaffList :roleList="roleList"
                               ref="deptStaffList"></deptStaffList>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import deptTree from './components/deptTree'
import deptDetail from './components/deptDetail'
import deptStaffList from './components/deptStaffList'
export default {
  name: 'DeptConfigure',
  components: {
    deptTree,
    deptDetail,
    deptStaffList
  },
  props: {
    dynamicComponentParams: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      userDomain: '',
      orgDeptDetail: {},
      roleList: [],
      currentUserDeptDetail: {} // 当前用户的所属机构/部门详情
    }
  },
  computed: {
    showBack () {
      // 不是【机构管理】列表进来则不展示【返回】按钮
      let isShow = false
      if (this.$route.query.dataFrom !== 'staff') {
        return isShow
      }
      if (this.userDomain === 'admin') {
        isShow = true
      } else {
        isShow = false
      }
      return isShow
    }
  },
  watch: {},
  created () {
    this.initDetial()
  },
  mounted () {
    // 当前用户的归属域
    const domain = this.$aspUtils.getDomainObject(this)
    this.userDomain = domain.userInfo.domain
  },
  methods: {
    initDetial () {
      // 初始化，默认获取当前用户的详情信息
      this.currentUserDeptDetail = {}
      this.$aspHttps.asp_Get(this.$apiConfig.managerPathPrefix + '/organization/getByCurrentUser').then(response => {
        if (this.$reponseStatus(response)) {
          Object.assign(this.currentUserDeptDetail, response.data)
          // 刷新树
          this.$refs.deptTree.initOrganizationTree(response.data)
          this.updateDept()
        }
      })
    },
    // 同步更新的机构/部门详情信息
    updateOrgDeptDetail ({ deptInfo, roleList }) {
      this.orgDeptDetail = Object.assign({}, deptInfo)
      this.roleList = JSON.parse(JSON.stringify(roleList))
      // 更新成员列表
      this.$refs.deptStaffList.queryStaffList(this.orgDeptDetail)
      // 更新成员列表--操作按钮显隐
      this.$refs.deptStaffList.updateAccountFlag(this.orgDeptDetail)
    },
    updateDept (data) {
      let type = 'organization'
      if (data) {
        type = !data.parentId ? 'organization' : 'department'
      } else {
        // 没有data说明当前节点被删除，需要通过当用户前节点更新详情
        data = this.currentUserDeptDetail
        const isOrg = !data.organizationId || data.id === data.organizationId
        type = isOrg ? 'organization' : 'department'
      }
      // 更新机构/部门详情
      this.$refs.deptDetail.getDeptDetial(data.id, type)
    },
    /** 返回 */
    componentBack () {
      this.$router.back()
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
