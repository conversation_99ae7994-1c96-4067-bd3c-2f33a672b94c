<!--机构详情组件-->
<template>
  <div class="webbas">
    <div class="background-css">
      <div class="list-page-content-css">
        <div v-if="isShow"
             class="query-area-content-css">
          <el-form>
            <el-row>
              <el-col>
                <el-form-item class="query-area-btn-css">
                  <asp-btn-hollow icon="el-icon-caret-left"
                                  name="返回"
                                  @click="componentBack()"></asp-btn-hollow>
                  <!-- {{ userDomain }} 不确定此代码有何用处-->
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="background-color-css dept-config">
          <el-row :gutter="8">
            <el-col :span="7">
              <el-card class="box-card box-card-tree"
                       shadow="hover">
                <div slot="header"
                     class="clearfix">
                  <span>部门列表</span>
                </div>
                <section class="tree">
                  <el-tree ref="deptTree"
                           :empty-text="deptTreeCfg.emptyText"
                           :data="deptTreeCfg.deptTreeData"
                           :props="deptTreeCfg.defaultProps"
                           :default-checked-keys="[treeShowId]"
                           :default-expanded-keys="[treeShowId]"
                           :expand-on-click-node="false"
                           :default-expand-all="true"
                           :render-content="renderTree"
                           class="filter-tree"
                           icon-class="el-icon-caret-right"
                           node-key="id"
                           label="text"
                           show-checkbox></el-tree>
                </section>
              </el-card>
            </el-col>
            <el-col :span="17">
              <div class="list-page-content-css">
                <section class="query-area-content-css">
                  <el-form ref="orgInfo"
                           :model="orgInfo"
                           class="label-120-css">
                    <el-row>
                      <el-row>
                        <el-col :span="12"
                                class="el-collapse-120">
                          <el-form-item label="机构/部门名称："
                                        prop="name">
                            <span v-text="orgInfo.name"></span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12"
                                class="el-collapse-120">
                          <el-form-item label="归属域："
                                        prop="name">
                            <span v-text="orgInfo.domain"></span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="12"
                                class="el-collapse-120">
                          <el-form-item label="归属机构："
                                        prop="name">
                            <span v-text="orgInfo.belongOrganization"></span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12"
                                class="el-collapse-120">
                          <el-form-item label="机构/部门描述："
                                        prop="name">
                            <span v-text="orgInfo.description"></span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="24"
                                class="el-collapse-120">
                          <el-form-item label="归属区域："
                                        prop="name">
                            <span v-text="orgInfo.divisionName"></span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="24"
                                class="el-collapse-120">
                          <el-form-item label="机构所属角色："
                                        prop="name">
                            <div>{{ role.tableData | formatInfoRole }}</div>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-row>
                  </el-form>
                </section>
                <section class="list-area-css">
                  <el-form :inline="true"
                           class="list-top-btn">
                    <el-col :span="14"
                            class="el-collapse-98">
                      <el-form-item>
                        <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_020120' })"
                                       v-if="addAccountFlag"
                                       icon="el-icon-plus"
                                       name="新增"
                                       @click="handleAddStaff">
                        </asp-btn-solid>
                        <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_020108' })"
                                       icon="iconfont el-icon-addpeople mini-font"
                                       name="给成员分配角色"
                                       @click="handleAuthorizationStaff">
                        </asp-btn-solid>
                      </el-form-item>
                    </el-col>
                    <el-col :span="10"
                            class="el-collapse-98">
                      <el-form-item label="真实姓名："
                                    prop="name">
                        <el-input v-model.trim="searchName.name"
                                  clearable>
                          <el-button slot="append"
                                     icon="el-icon-search"
                                     @click="handleSearchName"></el-button>
                        </el-input>
                      </el-form-item>
                    </el-col>
                  </el-form>
                  <div class="list-table-css">
                    <asp-table ref="staffTable"
                               :url="table.url"
                               :param="table.searchForm"
                               :prefix="table.prefix"
                               :index-switch="false"
                               :preLoading="false"
                               type=""
                               @select="tableSelect"
                               @select-all="tableSelectAll">
                      <asp-table-column type="selection"
                                        width="55"
                                        align="center"></asp-table-column>
                      <asp-table-column prop="userName"
                                        label="登录名"
                                        width="85"
                                        sort-key="userName">
                        <template slot-scope="{ scope }">
                          <template v-if="isGoStaff">
                            <el-button type="text"
                                       @click="handleGoStaff(scope.row)">
                              {{ scope.row.userName }}
                            </el-button>
                          </template>
                          <template v-else>
                            {{ scope.row.userName }}
                          </template>
                        </template>
                      </asp-table-column>
                      <asp-table-column prop="realName"
                                        label="真实姓名"
                                        width="105"
                                        sort-key="realName">
                      </asp-table-column>
                      <asp-table-column prop="mobile"
                                        label="手机"
                                        width="110"
                                        sort-key="mobile">
                      </asp-table-column>
                      <asp-table-column prop="email"
                                        label="邮箱"
                                        min-width="80">
                      </asp-table-column>
                      <asp-table-column prop="sex"
                                        width="55"
                                        label="性别">
                        <template slot-scope="{ scope }">{{
                          scope.row.sex | formatSex
                        }}</template>
                      </asp-table-column>
                      <asp-table-column label="状态"
                                        width="95">
                        <template slot-scope="{ scope }">
                          <template v-if="scope.row.status === 'NORMAL'">
                            <el-tag type="success">正常</el-tag>
                          </template>
                          <template v-else-if="scope.row.status === 'INACTIVE'">
                            <el-tag type="warning">禁用</el-tag>
                          </template>
                          <template v-else-if="scope.row.status === 'PASSWORD_EXPIRED'">
                            <el-tag type="primary">密码过期</el-tag>
                          </template>
                          <template v-else-if="scope.row.status === 'EXPIRED'">
                            <el-tag type="danger">账号过期</el-tag>
                          </template>
                          <template v-else-if="scope.row.status === 'LOCKED'">
                            <el-tag type="danger">锁定</el-tag>
                          </template>
                        </template>
                      </asp-table-column>
                    </asp-table>
                  </div>
                </section>
              </div>
            </el-col>
          </el-row>
        </div>
        <!-- 新增，编辑机构 -->
        <editDept :dialog-param="deptTreeCfg.detpModelParam"
                  @uploadDept="queryDeptTreeData"></editDept>
        <!-- 当前机构添加用户 -->
        <editStaff :dialog-param="staff.addModelParam"
                   @search="handleAddStaffDone"></editStaff>
        <!-- 当前机构用户授权 -->
        <authorizationStaff :dialog-param="staff.authorizationModelParam"
                            @updateAuthorStaff="updateAuthorList"></authorizationStaff>
      </div>
    </div>
  </div>
</template>

<script>
import editDept from './deptTreeEdit'
import editStaff from '../staff/editStaff'
import authorizationStaff from './authorizationStaff'
const VAR_INIT_STR = ''
export default {
  name: 'DeptDetailConfigure',
  components: {
    editDept,
    editStaff,
    authorizationStaff
  },
  filters: {
    formatSex: function (param) {
      if (!param) {
        return '-'
      }
      switch (param.toString()) {
        case '1':
          return '男'
        case '2':
          return '女'
        default:
          return '-'
      }
    },
    formatInfoRole: function (value) {
      if (value.length === 0) {
        return '无'
      }
      const arr = []
      value.map(item => {
        arr.push(item.name)
      })
      return arr.join(',')
    }
  },
  props: {
    dynamicComponentParams: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/user/queryOrganizationUserList',
        searchForm: {
          departmentId: '',
          organizationId: '',
          domain: '', // 当前机构所属域
          realName: ''
        }
      },
      checkData: {}, // 选中的节点--机构管理树形部门选择的节点部门数据
      addAccountFlag: true,
      userDomain: '',
      userId: '',
      isShow: true, // 是否展示返回按钮
      isGoStaff: false, // 登录名是否展示为链接
      organizationName: '', // 机构名称
      dataDivision: '', // 机构/部门地域Id
      treeShowId: '', // 选中节点Id
      deptTreeCfg: {
        emptyText: '加载中··',
        checkData: {}, // 机构管理树形部门选择的节点部门数据
        detpModelParam: {}, // 机构管理操作传递参数
        deptTreeData: [], // 机构管理树形节点
        defaultProps: {
          // 树形组件配置参数
          children: 'children',
          label: 'text'
        }
      },
      role: {
        // 角色对象
        tableLoading: false,
        tableData: []
      },
      staff: {
        tableLoading: false,
        paginationStatus: false,
        tableData: [],
        pagination: {
          page: 1,
          total: 0,
          pageSize: 10,
          selectPageSizes: [10, 20, 30, 40]
        },
        prePageSize: 10,
        addModelParam: {},
        authorizationModelParam: {},
        multipleSelection: []
      },
      searchForm: {
        order: 'asc', // 顺序/倒序排列
        sortName: 'wbUser.user_name' // 排序名称【默认用户名顺序排列】
      },
      deptText: '',
      // 机构基本信息
      orgInfo: {
        belongOrganization: '',
        description: '',
        division: '',
        divisionName: '',
        domain: '',
        id: '',
        name: '',
        parentId: '',
        parentName: '',
        type: ''
      },
      // 备份机构信息
      orgInfoCatch: {},
      // 搜索名字
      searchName: {
        name: ''
      },
      // 机构权限列表
      orgRoleList: []
    }
  },
  computed: {
    organizationId () {
      return this.$route.query.orgId
    }
  },
  watch: {
    userDomain () {
      if (this.userDomain === 'admin') {
        this.isShow = true
      } else {
        this.isShow = false
      }
    }
  },
  created () {
    const domain = this.$aspUtils.getDomainObject(this)
    this.userDomain = domain.userInfo.domain
    this.defauOrganizationName = domain.userInfo.organizationName
    this.userId = domain.userInfo.id
    this.addAccountFlag = this.userDomain !== 'channel'
    if (this.userDomain !== 'admin') {
      this.getUserDetail(this.userId)
    } else {
      this.treeShowId = this.$route.query.deptId
      if (this.treeShowId === '') {
        this.treeShowId = this.organizationId
      }
      this.getOrganization(this.organizationId)
    }
    // 成员详情跳转：wb_030101：系统成员管理staff==》dept，wb_100101：机构成员管理staffForDept==》deptForDept
    // 机构管理列表进来且拥有【系统成员管理】查询权限时，可点击【登录名】链接跳转至系统成员管理列表
    if (this.$route.query.dataFrom === 'dept' && domain.authInfo.wb_030101) {
      this.isGoStaff = true
    }
  },
  methods: {
    // 按钮权限
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    doAuthArr (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCodeArr: opt.btnCodeArr }
    },
    // 获取所属域
    getDomainName (code) {
      const domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
      domainList.forEach(item => {
        if (item.code === code) {
          this.orgInfo.domain = item.name
        }
      })
    },
    // 设置机构信息
    setOrgInfo (data) {
      this.orgInfo.type = data.type
      this.orgInfo.belongOrganization = data.belongOrganization
      this.orgInfo.description = data.description
      this.orgInfo.division = data.division
      this.orgInfo.divisionName = data.divisionName
      // this.orgInfo.domain = data.domain
      this.orgInfo.name = data.name
      this.getDomainName(data.domain)
    },
    /**
     * 通过用户ID查询用户详情，获取机构ID
     * @param userId 用户ID
     */
    getUserDetail (userId) {
      const urlGet = this.$apiConfig.managerPathPrefix + '/user/get'
      this.$aspHttps.asp_PostForm(urlGet, { id: userId }).then(response => {
        if (this.$reponseStatus(response)) {
          // const id = response.data.organizationId;
          // this.dynamicComponentParams = {
          //     dataFrom: 'dept',
          //     orgId: id, // 机构Id
          //     deptId: '',
          //     title: '机构详情'
          // }
          this.treeShowId = this.$route.query.deptId
          if (this.treeShowId === '') {
            this.treeShowId = this.organizationId
          }
          this.getOrganization(this.organizationId)
        }
      })
    },
    /**
     * 查询机构详情数据
     */
    getOrganization (id) {
      // Object.assign(this.$data, this.$options.data())
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/organization/get', { id: id }).then(response => {
        if (this.$reponseStatus(response)) {
          const orgData = response.data
          this.table.searchForm.domain = orgData.domain
          const respData = {
            children: [],
            division: orgData.division,
            expanded: false,
            domain: orgData.domain,
            id: orgData.id,
            level: 1,
            leaf: false,
            text: orgData.name
          }
          this.dataDivision = orgData.division
          this.organizationType = response
          this.organizationName = orgData.name
          // 更新树的信息
          this.deptTreeCfg.deptTreeData = []
          this.deptTreeCfg.deptTreeData.push(respData)
          this.queryDeptTreeData(id)
          // 更新机构信息
          this.setOrgInfo(orgData)
          this.orgInfoCatch = { ...orgData }
          // 更新机构信息里的角色信息
          this.queryRoleByData(this.deptTreeCfg.deptTreeData[0])
          // 手动点第一个节点
          this.handleClickNode({ level: 1 }, respData)
        }
      })
    },
    /**
     * queryDeptTreeData {String}
     * 树形菜单 查询数据入口
     * 页面页面所有请求，数据来源都与此方法相关
     * 这里初始化了全局数据
     **/
    queryDeptTreeData (id) {
      if (!id) {
        id = this.$route.query.orgId
      }
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/department/listDepartmentTree', { id: id }).then(response => {
        if (this.$reponseStatus(response)) {
          // 重构json数据
          this.deptTreeCfg.deptTreeData[0].children = response.data
          this.$nextTick(() => {
            const deptName = document.querySelectorAll('.deptName')
            for (let i = 0; i < deptName.length; i++) {
              if (
                Object.is(
                  deptName[i].getAttribute('data-key').toString(),
                  this.treeShowId
                )
              ) {
                deptName[i].click()
              }
            }
          })
        }
      })
    },
    /**
     * 查询角色列表数据
     * param data
     */
    queryRoleDataById (id) {
      this.role.tableLoading = true
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/department/getDepartmentContext', { id: id }).then(response => {
        if (this.$reponseStatus(response)) {
          this.role.tableData = response.data.listRoleDo
          // 获取当前部门地域信息
          this.dataDivision = response.data.division
          this.table.searchForm.domain = response.data.domain
          this.role.tableLoading = false
        }
      })
    },
    // 查询用户列表数据
    // 兼容了查机构的情况
    queryStaffList (departmentId = '', realName) {
      this.table.searchForm.organizationId = this.organizationId
      this.table.searchForm.departmentId = departmentId
      // departmentId = organizationId 是机构，departmentId置空
      if (departmentId === this.organizationId) {
        this.table.searchForm.departmentId = ''
      } else {
        // 其他是部门，organizationId置空
        this.table.searchForm.organizationId = ''
      }
      if (realName) {
        this.table.searchForm.realName = realName
      } else {
        this.table.searchForm.realName = ''
      }
      this.$refs.staffTable.asp_search()
    },
    // 获取机构（机构）的用户及角色信息
    queryRoleByData (data) {
      this.role.tableLoading = false
      const param = {
        id: data.id,
        domain: data.domain
      }
      if (data.realName) {
        param.realName = data.realName
      }
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/organization/getOrganizationContext', param).then(response => {
        if (this.$reponseStatus(response)) {
          this.role.tableData = response.data.listRoleVo
          // 保存机构的角色列表
          this.orgRoleList = response.data.listRoleVo
          // 获取当前机构的地域信息---设置当前机构节点的数据
          this.dataDivision = data.division
          this.role.tableLoading = false
        } else {
          this.role.tableLoading = false
        }
      })
    },
    // 获取部门详情，id部门id
    getDepartmentInfo (id) {
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/department/get', { id: id }).then(response => {
        if (this.$reponseStatus(response)) {
          // 获取当前部门详情
          response.data.belongOrganization = this.orgInfoCatch.name
          // TODO 后台加字段
          const areaData = this.$main_tools.areas.getUpJsonById(response.data.division, '')
          response.data.divisionName = areaData.text
          this.setOrgInfo(response.data)
        } else {
          // this.$message.success(response.message)
        }
      })
    },
    // 渲染树节点内容，绑定操作函数
    renderTree (h, { node, data, store }) {
      const _t = this
      const deptName = h(
        'span',
        {
          class: 'deptName',
          attrs: {
            'data-key': data.id,
            title: data.text.length > 8 ? data.text : ''
          }
          // 移动到div中
          // on: {
          //   click: () => {
          //     this.handleClickNode(node, data)
          //   }
          // }
        },
        data.text.length > 8 ? data.text.substring(0, 8) + '...' : data.text
      )
      // 修改部门按钮
      const edit = h('i', {
        class: 'el-icon-edit',
        on: {
          click: () => {
            this.operateDept('edit', data, node)
          }
        }
      })
      // 删除部门按钮
      const del = h('i', {
        class: 'el-icon-remove-outline',
        on: {
          click: () => {
            this.operateDept('delete', node, data)
          }
        }
      })
      // 新增部门按钮
      const add = h('i', {
        class: 'el-icon-circle-plus-outline',
        on: {
          click: () => {
            this.operateDept('add', data, node)
          }
        }
      })
      // 空占位符不做任何操作
      const noth = h('i', { class: 'el-icon-noth iconfont' }, '')
      const deptTreeOperate = h('span', { class: 'deptTreeOperate' }, getData(data.level))
      const className = `custom-tree-node ${currentClassName(data)}`
      return h('div',
        {
          class: className,
          on: {
            click: () => {
              this.handleClickNode(node, data)
            }
          }
        },
        [deptName, deptTreeOperate])
      // data.level === undefined ? [add, edit, del] : [add, noth, noth])
      function getData (level) {
        const arrys = ['channel', 'supplier']
        let userDomain = arrys.filter(e => {
          // return _t.$aspUtils.getSStorage(_t, e) && true // 判断有问题
          return _t.$main_tools.sessionStorage.getItem(e) && true
        })[0]
        userDomain = !userDomain ? 'admin' : userDomain
        let showAdd = noth
        let showEdit = noth
        let showDel = noth
        const domain = _t.$aspUtils.getDomainObject(_t)
        const buttonRight = domain ? domain.authInfo : []
        const deptAuthInfo = (buttonRight.wb_020101 || []).concat(buttonRight.wb_090101 || [])
        if (deptAuthInfo.find(function (e) { return e === 'wb_020117' }) ||
          deptAuthInfo.find(function (e) { return e === 'wb_090117' })) {
          showAdd = add
        }
        if (deptAuthInfo.find(function (e) { return e === 'wb_020118' }) ||
          deptAuthInfo.find(function (e) { return e === 'wb_090118' })) {
          showEdit = edit
        }
        if (deptAuthInfo.find(function (e) { return e === 'wb_020119' }) ||
          deptAuthInfo.find(function (e) { return e === 'wb_090119' })) {
          showDel = del
        }
        if (userDomain === 'channel') {
          return level === undefined ? [noth, showEdit, showDel] : [noth, noth, noth]
        } else {
          return level === undefined ? [showAdd, showEdit, showDel] : [showAdd, noth, noth]
        }
      }
      function currentClassName (data) {
        if (data.id === _t.checkData.id) {
          return 'tree-node-can-edit'
        }
        return ''
      }
    },
    // 树形菜单选中节点
    handleClickNode (node, data) {
      const _t = this
      _t.checkData = data
      this.deptTreeCfg.checkData = data // 获取对应节点 用户，角色数据
      const arrys = ['channel', 'supplier']
      let userDomain = arrys.filter(e => {
        return _t.$aspUtils.getSStorage(_t, e) && true
      })[0]
      userDomain = !userDomain ? 'admin' : userDomain
      this.addAccountFlag = userDomain === 'channel' ? data.level === undefined : true
      if (data.text.length !== undefined) {
        if (data.text.length > 20) {
          this.deptText = data.text.substring(0, 20) + '...'
          // document.getElementById('roleText').setAttribute('title', data.text)
          // document.getElementById('userText').setAttribute('title', data.text)
        } else {
          this.deptText = data.text
        }
      }
      // 处理机构
      if (Number(node.level) === 1) {
        this.setOrgInfo(this.orgInfoCatch)
        // 查询机构对应的角色列表和用户列表
        this.queryRoleByData(data)
      } else {
        // 处理部门
        // 获取信息
        this.getDepartmentInfo(data.id)
        // 获取角色信息
        // this.queryRoleDataById(data.id)
      }
      this.queryStaffList(data.id)
    },
    /**
     * operateDept (type{String}, data{Object}, node{Object})
     * 新增/编辑/删除机构
     * */
    operateDept (type, data, node) {
      if (node.parent && node.parent.data && node.parent.data.id) {
        data.parentId = node.parent.data.id
        data.parentName = node.parent.data.text
      }
      const dataIsFirst = !data.parentId
      switch (type) {
        case 'add':
          this.deptTreeCfg.detpModelParam = {
            dataIsFirst: dataIsFirst, // true则为机构新增一级部门
            organizationId: this.$route.query.orgId,
            domain: this.table.searchForm.domain,
            currentData: data,
            title: '新增部门',
            orgType: '',
            orgDivision: this.orgInfoCatch.division,
            deptModelVisible: true
          }
          break
        case 'edit':
          this.deptTreeCfg.detpModelParam = {
            dataIsFirst: dataIsFirst, // true则为一级部门
            organizationId: this.$route.query.orgId,
            domain: this.table.searchForm.domain,
            currentData: data,
            title: '修改部门',
            orgType: this.orgInfoCatch.type,
            orgDivision: this.orgInfoCatch.division,
            deptModelVisible: true
          }
          break
        case 'delete':
          this.$confirm('是否要删除部门【' + node.text + '】？', '提示', {
            closeOnClickModal: false,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            beforeClose: (action, instance, done) => {
              const that = this
              if (action === 'confirm') {
                instance.confirmButtonLoading = true
                // 请求体
                const isShow = this.isShow
                this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/department/delete', { id: node.id }).then(response => {
                  if (this.$reponseStatus(response)) {
                    that.getOrganization(that.organizationId)
                    instance.confirmButtonLoading = false
                    done()
                  } else {
                    instance.confirmButtonLoading = false
                  }
                  this.isShow = isShow
                })
                // done() 回调
                // instance.confirmButtonLoading = false 初始化确认按钮状态
              }
              if (action === 'cancel') {
                if (instance.confirmButtonLoading === true) {
                  this.$message({ type: 'warning', message: '请求中•••' })
                } else {
                  done()
                }
              }
            }
          }).then(() => {
            this.$message({ type: 'success', message: '删除成功!' })
          }).catch(() => {
            this.$message({ type: 'info', message: '已取消删除' })
          })
          break
      }
    },
    // 新增成员
    handleAddStaff () {
      if (!this.deptTreeCfg.checkData.id) {
        this.$message.warning('请点击左边一个部门再添加')
        return
      }
      this.staff.addModelParam = {
        currentData: this.deptTreeCfg.checkData,
        deptDivision: this.dataDivision, // 部门或机构的地域Id
        title: `给【${this.deptTreeCfg.checkData.text}】添加用户`,
        staffModelVisible: true,
        editStaff: {
          id: '',
          userName: '',
          realName: '',
          domain: this.table.searchForm.domain,
          sex: '',
          mobile: '',
          telephone: '',
          email: '',
          organizationId: this.$route.query.orgId,
          organizationName: this.organizationName,
          departmentId: this.deptTreeCfg.checkData.id,
          departmentName: this.deptTreeCfg.checkData.text,
          password: VAR_INIT_STR,
          pwdConfirm: VAR_INIT_STR
        },
        dataFrom: 'dept'
      }
    },
    // 新增成员结束回调
    handleAddStaffDone () {
      let id = this.organizationId
      if (this.deptTreeCfg.checkData.id) {
        id = this.deptTreeCfg.checkData.id
      }
      this.queryStaffList(id)
    },
    // 分配权限
    handleAuthorizationStaff () {
      if (this.staff.multipleSelection.length === 0) {
        this.$message.warning('请勾选需要授权的用户')
        return false
      }
      const currentId = this.deptTreeCfg.checkData.id ? this.deptTreeCfg.checkData.id : this.$route.query.orgId
      const title = this.deptTreeCfg.checkData.text ? this.deptTreeCfg.checkData.text : this.organizationName

      this.staff.authorizationModelParam = {
        domain: this.table.searchForm.domain,
        currentId: currentId,
        title: `给【${title}】内用户授权`,
        authorizationModelVisible: true,
        roleList: this.orgRoleList,
        staffList: this.staff.multipleSelection,
        organizationId: this.$route.query.orgId
      }
    },
    tableSelect (selection, row) {
      this.tableSelectAll(selection)
    },
    tableSelectAll (selection) {
      this.staff.multipleSelection = selection
    },
    // 行数切换
    staffSizeChange (val) {
      this.staff.pagination.pageSize = val
      this.queryStaffList(this.deptTreeCfg.checkData.id)
    },
    // 翻页
    staffCurrentChange (val) {
      this.staff.pagination.page = val
      this.queryStaffList(this.deptTreeCfg.checkData.id)
    },
    componentBack () {
      this.$router.back()
    },
    updateRoleDataList (data) {
      if (data.dataIsOrg) {
        this.queryRoleByData(data.currentData)
      } else {
        this.queryRoleDataById(data.currentData.id)
      }
    },
    /**
     * 用户授权后更新列表
     * @param data
     */
    updateAuthorList (id) {
      // 判断是否为用户列表过来授权--TODO--待后续按钮权限处理分析
      if (this.$route.query.dataFrom !== 'dept') {
        this.componentBack()
      } else {
        // 授权成功后清除选中项
        this.staff.multipleSelection = []
        this.queryStaffList(id)
      }
    },
    // 搜索真实姓名
    handleSearchName () {
      let id = this.deptTreeCfg.checkData.id
      if (!id) {
        id = this.organizationId
      }
      this.queryStaffList(id, this.searchName.name)
    },
    // 成员名字点击
    handleGoStaff (data) {
      // TODO 可能要让成员管理那边弹出详情
      this.$router.push({
        path: '/system/staff',
        query: {
          realName: data.realName,
          dataFrom: 'dept'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.list-area-css {
  height: auto;
}
</style>
