<!--新增机构、修改机构组件-->
<template>
  <div v-if="orgVisibleFlag">
    <el-dialog
      :title="dialogParam.title"
      :close-on-click-modal="false"
      :visible.sync="dialogParam.orgModelVisible"
      width="60%"
    >
      <el-form
        ref="orgForm"
        :model="orgForm"
        :rules="orgFormRules"
        :inline="true"
      >
        <el-row class="el-collapse-90">
          <el-col :span="12">
            <el-form-item label="机构名称：" prop="name">
              <el-input
                v-model.trim="orgForm.name"
                placeholder="不能超过40个字符"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构类型：" prop="type">
              <el-select
                v-model="orgForm.type"
                :disabled="isEdit"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in orgTypeList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-90">
          <el-col :span="12">
            <el-form-item label="归属域：" prop="domain">
              <el-select
                v-model="orgForm.domain"
                :disabled="status === 'edit'"
                placeholder="请选择"
                @change="changeDomain"
              >
                <el-option
                  v-for="item in domainList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构代码：" prop="code">
              <el-input
                v-model.trim="orgForm.code"
                placeholder="不能超过20个字符"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-90">
          <el-col :span="12">
            <el-form-item label="归属区域：" prop="division">
              <el-cascader
                :options="orgAreas"
                :props="props"
                v-model="checkedDivision"
                filterable
                placeholder="请输入你所在地区"
                change-on-select
                class="width-100-css"
                @change="handlerArea"
              >
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属机构：" prop="parentId">
              <el-select
                v-model="orgForm.parentId"
                :disabled="status === 'edit'"
                class="el-select"
                placeholder="请选择"
                filterable
              >
                <el-option label="根机构" value="0"></el-option>
                <el-option
                  v-for="item in organizationList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-90">
          <el-col :span="24">
            <el-form-item
              label="机构描述："
              prop="description"
              class="departmentDesc"
            >
              <el-input
                :rows="3"
                v-model="orgForm.description"
                type="textarea"
                placeholder="不能超过40个字符"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <center>
          <el-button
            icon="el-icon-close"
            class="hollow-with-icon-btn"
            @click="operateEvent('cancel')"
            >取消
          </el-button>
          <el-button
            v-loading="submitStatus"
            :disabled="submitStatus"
            icon="el-icon-check"
            class="solid-with-icon-btn"
            @click="operateEvent('save')"
            >保存
          </el-button>
        </center>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'EditOrganization',
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  component: {},
  data() {
    return {
      isEdit: false,
      orgVisibleFlag: false,
      userDomain: '', // 当前登录用所属域
      currentDomain: '', // 当前所属域
      orgForm: {
        id: '',
        name: '',
        description: '',
        domain: '',
        address: '',
        checkedRoles: [],
        roles: [],
        division: '',
        type: '',
        code: '',
        parentId: ''
      }, // 机构表单参数对象
      checkRolesProgam: {
        checkAll: false, // 新增状态checkAll和isIndeterminate都是false
        isIndeterminate: false // 部分选中状态isIndeterminate是true
      },
      // 验证
      orgFormRules: {
        name: [
          { required: true, message: '请输入机构名称', trigger: 'blur' },
          { max: 40, message: '输入不能超过40个字符', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入机构描述', trigger: 'blur' },
          { max: 40, message: '输入不能超过40个字符', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' }
        ],
        domain: [
          { required: true, message: '请选择归属域', trigger: 'change' }
        ],
        division: [
          { required: true, message: '请选择地域', trigger: 'change' }
        ],
        parentId: [
          { required: true, message: '请选择归属机构', trigger: 'change' }
        ],
        code: [{ max: 20, message: '输入不能超过20个字符', trigger: 'blur' }]
      },
      submitStatus: false,
      // 新增 修改
      status: '',
      orgAreas: [], // 地域数据参数
      props: {
        key: 'id',
        label: 'text',
        value: 'id',
        children: 'children'
      },
      parentDivision: '', // 获取上一级地域Id
      checkedDivision: [], // 选中的地域数据
      organizationList: [], // 所属机构
      domainList: [], // 所属域
      orgTypeList: [
        {
          code: '0',
          name: ' '
        }
      ] // 机构类型
    }
  },
  watch: {
    'dialogParam.orgModelVisible'(val) {
      if (val) {
        if (this.dialogParam.editOrgForm.id) {
          this.isEdit = true
        } else {
          this.isEdit = false
        }
        // 机构每次显示前都请求，因为可能有删除和新增
        this.getOrganizationList()
        this.$nextTick(() => {
          this.$refs.orgForm && this.$refs.orgForm.resetFields()
          this.checkRolesProgam.checkAll = false // 新增状态checkAll和isIndeterminate都是false
          this.checkRolesProgam.isIndeterminate = false // 部分选中状态isIndeterminate是true
          this.checkedDivision = []
          this.orgAreas = this.$aspUtils.getDivisions(this) // 获取完整路径
          if (this.dialogParam.editOrgForm.id !== '') {
            // 修改机构
            this.status = 'edit'
            // 获取机构详情
            this.getOrganizationContent(this.dialogParam.editOrgForm.id)
            // 获取可选角色信息---TODO--目前暂时只能写成这样 -- 需要时放开注释
            // this.getOrgRoles(this.dialogParam.editOrgForm.id, this.dialogParam.domain)
          } else {
            // 新增机构
            this.status = 'add'
            this.orgForm = {
              id: '',
              name: '',
              description: '',
              domain: this.userDomain, // 默认domain是admin域
              address: '',
              checkedRoles: '',
              roles: '',
              division: '',
              parentId: ''
            }
            this.currentDomain = this.orgForm.domain
            // 获取可选角色信息---TODO--目前暂时只能写成这样 -- 需要时放开注释
            // this.getOrgRoles('', this.orgForm.domain)
          }
          this.orgVisibleFlag = true
          // clearValidate
          setTimeout(() => this.$refs.orgForm.clearValidate())
        })
      }
    }
  },
  created() {
    const domain = this.$aspUtils.getDomainObject(this)
    this.userDomain = domain.userInfo.domain
  },
  mounted() {
    this.getDomainList()
    this.getOrgType()
  },
  methods: {
    /**
     * 新增/保存修改
     * @param type 操作类型
     */
    operateEvent(type) {
      this.submitStatus = true
      /* if (this.status === 'add' && type === 'save') {
                let checkUrl = this.$apiConfig.managerPathPrefix + '/organization/checkOrganizationName'
                let checkParam = {
                    division: this.orgForm.division, // 地域
                    organizationId: this.orgForm.id, // 机构Id
                    organizationName: this.orgForm.name // 机构名称
                }
                this.$aspHttps.asp_Post(checkUrl, checkParam).then((response) => {
                    if (this.$reponseStatus(response)) {
                        if (response.data) {
                            this.submit(type)
                        } else {
                            this.submitStatus = false
                            this.$message.error('机构名称+所属区域全系统唯一！')
                        }
                    }
                })
            } else {
                this.submit(type)
            } */
      this.submit(type)
    },
    /**
     * 修改归属域
     * @param val 归属域
     */
    changeDomain(val) {
      this.orgForm.parentId = ''
      this.getOrganizationList(val)
    },
    /**
     * 提交保存
     * param type 保存/取消操作
     */
    submit(type) {
      // 关闭弹框 初始化请求信息
      const resetDailog = () => {
        this.dialogParam.orgModelVisible = false
        this.orgVisibleFlag = false
        this.submitStatus = false
        this.$refs.orgForm.resetFields()
      }
      switch (type) {
        case 'save':
          this.$refs.orgForm.validate(valid => {
            if (valid) {
              const param = {
                id: this.orgForm.id, // 机构Id
                name: this.orgForm.name, // 机构名称
                description: this.orgForm.description, // 描述
                division: this.orgForm.division, // 地域
                domain: this.orgForm.domain, // 所属域
                address: this.orgForm.address, // 地址
                roles: [], // 配置角色数组
                type: this.orgForm.type, // 机构类型
                code: this.orgForm.code, // 机构代码
                parentId: this.orgForm.parentId // 归属机构
              }
              this.submitStatus = true
              // 判断是做新增提交还是修改提交
              let submitUrl = ''
              if (this.status === 'edit') {
                submitUrl =
                  this.$apiConfig.managerPathPrefix + '/organization/update'
              } else {
                submitUrl =
                  this.$apiConfig.managerPathPrefix + '/organization/insert'
              }
              this.$aspHttps.asp_Post(submitUrl, param).then(response => {
                if (this.$reponseStatus(response)) {
                  if (this.status === 'add') {
                    this.orgForm.id = response.data
                  }
                  this.$emit('search')
                  // this.operateEvent('cancel')
                  // 新增时如果没有勾选角色，则不调用修改角色的请求，修改角色信息不可修改，亦不调用修改角色的请求
                  if (
                    (this.status === 'add' &&
                      this.orgForm.checkedRoles.length === 0) ||
                    this.status === 'edit'
                  ) {
                    resetDailog()
                    this.$message.success('操作成功！')
                    this.$emit('search')
                  } else {
                    this.operateEvent('updateRoles')
                  }
                } else {
                  this.submitStatus = false
                }
              })
            } else {
              this.submitStatus = false
            }
          })
          break
        case 'updateRoles': {
          const submitUrl =
            this.$apiConfig.managerPathPrefix +
            '/organizationRole/updateOrganizationRoles'
          const param = {
            organizationId: this.orgForm.id,
            roleList: this.orgForm.checkedRoles
          }
          this.$aspHttps.asp_Post(submitUrl, param).then(response => {
            if (this.$reponseStatus(response)) {
              resetDailog()
              this.$message.success('操作成功！')
              this.$emit('search')
            } else {
              this.submitStatus = false
            }
          })
          break
        }
        case 'cancel':
          resetDailog()
          break
      }
    },
    /**
     * 获取机构树形节点对应角色信息
     * param data 查询参数
     */
    getOrgRoles(id, domain) {
      const param = {
        organizationId: id,
        domain: domain
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix +
            '/organizationRole/listOrganizationRoles',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.orgForm.roles = response.data
            this.orgForm.checkedRoles = []
            let checkStatus = false
            const checkedValues = []
            if (this.status === 'edit') {
              this.orgForm.roles.forEach(function(item) {
                if (item.check.toString() === 'true') {
                  // 初始化已选中的角色数据，并初始化半选状态
                  checkStatus = true
                  checkedValues.push(item.id)
                }
              })
              if (this.orgForm.roles.length === checkedValues.length) {
                this.checkRolesProgam.checkAll = true
                checkStatus = false
              }
            }
            this.checkRolesProgam.isIndeterminate = checkStatus
            this.orgForm.checkedRoles = checkedValues
          }
        })
    },
    /**
     * 获取机构详情
     * @param id 查询参数，机构（机构）id
     */
    getOrganizationContent(id) {
      this.$aspHttps
        .asp_PostForm(
          this.$apiConfig.managerPathPrefix + '/organization/get',
          { id: id }
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.reValueForm(response.data)
          } else {
            this.$message.success(response.message)
          }
        })
    },
    /**
     * 操作复选框全选按钮
     * @param val 是否选取，true为全选，false为全不选
     */
    handleCheckAllChange(val) {
      const prototypeRoles = []
      this.orgForm.roles.forEach(function(item) {
        prototypeRoles.push(item.id)
      })
      this.orgForm.checkedRoles = val ? prototypeRoles : []
      this.checkRolesProgam.isIndeterminate = false
    },
    /**
     * 统计复选框选择个数
     * @param value 选中的节点数组
     */
    handleCheckedRolesChange(value) {
      // this.orgForm.checkedRoles = value
      const checkedCount = value.length
      this.checkRolesProgam.checkAll =
        checkedCount === this.orgForm.roles.length
      this.checkRolesProgam.isIndeterminate =
        checkedCount > 0 && checkedCount < this.orgForm.roles.length
    },
    /**
     * form表单初始化赋值
     * @param data api接口请求到的数据
     */
    reValueForm(data) {
      this.orgForm = {
        id: data.id,
        name: data.name,
        description: data.description,
        domain: data.domain,
        address: data.address,
        checkedRoles: this.orgForm.checkedRoles,
        roles: this.orgForm.roles,
        division: data.division,
        type: data.type,
        code: data.code,
        parentId: data.parentId
      }
      this.currentDomain = data.domain
      // 数据回显 false不包含全国，true包含全国
      this.checkedDivision = this.$main_tools.areas.getTotalIds(
        this.orgForm.division,
        JSON.stringify(this.orgAreas),
        false
      )
      if (this.checkedDivision === null) {
        this.checkedDivision = []
      }
      setTimeout(() => this.$refs.orgForm.clearValidate())
    },
    /**
     * 处理获取的地域id
     * @param val 获取到的地域Id数组
     */
    handlerArea(val) {
      this.checkedDivision = val
      // 数据处理
      this.orgForm.division = val[val.length - 1]
    },
    /**
     * 更新获取新的可选角色
     * @param val
     */
    updateRoles(val) {
      if (val === this.currentDomain) {
        return
      }
      this.currentDomain = val
      // 修改所属域
      this.getOrgRoles(this.orgForm.id, val)
    },
    // 获取所属域
    getDomainList() {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    // 获取机构类型
    getOrgType() {
      this.orgTypeList = this.$aspUtils.getCodeValueByType(
        this,
        'ORGANIZATION_TYPE'
      )
    },
    // 获取所属机构
    getOrganizationList(val) {
      const params = {
        domain: val || this.userDomain
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/organization/listForCombo',
          params
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.organizationList = response.data
          }
        })
    }
  }
}
</script>
