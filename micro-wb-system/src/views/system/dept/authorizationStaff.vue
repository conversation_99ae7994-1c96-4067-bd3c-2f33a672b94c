<!--
props 说明
{
    domain: this.organizationDomain,
    currentId: id,  // 当前的机构或部门id，回传给父组件用的
    title: `给【${this.deptTreeCfg.checkData.text}】内用户授权`,
    authorizationModelVisible: true,
    roleList: [],
    staffList: [],
    organizationId: this.dynamicComponentParams.orgId
}
 -->
<template>
  <asp-dialog v-model="dialogParam.authorizationModelVisible"
              :visible.sync="dialogParam.authorizationModelVisible"
              :title="dialogParam.title"
              width="700">
    <template>
      <asp-table ref="table"
                 :data="roleList"
                 :show-page="false"
                 type="">
        <asp-table-column width="auto"
                          prop="name"
                          show-overflow-tooltip
                          label="角色 / 姓名"
                          align="center">
        </asp-table-column>
        <template v-for="item in staffList">
          <asp-table-column :key="item.id"
                            :label="item.realName"
                            align="center">
            <template slot-scope="{ scope }">
              <div>
                <template v-if="isChecked[scope.row.id][item.id]">
                  <el-checkbox :name="item.id + ',' + scope.row.id"
                               checked
                               @change="staffRoleClick"></el-checkbox>
                </template>
                <template v-else>
                  <el-checkbox :name="item.id + ',' + scope.row.id"
                               @change="staffRoleClick"></el-checkbox>
                </template>
              </div>
            </template>
          </asp-table-column>
        </template>
      </asp-table>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow size="small"
                      name="取消"
                      @click="dialogParam.authorizationModelVisible = false">
      </asp-btn-hollow>
      <asp-btn-solid v-loading="submitStatus"
                     name="保存"
                     @click="submit">
      </asp-btn-solid>
    </template>
  </asp-dialog>
</template>

<script>
export default {
  name: 'AuthorizationStaff',
  components: {},
  props: {
    dialogParam: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      submitStatus: false,
      tableLoading: true,
      staffIdStr: '', // 选择用户信息转换成String
      userIds: [], // 用户Id数组
      staff: [],
      // 父级传递 角色信息
      roleList: [],
      roleList0: [],
      // 父级传递 用户信息 curTableData 构造用户信息
      staffList: [],
      isChecked: {}, // 用户角色关联的二维数组
      staffRoleCheckedData: [], // 将角色用户关联关系重组封装成组合数组
      // 当前选中用户授权ID
      listRoleByStaffIds: [],
      subParams: [] // 提交授权参数
    }
  },
  watch: {
    'dialogParam.authorizationModelVisible' (val) {
      if (val) {
        this.staffIdStr = this.changeStaffIdsToString(
          this.dialogParam.staffList
        )
        // 固定用organizationId
        this.rebuildTable(this.userIds, this.dialogParam.organizationId)
        const params = []
        this.userIds.forEach(function (item) {
          params.push({
            userId: item,
            roleList: []
          })
        })
        this.subParams = params
        this.$nextTick(() => {
          // 修改标题【】中的字体颜色
          const span = document.querySelectorAll(
            '.el-dialog__header .el-dialog__title'
          )[0]
          var reg = new RegExp('\\【(.+?)\\】', 'g')
          const title = this.dialogParam.title.replace(reg, function (res) {
            return `<span class="color-auth-css">${res}</span>`
          })
          span.innerHTML = title
        })
      } else {
        Object.assign(this.$data, this.$options.data())
      }
    }
  },
  methods: {
    submit () {
      // 赋权用户信息 提交信息待确认
      // this.dialogParam.authorizationModelVisible = false
      this.StaffRoleFormSubmit()
    },
    // 重新组合展示数据
    rebuildTable (userIds, id) {
      let url = ''
      var params = {}
      params = {
        organizationId: id,
        userIds: userIds
      }
      url = this.$apiConfig.managerPathPrefix + '/organizationRole/listRole'

      this.$aspHttps.asp_Post(url, params).then(response => {
        if (this.$reponseStatus(response)) {
          // 渲染后面的弹框--与请求数据无关--列数据
          this.staffList = this.dialogParam.staffList // 列数据，用户集合
          // this.columnData = this.dialogParam.staffList // 列数据，用户集合
          this.roleList = this.dialogParam.roleList // 行数据，角色集合
          // this.rowData = this.dialogParam.roleList // 行数据，角色集合
          const respData = response.data // 响应数据
          this.staffRoleCheckedData = []
          for (let i = 0; i < respData.length; i++) {
            this.staffRoleCheckedData.push(
              respData[i].userId + ',' + respData[i].roleId
            )
          }
          // 初始化二维数组
          for (let i = 0; i < this.staffList.length; i++) {
            for (let j = 0; j < this.roleList.length; j++) {
              if (this.isChecked[this.roleList[j].id] === undefined) {
                this.isChecked[this.roleList[j].id] = {}
              }
              this.isChecked[this.roleList[j].id][this.staffList[i].id] = false
              for (let k = 0; k < respData.length; k++) {
                if (
                  respData[k].userId === this.staffList[i].id &&
                  respData[k].roleId === this.roleList[j].id
                ) {
                  // 行数据和列数据存在关联关系就为true,否则为false
                  this.isChecked[this.roleList[j].id][this.staffList[i].id] = true
                }
              }
            }
          }
          const that = this
          this.$nextTick(() => {
            that.roleList0 = this.roleList
            that.tableLoading = false
          })
        }
      })
    },
    // 修改用户数组成String
    changeStaffIdsToString (list) {
      let Str = ''
      let num = 0
      const Ids = []
      list.forEach(function (item) {
        if (item) {
          Ids.push(item.id)
          if (num === 0) {
            Str = item.id
            num = 1
          } else {
            Str = Str + ',' + item.id
          }
        }
      })
      this.userIds = Ids
      return Str
    },
    /**
     * 勾选用户赋权操作
     * 该方法适配兼容火狐浏览器
     * @param event 勾选状态，true为勾选，false为未勾选
     * @param target 勾选对象数据
     */
    staffRoleClick (event, target) {
      // 修改用户id/角色id组合数组
      if (event) {
        // 选中角色赋值
        this.staffRoleCheckedData.push(target.target.name)
      } else {
        // 取消选中的角色，清除掉该数据
        for (var i = 0; i < this.staffRoleCheckedData.length; i++) {
          if (target.target.name === this.staffRoleCheckedData[i]) {
            this.staffRoleCheckedData.splice(i, 1)
          }
        }
      }
      const dataIds = target.target.name.split(',')
      // 修改选中状态this.isChecked[roleId][userId]
      this.isChecked[dataIds[1]][dataIds[0]] = event
    },
    StaffRoleFormSubmit () {
      this.submitStatus = true
      for (let i = 0; i < this.staffRoleCheckedData.length; i++) {
        const staffRoleData = this.staffRoleCheckedData[i].split(',')
        this.getRoleId(staffRoleData)
      }
      const param = {
        roleType: '2', // 机构角色
        userRoleUpdateList: this.subParams
      }
      this.tableLoading = true
      const url =
        this.$apiConfig.managerPathPrefix + '/userRole/updateUserRoles'
      this.$aspHttps.asp_Post(url, param).then(response => {
        if (this.$reponseStatus(response)) {
          this.$message.success('授权成功')
          // 赋权用户信息 提交信息待确认
          this.dialogParam.authorizationModelVisible = false
          // 刷新父级列表
          this.$emit('updateAuthorStaff', this.dialogParam.currentId)
          this.submitStatus = false
          this.tableLoading = false
        } else {
          this.submitStatus = false
          this.tableLoading = false
        }
      })
    },
    getRoleId (data) {
      this.subParams.forEach(function (item) {
        if (item.userId === data[0]) {
          item.roleList.push(data[1])
        }
      })
    }
  }
}
</script>
