<template>
  <asp-dialog :visible.sync="dialogParam.deptModelVisible"
              :title="dialogParam.title"
              width="60%">
    <template>
      <el-form ref="deptForm"
               :model="deptForm"
               :rules="deptFormRules"
               :inline="true">
        <el-row v-if="refush"
                class="el-collapse-90">
          <el-col :span="12">
            <el-input v-show="false"
                      v-model.trim="deptForm.departmentId"
                      autocomplete="off"
                      type="hidden">
            </el-input>
            <el-input v-show="false"
                      v-model="deptForm.parentId"
                      autocomplete="off"
                      type="hidden">
            </el-input>
            <el-form-item label="部门名称:"
                          prop="departmentName">
              <el-input v-model.trim="deptForm.departmentName"
                        autocomplete="off"
                        type="text"
                        placeholder="不能超过40个字符">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构类型:"
                          prop="orgType">
              <el-select v-model="deptForm.orgType"
                         disabled
                         placeholder="请选择">
                <el-option v-for="item in orgTypeList"
                           :key="item.code"
                           :label="item.name"
                           :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
            <!-- 部门没有归属机构字段 -->
            <!-- <el-form-item label="归属机构:" prop="parentId">
                                <el-input v-model.trim="deptForm.parentName" placeholder="不能超过20个字符" disabled>
                                </el-input>
                            </el-form-item> -->
          </el-col>
        </el-row>
        <el-row class="el-collapse-90">
          <el-col :span="12">
            <el-form-item label="归属域:"
                          prop="domain">
              <el-select v-model="deptForm.domain"
                         placeholder="请选择"
                         autocomplete="off"
                         disabled>
                <el-option v-for="item in domainList"
                           :key="item.code"
                           :label="item.name"
                           :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构代码:"
                          prop="code">
              <el-input v-model.trim="deptForm.code"
                        placeholder="不能超过20个字符">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-90">
          <el-col :span="12">
            <el-form-item label="归属区域:"
                          prop="division">
              <el-input placeholder="请选择"
                        readonly
                        :disabled="status === 'edit'"
                        v-model="deptForm.divisionName">
                <el-button v-if="status === 'add'"
                           slot="append"
                           @click="operateArea">选择</el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-90">
          <el-col :span="24">
            <el-form-item label="部门描述:"
                          prop="departmentDesc">
              <el-input :rows="3"
                        v-model="deptForm.departmentDesc"
                        type="textarea"
                        placeholder="不能超过40个字符">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow name="取消"
                      icon="el-icon-close"
                      @click="handleCancel()"></asp-btn-hollow>
      <asp-btn-solid v-loading="isBtnLoading"
                     :disabled="isBtnLoading"
                     name="保存"
                     icon="el-icon-check"
                     @click="handleSave()"></asp-btn-solid>
    </template>
    <transferAreas :dialog-param="checkAreaParam"
                   :multiple="false"
                   @updateAreaData="updateCheckedArea"></transferAreas>
  </asp-dialog>
</template>

<script>
import transferAreas from '../components/areas/transferAreas.vue'
export default {
  name: 'DeptTreeEdit',
  components: {
    transferAreas
  },
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    var validateDepartmentName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('部门名称不能为空'))
      } else {
        const checkDepartmentNameUrl =
          this.$apiConfig.managerPathPrefix +
          '/department/checkDepartmentName'
        const param = {
          departmentId: this.deptForm.departmentId,
          organizationId: this.deptForm.organizationId,
          departmentName: value
        }
        this.$aspHttps
          .asp_Post(checkDepartmentNameUrl, param)
          .then(response => {
            if (this.$reponseStatus(response)) {
              if (response.data) {
                callback()
              } else {
                callback(new Error('部门名称已存在'))
              }
            }
          })
      }
    }
    return {
      refush: true,
      checkAreaParam: { // 选择地域相关操作传递参数
        areaModelVisible: false,
        parentDivision: '',
        title: '关联归属区域',
        checkedIds: [],
        checkedDatas: []
      },
      // 组织表单参数对象
      deptForm: {
        departmentId: '',
        parentId: '',
        parentName: '',
        departmentName: '',
        departmentDesc: '',
        organizationId: '', // 组织id
        domain: '',
        address: '',
        checkAll: false, // 新增状态checkAll和isIndeterminate都是false
        isIndeterminate: false, // 部分选中状态isIndeterminate是true
        checkedRoles: [],
        roles: [],
        division: [],
        orgType: '',
        code: ''
      },
      // 验证
      deptFormRules: {
        departmentName: [
          { required: true, message: '请输入部门名称', trigger: 'blur' },
          { max: 40, message: '输入不能超过40个字符', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' },
          { validator: validateDepartmentName, trigger: 'blur' }
        ],
        departmentDesc: [
          { required: true, message: '请输入部门描述', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' },
          { max: 40, message: '输入不能超过40个字符', trigger: 'blur' }
        ],
        domain: [
          { required: true, message: '请选择归属域', trigger: 'change' }
        ],
        division: [
          { required: true, message: '请选择归属区域', trigger: 'change' }
        ],
        parentId: [
          { required: true, message: '请选择归属机构', trigger: 'change' }
        ],
        code: [{ max: 20, message: '输入不能超过20个字符', trigger: 'blur' }]
      },
      isBtnLoading: false,
      // 新增 修改
      status: '',
      orgAreas: [], // 地域数据参数
      props: {
        key: 'id',
        label: 'text',
        value: 'id',
        children: 'children'
      },
      parentDivision: '000', // 获取上一级地域Id
      domainList: [], // 所属域
      orgTypeList: [
        {
          code: '0',
          name: ' '
        }
      ] // 机构类型
    }
  },
  watch: {
    'dialogParam.deptModelVisible' (val) {
      if (val) {
        this.isBtnLoading = false
        this.$nextTick(() => {
          this.$refs.deptForm && this.$refs.deptForm.resetFields()
          this.initForm()
          this.deptForm.organizationId = this.dialogParam.organizationId
          if (this.dialogParam.title.indexOf('修改') >= 0) {
            this.status = 'edit'
            // 修改一级部门
            if (this.dialogParam.dataIsFirst) {
              this.setDivisionByOrgDivision(this.dialogParam.orgDivision)
              // 获取部门详情
              this.getDepartmentContent(this.dialogParam.currentData.id, true)
            } else {
              // 获取上级部门的地域ID，division----和地域信息
              this.setDivisionByParent(this.dialogParam.currentData.parentId).then(() => {
                // 获取部门详情
                this.getDepartmentContent(this.dialogParam.currentData.id, true)
              })
            }
          } else {
            // 新增
            this.status = 'add'
            this.deptForm.domain = this.dialogParam.domain

            // 当前节点id
            this.deptForm.parentId = this.dialogParam.currentData && this.dialogParam.currentData.id
            // this.deptForm.parentName = this.dialogParam.currentData && this.dialogParam.currentData.text
            this.deptForm.orgType = this.dialogParam.orgType

            // 新增一级部门--机构新增部门
            if (this.dialogParam.dataIsFirst) {
              this.deptForm.parentId = ''
              this.setDivisionByOrgDivision(this.dialogParam.orgDivision)
              // 获取角色信息
              this.getOrgRolesContext(this.dialogParam.currentData)
            } else {
              // 新增子部门
              // 获取地域ID，division
              this.setDivisionByParent(this.deptForm.parentId)
            }
            this.deptForm.departmentId = ''
          }
        })
      } else {
        this.resetForm()
      }
    }
  },
  created () {
    this.getDomainList()
    this.getOrgType()
  },
  methods: {
    // 初始化界面，初始化form表单
    resetForm () {
      this.dialogParam.deptModelVisible = false
      this.isBtnLoading = false
      this.$refs.deptForm && this.$refs.deptForm.resetFields()
      this.initForm()
      // Object.assign(this.$data, this.$options.data());
    },
    // 初始化form表单
    initForm () {
      this.deptForm = {
        departmentId: '',
        parentId: '',
        parentName: '',
        departmentName: '',
        departmentDesc: '',
        organizationId: '', // 组织id
        domain: '',
        address: '',
        checkAll: false, // 新增状态checkAll和isIndeterminate都是false
        isIndeterminate: false, // 部分选中状态isIndeterminate是true
        checkedRoles: [],
        roles: [],
        division: [],
        orgType: '',
        code: ''
      }
    },
    /**
     * 新增/保存修改
     * @param type 操作类型
     */
    handleSave () {
      this.$refs.deptForm.validate(valid => {
        if (valid) {
          const param = {
            address: this.deptForm.address,
            description: this.deptForm.departmentDesc,
            division: this.deptForm.division, // ???
            domain: this.deptForm.domain,
            email: this.deptForm.email,
            id: this.deptForm.departmentId,
            name: this.deptForm.departmentName,
            code: this.deptForm.code,
            organizationId: this.deptForm.organizationId, // 组织id
            parentId: this.deptForm.parentId,
            type: this.deptForm.orgType,
            roles: this.deptForm.checkedRoles // 选中的角色信息
          }
          this.isBtnLoading = true
          // 判断是做新增提交还是修改提交
          let submitUrl = ''
          if (this.status === 'edit') {
            submitUrl = this.$apiConfig.managerPathPrefix + '/department/update'
          } else {
            submitUrl = this.$apiConfig.managerPathPrefix + '/department/insert'
          }
          this.$aspHttps.asp_Post(submitUrl, param).then(response => {
            this.isBtnLoading = false
            if (this.$reponseStatus(response)) {
              if (this.status === 'add') {
                this.deptForm.departmentId = response.data
              }
              // 组织详情新增部门，且未选择角色时，不调用修改部门角色信息的接口
              if ((this.status === 'add' && this.deptForm.checkedRoles.length === 0) || this.status === 'edit') {
                this.resetForm()
                this.$message.success('操作成功！')
                this.$emit('uploadDept', param.organizationId)
              } else {
                this.updateDeptRoleList()
              }
            }
          })
        }
      })
    },
    // 取消
    handleCancel () {
      this.resetForm()
    },
    // 修改部门关联角色信息
    updateDeptRoleList () {
      const param = {
        departmentId: this.deptForm.departmentId,
        roleList: this.deptForm.checkedRoles
      }
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/departmentRole/updateDepartmentRoles', param).then(response => {
        this.isBtnLoading = false
        if (this.$reponseStatus(response)) {
          this.resetForm()
          this.$message.success('操作成功！')
          this.$emit('uploadDept', this.dialogParam.organizationId)
        }
      })
    },
    // 设置地域（根据机构）
    setDivisionByOrgDivision (division) {
      this.parentDivision = division
    },
    // 设置地域（根据父部门）
    setDivisionByParent (parentId) {
      return new Promise(resolve => {
        // 如果是机构id就不请求了
        if (parentId === this.dialogParam.organizationId) {
          resolve()
          return
        }
        this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/department/get', { id: parentId }).then(response => {
          if (this.$reponseStatus(response)) {
            // 获取当前部门上一级地域数据
            this.parentDivision = response.data.division
            resolve()
          } else {
            this.$message.success(response.message)
          }
        })
      })
    },
    // 获取组织所关联的角色信息
    // @param data 查询组织对应角色信息
    getOrgRolesContext (data) {
      const param = {
        id: data.id,
        domain: data.domain
      }
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/organization/getOrganizationContext', param).then(response => {
        if (this.$reponseStatus(response)) {
          const roleList = []
          this.deptForm.roles = []
          this.deptForm.checkedRoles = []
          response.data.listRoleVo.forEach(function (item) {
            roleList.push({
              check: 'false',
              id: item.id,
              name: item.name
            })
          })
          this.deptForm.roles = roleList
        }
      })
    },
    // 获取部门详情
    // @param data 查询参数，id部门id
    getDepartmentContent (id) {
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/department/get', { id: id }).then(response => {
        if (this.$reponseStatus(response)) {
          // 获取当前部门详情
          this.setForm(response.data)
        }
      })
    },
    /**
     * 操作复选框全选按钮
     * @param val 是否选取，true为全选，false为全不选
     */
    handleCheckAllChange (val) {
      const prototypeRoles = []
      this.deptForm.roles.forEach(function (item) { prototypeRoles.push(item.id) })
      this.deptForm.checkedRoles = val ? prototypeRoles : []
      this.deptForm.isIndeterminate = false
    },
    /**
     * 统计复选框选择个数
     * @param value 选中的节点数组
     */
    handleCheckedRolesChange (value) {
      const checkedCount = value.length
      this.deptForm.checkAll = checkedCount === this.deptForm.roles.length
      this.deptForm.isIndeterminate = checkedCount > 0 && checkedCount < this.deptForm.roles.length
    },
    // form表单回显
    // @param data api接口请求到的数据
    setForm (data) {
      this.deptForm = {
        departmentId: data.id,
        parentId: data.parentId,
        parentName: this.dialogParam.currentData.parentName,
        orgType: data.type,
        type: data.type,
        code: data.code,
        departmentName: data.name,
        departmentDesc: data.description,
        organizationId: data.organizationId,
        domain: data.domain,
        email: data.email,
        address: data.address,
        checkAll: this.deptForm.checkAll, // 新增状态checkAll和isIndeterminate都是false
        isIndeterminate: this.deptForm.isIndeterminate, // 部分选中状态isIndeterminate是true
        checkedRoles: this.deptForm.checkedRoles,
        roles: this.deptForm.roles,
        division: data.division,
        divisionName: data.divisionName
      }
      setTimeout(() => {
        this.refush = false
        this.$nextTick(() => {
          this.refush = true
        })
      }, 50)
      setTimeout(() => this.$refs.deptForm.clearValidate())
    },
    // 操作地域--TODO-操作地域--条件必须至少选择机构
    operateArea () {
      if (this.parentDivision === null || this.parentDivision === '') {
        this.$message.warning('请至少要选择一个机构！')
        return
      }
      // 打开弹窗前获取数据--地域数据
      this.checkAreaParam.areaModelVisible = true
      this.checkAreaParam.parentDivision = this.parentDivision
      const { division, divisionName } = this.deptForm
      if (division) this.checkAreaParam.checkedIds = [this.deptForm.division]
      if (division && divisionName) {
        this.checkAreaParam.checkedDatas = [
          {
            id: this.deptForm.division,
            pathName: this.deptForm.divisionName
          }
        ]
      }
    },
    // 获取/更新选择的所属地域数据
    updateCheckedArea (data) {
      // 更新数据
      this.deptForm.division = data.Ids[0]
      this.deptForm.divisionName = data.labels[0]
      this.checkAreaParam = { // 并初始化弹窗数据
        areaModelVisible: false,
        parentDivision: '',
        title: '关联归属区域',
        checkedIds: [],
        checkedDatas: []
      }
    },
    // 获取所属域
    getDomainList () {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    // 获取机构类型
    getOrgType () {
      this.orgTypeList = this.$aspUtils.getCodeValueByType(this, 'ORGANIZATION_TYPE')
    }
  }
}
</script>
