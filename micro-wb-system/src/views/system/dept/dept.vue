/** * Created by aspire on 2018/10/19. * 机构管理 */
<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form ref="searchForm" :inline="true" :model="table.searchForm">
          <el-row>
            <el-col :span="6">
              <el-form-item prop="name" label="机构名称：">
                <el-input
                  v-model.trim="table.searchForm.name"
                  placeholder
                  name="name"
                  @keyup.enter.native="search()"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="domain" label="管理域：">
                <asp-select-all
                  v-model="table.searchForm.domain"
                  :code-list="domainList"
                ></asp-select-all>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="query-area-btn-css">
                <asp-btn-solid
                  icon="el-icon-search"
                  name="查询"
                  @click="search()"
                ></asp-btn-solid>
                <asp-btn-hollow
                  icon="el-icon-refresh"
                  name="重置"
                  @click="reset()"
                ></asp-btn-hollow>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <asp-table
        ref="table"
        :url="table.url"
        :param="table.searchForm"
        :prefix="table.prefix"
        index-switch
        type=""
      >
        <template slot="header">
          <asp-btn-solid
            v-hasAuth="doAuth({ btnCode: 'wb_020104' })"
            icon="el-icon-plus"
            name="新增"
            @click="addOrg"
          >
          </asp-btn-solid>
          <asp-btn-hollow
            v-hasAuth="doAuth({ btnCode: 'wb_02010301' })"
            icon="el-icon-download"
            name="导出"
            @click="exportList"
          >
          </asp-btn-hollow>
        </template>
        <asp-table-column
          prop="name"
          label="机构名称"
          min-width="145"
          sort-key="name"
        >
          <template slot-scope="{ scope }">
            <span class="singleRow">{{ scope.row.name }}</span>
          </template>
        </asp-table-column>
        <asp-table-column
          prop="domain"
          label="管理域"
          min-width="70"
          sort-key="domain"
        >
          <template slot-scope="{ scope }">{{
            $aspUtils.formatDict(scope.row.domain, domainList)
          }}</template>
        </asp-table-column>
        <asp-table-column
          prop="divisionName"
          label="所属区域"
          min-width="80"
          sort-key="divisionName"
        >
        </asp-table-column>
        <asp-table-column prop="parentName" label="归属机构" min-width="120">
        </asp-table-column>
        <asp-table-column prop="description" label="机构描述" min-width="150">
          <template slot-scope="{ scope }">
            <span class="singleRow">{{ scope.row.description }}</span>
          </template>
        </asp-table-column>
        <asp-table-column label="状态" width="70">
          <template slot-scope="{ scope }">
            <template v-if="scope.row.status === '1'">
              <el-tag type="success">正常</el-tag>
            </template>
            <template v-else-if="scope.row.status === '2'">
              <el-tag type="warning">禁用</el-tag>
            </template>
            <template v-else-if="scope.row.status === '9'">
              <el-tag type="primary">删除</el-tag>
            </template>
            <template v-else>
              <el-tag type="danger">未知</el-tag>
            </template>
          </template>
        </asp-table-column>
        <asp-table-column label="操作" width="140" fixed="right">
          <template slot-scope="{ scope }">
            <asp-btn-text
              name="详情"
              @click="searchDepartment(scope.row.id, '')"
            >
            </asp-btn-text>
            <asp-btn-text
              v-hasAuth="doAuth({ btnCode: 'wb_020105' })"
              name="修改"
              @click="editOrg(scope.row)"
            >
            </asp-btn-text>
            <asp-btn-text
              v-hasAuth="doAuth({ btnCode: 'wb_020106' })"
              v-show="userDomain === scope.row.domain || userDomain === 'admin'"
              name="删除"
              @click="deleteOrg(scope.row.id)"
            >
            </asp-btn-text>
          </template>
        </asp-table-column>
      </asp-table>
    </div>
    <EditOrg
      ref="editOrg"
      :dialog-param="orgModelParam"
      @search="search"
    ></EditOrg>
  </div>
</template>

<script>
import EditOrg from './editOrg'
export default {
  name: 'Dept',
  components: {
    EditOrg
  },
  data() {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/organization/list',
        searchForm: {
          domain: '',
          name: '',
          type: '',
          division: ''
        }
      },
      userDomain: '',
      orgModelParam: {}
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDomainList()
    const domain = this.$aspUtils.getDomainObject(this)
    this.userDomain = domain.userInfo.domain
  },
  mounted() {
    this.search()
  },
  methods: {
    /**
     * 按钮权限
     * @method doAuth
     * @param {object} opt
     * @return {object}
     */
    doAuth(opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    /**
     * 查询机构详情
     * @param orgId
     * @param deptId
     */
    searchDepartment(orgId, deptId) {
      this.$router.push({
        path: '/system/deptDetailConfigure',
        query: {
          dataFrom: 'dept',
          orgId: orgId,
          deptId: deptId,
          title: '机构详情'
        }
      })
    },
    search() {
      this.$refs.table.asp_search()
    },
    reset(formName) {
      // console.log(formName)
      this.$refs.table.asp_reset()
    },
    /**
     * 导出excel
     * @method exportList
     */
    exportList() {
      let params = ''
      const obj = this.table.searchForm
      for (const key in obj) {
        params += key + '=' + (obj[key] === undefined ? '' : obj[key]) + '&'
      }
      params = params.substring(0, params.length - 1)
      let url =
        this.$apiConfig.managerPathPrefix + '/organization/export?' + params
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    // 新增机构
    addOrg() {
      this.orgModelParam = {
        title: '新增机构',
        orgModelVisible: true,
        editOrgForm: {
          id: ''
        }
      }
    },
    // 修改机构详情
    editOrg(data) {
      const orgId = data.id
      if (orgId === null || orgId === '') {
        this.$message.warning('数据异常请刷新列表！')
      }
      this.orgModelParam = {
        title: '修改机构',
        domain: data.domain, // 当前机构所属域
        orgModelVisible: true,
        editOrgForm: {
          id: orgId
        }
      }
    },
    /**
     * 删除机构详情
     * @param id
     */
    deleteOrg(id) {
      this.$confirm('确认删除此机构？', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            // 请求体
            this.$aspHttps
              .asp_PostForm(
                this.$apiConfig.managerPathPrefix + '/organization/delete',
                { id: id }
              )
              .then(response => {
                if (this.$reponseStatus(response)) {
                  this.search(false)
                  instance.confirmButtonLoading = false
                  done()
                } else {
                  instance.confirmButtonLoading = false
                }
              })
            // done() 回调
            // instance.confirmButtonLoading = false 初始化确认按钮状态
          }
          if (action === 'cancel') {
            if (instance.confirmButtonLoading === true) {
              this.$message({ type: 'warning', message: '请求中•••' })
            } else {
              done()
            }
          }
        }
      })
        .then(() => {
          this.$message({ type: 'success', message: '删除成功!' })
        })
        .catch(() => {
          this.$message({ type: 'info', message: '已取消删除' })
        })
    },
    // 获取所属域
    getDomainList() {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    }
  }
}
</script>
