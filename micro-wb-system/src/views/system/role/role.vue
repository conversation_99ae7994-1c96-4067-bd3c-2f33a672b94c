/** * 系统角色管理 */
<template>
  <div class="webbas">
    <template>
      <div class="list-page-content-css">
        <div class="query-area-content-css">
          <el-form ref="searchForm"
                   :inline="true"
                   :model="table.searchForm"
                   @submit.native.prevent>
            <el-row class="el-collapse-130">
              <el-col :span="6">
                <el-form-item prop="name"
                              label="角色名称：">
                  <el-input v-model.trim="table.searchForm.name"
                            placeholder=""
                            name="name"
                            @keyup.enter.native="search()">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="roleType"
                              label="角色类型：">
                  <asp-select-all v-model="table.searchForm.roleType"
                                  :code-list="dictRoleList"></asp-select-all>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="domain"
                              label="管理域：">
                  <asp-select-all v-model="table.searchForm.domain"
                                  :code-list="domainList"
                                  @change="getDomain"></asp-select-all>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="organizationId"
                              label="归属机构：">
                  <asp-select-all v-model="table.searchForm.organizationId"
                                  :code-list="organizationList"></asp-select-all>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="el-collapse-130">
              <el-col v-if="showRoleSensFlag"
                      :span="6">
                <el-form-item prop="sensLevel"
                              label="敏感数据分级情况：">
                  <asp-select-all v-model="table.searchForm.sensLevel"
                                  :code-list="sensLevelList"></asp-select-all>
                </el-form-item>
              </el-col>
              <el-col v-if="showRoleSensFlag"
                      :span="6">
                <el-form-item prop="isBatchLoad"
                              label="批量下载数据功能：">
                  <asp-select-all v-model="table.searchForm.isBatchLoad"
                                  :code-list="batchLoalList"></asp-select-all>
                </el-form-item>
              </el-col>
              <el-col :span="showRoleSensFlag ? 12 : 24">
                <el-form-item class="query-area-btn-css">
                  <asp-btn-solid name="查询"
                                 icon="el-icon-search"
                                 @click="search()"></asp-btn-solid>
                  <asp-btn-hollow icon="el-icon-refresh"
                                  name="重置"
                                  @click="reset()"></asp-btn-hollow>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <asp-table ref="table"
                   :url="table.url"
                   :param="table.searchForm"
                   :prefix="table.prefix"
                   type="">
          <template slot="header">
            <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_040104' })"
                           name="新增"
                           icon="el-icon-plus"
                           @click="addRole"></asp-btn-solid>
            <asp-btn-hollow v-hasAuth="doAuth({ btnCode: 'wb_040111' })"
                            name="导出"
                            icon="el-icon-download"
                            @click="exportList"></asp-btn-hollow>
          </template>
          <asp-table-column sort-key="name"
                            prop="name"
                            min-width="160"
                            label="角色名称"></asp-table-column>
          <asp-table-column :formatter="dictRoleType"
                            prop="roleType"
                            width="85"
                            label="角色类型">
          </asp-table-column>
          <asp-table-column :formatter="dictDomain"
                            sort-key="domain"
                            prop="domain"
                            width="115"
                            label="归属管理域">
          </asp-table-column>
          <asp-table-column prop="organizationName"
                            width="130"
                            show-overflow-tooltip
                            label="归属机构"></asp-table-column>
          <asp-table-column prop="description"
                            min-width="160"
                            show-overflow-tooltip
                            label="角色描述"></asp-table-column>
          <asp-table-column prop="roleKey"
                            min-width="125"
                            label="角色助记码"></asp-table-column>
          <asp-table-column v-if="showRoleSensFlag"
                            :formatter="dictSensLevel"
                            prop="sensLevel"
                            width="139"
                            label="敏感数据分级情况"></asp-table-column>
          <asp-table-column v-if="showRoleSensFlag"
                            :formatter="dictBatchLoad"
                            prop="isBatchLoad"
                            width="139"
                            label="批量下载数据功能"></asp-table-column>
          <asp-table-column :width="this.$aspFontSize.asp_ColButtonSize([2, 2, 2, 4])"
                            label="操作"
                            fixed="right">
            <template slot-scope="{ scope }">
              <asp-btn-text name="查看"
                            @click="detailRole(scope.$index, scope.row)">
              </asp-btn-text>
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_040105' })"
                            v-show="scope.row.originType == '0' && scope.row.visible !== 0 && scope.row.autoAssign !== 1"
                            :disabled="scope.row.canModify === 0"
                            name="修改"
                            @click="editRole(scope.$index, scope.row)">
              </asp-btn-text>
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_040106' })"
                            v-show="scope.row.originType == '0' && scope.row.visible !== 0 && scope.row.autoAssign !== 1"
                            :disabled="scope.row.canModify === 0"
                            name="删除"
                            @click="delRole(scope.row)">
              </asp-btn-text>
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_040107' })"
                            v-show="scope.row.originType == '0'"
                            name="分配权限"
                            @click="authResourceEvent(scope.$index, scope.row)">
              </asp-btn-text>
            </template>
          </asp-table-column>
        </asp-table>
      </div>
      <!-- 新增/修改角色 -->
      <editRole ref="editRole"
                :dialog-param="roleModelParam"
                @search="search"></editRole>
    </template>
  </div>
</template>

<script>
import editRole from './editRole'
export default {
  name: 'Role',
  components: { editRole },
  data () {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/role/listPage',
        searchForm: {
          order: 'asc', // 顺序/倒序排列
          sortName: 'name', // 排序名称
          name: '',
          organizationId: '',
          roleType: '',
          domain: '',
          sensLevel: '',
          isBatchLoad: ''
        }
      },
      roleList: [],
      organizationList: [],
      domainList: [],
      dictRoleList: [],
      roleModelParam: {}
    }
  },
  computed: {
    batchLoalList () {
      const list = this.$aspUtils.getCodeValueByType(this, 'WB_ROLE_IS_BATCH_LOAD_TYPE')
      return list
    },
    sensLevelList () {
      const list = this.$aspUtils.getCodeValueByType(this, 'WB_ROLE_SENS_LEVEL_TYPE')
      return list
    },
    // 是否展示敏感数据分级情况/批量下载数据功能，0-不展示，1-展示
    showRoleSensFlag () {
      const flag = this.$aspUtils.getListAllObject(this).domainConfig.showRoleSensitiveFields
      return flag === '1'
    }
  },
  beforeMount () {
    if (!this.showRoleSensFlag) {
      delete this.table.searchForm.sensLevel
      delete this.table.searchForm.isBatchLoad
    }
  },
  mounted () {
    // this.search()
    this.getOrganizationList()
    this.getDomainList()
    this.getDictRoleList()
  },
  methods: {
    /**
     * 导出excel
     * @method exportList
     */
    exportList (pageRestart) {
      // console.log(pageRestart)
      let params = ''
      const obj = this.table.searchForm
      for (const key in obj) {
        params += key + '=' + (obj[key] === undefined ? '' : obj[key]) + '&'
      }
      params = params.substring(0, params.length - 1)
      let url = this.$apiConfig.managerPathPrefix + '/role/export?' + params
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    /**
     * 按钮权限
     * @method doAuth
     * @param {object} opt
     * @return {object}
     */
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    search () {
      this.$nextTick(() => {
        this.$refs.table.asp_search()
      })
    },
    reset () {
      this.$refs.table.asp_reset()
      // 重置后重新请求归属机构
      this.getOrganizationList()
    },
    // 新增角色
    addRole () {
      this.roleModelParam = {
        title: '新增角色',
        oper: 'add',
        roleModelVisible: true,
        editRole: {
          id: '',
          roleKey: '',
          roleType: '',
          name: '',
          domain: '',
          description: '',
          organizationId: '',
          isBatchLoad: '0', // 默认不具备-0
          sensLevel: '0' // 默认非敏感-0
        },
        organizationList: this.organizationList,
        domainList: this.domainList,
        dictRoleType: this.dictRoleList
      }
    },
    // 修改角色
    editRole (index, row) {
      this.roleModelParam = {
        title: '修改角色',
        oper: 'edit',
        roleModelVisible: true,
        editRole: {
          id: row.id,
          roleKey: row.roleKey,
          roleType: row.roleType,
          domain: row.domain,
          name: row.name,
          description: row.description,
          organizationId: row.organizationId,
          isBatchLoad: row.isBatchLoad || '0', // 默认不具备-0
          sensLevel: row.sensLevel
        },
        organizationList: this.organizationList,
        domainList: this.domainList,
        dictRoleType: this.dictRoleList
      }
    },
    // 查看角色
    detailRole (index, row) {
      this.roleModelParam = {
        title: '查看角色',
        oper: 'detail',
        roleModelVisible: true,
        readonly: true,
        editRole: {
          id: row.id,
          roleKey: row.roleKey,
          roleType: row.roleType,
          domain: row.domain,
          name: row.name,
          description: row.description,
          organizationId: row.organizationId,
          isBatchLoad: row.isBatchLoad || '0', // 默认不具备-0
          sensLevel: row.sensLevel
        },
        organizationList: this.organizationList,
        domainList: this.domainList,
        dictRoleType: this.dictRoleList
      }
    },
    // 删除角色
    delRole (row) {
      const showStr = '确认删除此角色？'
      this.$aspMsgbox.confirm(this, showStr, function (arg, instance) {
        // console.log(arg, instance)
        const url = this.$apiConfig.managerPathPrefix + '/role/delete'
        this.$aspHttps.asp_PostForm(url, { id: row.id }).then(response => {
          if (this.$reponseStatus(response)) {
            this.$message.success('删除成功!')
            this.search()
          }
        })
      })
    },
    // 分配角色
    authResourceEvent (index, row) {
      // 判断是否默认角色 0不是 1是
      let roleType = 0
      if (row.autoAssign === 1 && row.visible === 0) {
        roleType = 1
      }
      this.$router.push({
        path: '/system/newAuthResource',
        query: {
          id: row.id,
          name: row.name,
          roleType
        }
        // query: { row: row }
      })
    },
    // 获取所属机构
    getOrganizationList () {
      const params = {
        domain: this.table.searchForm.domain
      }
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/organization/listForCombo', params).then(response => {
        if (this.$reponseStatus(response)) {
          const list = []
          response.data.forEach(item => {
            list.push({
              code: item.id,
              name: item.name
            })
          })
          this.$nextTick(() => {
            this.organizationList = list
            // 切换管理域之后，判断当前归属机构是否存在，不存在则设置为全部
            if (list.map(item => { return item.code }).indexOf(this.table.searchForm.organizationId) === -1) {
              this.table.searchForm.organizationId = ''
            }
          })
        }
      })
    },
    // 获取所属域
    getDomainList () {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    // 获取选中所属域的所属机构
    getDomain () {
      this.$nextTick(() => {
        this.getOrganizationList()
      })
    },
    // 获取角色类型
    getDictRoleList () {
      this.dictRoleList = this.$aspUtils.getCodeValueByType(this, 'ROLE_TYPE')
    },
    dictRoleType (row) {
      const item = this.dictRoleList.find(val => val.code === row.roleType)
      return item ? item.name : ''
    },
    dictDomain (row) {
      const item = this.domainList.find(val => val.code === row.domain)
      return item ? item.name : ''
    },
    dictBatchLoad (row) {
      const item = this.batchLoalList.find(val => val.code === row.isBatchLoad)
      return item ? item.name : ''
    },
    dictSensLevel (row) {
      const item = this.sensLevelList.find(val => val.code === row.sensLevel)
      return item ? item.name : ''
    }
  }
}
</script>
