/** * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/9/26 */
<template>
  <div class="webbas">
    <asp-dialog v-model="dialogParam.visible"
                :visible.sync="dialogParam.visible"
                :title="'数据权限'"
                width="60%">
      <template>
        <el-row>
          <el-col>
            <el-checkbox-group v-model="dataResourceList"
                               @change="handleCheckChange">
              <el-checkbox v-for="item in dataResourceCheckboxList"
                           ref="dataRightCheckbox"
                           :key="item.id"
                           :value="item.id"
                           :label="item.id"
                           :disabled="item.disabled ? true : false">{{ item.text }}</el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-transfer v-if="transferFlag"
                         ref="ransfer"
                         v-model="ransferValue"
                         :data="ransferData"
                         :titles="['全选', '全选']"
                         :button-texts="['移除', '添加']"
                         :props="{ key: 'id', label: 'name' }"
                         filter-placeholder="条件过滤..."
                         filterable></el-transfer>
          </el-col>
        </el-row>
      </template>
      <template slot="footer-center">
        <asp-btn-hollow icon="el-icon-close"
                        name="取消"
                        @click="dialogParam.visible = false">
        </asp-btn-hollow>
        <asp-btn-solid icon="el-icon-check"
                       name="确定"
                       @click="save()">
        </asp-btn-solid>
      </template>
    </asp-dialog>
  </div>
</template>

<script>
// import Vue from 'vue'
export default {
  name: 'dataSourceDialog',
  components: {},
  mixins: [],
  props: {
    dialogParam: {
      type: Object,
      default: () => { return {} }
    }
  },
  data () {
    return {
      dataResourceList: [],
      dataResourceCheckboxList: [],
      transferFlag: false, // 控制可搜索穿梭框显示
      ransferValue: [], // 可搜索穿梭框取值
      ransferData: [], // 可搜索穿梭框元数据
      oldDataResourceList: [], // 所有勾选数据
      relationObject: {}
    }
  },
  computed: {},
  watch: {
    'dialogParam.visible' (val) {
      // console.log('watch', this.dialogParam)
      if (val) {
        this.init()
      }
    }
  },
  created () { },
  mounted () {
    // console.log('mounted', this.dialogParam)
    if (this.dialogParam.visible) {
      this.init()
    }
  },
  methods: {
    init () {
      this.ransferValue = []
      this.transferFlag = false
      // 更新数据
      this.dataResourceList = this.dialogParam.dataResourceList || []
      this.dataResourceCheckboxList = this.dialogParam.dataResourceCheckboxList || []
      this.relationObject = this.dialogParam.relationObject
      if (this.dataResourceList.length > 0) {
        this.oldDataResourceList = []
        this.oldDataResourceList = JSON.parse(
          JSON.stringify(this.dataResourceList)
        )
        this.handleCheckChange()
      }
    },
    handleCheckChange () {
      let arr = [] // 当前勾选数据
      if (this.oldDataResourceList.length > this.dataResourceList.length) {
        // 取消勾选
        arr = this.arrayDifference(
          this.oldDataResourceList,
          this.dataResourceList
        )
      }
      if (this.dataResourceList.length > this.oldDataResourceList.length) {
        // 勾选
        arr = this.arrayDifference(
          this.dataResourceList,
          this.oldDataResourceList
        )
      }
      if (arr.length > 0) {
        if (Object.prototype.hasOwnProperty.call(this.relationObject, arr[0])) {
          // 可选机构
          if (this.transferFlag) {
            // 取消勾选可选机构
            if (this.ransferValue.length > 0) {
              this.dataResourceList = []
              this.dataResourceList = JSON.parse(
                JSON.stringify(this.oldDataResourceList)
              )
              this.$message.error('已添加可选机构，请先删除！')
            } else {
              this.relationObject[arr[0]].rights = this.ransferValue
              this.transferFlag = false
              this.oldDataResourceList = []
              this.oldDataResourceList = JSON.parse(
                JSON.stringify(this.dataResourceList)
              )
            }
          } else {
            // 勾选可选机构
            if (
              this.dataResourceCheckboxList &&
              this.dataResourceCheckboxList.length > 0
            ) {
              this.dataResourceCheckboxList.forEach(item => {
                if (this.dataResourceList.indexOf(item.id) >= 0) {
                  this.transferFlag = true
                  this.oldDataResourceList = []
                  this.oldDataResourceList = JSON.parse(
                    JSON.stringify(this.dataResourceList)
                  )
                  this.getOrganizationList(item)
                }
              })
            }
          }
        } else {
          // 勾选其他
          this.oldDataResourceList = []
          this.oldDataResourceList = JSON.parse(
            JSON.stringify(this.dataResourceList)
          )
        }
      } else {
        // 打开弹框
        if (
          this.dataResourceCheckboxList &&
          this.dataResourceCheckboxList.length > 0
        ) {
          this.dataResourceCheckboxList.forEach(item => {
            if (
              this.dataResourceList.indexOf(item.id) >= 0 &&
              Object.prototype.hasOwnProperty.call(this.relationObject, item.id)
            ) {
              this.transferFlag = true
              this.getOrganizationList(item)
            }
          })
        }
      }
    },
    // 两个数组的差集 a-b
    arrayDifference (a, b) {
      const clone = a.slice(0)
      for (let i = 0; i < b.length; i++) {
        const temp = b[i]
        for (let j = 0; j < clone.length; j++) {
          if (temp === clone[j]) {
            clone.splice(j, 1)
          }
        }
      }
      const arr = clone.filter(function (element, index, self) {
        // 去重
        return self.indexOf(element) === index
      })
      return arr
    },
    // 获取所属机构
    getOrganizationList (item) {
      const params = {
        resourceId: item.id
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/role/listOrgForDataRight',
          params
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.ransferData = response.data
            if (
              this.dataResourceCheckboxList &&
              this.dataResourceCheckboxList.length > 0
            ) {
              this.dataResourceCheckboxList.forEach(item => {
                if (this.dataResourceList.indexOf(item.id) >= 0) {
                  if (
                    Object.prototype.hasOwnProperty.call(this.relationObject, item.id) &&
                    Object.prototype.hasOwnProperty.call(this.relationObject[item.id], 'rights') &&
                    this.relationObject[item.id].rights.length > 0
                  ) {
                    this.ransferValue = this.relationObject[item.id].rights
                  }
                }
              })
            }
          }
        })
    },
    save () {
      this.dialogParam.visible = false
      if (
        this.dataResourceCheckboxList &&
        this.dataResourceCheckboxList.length > 0
      ) {
        this.dataResourceCheckboxList.forEach(item => {
          if (this.dataResourceList.indexOf(item.id) >= 0) {
            if (Object.prototype.hasOwnProperty.call(this.relationObject, item.id)) {
              this.relationObject[item.id].rights = this.ransferValue
            }
          }
        })
      }
      this.$emit('updateDataSources', { relationObject: this.relationObject, dataResourceList: this.dataResourceList })
    }
  }
}
</script>
