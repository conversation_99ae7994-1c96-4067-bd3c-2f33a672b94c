/** * Created by yuxuan on 2021/9/26 */
<template>
  <div class="webbas"
       v-loading="!bodyShow"
       :class="tabCell ? 'tabCellHeight' : 'singleCellHeight'"
       style="overflow: hidden;overflow-y: scroll;border:1px solid #dee8f8;">
    <div v-loading="false"
         :empty-text="treeEmptyText"
         class="background-css"
         style="border: none;">
      <div class="webbas-tree-css">
        <el-tree ref="tree"
                 :data="authResourceDataContent"
                 :empty-text="treeEmptyText"
                 :props="{ children: 'children', label: 'text' }"
                 :default-checked-keys="authResourceCheckData"
                 :render-content="renderContent"
                 node-key="id"
                 show-checkbox
                 default-expand-all
                 highlight-current
                 @check="checkNodes"
                 @node-click="resourceNode">
        </el-tree>
      </div>
    </div>
    <dataSourceDialog v-if="dialogParam.visible"
                      :dialogParam="dialogParam"
                      @updateDataSources="updateDataSources"></dataSourceDialog>
  </div>
</template>

<script>
// import Vue from 'vue'
import dataSourceDialog from './dataSourceDialog'
export default {
  name: 'NewAuthResource',
  components: { dataSourceDialog },
  props: {
    tabCell: {
      type: Boolean,
      default: false
    },
    bodyShow: {
      type: Boolean,
      default: true
    },
    relationObject: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dependency: {
      type: Object,
      default: () => {
        return {}
      }
    },
    authResourceCheckData: {
      type: Array,
      default: () => {
        return []
      }
    },
    authResourceDataContent: {
      type: Array,
      default: () => {
        return []
      }
    },
    dataResourceList: {
      type: Array,
      default: () => {
        return []
      }
    },
    oldData: { // 不可点击的选中数据
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      dataResourceListCell: this.dataResourceList,
      dataResourceCheckboxList: [],
      // dependency: {},
      currentRow: null,
      treeEmptyText: '加载中···',
      authResourceData: [],
      // authResourceCheckData: [],
      relationObjectCell: {},
      name: '',
      dialogParam: {
        visible: false
      }
    }
  },
  computed: {},
  watch: {
    authResourceDataContent () {
      this.initLebelStyle()
    }
  },
  created () { },
  mounted () {
    this.initLebelStyle()
  },
  methods: {
    initLebelStyle () {
      this.$nextTick(() => {
        // 叶子元素的div添加样式
        // treeNodeLeaf是上面的最底层节点的class属性的名字 previousSibling
        var treeNodeLeaf = document.getElementsByClassName('treeNodeLeaf')
        setTimeout(function () {
          // 等到树都加载完了再去执行的这里
          for (var i = 0; i < treeNodeLeaf.length; i++) {
            // 点击节点时会重写className改为手动设置内联样式
            treeNodeLeaf[i].parentElement.parentElement.style.float = 'left'
            treeNodeLeaf[i].parentElement.parentElement.style.width = '180px'
          }
        }, 0)
      })
    },
    resourceNode (nodeObj, nodeData, node) {
      console.log('resourceNode', nodeObj, nodeData, node)
      const dataKey = this.$refs.tree.getCheckedKeys() || []
      if (nodeObj.type === '3') {
        this.dataResourceCheckboxList = nodeObj.dataRights || []
        this.dialogParam = {
          visible: dataKey.indexOf(nodeObj.id) > -1 && this.dataResourceCheckboxList.length !== 0,
          dataResourceList: this.dataResourceListCell,
          relationObject: this.relationObject,
          dataResourceCheckboxList: this.dataResourceCheckboxList
        }
      }
    },
    //  添加权限依赖relation
    checkNodes (data, { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }) {
      const flag = checkedKeys.includes(data.id)
      const _this = this
      // 获取当前选中的key
      const dataKey = this.$refs.tree.getCheckedKeys() || []
      let isAllRollBack = false // 最终选中节点id
      const allRollBackKeys = [] // 最终选中节点id
      checkChildrenNode(data)
      // 需要回滚---更新最新勾选数据（含联动勾选，取消勾选，回滚勾选等交互）
      this.$refs.tree.setCheckedKeys(isAllRollBack ? [...dataKey, ...allRollBackKeys] : dataKey)
      // 更新选择后的数据
      this.updatecheckedData(this.relationObject)
      // 逐级检查子孙节点
      function checkChildrenNode (nodeData) {
        isAllRollBack = _this.checkChange(nodeData, flag, dataKey) || isAllRollBack
        allRollBackKeys.push(nodeData.id)
        if (nodeData.children && nodeData.children.length > 0) {
          nodeData.children.forEach(child => {
            checkChildrenNode(child)
          })
        }
      }
    },
    /**
     * data 当前节点
     * flag 当前节点是否选中
     * 当前所有选中的节点
     */
    checkChange (data, flag, dataKey) {
      let checkResult = false
      if (data.type === '3' && !flag && Object.prototype.hasOwnProperty.call(data, 'children')) {
        // 数据权限
        for (let j = 0; j < data.dataRights.length; j++) {
          this.dataResourceListCell = this.dataResourceListCell.filter(
            (item) => item !== data.dataRights[j].id
          )
        }
      }
      let cancelFlag = true
      // 只判断叶子节点
      if (data.leaf || (data.dataRights && data.children.length === 0)) {
        // 节点是否被勾选
        if (flag || dataKey.indexOf(data.id) > 0) {
          data.relation &&
            (() => {
              data.relation.split(',').forEach((e) => { dataKey.indexOf(e) > -1 || dataKey.push(e) })
            })()
        } else {
          this.dependency[data.id] &&
            (() => {
              const dependencyData = this.dependency[data.id]
              for (let i = 0; i < dependencyData.length; i++) {
                cancelFlag = dataKey.indexOf(dependencyData[i]) === -1
                if (!flag && !cancelFlag) {
                  dataKey.push(data.id)
                  checkResult = true
                  data.checked = true
                  this.$message.error('查询权限不可取消，需取消其他操作权限后才能取消查询权限！')
                  return
                }
              }
            })()
        }
        this.oldData.forEach((e) => {
          if (e.parentId === data.parentId) {
            dataKey.indexOf(e.id) > -1 || dataKey.push(e.id)
          }
        })
      }
      return checkResult
    },
    /**
     * data: {
     * type：1-menu菜单；3-button按钮
     * }
     */
    renderContent (h, { node, data, store }) {
      const inner_span = h('span', {
        domProps: {
          innerHTML: node.label,
          title: node.label
        }
      })
      const flag = data.type === '3' && data.dataRights && data.dataRights.length !== 0 &&
        data.dataRights.every((item) => item.type === '6')
      const yuechi_icon = h('i', {
        class: flag ? 'iconfont el-icon-yuechi' : '',
        style: flag ? { 'font-size': '12px', 'margin-left': '2px' } : ''
      })
      // 这里要剔除掉首页这个特例
      const out_span = node.isLeaf === true && node.data.id !== this.$pageConfig.homePage.pageId
        ? h('span', { class: 'levelname treeNodeLeaf', style: { color: flag ? '#70B603' : '' } }, [yuechi_icon, inner_span])
        : h('span', { class: 'levelname treeNodeOthers' }, [yuechi_icon, inner_span])
      return out_span
    },
    updateDataSources ({ relationObject, dataResourceList }) {
      if (relationObject) this.relationObjectCell = relationObject
      this.dataResourceListCell = dataResourceList
      this.updatecheckedData(this.relationObjectCell)
    },
    getCheckedData () {
      const checkedNodesList = JSON.parse(
        JSON.stringify(this.$refs.tree.getCheckedNodes(false, true))
      ) || []
      return checkedNodesList
    },
    updatecheckedData (relationObject) {
      // const checkedList = this.getCheckedData()
      this.$emit('updateAuthResources', { checkedList: [], relationObject, dataResourceList: this.dataResourceListCell })
    }
  }
}
</script>
<style lang="scss" scoped>
.webbas {
  .tabCellHeight {
    height: calc(100vh - 245px);
  }
  .singleCellHeight {
    height: calc(100vh - 201px);
  }
}
</style>
