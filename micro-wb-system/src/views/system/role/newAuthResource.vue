/** * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/9/26 */
<template>
  <div class="webbas">
    <div style="background-color:#fcf8e3;padding: 10px; margin-bottom: 8px;">
      选中并置灰的权限当前账号无权修改，如需修改请联系管理员。
    </div>
    <div class="background-css">
      <div class="webbas-tree-css">
        <div class="tree-title">
          给角色<span>【{{ $route.query.row.name }}】</span>分配权限
        </div>
        <el-tree ref="tree"
                 :data="authResourceDataContent"
                 :empty-text="treeEmptyText"
                 :props="{ children: 'children', label: 'text' }"
                 :default-checked-keys="authResourceCheckData"
                 :render-content="renderContent"
                 node-key="id"
                 show-checkbox
                 default-expand-all
                 highlight-current
                 @check-change="checkChange"
                 @node-click="resourceNode">
        </el-tree>
      </div>
      <div class="center_button">
        <asp-btn-solid :loading="submitStatus"
                       icon="el-icon-check"
                       name="保存"
                       @click="authResourceSubmit">
        </asp-btn-solid>
        <asp-btn-hollow icon="el-icon-close"
                        name="取消"
                        @click="back()">
        </asp-btn-hollow>
      </div>
    </div>
    <asp-dialog v-model="dialogParam.visible"
                :visible.sync="dialogParam.visible"
                :title="'数据权限'"
                width="60%">
      <template>
        <el-row>
          <el-col>
            <el-checkbox-group v-model="dataResourceList"
                               @change="handleCheckChange">
              <el-checkbox v-for="item in dataResourceCheckboxList"
                           ref="dataRightCheckbox"
                           :key="item.id"
                           :value="item.id"
                           :label="item.id"
                           :disabled="item.disabled ? true : false">{{ item.text }}</el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-transfer v-if="transferFlag"
                         ref="ransfer"
                         v-model="ransferValue"
                         :data="ransferData"
                         :titles="['全选', '全选']"
                         :button-texts="['移除', '添加']"
                         :props="{ key: 'id', label: 'name' }"
                         filter-placeholder="条件过滤..."
                         filterable></el-transfer>
          </el-col>
        </el-row>
      </template>
      <template slot="footer-center">
        <asp-btn-hollow icon="el-icon-close"
                        name="取消"
                        @click="dialogParam.visible = false">
        </asp-btn-hollow>
        <asp-btn-solid icon="el-icon-check"
                       name="确定"
                       @click="save()">
        </asp-btn-solid>
      </template>
    </asp-dialog>
  </div>
</template>

<script>
// import Vue from 'vue'
export default {
  name: 'NewAuthResource',
  components: {},
  props: {},
  data () {
    return {
      dependency: {},
      currentRow: null,
      submitStatus: false,
      treeEmptyText: '加载中···',
      authResourceData: [],
      authResourceDataContent: [],
      authResourceCheckData: [],
      relationObject: {},
      name: '',
      oldData: [], // 不可点击的选中数据
      dialogParam: {
        visible: false
      },
      oldDataResourceList: [], // 所有勾选数据
      dataResourceList: [],
      dataResourceCheckboxList: [],
      transferFlag: false, // 控制可搜索穿梭框显示
      ransferData: [], // 可搜索穿梭框元数据
      ransferValue: [], // 可搜索穿梭框取值
      isBack: true // 是否直接返回
    }
  },
  computed: {
    // /system/newAuthResource: 系统角色管理的分配角色路由；/system/newAuthResourceForDept：机构角色管理的分配角色路由
    urlPart () {
      return this.$route.path === '/system/newAuthResourceForDept'
        ? '/roleOrganization'
        : '/role'
    }
  },
  watch: {
    'dialogParam.visible' (val) {
      if (val) {
        this.ransferValue = []
        this.transferFlag = false
        if (this.dataResourceList.length > 0) {
          this.oldDataResourceList = []
          this.oldDataResourceList = JSON.parse(
            JSON.stringify(this.dataResourceList)
          )
          this.handleCheckChange()
        }
      }
    }
  },
  created () {
    this.init(this.$route.query)
  },
  mounted () { },
  methods: {
    init (val) {
      // 初始化树形结构
      this.authResourceDataContent = []
      // 提交按钮 loading 状态
      this.submitStatus = false
      const listRoleResUrl = this.$apiConfig.managerPathPrefix + this.urlPart + '/listRoleResourceTree'
      this.$aspHttps.asp_PostForm(listRoleResUrl, { roleId: val.row.id }).then(response => {
        if (this.$reponseStatus(response)) {
          if (!response.data.treeNodes || response.data.treeNodes.length === 0) {
            this.treeEmptyText = '无可分配的权限'
            response.data.treeNodes = []
          }
          this.authResourceData = response.data.treeNodes
          this.authResourceDataContent = response.data.treeNodes
          this.relationObject = response.data.rightExtends
          this.authResourceCheckData = []
          this.oldData = []
          for (let i = 0; i < this.authResourceDataContent.length; i++) {
            this.initDataResource(this.authResourceDataContent[i])
          }
          for (let i = 0; i < this.authResourceData.length; i++) {
            this.initAuthResource(this.authResourceData[i])
          }
          this.name = val.row.name
          this.$nextTick(() => {
            // 叶子元素的div添加样式
            // levelname是上面的最底层节点的class属性的名字 previousSibling
            var _levelname = document.getElementsByClassName('levelname')
            setTimeout(function () {
              // 等到树都加载完了再去执行的这里
              for (var i = 0; i < _levelname.length; i++) {
                // 最底层的节点，包括多选框和名字都让他左浮动
                _levelname[i].parentNode.parentNode.style.cssFloat = 'left'
                _levelname[i].parentNode.parentNode.style.styleFloat = 'left'
                if (_levelname[i].parentNode.offsetWidth > 80) {
                  const node = _levelname[i].childNodes[0]
                  node.setAttribute('title', node.innerHTML)
                }
                if (
                  _levelname[i].parentNode.parentNode.parentNode
                    .previousSibling.style
                ) {
                  const parentPad = _levelname[
                    i
                  ].parentNode.parentNode.parentNode.previousSibling.style.paddingLeft.substring(
                    0,
                    2
                  )
                  if (parseInt(parentPad) === 0) {
                    _levelname[i].parentNode.style.paddingLeft = '36px'
                    _levelname[i].parentNode.style.width = '130px'
                  } else {
                    _levelname[i].parentNode.style.paddingLeft = '54px'
                    _levelname[i].parentNode.style.width = '130px'
                    // 四级，含有五级
                    if (parseInt(parentPad) === 36) {
                      _levelname[
                        i
                      ].parentNode.parentNode.parentNode.previousSibling.style.paddingLeft =
                        '54px'
                      _levelname[i].parentNode.style.paddingLeft = '90px'
                    }
                    if (parseInt(parentPad) === 54) {
                      _levelname[i].parentNode.style.paddingLeft =
                        parseInt(parentPad) + 36 + 'px'
                    }
                    // 五级
                    const parentPad2 = _levelname[
                      i
                    ].parentNode.parentNode.parentNode.parentNode.parentNode.previousSibling.style.paddingLeft.substring(
                      0,
                      2
                    )
                    if (parseInt(parentPad2) === 54) {
                      _levelname[
                        i
                      ].parentNode.parentNode.parentNode.previousSibling.style.paddingLeft =
                        '90px'
                      _levelname[i].parentNode.style.paddingLeft = '126px'
                    }
                  }
                }
              }
            }, 0)
          })
        }
      })
    },
    authResourceSubmit () {
      const checkedNodesList = JSON.parse(
        JSON.stringify(this.$refs.tree.getCheckedNodes(false, true))
      )
      const message = '已成功分配！'
      let resourceIds = []
      checkedNodesList.forEach(function (item) {
        if (item) {
          resourceIds.push(item.id)
        }
      })
      const rightExtends = this.relationObject

      const authResourceParams = {
        roleId: this.$route.query.row.id,
        listResource: (resourceIds = resourceIds.concat(this.dataResourceList)),
        rightExtends: rightExtends
      }

      this.submitStatus = true
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix +
          this.urlPart +
          '/updateRoleResource',
          authResourceParams
        )
        .then(response => {
          this.submitStatus = false
          if (this.$reponseStatus(response)) {
            this.$message.success(message)
            this.delFromCache = true // 是否清除当前页面的路由缓存，当为true时清除；
            this.delToCache = true // 是否清除即将前往的那个页面的路由缓存，当为true时清除；
            this.back()
          }
        })
    },
    // 初始化树结构数据，筛选出数据权限
    initDataResource (data) {
      // 当leaf是false,说明存在children，这里id为String，只选父级无效
      if (
        data.type === '3' &&
        data.children &&
        data.children.length !== 0 &&
        data.children.every(item => item.type === '6')
      ) {
        data.children.forEach(item => {
          if (item.checked === true || item.checked === 'true') {
            this.dataResourceList.push(item.id)
          }
        })
        data.dataRights = data.children || []
        data.children = []
      } else if (Object.prototype.hasOwnProperty.call(data, 'children') && data.children) {
        for (let i = 0; i < data.children.length; i++) {
          this.initDataResource(data.children[i])
        }
      }
    },
    // 初始化授权数据
    initAuthResource (data) {
      if (data.type === '3' && (data.checked === true || data.checked === 'true')) {
        this.authResourceCheckData.push(data.id)
      }
      // 当leaf是false,说明存在children，这里id为String，只选父级无效
      // 注：当button上绑定了数据权限时leaf会设置为false，数据权限放在children中传给前端，所以判断是否叶子节点时还需判断数据权限的button
      if (
        data.leaf.toString() === 'true' ||
        (data.dataRights && data.children && data.children.length === 0)
      ) {
        if (data.checked === true || data.checked === 'true') {
          this.authResourceCheckData.push(data.id)
          // 存储不可点击的选中数据
          if (data.disabled) {
            this.oldData.push(data)
          }
        }
        // 设置保存节点依赖关系
        data.relation &&
          (data => {
            data.relation.split(',').forEach(e => {
              this.dependency[e] = this.dependency[e] ? this.dependency[e] : []
              this.dependency[e].push(data.id)
            })
          })(data)
      } else if (data.children) {
        for (let i = 0; i < data.children.length; i++) {
          this.initAuthResource(data.children[i])
        }
      }
    },
    resourceNode (nodeObj, nodeData, node) {
      const dataKey = this.$refs.tree.getCheckedKeys() || []
      if (nodeObj.type === '3') {
        this.dataResourceCheckboxList = nodeObj.dataRights || []
        this.dialogParam.visible =
          dataKey.indexOf(nodeObj.id) > -1 &&
          this.dataResourceCheckboxList.length !== 0
      }
    },
    //  添加权限依赖relation
    checkChange (data, flag) {
      if (data.type === '3' && !flag && Object.prototype.hasOwnProperty.call(data, 'dataRights') && data.dataRights) {
        for (let j = 0; j < data.dataRights.length; j++) {
          this.dataResourceList = this.dataResourceList.filter(
            item => item !== data.dataRights[j].id
          )
        }
      }
      // 获取当前选中的key
      const dataKey = this.$refs.tree.getCheckedKeys() || []
      let cancelFlag = true
      // 只判断叶子节点
      // setTimeout(() =>{ // 注释掉，影响relation勾选，会弹出下面的error信息
      if (data.leaf || (data.dataRights && data.children && data.children.length === 0)) {
        // 节点是否被勾选
        if (flag || dataKey.indexOf(data.id) > 0) {
          data.relation &&
            (() => {
              data.relation.split(',').forEach(e => {
                dataKey.indexOf(e) > -1 || dataKey.push(e)
              })
            })()
        } else {
          this.dependency[data.id] &&
            (() => {
              const dependencyData = this.dependency[data.id]
              for (let i = 0; i < dependencyData.length; i++) {
                cancelFlag = dataKey.indexOf(dependencyData[i]) === -1
                if (!flag && !cancelFlag) {
                  dataKey.push(data.id)
                  // dataKey.push(data.parentId)
                  data.checked = true
                  // this.$refs.tree.setCheckedKeys(dataKey)
                  this.$message.error(
                    '查询权限不可取消，需取消其他操作权限后才能取消查询权限！'
                  )
                  return
                }
              }
            })()
        }
        this.oldData.forEach(e => {
          if (e.parentId === data.parentId) {
            dataKey.indexOf(e.id) > -1 || dataKey.push(e.id)
          }
        })
        // 默认选中首页
        /* if (dataKey && !(dataKey.indexOf('wb_01') > -1)) {
                                    dataKey.push('wb_01')
                                } */
        // 设置选中的key
        this.$refs.tree.setCheckedKeys(dataKey)
      }
      // },0)
    },
    renderContent (h, { node, data, store }) {
      const inner_span = h('span', {
        domProps: {
          innerHTML: node.label,
          title: node.label
        }
      })
      const flag =
        data.type === '3' &&
        data.dataRights &&
        data.dataRights.length !== 0 &&
        data.dataRights.every(item => item.type === '6')
      const yuechi_icon = h('i', {
        class: flag ? 'iconfont el-icon-yuechi' : '',
        style: flag ? { 'font-size': '12px', 'margin-left': '2px' } : ''
      })
      // 这里要剔除掉首页这个特例
      const out_span =
        node.isLeaf === true && node.data.id !== this.$pageConfig.homePage.pageId
          ? h(
            'span',
            {
              class: 'levelname',
              style: { color: flag ? '#70B603' : '' }
            },
            [yuechi_icon, inner_span]
          )
          : h('span', {}, [yuechi_icon, inner_span])
      return out_span
    },
    // 两个数组的差集 a-b
    arrayDifference (a, b) {
      const clone = a.slice(0)
      for (let i = 0; i < b.length; i++) {
        const temp = b[i]
        for (let j = 0; j < clone.length; j++) {
          if (temp === clone[j]) {
            clone.splice(j, 1)
          }
        }
      }
      const arr = clone.filter(function (element, index, self) {
        // 去重
        return self.indexOf(element) === index
      })
      return arr
    },
    handleCheckChange () {
      let arr = [] // 当前勾选数据
      if (this.oldDataResourceList.length > this.dataResourceList.length) {
        // 取消勾选
        arr = this.arrayDifference(
          this.oldDataResourceList,
          this.dataResourceList
        )
      }
      if (this.dataResourceList.length > this.oldDataResourceList.length) {
        // 勾选
        arr = this.arrayDifference(
          this.dataResourceList,
          this.oldDataResourceList
        )
      }
      if (arr.length > 0) {
        if (Object.prototype.hasOwnProperty.call(this.relationObject, arr[0])) {
          // 可选机构
          if (this.transferFlag) {
            // 取消勾选可选机构
            if (this.ransferValue.length > 0) {
              this.dataResourceList = []
              this.dataResourceList = JSON.parse(
                JSON.stringify(this.oldDataResourceList)
              )
              this.$message.error('已添加可选机构，请先删除！')
            } else {
              this.relationObject[arr[0]].rights = this.ransferValue
              this.transferFlag = false
              this.oldDataResourceList = []
              this.oldDataResourceList = JSON.parse(
                JSON.stringify(this.dataResourceList)
              )
            }
          } else {
            // 勾选可选机构
            if (
              this.dataResourceCheckboxList &&
              this.dataResourceCheckboxList.length > 0
            ) {
              this.dataResourceCheckboxList.forEach(item => {
                if (this.dataResourceList.indexOf(item.id) >= 0) {
                  this.transferFlag = true
                  this.oldDataResourceList = []
                  this.oldDataResourceList = JSON.parse(
                    JSON.stringify(this.dataResourceList)
                  )
                  this.getOrganizationList(item)
                }
              })
            }
          }
        } else {
          // 勾选其他
          this.oldDataResourceList = []
          this.oldDataResourceList = JSON.parse(
            JSON.stringify(this.dataResourceList)
          )
        }
      } else {
        // 打开弹框
        if (
          this.dataResourceCheckboxList &&
          this.dataResourceCheckboxList.length > 0
        ) {
          this.dataResourceCheckboxList.forEach(item => {
            if (
              this.dataResourceList.indexOf(item.id) >= 0 &&
              Object.prototype.hasOwnProperty.call(this.relationObject, item.id)
            ) {
              this.transferFlag = true
              this.getOrganizationList(item)
            }
          })
        }
      }
    },
    // 获取所属机构
    getOrganizationList (item) {
      const params = {
        resourceId: item.id
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/role/listOrgForDataRight',
          params
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.ransferData = response.data
            if (
              this.dataResourceCheckboxList &&
              this.dataResourceCheckboxList.length > 0
            ) {
              this.dataResourceCheckboxList.forEach(item => {
                if (this.dataResourceList.indexOf(item.id) >= 0) {
                  if (
                    Object.prototype.hasOwnProperty.call(this.relationObject, item.id) &&
                    Object.prototype.hasOwnProperty.call(this.relationObject[item.id], 'rights') &&
                    this.relationObject[item.id].rights.length > 0
                  ) {
                    this.ransferValue = this.relationObject[item.id].rights
                  }
                }
              })
            }
          }
        })
    },
    save () {
      this.dialogParam.visible = false
      if (
        this.dataResourceCheckboxList &&
        this.dataResourceCheckboxList.length > 0
      ) {
        this.dataResourceCheckboxList.forEach(item => {
          if (this.dataResourceList.indexOf(item.id) >= 0) {
            if (Object.prototype.hasOwnProperty.call(this.relationObject, item.id)) {
              this.relationObject[item.id].rights = this.ransferValue
            }
          }
        })
      }
    },
    // 返回
    back () {
      this.$router.back()
    }
  }
}
</script>
