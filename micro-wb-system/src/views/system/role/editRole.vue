<!--
props 说明
dialogParam
  {
    title: '新增角色',
    oper: 'add', // 操作类型
    from: 'role', // 页面来源(role=系统角色、roleForDept=部门角色)
    roleModelVisible: true, // 显示
    editRole: {
      id: '',
      roleKey: '', // 角色助记码
      roleType: '', // 角色类型
      name: '',
      domain: '', // 所属域
      description: '',
      organizationId: '' // 所属机构
    },
    organizationList: this.organizationList, // 所属机构下拉列表
    domainList: this.dictDomain // 所属域下拉列表
  }
-->
<template>
  <asp-dialog v-model="dialogParam.roleModelVisible"
              :visible.sync="dialogParam.roleModelVisible"
              :title="dialogParam.title"
              width="60%">
    <template>
      <el-form ref="editRole"
               :model="editRole"
               :rules="editRoleRules">
        <el-row class="el-collapse-130">
          <el-col :span="12">
            <el-form-item label="角色名称:"
                          prop="name">
              <el-input v-model="editRole.name"
                        :readonly="readonly"
                        placeholder="不能超过20个字符"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色类型:"
                          prop="roleType">
              <el-select v-model="editRole.roleType"
                         :disabled="editRoleStatus === 'detail' || readonly"
                         placeholder="请选择"
                         @change="roleTypeChange">
                <el-option v-for="item in dialogParam.dictRoleType"
                           :key="item.code"
                           :label="item.name"
                           :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-130">
          <el-col :span="12">
            <el-form-item v-if="!isRoleForDept"
                          label="所属域:"
                          prop="domain">
              <el-select v-model="editRole.domain"
                         :disabled="editRoleStatus === 'edit' || readonly || (editRoleStatus === 'add' && userDomain !== 'admin')"
                         placeholder="请选择"
                         @change="changeDomain">
                <el-option v-for="item in dialogParam.domainList"
                           :key="item.code"
                           :label="item.name"
                           :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色助记码:"
                          prop="roleKey">
              <el-input v-model="editRole.roleKey"
                        :readonly="readonly"
                        placeholder="不能超过20个字符"
                        auto-complete="off"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-130">
          <el-col :span="24">
            <el-form-item v-if="!isRoleForDept && showOid"
                          label="所属机构:"
                          prop="organizationId">
              <el-select v-model="editRole.organizationId"
                         :disabled="editRoleStatus === 'edit' || readonly"
                         placeholder="请选择"
                         filterable>
                <el-option v-for="item in dialogParam.organizationList"
                           :key="item.id"
                           :label="item.name"
                           :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="showRoleSensFlag"
                class="el-collapse-130">
          <el-col :span="24">
            <el-form-item label="敏感数据分级情况:"
                          prop="sensLevel">
              <el-select v-model="editRole.sensLevel"
                         :disabled="readonly"
                         placeholder="请选择"
                         filterable>
                <el-option v-for="item in sensLevelList"
                           :key="item.code"
                           :label="item.name"
                           :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="el-collapse-130">
          <el-col :span="24">
            <el-form-item label="角色描述:"
                          prop="description">
              <el-input v-model="editRole.description"
                        :readonly="readonly"
                        type="textarea"
                        placeholder="不能超过50个字符"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow name="取消"
                      icon="el-icon-close"
                      @click="dialogParam.roleModelVisible = false"></asp-btn-hollow>
      <asp-btn-solid v-show="!readonly"
                     name="保存"
                     icon="el-icon-check"
                     @click="editRoleSubmit('editRole')"></asp-btn-solid>
    </template>
  </asp-dialog>
</template>

<script>
export default {
  name: 'EditRole',
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      // organizationList: [],
      userDomain: '', // 当前登录用户所属的域
      editRoleStatus: '', // add为新增，edit为修改
      editRole: {
        id: '',
        roleKey: '',
        roleType: '',
        name: '',
        domain: '',
        description: '',
        organizationId: '',
        isBatchLoad: '0', // 默认不具备-0,
        sensLevel: ''
      },
      isRoleForDept: false,
      readonly: false, // 查看
      isClearValidate: false, // 是否清除校验提示展示语
      organizationList: [],
      editRoleRules: {
        roleKey: [
          { required: true, message: '该字段不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' },
          { validator: this.$main_tools.checkSpecial, trigger: 'blur' }
          //  { pattern: /^[0-9a-zA_Z]+$/, message: '只能输入英文、数字', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '该字段不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' },
          { validator: this.$main_tools.checkSpecial, trigger: 'blur' }
        ],
        roleType: [
          { required: true, message: '角色类型不能为空', trigger: 'change' }
        ],
        description: [
          { required: true, message: '角色描述不能为空', trigger: 'blur' },
          { max: 50, message: '输入不能超过50个字符', trigger: 'blur' }
        ],
        domain: [
          { required: true, message: '所属域不能为空', trigger: 'change' }
        ],
        organizationId: [
          { required: true, message: '所属机构不能为空', trigger: 'change' }
        ]
        // sensLevel: [
        //   { required: true, message: '敏感数据分级情况不能为空', trigger: 'change' }
        // ]
      }
    }
  },
  computed: {
    showOid: function () {
      return Number(this.editRole.roleType) === 2
    },
    sensLevelList () {
      const list = this.$aspUtils.getCodeValueByType(this, 'WB_ROLE_SENS_LEVEL_TYPE')
      return list
    },
    // 是否展示敏感数据分级情况/批量下载数据功能，0-不展示，1-展示
    showRoleSensFlag () {
      const flag = this.$aspUtils.getListAllObject(this).domainConfig.showRoleSensitiveFields
      return flag === '1'
    }
  },
  watch: {
    'dialogParam.roleModelVisible' (val) {
      this.readonly = this.dialogParam.readonly
      if (val) {
        this.isClearValidate = true
        if (this.showRoleSensFlag) {
          this.editRoleRules.sensLevel = [
            { required: true, message: '敏感数据分级情况不能为空', trigger: 'change' }
          ]
        }
        this.editRole = { ...this.dialogParam.editRole }
        this.editRoleStatus = this.dialogParam.oper
        this.isRoleForDept = this.dialogParam.from === 'roleForDept'
        if (this.dialogParam.editRole.id === '') {
          // 组织归属域名，当前用户是admin域就可选择，否则默认用户所属域
          this.editRole.domain = this.userDomain === 'admin' ? '' : this.userDomain
        }
        // 角色类型固定为部门角色
        if (this.isRoleForDept) {
          this.editRole.roleType = '2'
        }
        // setTimeout(() => {
        //   return this.$refs.editRole && this.$refs.editRole.clearValidate()
        // })
        // 详情和修改时请求
        if (this.editRoleStatus !== 'add' &&
          this.dialogParam.from !== 'roleForDept') this.getOrganizationList()
      }
    }
  },
  created () {
    const domain = this.$aspUtils.getDomainObject(this)
    this.userDomain = domain.userInfo.domain
  },
  methods: {
    // 改变角色类型
    roleTypeChange (val) {
      if (val === '2' && !this.isRoleForDept) {
        this.editRole.domain ? this.getOrganizationList() : (this.dialogParam.organizationList = [])
      }
      this.editRole.organizationId = ''
    },
    // 角色类型为机构角色时修改所属域，清空所属机构
    changeDomain (val) {
      if (this.showOid) {
        this.editRole.organizationId = ''
        this.getOrganizationList()
      }
    },
    editRoleSubmit (formName) {
      this.$refs.editRole.validate(valid => {
        if (valid) {
          let updateUrl = ''
          let messageText = ''
          if (this.editRoleStatus === 'add') {
            updateUrl = this.$apiConfig.managerPathPrefix + '/role/insert'
            if (this.isRoleForDept) {
              updateUrl = this.$apiConfig.managerPathPrefix + '/roleOrganization/insert'
            }
            messageText = '新增'
          } else {
            updateUrl = this.$apiConfig.managerPathPrefix + '/role/update'
            if (this.isRoleForDept) {
              updateUrl = this.$apiConfig.managerPathPrefix + '/roleOrganization/update'
            }
            messageText = '修改'
          }
          this.confirmSubmit(updateUrl, messageText)
        }
      })
    },
    confirmSubmit (updateUrl, messageText) {
      this.$confirm('确认要' + messageText + '此角色？', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            // 请求体
            this.$aspHttps.asp_Post(updateUrl, this.editRole).then(response => {
              if (this.$reponseStatus(response)) {
                this.$emit('search')
                this.dialogParam.roleModelVisible = false
                instance.confirmButtonLoading = false
                this.$message.success(messageText + '成功!')
                done() // 回调
              } else {
                instance.confirmButtonLoading = false
                done()
              }
            })
          }
          if (action === 'cancel') {
            if (instance.confirmButtonLoading === true) {
              this.$message({ type: 'warning', message: '请求中•••' })
            } else {
              done()
            }
          }
        }
      }).then(result => {
        if (result) return false
        // this.$message({type: 'success', message: messageText + '成功!'})
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消' + messageText })
      })
    },
    // 获取所属机构
    getOrganizationList () {
      const params = { domain: this.editRole.domain }
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/organization/listForCombo', params).then(response => {
        if (this.$reponseStatus(response)) {
          this.dialogParam.organizationList = response.data
        }
      })
    }
  }
}
</script>
