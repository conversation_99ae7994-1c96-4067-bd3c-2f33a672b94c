/** * Created by aspire on 2018/10/19. * 机构角色管理 */
<template>
  <div class="webbas">
    <template>
      <div class="list-page-content-css">
        <div class="query-area-content-css">
          <el-form ref="searchForm"
                   :inline="true"
                   :model="table.searchForm"
                   @submit.native.prevent>
            <el-row class="el-collapse-130">
              <el-col :span="6">
                <el-form-item prop="name"
                              label="角色名称：">
                  <el-input v-model.trim="table.searchForm.name"
                            placeholder=""
                            name="name"
                            @keyup.enter.native="search()">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col v-if="showRoleSensFlag"
                      :span="6">
                <el-form-item prop="sensLevel"
                              label="敏感数据分级情况：">
                  <asp-select-all v-model="table.searchForm.sensLevel"
                                  :code-list="sensLevelList"></asp-select-all>
                </el-form-item>
              </el-col>
              <el-col v-if="showRoleSensFlag"
                      :span="6">
                <el-form-item prop="isBatchLoad"
                              label="批量下载数据功能：">
                  <asp-select-all v-model="table.searchForm.isBatchLoad"
                                  :code-list="batchLoalList"></asp-select-all>
                </el-form-item>
              </el-col>
              <el-col :span="showRoleSensFlag ? 6 : 18">
                <el-form-item class="query-area-btn-css">
                  <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_070103' })"
                                 name="查询"
                                 icon="el-icon-search"
                                 @click="search()"></asp-btn-solid>
                  <asp-btn-hollow icon="el-icon-refresh"
                                  name="重置"
                                  @click="reset()"></asp-btn-hollow>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <asp-table ref="table"
                   :url="table.url"
                   :param="table.searchForm"
                   :prefix="table.prefix"
                   type="">
          <template slot="header">
            <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_070104' })"
                           name="新增"
                           icon="el-icon-plus"
                           @click="addRole"></asp-btn-solid>
            <asp-btn-hollow v-hasAuth="doAuth({ btnCode: 'wb_070108' })"
                            icon="el-icon-download"
                            name="导出"
                            @click="exportList">
            </asp-btn-hollow>
          </template>
          <asp-table-column prop="name"
                            min-width="150"
                            label="角色名称"
                            sort-key="name">
          </asp-table-column>
          <asp-table-column prop="description"
                            min-width="180"
                            show-overflow-tooltip
                            label="角色描述">
          </asp-table-column>
          <asp-table-column prop="roleKey"
                            min-width="100"
                            label="角色助记码">
          </asp-table-column>
          <asp-table-column v-if="showRoleSensFlag"
                            :formatter="dictSensLevel"
                            prop="sensLevel"
                            width="139"
                            label="敏感数据分级情况"></asp-table-column>
          <asp-table-column v-if="showRoleSensFlag"
                            :formatter="dictBatchLoad"
                            prop="isBatchLoad"
                            width="139"
                            label="批量下载数据功能"></asp-table-column>
          <asp-table-column :width="this.$aspFontSize.asp_ColButtonSize([2, 2, 2, 4])"
                            label="操作"
                            fixed="right">
            <template slot-scope="{ scope }">
              <asp-btn-text :disabled="scope.row.canModify === 0"
                            name="查看"
                            @click="detailRole(scope.$index, scope.row)">
              </asp-btn-text>
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_070105' })"
                            v-show="scope.row.originType == '0'"
                            :disabled="scope.row.canModify === 0"
                            name="修改"
                            @click="editRole(scope.$index, scope.row)">
              </asp-btn-text>
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_070106' })"
                            v-show="scope.row.originType == '0'"
                            :disabled="scope.row.canModify === 0"
                            name="删除"
                            @click="delRole(scope.row)">
              </asp-btn-text>
              <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_070107' })"
                            v-show="scope.row.originType == '0'"
                            name="分配权限"
                            @click="authResourceEvent(scope.$index, scope.row)">
              </asp-btn-text>
            </template>
          </asp-table-column>
        </asp-table>
      </div>
      <!-- 新增/修改角色 -->
      <editRole ref="editRole"
                :dialog-param="roleModelParam"
                @search="search"></editRole>
    </template>
  </div>
</template>

<script>
import editRole from './editRole'
export default {
  name: 'RoleForDept',
  components: { editRole },
  data () {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/roleOrganization/listPage',
        searchForm: {
          order: 'asc', // 顺序/倒序排列
          sortName: 'name', // 排序名称
          name: '',
          organizationId: '',
          roleDomain: '',
          isBatchLoad: '0', // 默认不具备-0,
          sensLevel: '0' // 默认非敏感-0
        }
      },
      urlPart: '',
      roleList: [],
      domainList: [],
      dictRoleList: [],
      roleModelParam: {}
    }
  },
  computed: {
    batchLoalList () {
      const list = this.$aspUtils.getCodeValueByType(this, 'WB_ROLE_IS_BATCH_LOAD_TYPE')
      return list
    },
    sensLevelList () {
      const list = this.$aspUtils.getCodeValueByType(this, 'WB_ROLE_SENS_LEVEL_TYPE')
      return list
    },
    // 是否展示敏感数据分级情况/批量下载数据功能，0-不展示，1-展示
    showRoleSensFlag () {
      const flag = this.$aspUtils.getListAllObject(this).domainConfig.showRoleSensitiveFields
      return flag === '1'
    }
  },
  beforeMount () {
    if (!this.showRoleSensFlag) {
      delete this.table.searchForm.sensLevel
      delete this.table.searchForm.isBatchLoad
    }
  },
  mounted () {
    // this.search()
    this.getDomainList()
    this.getDictRoleList()
  },
  methods: {
    /**
     * 导出excel
     * @method exportList
     */
    exportList (pageRestart) {
      // console.log(pageRestart)
      const listParams = {
        name: this.table.searchForm.name
        //   order: this.table.searchForm.order,
        //   sortName: this.table.searchForm.sortName,
        //   organizationId: this.table.searchForm.organizationId,
        //   roleDomain: this.table.searchForm.roleDomain
      }
      const param = Object.keys(listParams)
        .map(key => {
          return key + '=' + listParams[key]
        }).join('&')
      let url = this.$apiConfig.managerPathPrefix + this.urlPart + '/roleOrganization/export?' + param
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    /**
     * 按钮权限
     * @method doAuth
     * @param {object} opt
     * @return {object}
     */
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    search () {
      this.$nextTick(() => {
        this.$refs.table.asp_search()
      })
    },
    reset () {
      this.$refs.table.asp_reset()
    },
    // 新增角色
    addRole () {
      this.roleModelParam = {
        title: '新增角色',
        oper: 'add',
        from: 'roleForDept',
        roleModelVisible: true,
        editRole: {
          id: '',
          roleKey: '',
          roleType: '',
          name: '',
          domain: '',
          description: '',
          organizationId: '',
          isBatchLoad: '0', // 默认不具备-0,
          sensLevel: '0' // 默认非敏感-0
        },
        domainList: this.domainList,
        dictRoleType: this.dictRoleList
      }
    },
    // 修改角色
    editRole (index, row) {
      this.roleModelParam = {
        title: '修改角色',
        oper: 'edit',
        from: 'roleForDept',
        roleModelVisible: true,
        editRole: {
          id: row.id,
          roleKey: row.roleKey,
          roleType: row.roleType,
          domain: row.domain,
          name: row.name,
          description: row.description,
          organizationId: row.organizationId,
          isBatchLoad: row.isBatchLoad || '0', // 默认不具备-0
          sensLevel: row.sensLevel
        },
        domainList: this.domainList,
        dictRoleType: this.dictRoleList
      }
    },
    // 查看角色
    detailRole (index, row) {
      this.roleModelParam = {
        title: '查看角色',
        oper: 'detail',
        from: 'roleForDept',
        roleModelVisible: true,
        readonly: true,
        editRole: {
          id: row.id,
          roleKey: row.roleKey,
          roleType: row.roleType,
          domain: row.domain,
          name: row.name,
          description: row.description,
          organizationId: row.organizationId,
          isBatchLoad: row.isBatchLoad || '0', // 默认不具备-0
          sensLevel: row.sensLevel
        },
        domainList: this.domainList,
        dictRoleType: this.dictRoleList
      }
    },
    // 删除角色
    delRole (row) {
      const showStr = '确认删除此角色？'
      this.$aspMsgbox.confirm(this, showStr, function (arg, instance) {
        // console.log(arg, instance)
        const url = this.$apiConfig.managerPathPrefix + '/roleOrganization/delete'
        this.$aspHttps.asp_PostForm(url, { id: row.id }).then(response => {
          if (this.$reponseStatus(response)) {
            this.$message({ type: 'success', message: '删除成功!' })
            this.search()
          }
        })
      })
    },
    // 分配角色
    authResourceEvent (index, row) {
      this.$router.push({
        path: '/system/newAuthResourceForDept',
        query: {
          id: row.id,
          name: row.name
        }
        // query: { row: row }
      })
    },
    // 获取所属域
    getDomainList () {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    // 获取角色类型
    getDictRoleList () {
      this.dictRoleList = this.$aspUtils.getCodeValueByType(this, 'ROLE_TYPE')
    },
    dictBatchLoad (row) {
      const item = this.batchLoalList.find(val => val.code === row.isBatchLoad)
      return item ? item.name : ''
    },
    dictSensLevel (row) {
      const item = this.sensLevelList.find(val => val.code === row.sensLevel)
      return item ? item.name : ''
    }
  }
}
</script>
