<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form ref="searchForm"
                 :inline="true"
                 :model="table.searchForm"
                 @submit.native.prevent>
          <el-row class="el-collapse-98">
            <el-col :span="8">
              <el-form-item prop="name"
                            label="资源名称：">
                <el-input v-model.trim="table.searchForm.name"
                          placeholder></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="url"
                            label="访问地址：">
                <el-input v-model.trim="table.searchForm.url"
                          placeholder></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="type"
                            label="资源类别：">
                <el-select v-model="table.searchForm.type"
                           class="el-select"
                           placeholder>
                  <el-option label="全部"
                             value></el-option>
                  <el-option v-for="item in resourceList"
                             :disabled="item.code === '6'"
                             :key="item.code"
                             :label="item.name"
                             :value="item.code"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8"
                    class="el-collapse-98">
              <el-form-item label="所属域：">
                <asp-select-all v-model="table.searchForm.domain"
                                :code-list="domainList"></asp-select-all>
              </el-form-item>
            </el-col>
            <el-col :span="8"
                    class="el-collapse-98">
              <el-form-item label="更新时间：">
                <asp-date-range :start-date.sync="table.searchForm.updateDateStart"
                                :end-date.sync="table.searchForm.updateDateEnd"></asp-date-range>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="query-area-btn-css">
                <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_080103' })"
                               name="查询"
                               icon="el-icon-search"
                               @click="search()"></asp-btn-solid>
                <asp-btn-hollow icon="el-icon-refresh"
                                name="重置"
                                @click="reset()"></asp-btn-hollow>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <asp-table ref="table"
                 :url="table.url"
                 :param="table.searchForm"
                 :prefix="table.prefix"
                 type="">
        <template slot="header">
          <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_080105' })"
                         name="新增资源"
                         icon="el-icon-plus"
                         @click="add"></asp-btn-solid>
          <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_080117' })"
                         name="导入"
                         icon="el-icon-upload"
                         @click="importXml"></asp-btn-solid>
          <asp-btn-hollow v-hasAuth="doAuth({ btnCode: 'wb_080108' })"
                          name="导出"
                          icon="el-icon-download"
                          @click="exportList"></asp-btn-hollow>
        </template>
        <asp-table-column prop="id"
                          min-width="90"
                          label="资源ID">
        </asp-table-column>
        <asp-table-column :formatter="getResource"
                          width="80"
                          prop="type"
                          label="资源类别">
        </asp-table-column>
        <asp-table-column prop="name"
                          min-width="120"
                          show-overflow-tooltip
                          label="资源名称">
        </asp-table-column>
        <asp-table-column prop="parentId"
                          min-width="80"
                          label="父资源ID"></asp-table-column>
        <asp-table-column prop="url"
                          show-overflow-tooltip
                          label="访问地址"
                          min-width="150">
        </asp-table-column>
        <asp-table-column prop="module"
                          width="110"
                          label="所属领域模块"></asp-table-column>
        <asp-table-column :formatter="getDomain"
                          width="80"
                          prop="domain"
                          label="所属领域">
        </asp-table-column>
        <asp-table-column prop="dataRightNames"
                          label="数据权限名称"
                          min-width="150">
        </asp-table-column>
        <asp-table-column prop="updateDate"
                          label="更新时间"
                          width="155">
        </asp-table-column>
        <asp-table-column :width="this.$aspFontSize.asp_ColButtonSize([2, 2, 2, 2, 2])"
                          label="操作">
          <template slot-scope="{ scope }">
            <asp-btn-text name="查看"
                          @click="toDetail(scope.row)"></asp-btn-text>
            <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_080106' })"
                          name="修改"
                          @click="edit(scope.row)"></asp-btn-text>
            <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_080107' })"
                          name="删除"
                          @click="del(scope.row)"></asp-btn-text>
            <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_080109' })"
                          v-show="scope.row.status === 'enable'"
                          name="禁用"
                          @click="updateStatus(scope.row, 'disable')"></asp-btn-text>
            <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_080109' })"
                          v-show="scope.row.status === 'disable'"
                          name="有效"
                          @click="updateStatus(scope.row, 'enable')"></asp-btn-text>
          </template>
        </asp-table-column>
      </asp-table>
    </div>
    <!-- 新增/修改 -->
    <add :dialog-param="dialogParam"
         @search="search"></add>
    <!-- 查看 -->
    <detail :dialog-param="detailParam"></detail>
    <ImportResource :dialog-param="importParam"></ImportResource>
  </div>
</template>

<script>
import add from './add'
import detail from './detail'
import ImportResource from './import.vue'
export default {
  name: 'Resources',
  components: {
    add,
    detail,
    ImportResource
  },
  data () {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/resource/listPageResource',
        searchForm: {
          name: '',
          url: '',
          type: '',
          domain: '',
          updateDateStart: '',
          updateDateEnd: ''
        }
      },
      dialogParam: {
        title: '',
        isEdit: false,
        visible: false,
        data: {}
      },
      detailParam: {
        visible: false,
        isHistory: false,
        data: {}
      },
      importParam: {
        visible: false
      },
      resourceList: [],
      domainList: [],
      moduleList: []
    }
  },
  created () {
    this.getResourceList()
    this.getDomainList()
    this.getModuleList()
  },
  methods: {
    importXml () {
      this.importParam.visible = true
    },
    /**
     * 导出excel
     * @method exportList
     */
    exportList (pageRestart) {
      let params = ''
      const obj = this.table.searchForm
      for (const key in obj) {
        params += key + '=' + (obj[key] === undefined ? '' : obj[key]) + '&'
      }
      params = params.substring(0, params.length - 1)
      let url =
        this.$apiConfig.managerPathPrefix +
        '/resource/exportResource?' +
        params
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    doAuthArr (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCodeArr: opt.btnCodeArr }
    },
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    search () {
      this.$refs.table.asp_search()
    },
    reset () {
      this.$refs.table.asp_reset()
    },
    toDetail (row) {
      const url = this.$apiConfig.managerPathPrefix + '/resource/get'
      const param = { id: row.id }
      this.$aspHttps
        .asp_PostForm(url, param)
        .then(response => {
          this.detailParam.visible = true
          this.detailParam.data = response.data
        })
    },
    // 新增资源
    add () {
      this.dialogParam.title = '新增资源'
      this.dialogParam.isEdit = false
      this.dialogParam.visible = true
    },
    // 修改资源
    edit (row) {
      this.$aspHttps
        .asp_PostForm(
          this.$apiConfig.managerPathPrefix + '/resource/get',
          { id: row.id }
        )
        .then(response => {
          this.dialogParam.title = '编辑资源'
          this.dialogParam.isEdit = true
          this.dialogParam.visible = true
          this.dialogParam.data = response.data
        })
    },
    // 删除资源
    del (row) {
      this.$confirm('确认删除此资源？', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$aspHttps
            .asp_PostForm(
              this.$apiConfig.managerPathPrefix + '/resource/delete',
              { id: row.id }
            )
            .then(response => {
              if (this.$reponseStatus(response)) {
                this.$message.success('删除成功')
                this.search()
              }
            })
        })
        .catch(() => { })
    },
    updateStatus (row, status) {
      const param = {
        id: row.id,
        status: status
      }
      const url = this.$apiConfig.managerPathPrefix + '/resource/updateStatus'
      this.$aspHttps.asp_Post(url, param).then(response => {
        if (this.$reponseStatus(response)) {
          this.search()
        }
      })
    },
    getResourceList () {
      this.resourceList = this.$aspUtils.getCodeValueByType(this, 'RESOURCES_TYPE')
    },
    getDomainList () {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    getModuleList () {
      this.moduleList = this.$aspUtils.getCodeValueByType(
        this,
        'DOMAIN_MODULE_ALL'
      )
    },
    getResource (row) {
      const item = this.resourceList.find(val => val.code === row.type)
      return item ? item.name : '全部'
    },
    getDomain (row) {
      const item = this.domainList.find(val => val.code === row.domain)
      return item ? item.name : ''
    }
  }
}
</script>
