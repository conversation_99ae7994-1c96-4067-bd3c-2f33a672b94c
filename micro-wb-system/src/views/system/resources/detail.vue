/** * Created by TurboC on 2018/10/22. */

<template>
  <asp-dialog
    :visible.sync="dialogParam.visible"
    :title="'资源详情'"
    width="60%"
  >
    <section v-if="!dialogParam.isHistory">
      <center>
        <el-button
          :class="
            basicKey === '0' ? 'solid-with-icon-btn' : 'hollow-with-icon-btn'
          "
          @click="basicKey = '0'"
          >基本信息
        </el-button>
        <el-button
          :disabled="resourceForm.type !== '3'"
          :class="
            basicKey !== '0' ? 'solid-with-icon-btn' : 'hollow-with-icon-btn'
          "
          @click="basicKey = '1'"
          >权限设置
        </el-button>
      </center>
    </section>
    <el-form ref="resourceForm" :model="resourceForm">
      <section v-if="basicKey === '0'">
        <el-row>
          <el-col :span="12">
            <el-form-item label="资源ID：">
              {{
                dialogParam.isHistory
                  ? resourceForm.resourcesId
                  : resourceForm.id
              }}
            </el-form-item>
            <el-form-item label="资源名称：" prop="name">
              {{ resourceForm.name }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源描述：" prop="description">
              {{ resourceForm.description }}
            </el-form-item>
            <el-form-item label="所属领域：" prop="domain">
              {{ getDomain(resourceForm.domain) }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属领域模块：" prop="module">
              {{ resourceForm.module }}
            </el-form-item>
            <el-form-item label="父资源ID：" prop="parentId">
              {{ resourceForm.parentId }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源类别：" prop="type">
              {{ getResource(resourceForm.type) }}
            </el-form-item>
            <el-form-item label="资源KEY：" prop="resourceKey">
              {{ resourceForm.resourceKey }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="图标：" prop="icon">
              {{ resourceForm.icon }}
            </el-form-item>
            <el-form-item label="访问地址URL：" prop="url">
              {{ resourceForm.url }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单按钮依赖关系：" prop="relation">
              {{ resourceForm.relation }}
            </el-form-item>
            <el-form-item label="菜单列表展示顺序：" prop="orderKey">
              {{ resourceForm.orderKey }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="请求方法：" prop="method">
              {{ resourceForm.method }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="是否有拓展：" prop="hasExtend">
              {{ getHasExtend(resourceForm.hasExtend) || '否' }}
            </el-form-item>
          </el-col>
        </el-row>
      </section>
      <section v-if="!dialogParam.isHistory && basicKey === '1'">
        <div class="list-table-css">
          <el-table :data.sync="resourceForm.dataRights" border>
            <el-table-column
              prop="resourceKey"
              label="数据权限编码"
              min-width="60px"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="name"
              label="数据权限名称"
              min-width="60px"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="description"
              label="权限描述"
              min-width="60px"
              align="center"
            >
            </el-table-column>
          </el-table>
        </div>
      </section>
    </el-form>
    <div slot="footer">
      <center>
        <asp-btn-hollow
          name="取消"
          icon="el-icon-close"
          @click="dialogParam.visible = false"
        ></asp-btn-hollow>
      </center>
    </div>
  </asp-dialog>
</template>

<script>
export default {
  name: 'ResourceDetail',
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      resourceForm: {
        id: '',
        resourcesId: '',
        name: '',
        description: '',
        domain: '',
        module: '',
        parentId: '',
        type: '',
        resourceKey: '',
        icon: '',
        url: '',
        relation: '',
        orderKey: '',
        method: '',
        hasExtend: '0'
      },
      resourceList: [],
      domainList: [],
      basicKey: '0',
      dataList: [],
      extendOptionList: [
        { value: '0', label: '否' },
        { value: '1', label: '是' }
      ]
    }
  },
  watch: {
    'dialogParam.data': {
      handler(val) {
        this.resourceForm = Object.assign(this.resourceForm, { ...val })
      }
    },
    'dialogParam.visible'(val) {
      if (!val) {
        this.basicKey = '0'
      }
    }
  },
  created() {
    this.getResourceList()
    this.getDomainList()
  },
  methods: {
    // 获取资源类型
    getResourceList() {
      this.resourceList = this.$aspUtils.getCodeValueByType(this, 'RESOURCES_TYPE')
    },
    getDomainList() {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    getResource(code) {
      const item = this.resourceList.find(val => val.code === code)
      return item ? item.name : '全部'
    },
    getHasExtend(code) {
      const item = this.extendOptionList.find(val => val.code === code)
      return item ? item.label : '否'
    },
    getDomain(code) {
      const item = this.domainList.find(val => val.code === code)
      return item ? item.name : ''
    }
    // getMethod(key) {
    //     if (!key) return ''
    //     const state = { 0: 'GET', 1: 'POST' }
    //     return state[key]
    // },
  }
}
</script>
