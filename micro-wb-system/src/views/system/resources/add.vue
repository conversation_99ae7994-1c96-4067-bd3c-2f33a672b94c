/** * Created by aspire on 2018/10/22. */

<template>
  <asp-dialog
    :visible.sync="dialogParam.visible"
    :title="dialogParam.title"
    width="60%"
  >
    <section class="list-top-btn">
      <center>
        <el-button
          :class="
            basicKey === '0' ? 'solid-with-icon-btn' : 'hollow-with-icon-btn'
          "
          @click="changeButton('0')"
          >基本信息
        </el-button>
        <el-button
          :disabled="resourceForm.type !== '3'"
          :class="
            basicKey !== '0' ? 'solid-with-icon-btn' : 'hollow-with-icon-btn'
          "
          @click="changeButton('1')"
          >权限设置
        </el-button>
      </center>
    </section>
    <el-form ref="resourceForm" :model="resourceForm" :rules="resourceRules">
      <section v-show="basicKey === '0'">
        <el-row>
          <el-col :span="12">
            <el-form-item label="资源ID：" prop="id">
              <el-input
                v-model.trim="resourceForm.id"
                :disabled="dialogParam.isEdit || preserveFlag"
                placeholder="请输入资源ID"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源名称：" prop="name">
              <el-input
                v-model.trim="resourceForm.name"
                placeholder="请输入资源名称"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="资源描述：" prop="description">
              <el-input
                v-model.trim="resourceForm.description"
                placeholder="请输入资源描述"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属领域：" prop="domain">
              <el-select
                v-model="resourceForm.domain"
                :disabled="dialogParam.isEdit"
                placeholder="请选择"
                @change="domainChange"
              >
                <el-option
                  v-for="item in domainList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属领域模块：" prop="module">
              <el-select
                v-model="resourceForm.module"
                :disabled="dialogParam.isEdit"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in moduleList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="父资源ID：" prop="parentId">
              <el-input
                v-model.trim="resourceForm.parentId"
                placeholder="请输入父资源ID"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源类别：" prop="type">
              <el-select
                v-model="resourceForm.type"
                :disabled="dialogParam.isEdit"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in resourceList"
                  :disabled="item.code === '6'"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="资源KEY：" prop="resourceKey">
              <el-input
                v-model.trim="resourceForm.resourceKey"
                placeholder="请输入资源KEY"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="图标：" prop="icon">
              <el-input
                v-model.trim="resourceForm.icon"
                placeholder="请输入图标"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="访问地址URL：" prop="url">
              <el-input
                v-model.trim="resourceForm.url"
                placeholder="请输入访问地址URL"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单按钮依赖关系：" prop="relation">
              <el-input
                v-model.trim="resourceForm.relation"
                placeholder="请输入菜单按钮依赖关系"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="菜单列表展示顺序：" prop="orderKey">
              <el-input
                v-model.trim="resourceForm.orderKey"
                placeholder="不超过5位数字"
                maxlength="5"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求方法：" prop="method">
              <el-select v-model="resourceForm.method" placeholder="请选择">
                <el-option label="GET" value="GET"></el-option>
                <el-option label="POST" value="POST"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="是否有拓展：" prop="hasExtend">
              <!-- <el-input
                v-model.trim="resourceForm.orderKey"
                placeholder="不超过5位数字"
                maxlength="5"
              ></el-input> -->
              <el-radio-group :value="resourceForm.hasExtend"
                        @input="bindInput"
                        @change="bindRadioCompChange">
          <el-radio v-for="(cell, index) in extendOptionList"
                    :label="cell.value"
                    :value="cell.value"
                    :key="index"
                    :disabled="cell.disabled">{{cell.label}}
          </el-radio>
        </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </section>
      <section v-show="basicKey !== '0'" class="list-area-css">
        <div class="list-table-css">
          <el-table :data="resourceForm.dataRights" min-height="300" stripe>
            <!--<el-table-column min-width="70" label="数据权限类型">
                            <template slot-scope="scoped">
                                <el-form-item
                                    :prop="'dataRights.' + scoped.$index + '.dataRightType'"
                                >
                                    <el-select
                                        v-model="scoped.row.dataRightType"
                                        filterable
                                        allow-create
                                        default-first-option
                                        placeholder="&#45;&#45;请选择&#45;&#45;"
                                    >
                                        <el-option
                                            v-for="item in options"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        >
                                        </el-option>
                                    </el-select>
                                    &lt;!&ndash;<asp-select-opt v-model="scoped.row.dataRightType" :code-list="dataRightTypeList"></asp-select-opt>&ndash;&gt;
                                </el-form-item>
                            </template>
                        </el-table-column>-->
            <el-table-column min-width="70" label="数据权限编码">
              <template slot-scope="scoped">
                <el-form-item
                  :prop="'dataRights.' + scoped.$index + '.resourceKey'"
                  :rules="[
                    {
                      required: true,
                      validator: (rule, value, callback) => {
                        checkRepeatResource(
                          rule,
                          value,
                          callback,
                          'resourceKey',
                          '数据权限编码不能为空',
                          '数据权限编码不能重复'
                        );
                      },
                      trigger: 'blur'
                    }
                  ]"
                >
                  <el-input
                    v-model="scoped.row.resourceKey"
                    placeholder="不超过20个字符"
                    maxlength="20"
                    @blur="inputResource('resourceKey')"
                  >
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column min-width="70" label="数据权限名称">
              <template slot-scope="scoped">
                <el-form-item
                  :prop="'dataRights.' + scoped.$index + '.name'"
                  :rules="[
                    {
                      required: true,
                      validator: (rule, value, callback) => {
                        checkRepeatResource(
                          rule,
                          value,
                          callback,
                          'name',
                          '数据权限名称不能为空',
                          '数据权限名称不能重复'
                        );
                      },
                      trigger: 'blur'
                    }
                  ]"
                >
                  <el-input
                    v-model="scoped.row.name"
                    placeholder="不超过20个字符"
                    maxlength="20"
                    @blur="inputResource('name')"
                  >
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column min-width="100" label="权限描述">
              <template slot-scope="scoped">
                <el-form-item
                  :prop="'dataRights.' + scoped.$index + '.description'"
                >
                  <el-input
                    v-model="scoped.row.description"
                    placeholder="不超过100个字符"
                    maxlength="100"
                  >
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              :render-header="renderOperation"
              min-width="40"
              label="操作"
            >
              <template slot-scope="scoped">
                <el-button
                  type="text"
                  icon="el-icon-minus operation-cursor"
                  @click="delList(scoped.$index, scoped.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </section>
    </el-form>
    <div slot="footer">
      <center>
        <asp-btn-hollow
          icon="el-icon-close"
          name="取消"
          @click="cancel()"
        ></asp-btn-hollow>
        <asp-btn-solid
          :loading="loading"
          icon="el-icon-check"
          name="保存"
          @click="submit"
        ></asp-btn-solid>
      </center>
    </div>
  </asp-dialog>
</template>

<script>
export default {
  name: 'AddResource',
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      options: [],
      loading: false,
      resourceRules: {
        id: [
          { required: true, message: '资源ID不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' }
        ],
        parentId: [
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '资源名称不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' }
        ],
        type: [{ required: true, message: '请选择资源类别' }],
        domain: [{ required: true, message: '请选择资源领域' }],
        module: [{ required: true, message: '请选择资源领域模块' }],
        url: [
          { required: true, message: '访问地址URL不能为空', trigger: 'blur' },
          { max: 100, message: '输入不能超过100个字符', trigger: 'blur' }
        ],
        relation: [
          { max: 50, message: '输入不能超过50个字符', trigger: 'blur' }
        ],
        orderKey: [
          { pattern: /^[0-9]+$/, message: '只能输入数字', trigger: 'blur' }
        ],
        description: [
          { max: 100, message: '输入不能超过100个字符', trigger: 'blur' }
        ],
        icon: [{ max: 20, message: '输入不能超过20个字符', trigger: 'blur' }],
        resourceKey: [
          { required: true, message: '资源KEY不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' }
        ]
      },
      resourceForm: {
        id: '',
        name: '',
        description: '',
        domain: '',
        module: '',
        parentId: '',
        type: '',
        resourceKey: '',
        icon: '',
        url: '',
        relation: '',
        orderKey: '',
        method: '',
        dataRights: [],
        hasExtend: '0'
      },
      extendOptionList: [
        { value: '0', label: '否' },
        { value: '1', label: '是' }
      ],
      resourceList: [],
      domainList: [],
      moduleList: [],
      basicKey: '0',
      finishSubmit: {},
      preserveFlag: false
    }
  },
  watch: {
    'dialogParam.data': {
      handler(val) {
        this.$nextTick(() => {
          // 如果是修改操作，给表达赋值，如果是新增操作，则清空表单
          if (this.dialogParam.isEdit) {
            this.resourceForm = Object.assign(this.resourceForm, { ...val })
          } else {
            this.resourceForm = {
              id: '',
              name: '',
              description: '',
              domain: '',
              module: '',
              parentId: '',
              type: '',
              resourceKey: '',
              icon: '',
              url: '',
              relation: '',
              orderKey: '',
              method: '',
              dataRights: [],
              hasExtend: '0'
            }
          }
        })
      }
    },
    'dialogParam.visible'(val) {
      if (!val) {
        this.$refs.resourceForm.resetFields()
        this.preserveFlag = false
        this.basicKey = '0'
        this.resourceForm.dataRights = []
      }
    }
  },
  created() {
    this.getResourceList()
    this.getDomainList()
  },
  methods: {
    bindInput (e) {
      console.log('bindInput', e)
      this.resourceForm.hasExtend = e
    },
    bindRadioCompChange (e) {
      console.log('bindRadioCompChange', e)
      this.resourceForm.hasExtend = e
    },
    changeButton(val) {
      if (this.basicKey === val) return
      this.$confirm('是否保存当前页面？', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const flagSubmit =
            Object.keys(this.finishSubmit).length === 0 ||
            !this.$aspUtils.isEqualObj(this.finishSubmit, this.resourceForm)
          if (flagSubmit) {
            this.$refs.resourceForm.validate((valid, object) => {
              if (!valid) {
                const arr = [
                  'id',
                  'name',
                  'domain',
                  'module',
                  'resourceKey',
                  'url'
                ]
                const flag = Object.keys(object).some(item => {
                  return item.indexOf('dataRights') > -1
                })
                if (
                  this.basicKey === '1' &&
                  !flag &&
                  Object.keys(object).some(item => {
                    return arr.indexOf(item) > -1
                  })
                ) {
                  this.$message.warning('请完善基本信息！')
                }
                if (this.basicKey === '0' && flag) {
                  this.$message.warning('请完善权限设置信息！')
                }
                return
              }
              this.commonSubmit(true, val)
            })
          } else {
            this.basicKey = val
          }
        })
        .catch(() => {
          this.basicKey = val
        })
    },
    // 数据权限编码唯一校验
    checkRepeatResource(
      rule,
      value,
      callback,
      fieldName,
      emptyMessage,
      repeatMessage
    ) {
      const index = rule.field.split('.')[1]
      if (value === '') {
        return callback(new Error(emptyMessage))
      }
      if (this.equalResource(index, value, fieldName)) {
        return callback(new Error(repeatMessage))
      } else {
        return callback()
      }
    },
    equalResource(index, value, fieldName) {
      index = parseInt(index)
      return this.resourceForm.dataRights.some((val, i) => {
        return i === index ? false : val[fieldName] === value
      })
    },
    // 数据权限input校验
    inputResource(fieldName) {
      for (let i = 0; i < this.resourceForm.dataRights.length; i++) {
        this.$refs.resourceForm.validateField(
          'dataRights.' + i + '.' + fieldName
        )
      }
    },
    // 权限设置操作按钮
    renderOperation(h, { column }) {
      return h('div', [
        h('span', column.label + ' '),
        h('i', {
          class: 'el-icon-plus operation-cursor',
          on: {
            click: this.addList
          }
        })
      ])
    },
    // 权限设置增加行
    addList() {
      const obj = {
        // dataRightType: '',
        resourceKey: '',
        name: '',
        description: '',
        parentId: this.resourceForm.id,
        type: '6'
      }
      this.resourceForm.dataRights.push(obj)
    },
    // 权限设置删除行
    delList(index, row) {
      if (row.resourceKey === '' && row.name === '' && row.description === '') {
        this.resourceForm.dataRights.splice(index, 1)
      } else {
        this.$confirm(
          '确认是否删除当前数据权限行，删除后角色关联关系将取消？',
          {
            closeOnClickModal: false,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.resourceForm.dataRights.splice(index, 1)
            this.$refs.resourceForm.validate()
          })
          .catch(() => {})
      }
    },
    // 获取资源类型
    getResourceList() {
      this.resourceList = this.$aspUtils.getCodeValueByType(this, 'RESOURCES_TYPE')
    },
    // 获取所属域
    getDomainList() {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    domainChange(value) {
      this.resourceForm.module = ''
      this.moduleList = []
      const moduleName = 'DOMAIN_MODULE_' + value.toUpperCase()
      this.moduleList = this.$aspUtils.getCodeValueByType(this, moduleName)
    },
    submit() {
      this.$refs.resourceForm.validate((valid, object) => {
        if (!valid) {
          // 基本信息必填项，有更改需手动改变arr
          const arr = ['id', 'name', 'domain', 'module', 'resourceKey', 'url']
          const flag = Object.keys(object).some(item => {
            return item.indexOf('dataRights') > -1
          })
          if (
            this.basicKey === '1' &&
            !flag &&
            Object.keys(object).some(item => {
              return arr.indexOf(item) > -1
            })
          ) {
            this.$message.warning('请完善基本信息！')
          }
          if (this.basicKey === '0' && flag) {
            this.$message.warning('请完善权限设置信息！')
          }
          return
        }
        this.$confirm('确认(新增/编辑)此资源？', {
          closeOnClickModal: false,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.commonSubmit(false)
          })
          .catch(() => {})
      })
    },
    commonSubmit(flag, val) {
      const param = {}
      let requestUrl = ''
      if (
        this.resourceForm.type === '3' &&
        this.resourceForm.dataRights.length !== 0
      ) {
        this.resourceForm.dataRights.forEach(item => {
          item.parentId = this.resourceForm.id
        })
      }
      this.loading = true
      if (this.basicKey === '0') {
        requestUrl = this.dialogParam.isEdit
          ? '/resource/update'
          : '/resource/insert'
        param.id = this.resourceForm.id
        param.name = this.resourceForm.name
        param.description = this.resourceForm.description
        param.domain = this.resourceForm.domain
        param.module = this.resourceForm.module
        param.parentId = this.resourceForm.parentId
        param.type = this.resourceForm.type
        param.resourceKey = this.resourceForm.resourceKey
        param.icon = this.resourceForm.icon
        param.url = this.resourceForm.url
        param.relation = this.resourceForm.relation
        param.orderKey = this.resourceForm.orderKey
        param.method = this.resourceForm.method
        param.hasExtend = this.resourceForm.hasExtend
      }
      if (this.basicKey === '1') {
        if (
          this.resourceForm.dataRights.some(item => {
            return item.parentId === ''
          })
        ) {
          this.$message.warning('请完善基本信息的资源ID')
        }
        requestUrl = '/resource/insertDataRight'
        param.parentId = this.resourceForm.id
        param.dataRights = this.resourceForm.dataRights
      }
      this.$aspHttps
        .asp_Post(this.$apiConfig.managerPathPrefix + requestUrl, param)
        .then(response => {
          if (this.basicKey === '1' && response.status === '902') {
            this.$message.warning('请先保存基本信息')
          }
          if (this.$reponseStatus(response)) {
            this.$message.success('提交成功')
            this.finishSubmit = Object.assign({}, this.resourceForm)
            if (flag) {
              this.basicKey = val
            }
            if (this.basicKey === '0') {
              this.preserveFlag = true
            }
            this.dialogParam.visible = false
            this.$emit('search')
          }
          this.loading = false
        })
        .catch(() => (this.loading = false))
    },
    onClose(formName) {
      this.$refs[formName].resetFields()
      this.$emit('search')
    },
    cancel() {
      this.dialogParam.visible = false
      this.$refs.resourceForm.resetFields()
    }
  }
}
</script>
