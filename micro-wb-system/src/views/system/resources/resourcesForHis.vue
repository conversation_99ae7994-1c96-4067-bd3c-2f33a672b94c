<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form
          ref="searchForm"
          :inline="true"
          :model="table.searchForm"
          @submit.native.prevent
        >
          <el-row class="el-collapse-98">
            <el-col :span="8">
              <el-form-item prop="name" label="资源名称：">
                <el-input
                  v-model.trim="table.searchForm.name"
                  placeholder
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="操作类型：">
                <asp-select-all
                  v-model="table.searchForm.operatorType"
                  :code-list="operatorTypeList"
                ></asp-select-all>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="type" label="资源类别：">
                <el-select
                  v-model="table.searchForm.type"
                  class="el-select"
                  placeholder
                >
                  <el-option label="全部" value></el-option>
                  <el-option
                    v-for="item in resourceList"
                    :disabled="item.code === '6'"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8" class="el-collapse-98">
              <el-form-item label="所属域：">
                <asp-select-all
                  v-model="table.searchForm.domain"
                  :code-list="domainList"
                ></asp-select-all>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="el-collapse-98">
              <el-form-item label="更新时间：">
                <asp-date-range
                  :start-date.sync="table.searchForm.updateDateStart"
                  :end-date.sync="table.searchForm.updateDateEnd"
                ></asp-date-range>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="query-area-btn-css">
                <asp-btn-solid
                  v-hasAuth="doAuth({ btnCode: 'wb_080112' })"
                  name="查询"
                  icon="el-icon-search"
                  @click="search()"
                ></asp-btn-solid>
                <asp-btn-hollow
                  icon="el-icon-refresh"
                  name="重置"
                  @click="reset('searchForm')"
                ></asp-btn-hollow>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <asp-table
        ref="table"
        :url="table.url"
        :param="table.searchForm"
        :prefix="table.prefix"
        type=""
      >
        <template slot="header">
          <asp-btn-hollow
            v-hasAuth="doAuth({ btnCode: 'wb_080115' })"
            name="导出"
            icon="el-icon-download"
            @click="exportList"
          ></asp-btn-hollow>
        </template>
        <asp-table-column prop="resourcesId" min-width="90" label="资源ID">
        </asp-table-column>
        <asp-table-column
          :formatter="getResource"
          width="80"
          prop="type"
          label="资源类别"
        >
        </asp-table-column>
        <asp-table-column
          prop="name"
          min-width="120"
          show-overflow-tooltip
          label="资源名称"
        >
        </asp-table-column>
        <asp-table-column
          prop="parentId"
          min-width="80"
          label="父资源ID"
        ></asp-table-column>
        <asp-table-column
          prop="module"
          width="110"
          label="所属领域模块"
        ></asp-table-column>
        <asp-table-column
          :formatter="getDomain"
          width="80"
          prop="domain"
          label="所属领域"
        >
        </asp-table-column>
        <asp-table-column
          :formatter="getOperatorType"
          prop="operatorType"
          width="80"
          label="操作类型"
        >
        </asp-table-column>
        <asp-table-column prop="createDate" label="更新时间" width="155">
        </asp-table-column>
        <asp-table-column
          :width="this.$aspFontSize.asp_ColButtonSize([2])"
          label="操作"
        >
          <template slot-scope="{ scope }">
            <asp-btn-text
              name="查看"
              @click="toDetail(scope.row)"
            ></asp-btn-text>
          </template>
        </asp-table-column>
      </asp-table>
    </div>
    <!-- 查看 -->
    <detail :dialog-param="detailParam"></detail>
  </div>
</template>

<script>
import detail from './detail'
export default {
  name: 'ResourcesForHis',
  components: {
    detail
  },
  data() {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/resource/listPageResourceHisory',
        searchForm: {
          name: '',
          operatorType: '',
          type: '',
          domain: '',
          updateDateStart: '',
          updateDateEnd: ''
        }
      },
      detailParam: {
        visible: false,
        isHistory: true,
        data: {}
      },
      pagination: {
        page: 1,
        total: 0,
        pageSize: 10,
        selectPageSizes: [10, 20, 30, 40]
      },
      prePageSize: 10, // 上一次的pageSize，默认10
      resourceList: [],
      domainList: [],
      moduleList: [],
      operatorTypeList: [
        // 操作类型
        {
          code: '1',
          name: '新增'
        },
        {
          code: '2',
          name: '变更'
        },
        {
          code: '3',
          name: '删除'
        }
      ]
    }
  },
  created() {
    this.getResourceList()
    this.getDomainList()
    this.getModuleList()
  },
  methods: {
    doAuth(opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    exportList() {
      let params = ''
      const obj = this.table.searchForm
      for (const key in obj) {
        params += key + '=' + (obj[key] === undefined ? '' : obj[key]) + '&'
      }
      params = params.substring(0, params.length - 1)
      let url =
        this.$apiConfig.managerPathPrefix +
        '/resource/exportResourceHistory?' +
        params
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    search() {
      this.$refs.table.asp_search()
    },
    reset() {
      this.$refs.table.asp_reset()
    },
    toDetail(row) {
      const url = this.$apiConfig.managerPathPrefix + '/resource/getHistory'
      const param = { historyId: row.id }
      this.$aspHttps
        .asp_PostForm(url, param)
        .then(response => {
          this.detailParam.visible = true
          this.detailParam.data = response.data
        })
    },
    getResourceList() {
      this.resourceList = this.$aspUtils.getCodeValueByType(this, 'RESOURCES_TYPE')
    },
    getDomainList() {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    getModuleList() {
      this.moduleList = this.$aspUtils.getCodeValueByType(
        this,
        'DOMAIN_MODULE_ALL'
      )
    },
    getResource(row) {
      const item = this.resourceList.find(val => val.code === row.type)
      return item ? item.name : '全部'
    },
    getDomain(row) {
      const item = this.domainList.find(val => val.code === row.domain)
      return item ? item.name : ''
    },
    getOperatorType(row) {
      const item = this.operatorTypeList.find(
        val => val.code === row.operatorType
      )
      return item ? item.name : '全部'
    }
  }
}
</script>
