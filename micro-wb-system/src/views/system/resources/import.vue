/** * Created by TurboC on 2018/10/22. */
<template>
  <asp-dialog :visible.sync="dialogParam.visible"
              title="资源导入"
              width="60%">
    <el-form ref="importForm"
             :model="importForm"
             :rules="rules">
      <el-row class="el-collapse-130">
        <el-col :span="12">
          <el-form-item label="导入类型:"
                        prop="type">
            <asp-select-all v-model="importForm.type"
                            :isShowAll="false"
                            :code-list="resourceTypeList"></asp-select-all>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="选择文件:"
                        prop="file">
            <el-upload :auto-upload="false"
                       :file-list="fileList"
                       :on-exceed="handleExceed"
                       :on-change="handleChange"
                       :on-remove="handleRemove"
                       :show-file-list="showFlag"
                       :limit="1"
                       action="">
              <div>
                <el-button class="solid-with-icon-btn attachBtn"
                           icon="el-icon-upload2">选择文件</el-button>
                <slot name="right"></slot>
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="el-upload-tip">
        <div>1、可上传的文件类型：xml，单个文件数据条目上限为1000条</div>
        <div>2、导入的资源有可能会覆盖现有资源</div>
      </el-row>
    </el-form>
    <div slot="footer">
      <center>
        <asp-btn-hollow name="取消"
                        icon="el-icon-close"
                        @click="dialogParam.visible = false"></asp-btn-hollow>
        <asp-btn-solid :loading="loading"
                       icon="el-icon-check"
                       name="保存"
                       @click="submit"></asp-btn-solid>
      </center>
    </div>
  </asp-dialog>
</template>

<script>
export default {
  name: 'ImportResource',
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      importForm: {
        type: '',
        file: ''
      },
      loading: false,
      showFlag: false,
      fileList: [],
      rules: {
        type: [
          { required: true, message: '导入类型不能为空', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    'dialogParam.visible' (val) {
      // 清空表单数据-弹框开关是form表单节点渲染不同步，会报错
      // this.$refs.importForm.resetFields()
      this.importForm = {
        type: '',
        file: ''
      }
    }
  },
  computed: {
    // 导入类型
    resourceTypeList () {
      const list = this.$aspUtils.getCodeValueByType(this, 'METADATA_FILE_TYPE') || []
      return list
    }
  },
  created () { },
  methods: {
    handleChange (file, fileList) {
      console.log('Change', file, fileList)
      this.handleExceed([file.raw], fileList)
    },
    handleExceed (files, fileList) {
      console.log('Exceed', files, fileList)
      this.showFlag = false
      this.handleRemove()
      if (!files || !files.length) {
        return
      }
      const file = files[0]
      if (file.size === 0) {
        this.$message.warning('上传附件内容不能为空！')
        return false
      }
      const index = file.name.lastIndexOf('.')
      const str = file.name.substring(index + 1, file.name.length)
      if (!['xml'].includes(str)) {
        this.$message.warning('上传附件只能是xml格式!')
        return false
      }
      this.showFlag = true
      this.fileList = [file]
      this.importForm.file = file
    },
    handleRemove (file, fileList) {
      this.fileList = []
      this.importForm.file = ''
    },
    submit () {
      this.$refs.importForm.validate(valid => {
        if (!valid) return
        if (!this.importForm.file) {
          this.$message.error('请选择要导入的文件')
          return
        }
        const fd = new FormData()
        fd.append('file', this.importForm.file)
        const url = '/resource/importResource?type=' + this.importForm.type
        this.$aspHttps.asp_FileUpload(this.$apiConfig.managerPathPrefix + url, fd).then(res => {
          if (this.$reponseStatus(res)) {
            this.$message.success('导入成功')
            // } else {
            //   this.$message.error(res.message || '导入失败')
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-upload-tip {
  padding-left: 65px;
  font-size: 12px;
}
</style>
