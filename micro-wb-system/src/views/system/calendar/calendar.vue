/** * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/9/25 */
<template>
  <div class="webbas">
    <div class="">
      <div class="list-page-content-css">
        <div class="query-area-content-css">
          <el-form ref="searchForm" :model="searchForm">
            <el-row>
              <el-col :span="2">
                <el-select
                  v-model="searchForm.year"
                  name="year"
                  @change="init(!isShow)"
                >
                  <el-option
                    v-for="item in years"
                    :label="item"
                    :key="item"
                    :value="item"
                  ></el-option>
                </el-select>
              </el-col>
              <el-col v-if="authStatus" :span="17">
                <el-form-item label="">
                  <span
                    >单击日期时字体颜色切换，双击日期时变为绿色字体。红色字体为节假日，绿色字体为休息日变为工作日</span
                  >
                </el-form-item>
              </el-col>
              <el-col v-if="authStatus" :span="5">
                <el-form-item class="query-area-btn-css">
                  <asp-btn-solid
                    v-if="isShow"
                    name="设置节假日"
                    @click="setCalendar"
                  >
                  </asp-btn-solid>
                  <asp-btn-solid v-if="!isShow" name="保存" @click="save()">
                  </asp-btn-solid>
                  <asp-btn-hollow v-if="!isShow" name="取消" @click="cancel()">
                  </asp-btn-hollow>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div v-if="refush" class="background-css">
        <div v-if="refush" style='display: flex;flex-wrap: wrap;justify-content: space-between;'>
          <!-- 临时解决：添加style解决谷歌浏览器日历页面四月前面空出来没有日历控件展示的问题 -->
          <div
            v-for="(item, index) in values"
            :key="item"
            style="width:33%;margin-bottom:40px"
          >
            <Calendars
              :id="index"
              :dialog-param="getParam(item)"
              @clickEvent="refushCalendar"
            ></Calendars>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Calendars from './calendars'
import calendar from './calendar.js'
export default {
  name: 'Calendar',
  components: { Calendars },
  mixins: [calendar],
  data() {
    const nowYear = new Date().getFullYear()
    return {
      refush: true,
      isRoll: true, // 是否滚动到指定位置
      isEdit: false, // 是否可以设置
      isShow: true, // 设置按钮是否显示
      authStatus: false,
      searchForm: {
        year: nowYear
      },
      setHolidays: [],
      values: [],
      yearDates: {},
      holidays: {
        workDay: [],
        dayOff: []
      },
      years: [nowYear - 3, nowYear - 2, nowYear - 1, nowYear, nowYear + 1]
    }
  },
  created() {
    // 查询日历信息
    this.init(false)
    // 获取按钮权限
    const domain = this.$aspUtils.getDomainObject(this)
    const btnRight = domain.authInfo
    if (btnRight.wb_01) {
      btnRight.wb_01.some(e => {
        if (e === 'wb_010101') {
          this.isShow = true
          this.authStatus = true
        }
      })
    }
  },
  methods: {
    // 查询日历信息
    init(val) {
      this.holidays.workDay = []
      this.holidays.dayOff = []
      const param = {
        year: this.searchForm.year + ''
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.supportPathPrefix + '/holiday/listHolidays',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            if (response.data.length > 0) {
              response.data.forEach(e => {
                if (parseInt(e.type) === 2) {
                  this.holidays.workDay.push(e.holidayDate)
                } else if (parseInt(e.type) === 1) {
                  this.holidays.dayOff.push(e.holidayDate)
                }
              })
            }
            // 初始化日历
            this.populateValue(this.searchForm.year)
            if (val) {
              this.setCalendar()
            }
          }
        })
    },
    // 初始化日历
    populateValue(year) {
      this.isEdit = false
      this.values = []
      for (let i = 1; i < 13; i++) {
        if (i < 10) {
          this.values.push(year + '-0' + i)
        } else {
          this.values.push(year + '-' + i)
        }
        // 更新年月日历（阴历）信息（默认每月31天）；同时前后各推1年
        if (!this.yearDates[`${year}-${i}`]) this.yearDates[`${year}-${i}`] = this.getDatesCongfig(year, i)
        let year0 = ~~year - 1
        if (!this.yearDates[`${year0}-${i}`]) this.yearDates[`${year0}-${i}`] = this.getDatesCongfig(year0, i)
        year0 = ~~year + 1
        if (!this.yearDates[`${year0}-${i}`]) this.yearDates[`${year0}-${i}`] = this.getDatesCongfig(year0, i)
      }
      this.refushCalendar()
    },
    getParam(date) {
      return {
        yearDates: this.yearDates,
        date: date,
        setHolidays: this.setHolidays,
        holidays: this.holidays,
        isEdit: this.isEdit,
        isRoll: this.isRoll,
        year: this.searchForm.year + ''
      }
    },
    // 设置节假日
    setCalendar() {
      this.isRoll = false
      this.isShow = false
      this.isEdit = true
      this.refushCalendar()
    },
    // 刷新日历
    refushCalendar() {
      this.refush = false
      this.$nextTick(() => {
        this.refush = true
      })
    },
    getDatesCongfig (year, month) {
      const currentDate = {}
      for (let i = 1; i <= 31; i++) {
        const date = `${year}-${month}-${i}`
        currentDate[date] = this.solar2lunar(~~year, month, i)
      }
      return currentDate
    },
    // 保存设置
    save() {
      if (this.setHolidays) {
        this.$aspHttps
          .asp_Post(
            this.$apiConfig.supportPathPrefix + '/holiday/setHolidays',
            this.setHolidays
          )
          .then(response => {
            if (this.$reponseStatus(response)) {
              this.$message.success('保存成功！')
              this.cancel()
            }
          })
      } else {
        this.$message.success('保存成功！')
        this.cancel()
      }
    },
    // 取消设置
    cancel() {
      this.isShow = true
      this.isEdit = false
      this.setHolidays = []
      this.init(false)
    }
  }
}
</script>
