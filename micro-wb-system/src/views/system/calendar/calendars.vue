/** * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/9/25 * 日历附件 */
<template>
  <div class="calendar-style">
    <div class="calendar-title">
      <div class="calendar-months">{{ toMonths[months] }}</div>
      <div class="calendar-holiday">{{ toHoliday[months] }}</div>
    </div>
    <el-calendar v-model="value">
      <template
        slot="dateCell"
        slot-scope="{ data }"
      >
        <div
          v-if="dialogParam.isEdit"
          @click="setDayOff(data)"
          @dblclick="setWorkDay(data)"
        >
          <span
            :class="getCssCLs(data.day)"
            :style="getStyle(new Date(data.day))"
          >
            {{ showCal1(data.day) }}
          </span>
          <span class="calendar-font-10-css">
            {{ showCal2(new Date(data.day)) }}
          </span>
        </div>
        <div v-else>
          <span
            :class="getCssCLs(data.day)"
            :style="getStyle(new Date(data.day))"
          >
            {{ showCal1(data.day) }}
          </span>
          <span class="calendar-font-10-css">
            {{ showCal2(new Date(data.day)) }}
          </span>
        </div>
      </template>
    </el-calendar>
  </div>
</template>

<script>
import calendar from './calendar.js'
export default {
  name: 'Calendars',
  components: {},
  mixins: [calendar],
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      isShow: true,
      currentMonth: new Date().getMonth() + 1, // 当前月份
      value: '',
      months: '',
      time: null,
      holidays: [
        '元旦',
        '除夕',
        '春节',
        '清明节',
        '劳动节',
        '端午节',
        '七夕节',
        '重阳节',
        '中秋节',
        '七夕节',
        '重阳节',
        '国庆节'
      ],
      toMonths: {
        1: '一月',
        2: '二月',
        3: '三月',
        4: '四月',
        5: '五月',
        6: '六月',
        7: '七月',
        8: '八月',
        9: '九月',
        10: '十月',
        11: '十一月',
        12: '十二月'
      },
      toHoliday: {
        1: '',
        2: '',
        3: '',
        4: '',
        5: '',
        6: '',
        7: '',
        8: '',
        9: '',
        10: '',
        11: '',
        12: ''
      }
    }
  },
  computed: {
    showCal2 () {
      return function (today) {
        const year = today.getFullYear()
        const month = today.getMonth()
        const date = today.getDate()
        const currentMonth = `${year}-${month + 1}`
        const currentDate = `${year}-${month + 1}-${date}`
        // console.log('showCal2', currentMonth, currentDate)
        const resultStr = this.dialogParam.yearDates[currentMonth]
        // console.log('showCal2=====resultStr', resultStr)
        const val = resultStr[currentDate]
        // 获取指定日期的前一天
        const nextDay = this.getNextDate(`${val.cYear}-${val.cMonth}-${val.cDay}`, 1)
        // 获取指定日期的下一周 为了计算感恩节 为每年11月第四个星期四
        const nextWeek = this.getNextDate(`${val.cYear}-${val.cMonth}-${val.cDay}`, -21)
        const nextWeek1 = this.getNextDate(`${val.cYear}-${val.cMonth}-${val.cDay}`, -28)
        console.log(resultStr[nextWeek], resultStr[nextDay])
        if (val && val.IDayCn === '廿九' && val.IMonthCn === '腊月' && (!resultStr[nextDay] || (resultStr[nextDay] && resultStr[nextDay].IDayCn !== '三十'))) return '除夕'
        if (val && val.IDayCn === '初五' && val.IMonthCn === '五月') return '端午节'
        if (val && val.IDayCn === '十五' && val.IMonthCn === '八月') return '中秋节'
        if (val && val.IDayCn === '初一' && val.IMonthCn === '正月') return '春节'
        if (val && val.IDayCn === '三十' && val.IMonthCn === '腊月') return '除夕'
        if (val && val.IDayCn === '初七' && val.IMonthCn === '七月') return '七夕节'
        if (val && val.IDayCn === '初九' && val.IMonthCn === '九月') return '重阳节'
        // 1、 resultStr[nextWeek] && resultStr[nextWeek].cMonth === 11 判断21天前还是在11月份
        // 2、 (!resultStr[nextWeek1] || (resultStr[nextWeek1] && resultStr[nextWeek1].cMonth !== 11)) 判断28天前不在11月份
        // 1 + 2 可以确定当前就是十一月份第四个星期四
        if (val && val.ncWeek === '星期四' && val.cMonth === 11 && resultStr[nextWeek] && resultStr[nextWeek].cMonth === 11 && (!resultStr[nextWeek1] || (resultStr[nextWeek1] && resultStr[nextWeek1].cMonth !== 11))) {
          return '感恩节'
        }
        if (month === 0 && date === 1) return '元旦'
        if (month === 1 && date === 14) return '情人节'
        if (month === 2 && date === 8) return '妇女节'
        if (month === 2 && date === 12) return '植树节'
        if (month === 3 && date === 1) return '愚人节'
        if (month === 4 && date === 1) return '劳动节'
        if (month === 4 && date === 4) return '青年节'
        if (month === 5 && date === 1) return '儿童节'
        if (month === 6 && date === 1) return '建党节'
        if (month === 7 && date === 1) return '建军节'
        if (month === 8 && date === 10) return '教师节'
        if (month === 9 && date === 1) return '国庆节'
        if (month === 9 && date === 31) return '万圣夜'
        if (month === 10 && date === 1) return '万圣节'
        if (month === 11 && date === 24) return '平安夜'
        if (month === 11 && date === 25) return '圣诞节'
        if (val && val.IDayCn === '初一') return val.IMonthCn
        if (val && val.Term) return val.Term
        return val.IDayCn
      }
    }
  },
  created () { },
  mounted () {
    this.init()
    /* 农历部分 */
    // const resultStr = this.solar2lunar(2021, 4, 11)
    // console.log('resultStr', resultStr)
    // this.$nextTick(() => {
    //   // 根据当前月份显示滚动条位置
    //   if (!this.dialogParam.isEdit && this.dialogParam.isRoll) {
    //     if (
    //       parseInt(this.dialogParam.year) === new Date().getFullYear() &&
    //       this.currentMonth > 3
    //     ) {
    //       // const con = document.getElementsByClassName('frame-main-content')
    //       // IE不支持scroll
    //       if (this.currentMonth < 4) {
    //         // con[0].scroll(0, 350)
    //         document.getElementById('1').scrollIntoView()
    //       } else if (this.currentMonth < 7) {
    //         // con[0].scroll(0, 350)
    //         document.getElementById('4').scrollIntoView()
    //       } else if (this.currentMonth < 10) {
    //         // con[0].scroll(0, 700)
    //         document.getElementById('7').scrollIntoView()
    //       } else {
    //         // con[0].scroll(0, con[0].scrollHeight)
    //         document.getElementById('10').scrollIntoView()
    //       }
    //     }
    //   }
    //   setTimeout(() => {
    //     // 节假日 —— 农历
    //     const unifyDay = document.querySelectorAll('.unifyDay')
    //       ? document.querySelectorAll('.unifyDay')
    //       : []
    //     /* this.$lodash.forEach(unifyDay, function(value, key) {
    //                 value.parentNode.parentNode.parentNode.style.backgroundColor = '#fff0f0'
    //                 value.parentNode.childNodes[2].style.color = '#e02d2d'
    //             }) */
    //     unifyDay.forEach(item => {
    //       item.parentNode.parentNode.parentNode.style.backgroundColor =
    //         '#fff0f0'
    //       item.parentNode.childNodes[2].style.color = '#e02d2d'
    //     })
    //     // 节假日 —— 公历
    //     const dayOff = document.querySelectorAll('.dayOff')
    //       ? document.querySelectorAll('.dayOff')
    //       : []
    //     /* this.$lodash.forEach(dayOff, function(value, key) {
    //                 value.parentNode.parentNode.parentNode.style.backgroundColor = '#fff0f0'
    //             }) */
    //     dayOff.forEach(item => {
    //       item.parentNode.parentNode.parentNode.style.backgroundColor =
    //         '#fff0f0'
    //     })
    //     // 休息日变为工作日
    //     const workday = document.querySelectorAll('.workDay')
    //       ? document.querySelectorAll('.workDay')
    //       : []
    //     /* this.$lodash.forEach(workday, function(value, key) {
    //                 value.parentNode.parentNode.parentNode.style.backgroundColor = '#a8dca8'
    //             }) */
    //     workday.forEach(item => {
    //       item.parentNode.parentNode.parentNode.style.backgroundColor =
    //         '#a8dca8'
    //     })
    //     // 下个月日期不展示、不可点击
    //     const next = document.querySelectorAll('.next')
    //       ? document.querySelectorAll('.next')
    //       : []
    //     /* this.$lodash.forEach(next, function(value, key) {
    //                 value.parentNode.style.height = '51px'
    //             }) */
    //     next.forEach(item => {
    //       item.parentNode.style.height = '51px'
    //     })
    //     // 上个月日期不展示、不可点击
    //     const prev = document.querySelectorAll('.prev')
    //       ? document.querySelectorAll('.prev')
    //       : []
    //     /* this.$lodash.forEach(prev, function(value, key) {
    //                 value.style.backgroundColor = '#fff'
    //                 value.style.pointerEvents = 'none'
    //                 value.childNodes[0].style.display = 'none'
    //             }) */
    //     prev.forEach(item => {
    //       item.style.backgroundColor = '#fff'
    //       item.style.pointerEvents = 'none'
    //       item.childNodes[0].style.display = 'none'
    //     })
    //   }, 0)
    // })
  },
  methods: {
    getNextDate (date, day) {
      const dd = new Date(date)
      dd.setDate(dd.getDate() + day)
      const y = dd.getFullYear()
      const m = dd.getMonth() + 1
      const d = dd.getDate()
      return y + '-' + m + '-' + d
    },
    // 页面初始化
    init () {
      if (this.dialogParam.date) {
        this.months = parseInt(this.dialogParam.date.substring(5, 7))
        const nowMonth = new Date().getMonth() + 1
        if (this.months === nowMonth) this.value = new Date()
        else this.value = new Date(this.dialogParam.date + '-01')
      }
    },
    // 设置节假日
    setDayOff (data) {
      this.isShow = false
      clearTimeout(this.time) // 首先清除计时器
      this.time = setTimeout(() => {
        this.setDay(data, 'click')
      }, 300)
    },
    // 设置工作日
    setWorkDay (data) {
      this.isShow = false
      clearTimeout(this.time) // 首先清除计时器
      this.setDay(data, 'dblclick')
    },
    // 设置日历
    setDay (data, type) {
      const param = {
        holidayDate: data.day,
        holidayName: this.showCal2(new Date(data.day)),
        type: 3
      }
      if (this.dialogParam.holidays.dayOff.indexOf(data.day) > -1) {
        this.dialogParam.holidays.dayOff.forEach((e, index) => {
          if (e === data.day) {
            this.dialogParam.holidays.dayOff.splice(index, 1)
          }
        })
      } else if (this.dialogParam.holidays.workDay.indexOf(data.day) > -1) {
        this.dialogParam.holidays.workDay.forEach((e, index) => {
          if (e === data.day) {
            this.dialogParam.holidays.workDay.splice(index, 1)
          }
        })
      } else {
        if (type === 'click') {
          // 单击 —— 节假日
          this.dialogParam.holidays.dayOff.push(data.day)
          param.type = 1
        } else {
          // 双击 —— 工作日
          this.dialogParam.holidays.workDay.push(data.day)
          param.type = 2
        }
      }
      // 已设置的日期
      if (this.dialogParam.setHolidays.length > 0) {
        const result = this.dialogParam.setHolidays.some(e => {
          if (e.holidayDate === data.day) {
            e.type = param.type
            return true
          } else {
            return false
          }
        })
        if (!result) {
          this.dialogParam.setHolidays.push(param)
        }
      } else {
        this.dialogParam.setHolidays.push(param)
      }
      // 刷新日历
      // this.$emit('clickEvent')
    },
    // 显示公历
    showCal1 (today) {
      if (
        parseInt(
          today
            .split('-')
            .slice(2)
            .join('')
        ) < 10
      ) {
        return parseInt(
          today
            .split('-')
            .slice(2)
            .join('')
        )
      } else {
        return today
          .split('-')
          .slice(2)
          .join('')
      }
    },
    // 显示农历
    // showCal2(today) {
    //   const year = today.getFullYear()
    //   const month = today.getMonth()
    //   const date = today.getDate()
    //   console.log('showCal2', `${year}-${month}-${date}`)
    //   const resultStr = this.solar2lunar(year, month + 1, date)
    //   console.log('showCal2=====resultStr', resultStr)
    //   if (month === 0 && date === 1) return '元旦'
    //   if (month === 1 && date === 14) return '情人节'
    //   if (month === 2 && date === 8) return '妇女节'
    //   if (month === 2 && date === 12) return '植树节'
    //   if (month === 3 && date === 1) return '愚人节'
    //   if (month === 3 && date === 5) return '清明节'
    //   if (month === 4 && date === 1) return '劳动节'
    //   if (month === 4 && date === 4) return '青年节'
    //   if (month === 5 && date === 1) return '儿童节'
    //   if (month === 6 && date === 1) return '建党节'
    //   if (month === 7 && date === 1) return '建军节'
    //   if (month === 8 && date === 10) return '教师节'
    //   if (month === 9 && date === 1) return '国庆节'
    //   if (month === 10 && date === 28) return '感恩节'
    //   if (month === 11 && date === 24) return '平安夜'
    //   if (month === 11 && date === 25) return '圣诞节'

    //   return '待计算'
    // },
    // 给公历设置class
    getCssCLs (today) {
      const name = this.showCal2(new Date(today))
      if (this.holidays.indexOf(name) > -1) {
        if (
          !this.toHoliday[parseInt(today.substring(5, 7))] ||
          this.toHoliday[parseInt(today.substring(5, 7))] === '除夕'
        ) {
          this.toHoliday[parseInt(today.substring(5, 7))] = name
        }
      }
      if (this.dialogParam.holidays.workDay.indexOf(today) > -1) {
        return 'workDay'
      } else if (this.dialogParam.holidays.dayOff.indexOf(today) > -1) {
        if (this.holidays.indexOf(name) > -1) {
          return 'unifyDay'
        } else {
          return 'dayOff'
        }
      } else {
        return ' '
      }
    },
    // 周六、周日字体变红
    getStyle (val) {
      // 获取的date 为0、1、2、3、4、5、6 对应的星期日、一、二、三、四、五、六
      if (val.getDay() === 6 || val.getDay() === 0) {
        return { color: '#e02d2d' }
      }
    }
  }
}
</script>