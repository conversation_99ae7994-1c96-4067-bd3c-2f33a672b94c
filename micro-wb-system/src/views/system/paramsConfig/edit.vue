/** * Created by TurboC on 2012/03/17. */

<template>
  <asp-dialog
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="60%"
  >
    <template>
      <el-form
        ref="paramsEdit"
        :model="dataModel"
        :rules="rules"
        :inline="true"
        class="el-collapse-100"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="参数名称:" prop="name">
              <el-input v-show="false" v-model.trim="dataModel.name"></el-input>
              <span>{{ dataModel.name }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参数组名称:" prop="groupName">
              <el-input
                v-show="false"
                v-model.trim="dataModel.groupName"
              ></el-input>
              <span>{{ dataModel.groupName }} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否可编辑:" prop="editFlag">
              <el-input
                v-show="false"
                v-model.trim="dataModel.editFlag"
              ></el-input>
              <span>{{ dataModel.editFlag == "1" ? "是" : "否" }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否启用:" prop="description">
              <el-input
                v-show="false"
                v-model.trim="dataModel.status"
              ></el-input>
              <span>{{ dataModel.status == "1" ? "是" : "否" }} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="isMultipleMode">
          <el-row
            :key="configOption.value"
            v-for="configOption in configOptions"
          >
            <el-col :span="24">
              <template v-if="configOption.type == 'textbox'">
                <el-form-item
                  :label="(configOption.label || '参数值') + ':'"
                  :prop="'extra.' + configOption.alias"
                >
                  <el-input
                    v-model.trim="dataModel['extra'][configOption.alias]"
                    :minlength="parseInt(configOption.minlength)"
                    :maxlength="parseInt(configOption.maxlength)"
                    type="textarea"
                    placeholder="请输入内容"
                    show-word-limit
                    auto-complete="off"
                  ></el-input>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'number'">
                <el-form-item
                  :label="(configOption.label || '参数值') + ':'"
                  :prop="'extra.' + configOption.alias"
                >
                  <el-input-number
                    v-model="dataModel['extra'][configOption.alias]"
                    :min="configOption.min"
                    :max="configOption.max"
                    label="描述文字"
                  ></el-input-number>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'select'">
                <template
                  v-if="
                    configOption.multiple == true ||
                    configOption.multiple == 'true'
                  "
                >
                  <el-form-item
                    :label="(configOption.label || '参数值') + ':'"
                    :prop="'extra.' + configOption.alias"
                  >
                    <el-select
                      v-model="dataModel['extra'][configOption.alias]"
                      :disabled="dialogParam.name === 'login-types'"
                      multiple
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in configOption.data"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </template>
                <template v-else>
                  <el-form-item
                    :label="(configOption.label || '参数值') + ':'"
                    :prop="'extra.' + configOption.alias"
                  >
                    <el-select
                      v-model="dataModel['extra'][configOption.alias]"
                      placeholder="请选择"
                    >
                      <el-option label="请选择" value=""></el-option>
                      <el-option
                        v-for="(item, index) in configOption.data"
                        :key="index"
                        :label="item.name"
                        :value="item.code"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </template>
              <template v-else-if="configOption.type == 'radio'">
                <el-form-item
                  :label="(configOption.label || '参数值') + ':'"
                  :prop="'extra.' + configOption.alias"
                >
                  <el-radio
                    v-for="(item, index) in configOption.data"
                    :key="index"
                    v-model="dataModel['extra'][configOption.alias]"
                    :label="item.code"
                    >{{ item.name }}</el-radio
                  >
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'radiobutton'">
                <el-form-item
                  :label="(configOption.label || '参数值') + ':'"
                  :prop="'extra.' + configOption.alias"
                >
                  <el-radio-group
                    v-model="dataModel['extra'][configOption.alias]"
                  >
                    <el-radio-button
                      v-for="item in configOption.data"
                      :key="item.name"
                      :label="item.name"
                    ></el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'checkbox'">
                <el-form-item
                  :label="(configOption.label || '参数值') + ':'"
                  :prop="'extra.' + configOption.alias"
                >
                  <el-checkbox-group
                    v-model="dataModel['extra'][configOption.alias]"
                  >
                    <el-checkbox
                      v-for="item in configOption.data"
                      :key="item.name"
                      :label="item.name"
                    ></el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'checkboxbutton'">
                <el-form-item
                  :label="(configOption.label || '参数值') + ':'"
                  :prop="'extra.' + configOption.alias"
                >
                  <el-checkbox-group
                    v-model="dataModel['extra'][configOption.alias]"
                    size="small"
                  >
                    <el-checkbox-button
                      v-for="item in configOption.data"
                      :label="item.code"
                      :key="item.code"
                      >{{ item.name }}</el-checkbox-button
                    >
                  </el-checkbox-group>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'colorPicker'">
                <el-form-item
                  :label="(configOption.label || '参数值') + ':'"
                  :prop="'extra.' + configOption.alias"
                >
                  <el-color-picker
                    v-model="dataModel['extra'][configOption.alias]"
                    :predefine="configOption.predefine"
                  ></el-color-picker>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'switch'">
                <el-form-item
                  :label="(configOption.label || '参数值') + ':'"
                  :prop="'extra.' + configOption.alias"
                >
                  <el-tooltip
                    :content="
                      'Switch value: ' + dataModel['extra'][configOption.alias]
                    "
                    placement="top"
                  >
                    <el-switch
                      v-model="dataModel['extra'][configOption.alias]"
                      :active-value="configOption.activeText"
                      :inactive-value="configOption.inactiveText"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    >
                    </el-switch>
                  </el-tooltip>
                </el-form-item>
              </template>
            </el-col>
          </el-row>
        </template>
        <template v-else>
          <el-row>
            <el-col :span="24">
              <template v-if="configOption.type == 'textbox'">
                <el-form-item label="参数值:" prop="value">
                  <el-input
                    v-model.trim="dataModel.value"
                    :minlength="parseInt(configOption.minlength)"
                    :maxlength="parseInt(configOption.maxlength)"
                    type="textarea"
                    placeholder="请输入内容"
                    show-word-limit
                    auto-complete="off"
                  ></el-input>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'number'">
                <el-form-item label="参数值:" prop="value">
                  <el-input-number
                    v-model="dataModel.value"
                    :min="configOption.min"
                    :max="configOption.max"
                    label="描述文字"
                  ></el-input-number>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'select'">
                <template
                  v-if="
                    configOption.multiple == true ||
                    configOption.multiple == 'true'
                  "
                >
                  <el-form-item label="参数值:" prop="value">
                    <el-select
                      v-model="dataModel.value"
                      :disabled="dialogParam.name === 'login-types'"
                      multiple
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in configOption.data"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </template>
                <template v-else>
                  <el-form-item label="参数值:" prop="value">
                    <el-select v-model="dataModel.value" placeholder="请选择">
                      <el-option label="请选择" value=""></el-option>
                      <el-option
                        v-for="(item, index) in configOption.data"
                        :key="index"
                        :label="item.name"
                        :value="item.code"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </template>
              <template v-else-if="configOption.type == 'radio'">
                <el-form-item label="参数值:" prop="value">
                  <el-radio
                    v-for="(item, index) in configOption.data"
                    :key="index"
                    v-model="dataModel.value"
                    :label="item.code"
                    >{{ item.name }}</el-radio
                  >
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'radiobutton'">
                <el-form-item label="参数值:" prop="value">
                  <el-radio-group v-model="dataModel.value">
                    <el-radio-button
                      v-for="item in configOption.data"
                      :key="item.name"
                      :label="item.name"
                    ></el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'checkbox'">
                <el-form-item label="参数值:" prop="value">
                  <el-checkbox-group v-model="dataModel.value">
                    <el-checkbox
                      v-for="item in configOption.data"
                      :key="item.name"
                      :label="item.name"
                    ></el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'checkboxbutton'">
                <el-form-item label="参数值:" prop="value">
                  <el-checkbox-group v-model="dataModel.value" size="small">
                    <el-checkbox-button
                      v-for="item in configOption.data"
                      :label="item.code"
                      :key="item.code"
                      >{{ item.name }}</el-checkbox-button
                    >
                  </el-checkbox-group>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'colorPicker'">
                <el-form-item label="参数值:" prop="value">
                  <el-color-picker
                    v-model="dataModel.value"
                    :predefine="configOption.predefine"
                  ></el-color-picker>
                </el-form-item>
              </template>
              <template v-else-if="configOption.type == 'switch'">
                <el-form-item label="参数值:" prop="value">
                  <el-tooltip
                    :content="'Switch value: ' + dataModel.value"
                    placement="top"
                  >
                    <el-switch
                      v-model="dataModel.value"
                      :active-value="configOption.activeText"
                      :inactive-value="configOption.inactiveText"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    >
                    </el-switch>
                  </el-tooltip>
                </el-form-item>
              </template>
            </el-col>
          </el-row>
        </template>
        <el-row>
          <el-col :span="24">
            <el-form-item label="更新时间:" prop="lastUpdateDate">
              <el-input
                v-show="false"
                v-model.trim="dataModel.lastUpdateDate"
              ></el-input>
              <span>{{ dataModel.lastUpdateDate }} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="参数描述:" prop="description">
              <el-input
                v-show="false"
                v-model.trim="dataModel.description"
              ></el-input>
              <span>{{ dataModel.description }} </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow
        name="取消"
        icon="el-icon-close"
        @click="cancel()"
      ></asp-btn-hollow>
      <asp-btn-solid
        v-loading="submitStatus"
        name="保存"
        icon="el-icon-check"
        @click="save()"
      ></asp-btn-solid>
    </template>
  </asp-dialog>
</template>

<script>
export default {
  name: 'ParamsConfigEdit',
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      submitStatus: false,
      configOption: {
        type: 'textbox'
      },
      dataModel: {
        description: '',
        editFlag: '',
        groupName: '',
        lastUpdateDate: '',
        name: '',
        status: '',
        value: '',
        extra: {}
      },
      rules: {},
      isMultipleMode: false, // 是否多字段
      configOptions: [], // 字段配置项
      values: []
    }
  },
  computed: {},
  watch: {
    'dialogParam.modelVisible'(val) {
      if (val) {
        this.isMultipleMode = false
        this.$nextTick(() => {
          this.getDetail()
        })
      } else {
        this.$refs.paramsEdit.resetFields()
      }
    }
  },
  created() {},
  methods: {
    getDetail() {
      if (this.dialogParam.name) {
        const param = { name: this.dialogParam.name }
        this.$aspHttps
          .asp_Post(
            this.$apiConfig.supportPathPrefix + '/platformConfig/get',
            param
          )
          .then((response) => {
            if (this.$reponseStatus(response)) {
              this.dataModel = response.data
              this.configOption = response.data.configOption
                ? JSON.parse(response.data.configOption)
                : {}
              if (Array.isArray(this.configOption)) {
                this.isMultipleMode = true
                this.configOptions = this.configOption
                this.values = this.configOptions.map((item) => {
                  return item.alias
                })
                const valueMap = JSON.parse(this.dataModel.value)
                this.$set(this.dataModel, 'extra', valueMap)
              } else {
                if (this.configOption.type === 'select') {
                  this.dataModel.value = this.dataModel.value
                    ? this.dataModel.value.split(',')
                    : ''
                }
              }
            }
          })
      }
    },
    cancel() {
      this.submitStatus = false
      this.dialogParam.modelVisible = false
    },
    confirmSubmit() {
      this.$confirm('确认要修改吗？', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            // 请求体
            const param = {
              name: this.dataModel.name,
              status: this.dataModel.status
            }
            // 多字段
            if (this.isMultipleMode) {
              param.value = JSON.stringify(
                this.values.reduce((acc, key) => {
                  acc[key] = this.dataModel.extra[key]
                  return acc
                }, {})
              )
            } else {
              param.value = Array.isArray(this.dataModel.value)
                ? this.dataModel.value.join(',')
                : this.dataModel.value
            }
            this.submitStatus = true
            this.$aspHttps
              .asp_Post(
                this.$apiConfig.supportPathPrefix + '/platformConfig/update',
                param
              )
              .then((response) => {
                this.submitStatus = false
                if (this.$reponseStatus(response)) {
                  this.$aspUtils.getListAll(this)
                  this.dialogParam.modelVisible = false
                  this.$message.success('更新成功！')
                  this.$emit('search')
                }
                instance.confirmButtonLoading = false
                done() // 回调
              })
          }
          if (action === 'cancel') {
            if (instance.confirmButtonLoading === true) {
              this.$message({ type: 'warning', message: '请求中•••' })
            } else {
              done()
            }
          }
        }
      })
        .then((result) => {
          if (result) return false
          // this.$message({type: 'success', message: messageText + '成功!'})
        })
        .catch(() => {
          this.$message({ type: 'info', message: '已取消' })
        })
    },
    save() {
      this.$refs.paramsEdit.validate((valid) => {
        if (valid) {
          if (this.isMultipleMode) {
            const textBoxes = this.configOptions.filter(
              (item) => item.type === 'textbox'
            )
            for (let index = 0; index < textBoxes.length; index++) {
              const formItem = textBoxes[index]
              this.dataModel.extra[formItem.alias] = this.dataModel.extra[
                formItem.alias
              ]
                ? this.dataModel.extra[formItem.alias].trim()
                : ''
              if (
                !(
                  formItem.minLength <=
                    this.dataModel.extra[formItem.alias].length &&
                  this.dataModel.extra[formItem.alias].length <=
                    formItem.maxLength
                )
              ) {
                this.$message.error(
                  `${formItem.label}长度必须在【${formItem.minLength}，${formItem.maxLength}】`
                )
                this.submitStatus = false
                return false
              }
            }
          } else if (this.configOption.type === 'textbox') {
            this.dataModel.value = this.dataModel.value
              ? this.dataModel.value.trim()
              : ''
            if (
              !(
                this.configOption.minLength <= this.dataModel.value.length &&
                this.dataModel.value.length <= this.configOption.maxLength
              )
            ) {
              this.$message.error(
                `参数值长度必须在【${this.configOption.minLength}，${this.configOption.maxLength}】`
              )
              this.submitStatus = false
              return
            }
          }
          this.confirmSubmit()
        }
      })
    }
  }
}
</script>
