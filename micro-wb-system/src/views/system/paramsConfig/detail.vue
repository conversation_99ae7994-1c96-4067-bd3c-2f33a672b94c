/** * Create by TurboC on 2012/03/17. * 参数配置详情 */
<template>
  <asp-dialog
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="60%"
  >
    <template>
      <el-form
        ref="paramsDetail"
        :model="dataModel"
        :inline="true"
        class="el-collapse-100"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="参数名称:" prop="name">
              <span>{{ dataModel.name }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参数组名称:" prop="description">
              <span>{{ dataModel.groupName }} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否可编辑:" prop="editFlag">
              <span>{{ dataModel.editFlag == 1 ? "是" : "否" }} </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否启用:" prop="status">
              <span>{{ dataModel.status == 1 ? "是" : "否" }} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="isMultipleMode">
          <el-row v-for="item in configOptions" :key="item.alias">
            <el-col :span="24">
              <el-form-item :label="item.label + ':'" :prop="item.alias">
                <span>{{ item.value }} </span>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <template v-else>
          <el-row>
            <el-col :span="12">
              <el-form-item label="参数值:" prop="value">
                <span>{{ valueDisplay }} </span>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <el-row>
          <el-col :span="12">
            <el-form-item label="更新时间:" prop="description">
              <span>{{ dataModel.lastUpdateDate }} </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="参数描述:" prop="description">
              <span>{{ dataModel.description }} </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-solid
        name="关闭"
        icon="el-icon-close"
        @click="cancel()"
      ></asp-btn-solid>
    </template>
  </asp-dialog>
</template>

<script>
export default {
  name: 'ParamsConfigDetail',
  components: {},
  filters: {},
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    valueDisplay() {
      if (this.isMultipleMode) {
        return
      }
      if (this.dataModel.value) {
        const values = this.dataModel.value.split(',')
        return values.map(code => {
          if (typeof this.dictMap[code] === 'undefined') {
            return code
          } else {
            return this.dictMap[code]
          }
        }).join(',')
      }
      return this.dataModel.value
    }
  },
  data() {
    return {
      dataModel: {
        description: '',
        editFlag: '',
        groupName: '',
        lastUpdateDate: '',
        name: '',
        status: '',
        value: ''
      },
      configOptions: [],
      configOption: {},
      isMultipleMode: false,
      dictMap: {}
    }
  },
  watch: {
    'dialogParam.modelVisible'(val) {
      if (val) {
        this.$nextTick(() => {
          this.getDetail()
        })
      } else {
        this.dictMap = {}
        this.configOptions = []
        this.isMultipleMode = false
        this.configOption = {}
        this.$refs.paramsDetail.resetFields()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    getDetail() {
      if (this.dialogParam.name) {
        const param = { name: this.dialogParam.name }
        this.$aspHttps
          .asp_Post(
            this.$apiConfig.supportPathPrefix + '/platformConfig/get',
            param
          )
          .then((response) => {
            if (this.$reponseStatus(response)) {
              this.dataModel = response.data
              const configOptions = response.data.configOption
                ? JSON.parse(response.data.configOption)
                : {}
              this.configOption = configOptions
              if (Array.isArray(configOptions)) {
                this.configOptions = configOptions
                this.isMultipleMode = true
                const valueObject = JSON.parse(this.dataModel.value)
                this.configOptions.forEach(configOption => {
                  configOption.value = valueObject[configOption.alias]
                  if (configOption.data instanceof Array && ['radio', 'select'].includes(configOption.type)) {
                    if (typeof configOption.value === 'string') {
                      configOption.value = configOption.value.split(',')
                    }
                    const dictMap = {}
                    configOption.data.forEach((cur) => {
                      dictMap[cur.code] = cur.name
                    })
                    configOption.value = configOption.value.map(code => {
                      if (typeof dictMap[code] === 'undefined') {
                        return code
                      } else {
                        return dictMap[code]
                      }
                    }).join(',')
                  } else {
                    configOption.value = (valueObject[configOption.alias] || '').toString()
                  }
                })
                // console.log(this.configOptions)
                Object.assign(this.dataModel, JSON.parse(this.dataModel.value))
                // if() {

                // }
              } else {
                this.isMultipleMode = false
                this.configOptions = []
              }
              if (this.configOption.data instanceof Array && ['radio', 'select'].includes(this.configOption.type)) {
                this.configOption.data.forEach((cur) => {
                  this.dictMap[cur.code] = cur.name
                })
                console.log(this.dictMap)
              }
            }
          })
      }
    },
    cancel() {
      this.dialogParam.modelVisible = false
    }
  }
}
</script>
