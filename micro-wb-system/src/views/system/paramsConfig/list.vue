/** * Created by TurboC on 2012/03/17. * 参数配置 */
<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form
          ref="searchForm"
          :inline="true"
          :model="table.searchForm"
          @submit.native.prevent
        >
          <el-row>
            <el-col :span="8">
              <el-form-item prop="name" label="参数名称：">
                <el-input v-model.trim="table.searchForm.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="description" label="参数描述：">
                <el-input
                  v-model.trim="table.searchForm.description"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="query-area-btn-css">
                <asp-btn-solid
                  icon="el-icon-search"
                  name="查询"
                  @click="search()"
                ></asp-btn-solid>
                <asp-btn-hollow
                  icon="el-icon-refresh"
                  name="重置"
                  @click="reset()"
                ></asp-btn-hollow>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <asp-table
        ref="table"
        :url="table.url"
        :param="table.searchForm"
        :prefix="table.prefix"
        :index-switch="false"
        type=""
      >
        <asp-table-column
          prop="name"
          min-width="120"
          label="参数名称"
          sort-key="name"
        >
        </asp-table-column>
        <asp-table-column
          prop="value"
          min-width="150"
          label="参数值"
          sort-key="value"
        >
        </asp-table-column>
        <asp-table-column
          prop="description"
          min-width="150"
          label="参数描述"
          sort-key="description"
        >
        </asp-table-column>
        <asp-table-column
          :width="this.$aspFontSize.asp_ColFontSize(5)"
          label="是否可编辑"
        >
          <template slot-scope="{ scope }">
            <template v-if="scope.row.editFlag === '1'">
              <el-tag type="success">是</el-tag>
            </template>
            <template v-else-if="scope.row.editFlag === '0'">
              <el-tag type="danger">否</el-tag>
            </template>
          </template>
        </asp-table-column>
        <asp-table-column
          :time-format="true"
          prop="updateDate"
          label="更新时间"
        >
        </asp-table-column>
        <asp-table-column
          :width="this.$aspFontSize.asp_ColButtonSize([2, 2])"
          label="操作"
        >
          <template slot-scope="{ scope }">
            <asp-btn-text
              name="查看"
              @click="detailConfig(scope.row)"
            ></asp-btn-text>
            <asp-btn-text
              v-hasAuth="doAuth({ btnCode: 'wb_110104' })"
              v-if="modifyAuth(scope)"
              name="修改"
              @click="editConfig(scope.row)"
            ></asp-btn-text>
          </template>
        </asp-table-column>
      </asp-table>
    </div>
    <!-- 子组件入口 -->
    <EditConfig
      ref="editOrg"
      :dialog-param="orgModelParam"
      @search="search"
    ></EditConfig>
    <DetailConfig
      ref="detailConfig"
      :dialog-param="detailModelParam"
    ></DetailConfig>
  </div>
</template>

<script>
import EditConfig from './edit'
import DetailConfig from './detail'
export default {
  name: 'ParameterConfig',
  components: {
    EditConfig,
    DetailConfig
  },
  data() {
    return {
      table: {
        prefix: this.$apiConfig.supportPathPrefix,
        url: '/platformConfig/listPage',
        searchForm: {
          name: '',
          description: ''
        }
      },
      orgModelParam: { modelVisible: false },
      detailModelParam: { modelVisible: false }
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 按钮权限
    doAuth(opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    modifyAuth(scope) {
      return scope.row.editFlag === '1'
    },
    search() {
      this.$refs.table.asp_search()
    },
    reset() {
      this.$refs.table.asp_reset()
    },
    // 查看参数详情
    detailConfig(row) {
      this.detailModelParam = {
        title: '参数配置详情',
        modelVisible: true,
        name: row.name
      }
    },
    // 修改参数配置
    editConfig(row) {
      this.orgModelParam = {
        title: '修改参数配置',
        modelVisible: true,
        name: row.name
      }
    }
  }
}
</script>
