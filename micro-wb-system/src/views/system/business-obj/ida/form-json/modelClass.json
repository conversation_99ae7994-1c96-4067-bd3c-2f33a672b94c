{"formList": [{"label": "栅格布局", "type": "row", "columnName": "row_1638841638757", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 12, "childList": [{"label": "别名", "type": "input", "columnName": "key", "defaultValue": "", "isModel": true, "class": "", "required": true, "rules": [{"required": true, "message": "别名不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 120, "operations": [{"status": ["edit"], "attr": "disabled", "formatter": ""}], "requiredCutomizeTips": ""}]}, {"span": 12, "childList": [{"label": "描述", "type": "input", "columnName": "comment", "defaultValue": "", "isModel": true, "class": "", "required": true, "rules": [{"required": true, "message": "描述不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 120, "operations": [], "requiredCutomizeTips": ""}]}], "name": "layout", "labelWidth": 160}, {"label": "栅格布局", "type": "row", "columnName": "row_1638841640814", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 12, "childList": [{"label": "数据库表名", "type": "input", "columnName": "name", "defaultValue": "", "isModel": true, "class": "", "required": true, "rules": [{"required": true, "message": "数据库表名不能为空", "trigger": "blur"}, {"message": "只能以英文开头，英文、数字、下划线组成，长度不超过32位", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,31}$", "trigger": "blur"}], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 120, "operations": [{"status": ["edit"], "attr": "disabled", "formatter": ""}], "requiredCutomizeTips": ""}]}, {"span": 12, "childList": [{"label": "栅格布局", "type": "row", "columnName": "row_1639621465416", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 12, "childList": [{"label": "是否已生成表", "type": "select", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": false, "rules": [], "columnName": "createdTable", "targetName": "createdTableName", "defaultValue": "0", "defaultContent": "否", "isModel": true, "operation": [], "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "text": "0"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "是", "value": "1"}, {"label": "否", "value": "0"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": false, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "0", "showOptions": [{"label": "是", "value": "1", "disabled": false}, {"label": "否", "value": "0", "disabled": false}], "labelWidth": 120, "filterOptionStatus": [], "disabledOptionStatus": [], "copyOldVal": "2", "statusList": ["add"], "operations": [{"status": ["add", "edit"], "attr": "label", "formatter": ""}], "requiredCutomizeTips": "", "dynamic": {"single_mainform_list": [{"key": 1639622043393, "source": {"label": "是否已生成表:createdTable", "columnName": "createdTable", "targetName": "createdTableName", "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "text": "0"}, "name": "basic", "type": "select"}, "target": [{"label": "buttonGroup_create", "columnName": "buttonGroup_create", "name": "layout", "type": "buttonGroup"}], "condition": [{"columnName": "createdTable", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "1", "type": ["hidden"], "result": true, "value": "", "status": ["edit"], "numberSign": ">=", "numberValue": 1}, {"columnName": "createdTable", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "0", "type": ["hidden"], "result": false, "value": "", "status": ["edit"], "numberSign": ">=", "numberValue": 1}]}]}}]}, {"span": 12, "childList": [{"label": "", "type": "buttonGroup", "isLabelWidth": false, "columnName": "buttonGroup_create", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "left", "operation": [], "toolList": [{"columnName": "createTab", "type": "primary", "icon": "el-icon-caret-top", "label": "生成表", "interactive": "button_custom_submit_only", "validateProp": "", "apiName": "/web/ida/bus/businessTable/createTable", "class": "solid-with-icon-btn create-tab-css", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {"id": "$id$"}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": ""}], "name": "layout", "labelWidth": 160, "operations": [{"status": ["add"], "show": false}]}]}], "name": "layout", "labelWidth": 160}]}], "name": "layout", "labelWidth": 160}, {"label": "", "type": "buttonGroup", "isLabelWidth": false, "columnName": "buttonGroup_1638842099877", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "left", "operation": [], "toolList": [{"columnName": "add", "type": "primary", "icon": "el-icon-plus", "label": "新增", "interactive": "", "validateProp": "", "apiName": "", "class": "solid-with-icon-btn", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "bpmButtonList": [], "activeType": "button_group_other"}], "name": "layout", "labelWidth": 160, "dynamic": {}}, {"label": "子表单", "type": "normalChildList", "button": false, "isLabelWidth": false, "columnName": "businessColumn", "defaultValue": [], "isModel": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "formFields": [{"label": "注释", "type": "input", "columnName": "comment", "defaultValue": "主键", "isModel": true, "class": "", "required": true, "rules": [{"required": true, "message": "注释不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "", "isCompare": false, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "主键", "parentName": "businessColumn", "is-table-column": true, "labelWidth": 160, "copyOldVal": "", "statusList": [], "requiredCutomizeTips": ""}, {"label": "属性名", "type": "input", "columnName": "name", "defaultValue": "", "isModel": true, "class": "", "required": true, "rules": [{"required": true, "message": "属性名不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "", "isCompare": false, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "parentName": "businessColumn", "is-table-column": true, "labelWidth": 160, "copyNewVal": "", "requiredCutomizeTips": ""}, {"label": "必填", "type": "switch", "columnName": "required", "defaultValue": true, "isModel": true, "icon-dev": "iconfont iconkaiguan", "icon": "", "isLabelWidth": false, "operation": [], "required": false, "rules": [], "span": 24, "width": "80", "hidden": false, "props": {"disabled": false, "readonly": false}, "isCompare": false, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "parentName": "businessColumn", "is-table-column": true, "labelWidth": 160, "copyNewVal": true, "copyOldVal": false, "requiredCutomizeTips": "", "statusList": ["add"]}, {"label": "数据类型", "type": "select", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": false, "rules": [], "columnName": "type", "targetName": "typeName", "defaultValue": "<PERSON><PERSON><PERSON>", "defaultContent": "字符串", "isModel": true, "operation": [], "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "字符串", "value": "<PERSON><PERSON><PERSON>"}, {"label": "整型", "value": "int"}, {"label": "浮点型", "value": "number"}, {"label": "日期", "value": "date"}, {"label": "大文本", "value": "clob"}, {"label": "JSON", "value": "json"}], "allItemSwitch": false, "span": 24, "width": "", "isCompare": false, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "showOptions": [{"label": "字符串", "value": "<PERSON><PERSON><PERSON>", "disabled": false}, {"label": "整型", "value": "int", "disabled": false}, {"label": "浮点型", "value": "number", "disabled": false}, {"label": "日期", "value": "date", "disabled": false}, {"label": "大文本", "value": "clob", "disabled": false}, {"label": "JSON", "value": "json", "disabled": false}], "parentName": "businessColumn", "is-table-column": true, "labelWidth": 160, "filterOptionStatus": [], "disabledOptionStatus": [], "copyNewVal": "<PERSON><PERSON><PERSON>", "copyOldVal": false, "statusList": [], "requiredCutomizeTips": ""}, {"label": "属性长度", "type": "input", "columnName": "length", "defaultValue": "50", "isModel": true, "class": "", "required": true, "rules": [{"required": true, "message": "属性长度不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "", "isCompare": false, "isDisabledRequired": true, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "parentName": "businessColumn", "is-table-column": true, "labelWidth": 160, "copyNewVal": "50", "copyOldVal": "5", "statusList": [], "requiredCutomizeTips": ""}, {"label": "默认值", "type": "input", "columnName": "defaultValue", "defaultValue": "", "isModel": true, "class": "", "required": false, "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "", "isCompare": false, "isDisabledRequired": true, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "parentName": "businessColumn", "is-table-column": true, "labelWidth": 160, "copyNewVal": "", "requiredCutomizeTips": ""}], "hidden": false, "span": 24, "expand": false, "isTitle": false, "showSubTitle": false, "subTitle": "", "subTitleStatus": [], "titleHorizontal": false, "isVirtualTable": false, "operation": [], "toolList": [{"parentName": "businessColumn", "is-table-column": true, "columnName": "delete", "label": "", "type": "text", "class": "", "icon": "el-icon-close", "bpmFlowImageCloumnName": "", "default": "show", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "child_form_delete", "validateProp": "", "apiMethod": "", "apiName": "", "apiCloseDialogWithResposne": false, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "activeType": "sub_form_operate", "defaultValue": "", "isModel": true, "icon-dev": "iconfont icontext", "isLabelWidth": false, "span": 24, "width": "100%", "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": true, "isNumberType": false}], "show-index": false, "show-single-selection": false, "show-add": false, "show-top-add": false, "show-operation": true, "isAddId": false, "operation-width": 100, "show-row-number": false, "row-number": 10, "row-number-message": "表格最大行数为10条", "props": {"default-expand-all": true}, "default-data": true, "isInnerArrayComp": false, "childList": [], "validate-config": {"validate": false, "defaultText": "请输入唯一值", "type": false, "value": [], "object": []}, "compareProps": [], "selection": false, "isNullRowValidate": true, "nullRowValidateTip": "不允许为空行!", "isCompare": false, "isAlterColumn": false, "tooltipList": [], "name": "layout", "labelWidth": 160, "compCompareStatus": [], "alterColumnStatus": [], "showOperationStatus": [], "showAddStatus": []}, {"label": "", "type": "buttonGroup", "isLabelWidth": false, "columnName": "buttonGroup_1639381568000", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "el-icon-check", "label": "保存", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/web/ida/bus/businessTable/save", "class": "solid-with-icon-btn", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": true, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {"businessColumn": "$businessColumn$", "comment": "$comment$", "createdTable": "$createdTable$", "key": "$key$", "name": "$name$"}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": 0, "actionWithPrePageList": []}, {"columnName": "cancel", "type": "warning", "icon": "el-icon-close", "label": "取消", "interactive": "", "validateProp": "", "apiName": "", "class": "hollow-with-icon-btn", "default": "show", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_close_dialog", "apiCloseDialog": false}], "name": "layout", "labelWidth": 160, "isDialogFooterBtns": true}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "starPostion": "left", "class": "", "exportName": "modelClass", "defaultClass": "webbas", "size": "mini", "statusList": ["add", "edit"], "serverProps": {"localProxy": "/proxy_webbas", "nigxProxy": "/proxy_webbas", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "titleName": "业务实体新增"}, "dataConfig": {"data_dy_single_list": []}, "virtual_model": {"businessColumn": [{"id": 1639570104220, "comment": "主键", "name": "", "required": true, "type": "<PERSON><PERSON><PERSON>", "typeName": "字符串", "length": "50", "defaultValue": ""}]}, "model": {"key": "", "comment": "", "name": "", "createdTable": "0", "createdTableName": "否", "businessColumn": [], "typeName": "字符串"}}