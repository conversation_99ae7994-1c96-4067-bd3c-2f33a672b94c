{"list": [{"type": "form", "label": "查询表单", "columnName": "form_search", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1632375937103", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 6, "align": "left", "list": [{"type": "input", "label": "别名：", "columnName": "key", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "支持关键字搜索", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 50, "bpmFlowImageCloumnName": "", "oldColumnName": "name"}]}, {"span": 6, "align": "left", "list": [{"type": "input", "label": "表名：", "columnName": "name", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "请输入", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 50, "oldColumnName": "input_1639563771115"}]}, {"span": 6, "align": "left", "list": [{"type": "input", "label": "描述：", "columnName": "comment", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "请输入", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 50, "bpmFlowImageCloumnName": "", "oldColumnName": "input_1639563847360"}]}, {"span": 6, "align": "right", "list": [{"type": "button", "label": "查询", "columnName": "search", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"type": "primary", "icon": "el-icon-search "}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "button_1639395099521", "formId": "form_search", "event": "submit", "tableId": "idaTab"}, {"type": "button", "label": "重置", "columnName": "reset", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-refresh", "type": "primary"}, "class": "hollow-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "button_1639395103", "tableId": "idaTab", "formId": "form_search", "event": "reset-submit"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false}, {"type": "empty", "label": "空容器", "columnName": "empty_1632376847123", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "button-group", "label": "按钮容器", "columnName": "button-group_1632376858571", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON>", "width": "100%", "list": [{"type": "button", "label": "新增", "columnName": "addBtn", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "idaForm", "title": "新增", "subTitle": "", "titleClass": "dialog-title-default", "width": "900px", "height": "auto", "toolList": []}, "props": {"type": "primary", "icon": "el-icon-plus"}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": false, "labelWidth": 100, "bpmFlowImageCloumnName": "", "tableId": "", "formId": "", "event": "dialog-form", "oldColumnName": "button_1632376887712"}], "position": "left", "class": "", "isDialogFooterBtns": false, "classify": "layout", "hidden": false, "hasParentForm": false, "labelWidth": 100}, {"type": "table", "label": "业务实体表格", "columnName": "idaTab", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": false, "show-pagination": true, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "highlight-current-row": false, "show-header": true, "default-sort": ""}, "btnGroupDynamic": [], "list": [{"timeStamp": 1632377287534, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "别名", "columnName": "key", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "150", "min-width": "", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "activeType": "table_row_router", "apiStatusList": [], "requestBeforeRouter": false, "apiCloseDialog": false, "apiDyParam": {}, "beforeRouteApiParam": {}, "sfApiParam": {}, "apiUrlName": "", "apiMethodType": "", "apiMethod": "path", "apiName": "/capacityManage/capacityDetail/capacity_detailBasic", "apiParamType": "query", "sf_routerId": "", "sf_pageType": "", "apiParam": "{\"abilityId\":\"$abilityId$\"}", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "表名", "columnName": "name", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "150", "min-width": "", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "描述", "columnName": "comment", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "150", "min-width": "", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [{"type": "text", "timestamp": 1632377554951, "columnName": "edit", "label": "编辑", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "dialog-form", "apiMethod": "path", "apiName": "/capacityManage/onOffShelves/onOffShelvesManage", "apiSuccessTip": false, "apiFailTip": false, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiStatusList": [], "requestBeforeRouter": false, "apiCloseDialog": false, "apiDyParam": {}, "beforeRouteApiParam": {}, "sfApiParam": {}, "apiUrlName": "", "apiMethodType": "", "apiParamType": "query", "sf_routerId": "", "sf_pageType": "", "apiParam": "{\"abilityId\":\"$abilityId$\",\"sf_status\":\"$status$\"}", "dialogConfig": {"bindName": "idaForm", "title": "编辑", "subTitle": "", "titleClass": "dialog-title-default", "width": "900px", "height": "auto", "toolList": []}}, {"type": "text", "timestamp": 1638844021138, "columnName": "delete", "label": "删除", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/web/ida/bus/businessTable/delete", "apiSuccessTip": false, "apiFailTip": false, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"id\":\"$id$\"}"}], "operation-width": "100", "pagination": {"layout": "total, sizes, prev, pager, next, jumpe", "pageSizes": [10, 20, 30, 40], "currentPage": 1, "pageSize": 10, "total": 0, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/web/ida/bus/businessTable/pageList"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1632377287534, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "别名", "columnName": "key", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "150", "min-width": "", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "activeType": "table_row_router", "apiStatusList": [], "requestBeforeRouter": false, "apiCloseDialog": false, "apiDyParam": {}, "beforeRouteApiParam": {}, "sfApiParam": {}, "apiUrlName": "", "apiMethodType": "", "apiMethod": "path", "apiName": "/capacityManage/capacityDetail/capacity_detailBasic", "apiParamType": "query", "sf_routerId": "", "sf_pageType": "", "apiParam": "{\"abilityId\":\"$abilityId$\"}", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "表名", "columnName": "name", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "150", "min-width": "", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "描述", "columnName": "comment", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "150", "min-width": "", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "hasParentForm": false, "labelWidth": 100, "router": {}, "show-operation-status": [], "formId": "form_search"}], "classify": "empty", "hidden": false}], "model": {"key": "", "name": "", "comment": ""}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "listModelClass", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "mini", "class": "", "layoutType": "flex", "statusList": [], "localProxy": "/proxy_webbas", "nigxProxy": "/proxy_webbas", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": ""}, "titleName": "业务实体列表"}}