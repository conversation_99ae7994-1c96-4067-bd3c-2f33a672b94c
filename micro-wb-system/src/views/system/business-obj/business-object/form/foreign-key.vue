/** * Created by biyuanbing 2021/12/14. 添加子业务实体--外键 */
<template>
  <el-form ref="fks"
           :model="fksForm"
           :rules="rules"
           :inline="true"
           class="el-collapse-all">
    <el-row>
      <el-button type="text"
                 icon="el-icon-plus"
                 @click="addForeignKeyRow"></el-button>
    </el-row>
    <el-row v-for="(item, index) in fksForm.fks"
            :key="item.id">
      <el-form-item prop="fks">
        <el-col :span="6">
          <el-form-item :prop="'fks.' + index + '.from'"
                        :rules="[{ required: true, message: '必填', trigger: 'change' }]"
                        style="padding-right: 5px">
            <el-select v-model="item.from"
                       size="mini"
                       @change="handleFksData">
              <el-option v-for="item in subTableOption"
                         :key="item.name"
                         :value="item.name"
                         :label="item.comment"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item class="select-box"
                        style="padding-right: 5px">
            <el-select v-model="item.type"
                       size="mini"
                       @change="handleFksData">
              <el-option v-for="item in foreignOption"
                         :key="item.code"
                         :value="item.code"
                         :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item v-if="item.type !== 'fixedValue'"
                        :prop="'fks.' + index + '.value'"
                        :rules="[{ required: true, message: '必填', trigger: 'change' }]"
                        class="select-box"
                        style="padding-right: 5px">
            <el-select v-model="item.value"
                       size="mini"
                       @change="handleFksData">
              <el-option v-for="item in mainTableOption"
                         :key="item.name"
                         :value="item.name"
                         :label="item.comment"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-else
                        class="select-box"
                        style="padding-right: 5px">
            <el-input v-model="item.value"
                      @click="handleFksData"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="1"
                v-if="mainIdaData.type === 'sub' || fksForm.fks.length > 1">
          <el-form-item class="select-box">
            <el-button type="text"
                       icon="el-icon-close"
                       @click="deleteForeignKeyRow(index)"></el-button>
          </el-form-item>
        </el-col>
      </el-form-item>
    </el-row>
  </el-form>
</template>

<script>
export default {
  name: 'ForeignKey',
  components: {},
  props: {
    scopedParams: {
      type: Object,
      default () {
        return {}
      }
    },
    rowIndex: {
      type: Number
    },
    mainIdaData: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      foreignOption: [
        { code: 'childField', name: '子表字段 对应 父实例外键' },
        { code: 'fixedValue', name: '固定值' },
        { code: 'parentField', name: '子表外键 对应 父实例字段' }
      ],
      mainTableOption: [],
      subTableOption: [],
      fksForm: {
        fks: [
          {
            from: '',
            type: 'parentField',
            value: ''
          }
        ]
      },
      rules: {}
    }
  },
  computed: {

  },
  watch: {
    mainIdaData: {
      handler: async function (val) {
        if (val.id && this.mainTableOption.length === 0) {
          const data = await this.getBusinessTableData(val.id)
          this.mainTableOption = data
        }
      },
      deep: true,
      immediate: true
    },
    scopedParams: {
      handler: async function (val) {
        if (val.id) {
          const data = await this.getBusinessTableData(val.id)
          this.subTableOption = data
          this.fksForm.fks = val.fks || this.fksForm.fks
        }
      },
      deep: true,
      immediate: true
    }
  },
  created () { },
  methods: {
    async getBusinessTableData (id) {
      const data = await this.$aspHttps.asp_Post(this.$apiConfig.idaPathPrefix + '/businessTable/get', { id })
      if (data.status === '200') {
        return data.data.businessColumn
      }
    },
    handleFksData () {
      this.scopedParams.fks = this.fksForm.fks
      this.$emit('handleFksData', { index: this.rowIndex, fks: this.fksForm.fks })
    },
    // 增加外键行
    addForeignKeyRow () {
      const initObj = {
        from: '',
        type: 'parentField',
        value: ''
      }
      this.fksForm.fks.push(initObj)
    },
    // 删除外键行
    deleteForeignKeyRow (index) {
      this.fksForm.fks.splice(index, 1)
    },
    // 表单校验
    validFks () {
      let validResult = false
      this.$refs.fks.validate((valid) => {
        validResult = valid
      })
      return validResult
    }
  }
}
</script>
