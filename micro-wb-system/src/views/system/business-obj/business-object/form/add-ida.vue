/** * Created by TurboC on 2018/10/19. */

<template>
  <div>
    <template>
      <asp-btn-solid icon="el-icon-plus"
                     name="添加子业务实体"
                     style="margin-bottom: 8px"
                     @click="handleEmitEvent('checkbox')">
      </asp-btn-solid>
    </template>
    <el-table :data="tableData"
              style="width: 100%;">
      <el-table-column prop="type"
                       label="类型"
                       min-width="60">
        <template slot-scope="scoped">
          <el-select v-model="scoped.row.type"
                     size="mini">
            <el-option v-for="item in typeOption"
                       :key="item.code"
                       :value="item.code"
                       :label="item.name"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="tableComment"
                       label="描述"
                       width="180">
      </el-table-column>
      <el-table-column prop="tableKey"
                       label="别名"
                       min-width="100">
      </el-table-column>
      <el-table-column prop="fks"
                       label="外键"
                       min-width="300">
        <template slot-scope="scoped">
          <foreign-key :ref="'rowFks' + scoped.$index"
                       :scopedParams="scoped.row"
                       :rowIndex="scoped.$index"
                       :mainIdaData="mainIdaData"
                       @handleFksData="handleFksData"></foreign-key>
        </template>
      </el-table-column>
      <el-table-column label="操作"
                       width="180">
        <template slot-scope="scope">
          <asp-btn-hollow icon="el-icon-delete"
                          size="small"
                          @click="handleEmitEvent('delete', scope.$index)">
          </asp-btn-hollow>
          <asp-btn-hollow icon="el-icon-edit-outline"
                          name="配置子表"
                          @click="handleEmitEvent('open', scope.$index, scope.row)">
          </asp-btn-hollow>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import foreignKey from './foreign-key.vue'
export default {
  name: 'EditObject',
  components: { foreignKey },
  props: {
    subTableData: {
      type: Array,
      default () {
        return []
      }
    },
    mainIdaData: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      typeOption: [
        // { code: 'main', name: '主表' },
        { code: 'oneToMany', name: '一对多' },
        { code: 'oneToOne', name: '一对一' }
      ],
      tableData: []
    }
  },
  computed: {

  },
  watch: {
    subTableData: {
      handler: function (val) {
        if (val.length > 0) {
          val.forEach(item => {
            if (!item.type) {
              item.type = 'oneToOne'
            }
            const flag = item.tableKey + item.tableComment
            if (!this.tableData.some(e => (e.tableKey + e.tableComment) === flag)) {
              const e = JSON.parse(JSON.stringify(item))
              this.tableData.push(e)
            }
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  created () { },
  mounted () { },
  methods: {
    handleEmitEvent (type, index, row) {
      switch (type) {
        case 'checkbox':
          if (Object.keys(this.mainIdaData).length <= 0) {
            this.$message.error('请先选择主业务表')
          } else {
            this.$emit('openSubTable')
          }
          break
        case 'delete':
          this.handleDelete(row)
          break
        case 'open':
          this.$emit('openIdaDialog', { index, row })
          break
      }
    },
    handleFksData (data) {
      const { index, fks } = data
      this.tableData[index].fks = fks
    },
    handleDelete (index) {
      this.tableData.splice(index, 1)
    },
    // 校验列表行外键
    validRowFks () {
      const len = this.tableData.length
      const list = []
      for (let i = 0; i < len; i++) {
        const re = this.$refs['rowFks' + i] && this.$refs['rowFks' + '' + i].validFks()
        list.push(re)
      }
      return list.every(item => item === true)
    }
  }
}
</script>
