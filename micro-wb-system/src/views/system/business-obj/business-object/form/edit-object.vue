/** * Created by TurboC on 2018/10/19. */

<template>
  <asp-dialog v-model="dialogParam.obectModelVisible"
              :visible.sync="dialogParam.obectModelVisible"
              :title="dialogParam.title"
              class="webbas"
              width="80%">
    <template>
      <el-form ref="businessObject"
               :model="businessObject"
               :rules="rules"
               :inline="true"
               class="el-collapse-120">
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称："
                          prop="name">
              <el-input v-model.trim="businessObject.name"
                        auto-complete="off">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="别名："
                          prop="key">
              <el-input v-model.trim="businessObject.key">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="描述："
                          prop="desc">
              <el-input type="textarea"
                        v-model.trim="businessObject.desc"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主业务表："
                          prop="mainBusinessTab"
                          class="select-box">
              <span>{{ businessObject.mainBusinessTab }}</span>
              <asp-btn-solid icon="el-icon-search"
                             name="选择"
                             style="margin-left: 10px;"
                             @click="operateEvent('radio')">
              </asp-btn-solid>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <add-ida ref="addIda"
                     :subTableData="subTableData"
                     :mainIdaData="mainIdaData"
                     @openIdaDialog="openIdaDialog"
                     @openSubTable="operateEvent('checkbox')"></add-ida>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-solid icon="el-icon-check"
                     name="保存"
                     :loading="saveLoading"
                     @click="operateEvent('save')">
      </asp-btn-solid>
      <asp-btn-hollow icon="el-icon-close"
                      name="取消"
                      @click="operateEvent('cancel')">
      </asp-btn-hollow>
    </template>
    <ida-dialog :iadDialogParam="iadDialogParam"
                @rowSubTabData="rowSubTabData"></ida-dialog>
    <sub-table v-if="subTableDialogParam.subTableModelVisible"
               :subTableDialogParam="subTableDialogParam"
               :subTabData="mainIdaData"
               @selectSubTabData="selectSubTabData"></sub-table>
  </asp-dialog>
</template>

<script>
import addIda from './add-ida.vue'
import idaDialog from './ida-dialog.vue'
import subTable from './sub-table.vue'
export default {
  name: 'EditObject',
  components: {
    addIda,
    idaDialog,
    subTable
  },
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      businessObject: {
        name: '',
        key: '',
        desc: '',
        mainBusinessTab: '()'
      },
      saveLoading: false,
      subTabData: {},
      rules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        key: [{ required: true, message: '别名不能为空', trigger: 'blur' }]
      },
      iadDialogParam: {
        idaModelVisible: false,
        title: ''
      },
      subTableDialogParam: {
        type: 'radio',
        subTableModelVisible: false
      },
      mainIdaData: {}, // 主业务表
      subTableData: []
    }
  },
  computed: {},
  watch: {
    'dialogParam.obectModelVisible' (val) {
      if (val) {
        if (this.dialogParam.rowId && this.dialogParam.title === '编辑') {
          this.$aspHttps.asp_Post(this.$apiConfig.idaPathPrefix + '/businessObject/get', { id: this.dialogParam.rowId }).then(response => {
            if (this.$reponseStatus(response)) {
              const { desc, key, name, relationJson = '' } = response.data
              this.businessObject.desc = desc
              this.businessObject.key = key
              this.businessObject.name = name
              const newRelationJson = JSON.parse(relationJson) || {}
              this.mainIdaData.id = newRelationJson.id
              this.mainIdaData.type = newRelationJson.type || ''
              this.mainIdaData.tableKey = newRelationJson.tableKey
              this.mainIdaData.tableComment = newRelationJson.tableComment
              this.businessObject.mainBusinessTab = `${newRelationJson.tableComment}(${newRelationJson.tableKey})`
              this.subTableData = newRelationJson.children
            }
          })
        }
      } else {
        this.businessObject = {
          name: '',
          key: '',
          desc: '',
          mainBusinessTab: '()'
        }
        this.subTableData = []
        this.mainIdaData = {}
      }
    }
  },
  created () { },
  methods: {
    operateEvent (code) {
      switch (code) {
        case 'cancel':
          this.colseDialog()
          break
        case 'radio':
          this.subTabData = this.mainIdaData
          this.subTableDialogParam = {
            subTableModelVisible: true,
            type: 'radio'
          }
          break
        case 'checkbox':
          this.subTableDialogParam = {
            subTableModelVisible: true,
            type: 'checkbox'
          }
          break
        case 'save':
          this.handleSave()
          break
      }
    },
    handleSave () {
      this.$refs.addIda.validRowFks() && this.submitData()
    },
    submitData () {
      this.$refs.businessObject.validate((valid) => {
        if (valid) {
          const params = {}
          params.desc = this.businessObject.desc
          params.key = this.businessObject.key
          params.name = this.businessObject.name
          params.id = this.dialogParam.rowId || ''
          const relationJson = {
            id: this.mainIdaData.id,
            tableKey: this.mainIdaData.tableKey,
            type: this.mainIdaData.type || '',
            tableComment: this.mainIdaData.tableComment
          }
          relationJson.children = this.$refs.addIda.tableData
          params.relationJson = JSON.stringify(relationJson)
          this.saveLoading = true
          this.$aspHttps.asp_Post(this.$apiConfig.idaPathPrefix + '/businessObject/save', params).then(response => {
            this.saveLoading = false
            if (this.$reponseStatus(response)) {
              this.$message.success('保存成功')
              this.colseDialog()
              this.$emit('refreshTable')
            } else {
              this.$message.error('保存失败')
            }
          })
        }
      })
    },
    colseDialog () {
      this.dialogParam.obectModelVisible = false
    },
    openIdaDialog (data) {
      const { tableComment = '', tableKey = '', id = '', children = [] } = data.row
      this.iadDialogParam = {
        idaModelVisible: true,
        title: `[${tableComment}(${tableKey})]子表配置`,
        index: data.index,
        children,
        mainIdaData: {
          id,
          type: 'sub'
        }
      }
    },
    selectSubTabData (data) {
      if (this.subTableDialogParam.type === 'radio') {
        if (Object.keys(data).length <= 0) { return }
        const { tableComment, tableKey, id } = data
        this.mainIdaData = {
          id,
          tableComment,
          tableKey,
          type: 'par'
        }
        this.businessObject.mainBusinessTab = `${tableComment || ''}(${tableKey || ''})`
      } else if (this.subTableDialogParam.type === 'checkbox') {
        this.subTableData = data
      }
    },
    rowSubTabData (data) {
      this.$refs.addIda.tableData[data.index].children = data.children
    }
  }
}
</script>
