/** * Created by TurboC on 2018/10/19. */

<template>
  <div>
    <asp-dialog v-model="iadDialogParam.idaModelVisible"
                :visible.sync="iadDialogParam.idaModelVisible"
                :title="iadDialogParam.title"
                :appendToBody="true"
                class="webbas"
                width="80%">
      <template>
        <add-ida ref="addIda"
                 :subTableData="subTableData"
                 :mainIdaData="iadDialogParam.mainIdaData"
                 @openIdaDialog="openIdaDialog"
                 @openSubTable="operateEvent('checkbox')"></add-ida>
      </template>
      <template slot="footer-center">
        <asp-btn-solid name="保存"
                       @click="operateEvent('save')">
        </asp-btn-solid>
        <asp-btn-hollow icon="el-icon-close"
                        name="取消"
                        @click="operateEvent('cancel')">
        </asp-btn-hollow>
      </template>
    </asp-dialog>
    <sub-table v-if="subTableDialogParam.subTableModelVisible"
               :subTableDialogParam="subTableDialogParam"
               :subTabData="subTabData"
               @selectSubTabData="selectSubTabData"></sub-table>
    <ida-dialog v-if="subIadDialogParam.idaModelVisible"
                :iadDialogParam="subIadDialogParam"
                @rowSubTabData="rowSubTabData"></ida-dialog>
  </div>
</template>

<script>
import addIda from './add-ida.vue'
import subTable from './sub-table.vue'
export default {
  name: 'IdaDialog',
  components: { addIda, subTable },
  props: {
    iadDialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      businessObject: {
        name: '',
        key: '',
        desc: ''
      },
      subTableDialogParam: {
        type: 'checkbox',
        subTableModelVisible: false
      },
      subIadDialogParam: {
        idaModelVisible: false,
        title: ''
      },
      subTabData: {},
      subTableData: []
    }
  },
  computed: {

  },
  watch: {
    iadDialogParam: {
      handler: function (val) {
        if (val.idaModelVisible) {
          this.subTableData = val.children || []
        } else {
          this.subTableData = []
        }
      },
      deep: true,
      immediate: true
    }
  },
  created () {

  },
  methods: {
    operateEvent (val) {
      switch (val) {
        case 'save':
          if (this.$refs.addIda.validRowFks()) {
            this.$emit('rowSubTabData', {
              index: this.iadDialogParam.index,
              children: this.$refs.addIda.tableData
            })
            this.iadDialogParam.idaModelVisible = false
          }
          break
        case 'cancel':
          this.iadDialogParam.idaModelVisible = false
          break
        case 'checkbox':
          this.subTableDialogParam = {
            subTableModelVisible: true,
            type: 'checkbox'
          }
          break
      }
    },
    // 子列表选择实体数据
    selectSubTabData (data) {
      if (this.subTableDialogParam.type === 'checkbox') {
        this.subTableData = data
      }
    },
    openIdaDialog (data) {
      const { tableComment = '', tableKey = '', id = '', children = [] } = data.row
      const params = {
        idaModelVisible: true,
        title: `[${tableComment}(${tableKey})]子表配置`,
        index: data.index,
        children,
        mainIdaData: {
          id,
          type: 'sub'
        }
      }
      if (this.iadDialogParam.idaModelVisible) {
        this.subIadDialogParam = params
      } else {
        this.iadDialogParam = params
      }
    },
    rowSubTabData (data) {
      this.$refs.addIda.tableData[data.index].children = data.children
    }
  }
}
</script>
