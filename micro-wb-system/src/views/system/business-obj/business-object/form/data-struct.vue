/** * Created by TurboC on 2018/10/19. */

<template>
  <asp-dialog v-model="structDialogParam.structModelVisible"
              :visible.sync="structDialogParam.structModelVisible"
              title="JSON数据结构"
              :appendToBody="true"
              class="webbas"
              width="40%">
    <json-viewer :value="json"
                 :preview-mode="'true'"
                 :copyable="{copyText: '复制JSON',copiedText: '已复制',timeout: 2000,align: 'left'}">
    </json-viewer>
    <template slot="footer-center">
      <asp-btn-hollow icon="el-icon-close"
                      name="取消"
                      @click="structDialogParam.structModelVisible = false">
      </asp-btn-hollow>
    </template>
  </asp-dialog>
</template>

<script>
import JsonViewer from 'vue-json-viewer'
export default {
  name: 'DataStruct',
  components: { JsonViewer },
  props: {
    structDialogParam: {
      type: Object,
      default () {
        return {
          copyable: {
            copyText: '复制JSON',
            copiedText: '已复制',
            timeout: 2000,
            aligin: 'left'
          }
        }
      }
    }
  },
  data () {
    return {
      json: ''
    }
  },
  computed: {

  },
  watch: {
    structDialogParam: {
      handler: function (val) {
        if (val.structModelVisible) {
          this.getDataStruct()
        }
      },
      deep: true,
      immediate: true
    }
  },
  created () {

  },
  mounted () { },
  methods: {
    getDataStruct () {
      this.$aspHttps.asp_Post(this.$apiConfig.idaPathPrefix + '/businessObject/getObjStruct', { id: this.structDialogParam.rowId }).then(response => {
        if (this.$reponseStatus(response)) {
          this.json = response.data
        }
      })
    }
  }
}
</script>
