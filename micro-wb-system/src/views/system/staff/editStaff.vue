/** * Created by TurboC on 2018/10/19. */

<template>
  <asp-dialog v-model="dialogParam.staffModelVisible"
              :visible.sync="dialogParam.staffModelVisible"
              :title="dialogParam.title"
              width="70%">
    <template>
      <el-form ref="editStaff"
               :model="editStaff"
               :rules="rules"
               :inline="true"
               class="el-collapse-120">
        <el-row v-if="operateStatus">
          <el-col :span="12">
            <el-form-item v-if="isStafft && operateStatus && operateStatusCode === 'ADD'"
                          label="归属域："
                          prop="domain">
              <asp-select-opt v-model="editStaff.domain"
                              :code-list="dialogParam.editStaff.domainList"
                              :need-def-value="false"
                              @change="getOrganizationIds"></asp-select-opt>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="!isStaffForDept && operateStatus && operateStatusCode === 'ADD'"
                          label="归属机构："
                          prop="organizationId">
              <el-select v-model="editStaff.organizationId"
                         :disabled="isChangeOrg"
                         filterable
                         reserve-keyword
                         placeholder="请输入关键词"
                         @change="getOrgGrm">
                <el-tooltip v-for="item in organizationList"
                            :key="item.id"
                            :content="item.name"
                            class="item"
                            effect="dark"
                            placement="right">
                  <el-option :label="item.name"
                             :value="item.id">
                    {{
                      item.name.length >= 10
                        ? item.name.substring(0, 10) + "..."
                        : item.name
                    }}
                  </el-option>
                </el-tooltip>
              </el-select>
            </el-form-item>
            <el-form-item v-else-if="operateStatusCode !== 'ADD'"
                          label="归属机构："
                          prop="organizationName">
              <el-input type="text"
                        class="position-fixed-css"></el-input>
              <el-input v-model="editStaff.organizationName"
                        disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatus">
          <el-col :span="12">
            <el-form-item label="登录名："
                          prop="userName" v-if="operateStatusCode === 'ADD'">
              <el-input v-model.trim="editStaff.userName"
                        auto-complete="off">
              </el-input>
            </el-form-item>

            <el-form-item label="登录名：" v-else>
              <span>{{ editStaff.userName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="operateStatus"
                          label="归属部门："
                          prop="departmentName"
                          class="select-box">
              <div v-if="editStaff.departmentName"
                   class="icon-absolute-css">
                <i class="el-select__caret el-input__icon el-icon-circle-close"
                   @click="clearDeptName"></i>
              </div>
              <el-popover placement="bottom">
                <div class="overflow-auto-css height-200-css width-350-css">
                  <el-tree :data="departmentTreeData"
                           :props="defaultProps"
                           :expand-on-click-node="false"
                           @node-click="departTreeNodeClick"></el-tree>
                </div>
                <!--clearable="true"-->
                <el-input slot="reference"
                          v-model="editStaff.departmentName"
                          readonly
                          auto-complete="off"></el-input>
              </el-popover>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatus">
          <el-col :span="12">
            <el-form-item label="固定电话："
                          prop="telephone">
              <el-input type="password"
                        class="position-fixed-css"></el-input>
              <el-input v-model.trim="editStaff.telephone"
                        type="text"
                        auto-complete="new-password"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机："
                          prop="mobile">
              <el-input v-model.trim="editStaff.mobile"
                        maxlength="11">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatus">
          <el-col :span="12">
            <el-form-item label="邮箱："
                          prop="email">
              <el-input v-model.trim="editStaff.email"
                        auto-complete="off"
                        maxlength="100">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="expireDateStr"
                          class="change-date-icon"
                          label="账号有效期：">
              <el-date-picker v-model="editStaff.expireDateStr"
                              :disabled="isDisabled"
                              :picker-options="pickerOptions"
                              type="date"
                              align="right"
                              placeholder
                              value-format="yyyy-MM-dd"
                              class="width-100-css"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatus">
          <el-col :span="12">
            <el-form-item label="真实姓名："
                          prop="realName">
              <el-input v-model.trim="editStaff.realName"
                        placeholder="不能超过20个字符"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别："
                          prop="sex">
              <asp-select-opt v-model="editStaff.sex"
                              :code-list="sexList"></asp-select-opt>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatusCode === 'resetPw'">
          <el-col :span="12">
            <el-form-item label="新密码："
                          prop="newPassword">
              <el-input v-model="editStaff.newPassword"
                        type="password"
                        auto-complete="new-password"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码："
                          prop="pwdConfirm">
              <el-input v-model="editStaff.pwdConfirm"
                        type="password"
                        auto-complete="new-password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatus">
          <el-col :span="12">
            <el-form-item v-if="operateStatusCode === 'ADD'"
                          label="初始密码："
                          prop="newPassword">
              <el-input type="password"
                        class="position-fixed-css"></el-input>
              <el-input v-model="editStaff.newPassword"
                        type="password"
                        auto-complete="new-password"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="operateStatusCode === 'ADD'"
                          label="确认密码："
                          prop="pwdConfirm">
              <el-input type="password"
                        class="position-fixed-css"></el-input>
              <el-input v-model="editStaff.pwdConfirm"
                        type="password"
                        auto-complete="new-password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatus">
          <el-col v-if="userInforMaskItems.indexOf('managerOrganization') == -1"
                  :span="24">
            <el-form-item label="跨机构权限："
                          prop>
              <el-button type="primary"
                         size="small"
                         class="solid-no-icon-btn"
                         @click="operateSelectOrg">选择</el-button>
              <div v-text="selectedOrgText"></div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatus">
          <el-col v-if="userInforMaskItems.indexOf('division') == -1"
                  :span="24">
            <el-form-item label="归属区域："
                          size="small"
                          class="check-areas">
              <el-button type="primary"
                         class="solid-no-icon-btn"
                         @click="operateArea">选择</el-button>
              <div v-if="divisionSModelVisible"
                   class="el-form-item__error">
                所属地域不能为空
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatus && userInforMaskItems.indexOf('division') == -1"
                class="el-collapse-all">
          <el-col :span="24">
            <el-form-item>
              <div class="edit-staff-area-info"
                   :style="editStaff.listDivisionName.length > 0 ? {paddingLeft: '120px'} : {}">
                <template v-if="editStaff.listDivisionName.length === 0">
                  <center>{{ emptyText }}</center>
                </template>
                <template v-for="(item, index) in editStaff.listDivisionName">
                  <p :key="index">{{ item.pathName }}</p>
                </template>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow icon="el-icon-close"
                      name="取消"
                      @click="operateEvent('cancel')">
      </asp-btn-hollow>
      <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_100118' })"
                     v-if="dialogParam.dataFrom === 'staffForDept'"
                     v-show="operateStatusCode === 'EDIT' && editStaff.status !== 'EXPIRED'"
                     name="重置密码"
                     icon="el-icon-refresh"
                     @click="operateEvent('resetPw')">
      </asp-btn-solid>
      <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_030118' })"
                     v-if="dialogParam.dataFrom === 'staff'"
                     v-show="operateStatusCode === 'EDIT' && editStaff.status !== 'EXPIRED'"
                     name="重置密码"
                     icon="el-icon-refresh"
                     @click="operateEvent('resetPw')">
      </asp-btn-solid>
      <asp-btn-solid v-loading="submitStatus"
                     :disabled="submitStatus"
                     icon="el-icon-check"
                     name="保存"
                     @click="operateEvent('save')">
      </asp-btn-solid>
    </template>
    <!-- <CheckAreas :dialog-param="checkAreaParam" -->
    <!-- @updateAreaData="updateCheckedArea"></CheckAreas> -->
    <transferAreas :dialog-param="checkAreaParam"
                   @updateAreaData="updateCheckedArea"></transferAreas>
    <SelectOrg :dialog-param="dialogSelectOrgParam"
               @updateSelectOrg="updateSelectOrg"></SelectOrg>
  </asp-dialog>
</template>

<script>
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
// import CheckAreas from '../components/areas/checkAreas'
import transferAreas from '../components/areas/transferAreas'
import SelectOrg from './selectOrg'
// import qs from 'qs'
const VAR_INIT_STR = ''
export default {
  name: 'AddUser',
  components: { SelectOrg, transferAreas },
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    var validateLoginName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('登录名不能为空'))
      } else if (this.operateStatusCode !== 'ADD') {
        // 修改用户时，登录名无需校验
        callback()
      } else {
        var checkLoginNameUrl = this.$apiConfig.managerPathPrefix + '/user/checkUserName'
        const param = {
          domain: this.userDomain,
          userId: this.editStaff.id,
          userName: this.editStaff.userName
        }
        this.$aspHttps.asp_Post(checkLoginNameUrl, param).then(response => {
          if (this.$reponseStatus(response) && response.data) {
            callback()
          } else {
            callback(new Error('登录名已存在'))
          }
        })
      }
    }
    var validatePhone = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('手机号不能为空'))
      } else {
        var checkPhoneUrl =
          this.$apiConfig.managerPathPrefix + '/user/checkMobile'
        const param = {
          userId: this.editStaff.id, // 有Id值的不用校验自己的
          mobile: this.editStaff.mobile
        }
        this.$aspHttps.asp_Post(checkPhoneUrl, param).then(response => {
          if (this.$reponseStatus(response) && response.data) {
            callback()
          } else {
            callback(new Error('手机号已被使用'))
          }
        })
      }
    }
    var validateEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('邮箱不能为空'))
      } else {
        var checkEmailUrl =
          this.$apiConfig.managerPathPrefix + '/user/checkEmail'
        const param = {
          userId: this.editStaff.id,
          email: this.editStaff.email
        }
        this.$aspHttps.asp_Post(checkEmailUrl, param).then(response => {
          if (this.$reponseStatus(response) && response.data) {
            callback()
          } else {
            callback(new Error('邮箱已被使用'))
          }
        })
      }
    }
    return {
      isChangeOrg: true, // 是否修改机构
      userInforMaskItems: [],
      initDateStr: '5018-12-31', // 默认有效期3000年后
      userDomain: '', // 当前登录用所属域
      oldDeptId: '', // 原有部门Id，用于区分比较部门修改
      domain: '', // 用于区分域的用户
      modulus: '', // 用于密码加密操作
      exponent: '', // 用于密码加密操作
      clearableVisible: false, // 清除按钮是否显隐
      popoverTreeVisible: false,
      submitStatus: false,
      OrgLoading: false,
      tableColSave: [],
      divisionSModelVisible: false, // 地域数据是否为空
      queryOrgNameVisible: false, // 校验机构名称是否符合
      editStaff: {
        newPassword: VAR_INIT_STR,
        pwdConfirm: VAR_INIT_STR
      },
      // 重置密码
      resetPwVisible: false,
      isDisabled: false, // 账号有效期是否禁用
      operateStatusCode: '', // 操作状态参数
      zIndex: 1, // input框z-index是否显示，默认显示
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      rules: {
        userName: [
          { required: true, message: '登录名不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' },
          { validator: validateLoginName, trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '真实姓名不能为空', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' }
        ],
        domain: [
          { required: true, message: '归属域不能为空', trigger: 'change' }
        ],
        organizationId: [
          { required: true, message: '归属机构不能为空', trigger: 'change' }
          // { validator: this.$main_tools.validation.checkSpecial(rule, this.searchName, callback), trigger: 'blur' }
        ],
        organizationName: [
          { required: true, message: '归属机构不能为空', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'change' }
        ],
        departmentName: [
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' }
        ],
        telephone: [
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' },
          { max: 20, message: '输入不能超过20个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '邮箱不能为空', trigger: 'blur' },
          { max: 100, message: '输入不能超过100个字符', trigger: 'blur' },
          { validator: this.$main_tools.validation.email, trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          { validator: this.$main_tools.validation.mobile, trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '密码不能为空', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkPassword, trigger: 'blur' }
        ],
        pwdConfirm: [
          { required: true, message: '确认密码不能为空', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkPassword, trigger: 'blur' },
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value !== this.editStaff.newPassword) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        sex: [
          // { required: true, message: '性别不能为空', trigger: 'blur' }
        ],
        expireDateStr: [
          { required: true, message: '账号有效期不能为空', trigger: 'blur' }
        ]
      },
      // 机构相关配置
      checkOrganization: {},
      organizationList: [],
      departmentTreeData: [],
      checkedDept: [],
      keiVal: 'id',
      props: {
        key: this.keiVal,
        label: 'text',
        value: 'id',
        children: 'children'
      },
      defaultProps: {
        children: 'children',
        label: 'text'
      },
      checkAreaParam: { // 选择地域相关操作传递参数
        areaModelVisible: false,
        parentDivision: '',
        title: '关联归属区域',
        checkedIds: []
      },
      emptyText: '加载中··',
      dataDivision: '', // 所属机构或部门的地域Id
      // 管理的机构弹窗参数
      dialogSelectOrgParam: {
        modelVisible: false,
        title: '关联归属区域',
        data: {
          total: [],
          selected: []
        }
      },
      selectedOrgText: '',
      sexList: [
        { code: '1', name: '男' },
        { code: '2', name: '女' }
      ]
    }
  },
  computed: {
    operateStatus () {
      return (
        this.operateStatusCode === 'EDIT' || this.operateStatusCode === 'ADD'
      )
    },
    urlPart () {
      return this.dialogParam.dataFrom === 'staffForDept' ||
        this.dialogParam.dataFrom === 'deptForDept'
        ? '/userOrganization'
        : '/user'
    },
    isStaffForDept () {
      return (
        this.dialogParam.dataFrom === 'staffForDept' ||
        this.operateStatusCode === 'EDIT'
      )
    },
    isStafft () {
      return (
        this.dialogParam.dataFrom === 'staff' &&
        this.operateStatusCode === 'ADD'
      )
    }
  },
  watch: {
    async 'dialogParam.staffModelVisible' (val) {
      if (val) {
        this.userDomain = this.dialogParam.editStaff.domain
          ? this.dialogParam.editStaff.domain
          : this.userDomain
        // 初始化数据
        this.$refs.editStaff && this.$refs.editStaff.resetFields()
        this.clearHasDivisionVal()
        await this.getOrganizationList()
        this.divisionSModelVisible = false
        if (this.dialogParam.editStaff.id !== '') {
          this.operateStatusCode = 'EDIT'
          this.emptyText = '加载中··'
          this.editStaff = {
            ...this.editStaff,
            ...{ id: this.dialogParam.editStaff.id }
          }
          // 获取初始地域信息
          await this.getUserByUserId(this.editStaff.id)
          if (this.editStaff.organizationId) {
            this.getOrgByOrgId(this.editStaff.organizationId)
          }
        } else {
          this.operateStatusCode = 'ADD'
          this.emptyText = '暂无数据'
          this.editStaff = {
            id: '',
            domain: this.userDomain,
            userName: '',
            realName: '',
            sex: '',
            mobile: '',
            organizationId: '',
            organizationName: '',
            departmentId: '',
            departmentName: '',
            telephone: '',
            email: '',
            password: VAR_INIT_STR,
            newPassword: VAR_INIT_STR,
            pwdConfirm: VAR_INIT_STR,
            expireDateStr: '',
            divisions: [],
            listDivisionName: []
          }
          // 如果外面有传值
          this.editStaff.organizationId =
            this.dialogParam.editStaff.organizationId || ''
          if (this.editStaff.organizationId) {
            this.getOrgByOrgId(this.editStaff.organizationId)
          }
        }
        if (
          this.dialogParam.dataFrom === 'dept' ||
          this.dialogParam.dataFrom === 'deptForDept'
        ) {
          this.isChangeOrg = true
        }
        // 是否可编辑归属机构
        if (this.dialogParam.dataFrom === 'staff') {
          this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/user/checkEditOrg', { id: this.dialogParam.editStaff.id }).then((response) => {
            if (this.$reponseStatus(response)) {
              this.isChangeOrg = !response.data
            }
          })
        }
      } else {
        // 点击取消 数据重置
        this.$refs.editStaff.resetFields()
        this.submitStatus = false
        this.editStaff.blacklistTypes = []
        this.isChangeOrg = true
      }
    },
    operateStatusCode (val) {
      if (val !== 'ADD') {
        this.zIndex = 0
      }
    }
  },
  created () {
    const domain = this.$aspUtils.getDomainObject(this)
    this.userDomain = domain.userInfo.domain
    this.tableColSave.push({ rowId: { attachment: [] } })
    this.userInforMaskItems = domain.platformConfig.userInforMaskItems
      ? domain.platformConfig.userInforMaskItems.split(',')
      : []
    // this.userInforMaskItems = []
  },
  methods: {
    typesChange (val, code) {
      // let index = this.editStaff.blacklistTypes.indexOf(code)
      // if (val === 1) {
      //   index === -1 ? this.editStaff.blacklistTypes.push(code) : ''
      // } else {
      //   index > -1 ? this.editStaff.blacklistTypes.splice(index, 1) : ''
      // }
    },
    showDoAuth (val) {
      const result = (this.dialogParam.dataFrom === 'staffForDept' && val === 'wb_100118') ||
        (this.dialogParam.dataFrom === 'staff' && val === 'wb_030118')
      return result
    },
    // 按钮权限
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    doAuthArr (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCodeArr: opt.btnCodeArr }
    },
    // 清除部门相关数据
    clearDeptVal () {
      this.editStaff.departmentId = ''
      this.editStaff.departmentName = ''
      this.departmentTreeData = []
    },
    // 清除所属地域相关数据
    clearHasDivisionVal () {
      this.editStaff.divisions = []
      this.editStaff.listDivisionName = []
      this.emptyText = '暂无数据'
    },
    // 获取资源信息进行加密操作
    getConfig () {
      const { platformConfig } = this.$aspUtils.getListAllObject(this)
      this.modulus = platformConfig ? platformConfig.modulus : ''
      this.exponent = platformConfig ? platformConfig.exponent : ''
      // 提交
      this.submitOperStaff()
    },
    addRSA (password) {
      // RSA
      asp_RSAKey.RSASetPublic(this.modulus, this.exponent)
      return asp_RSAKey.RSAEncrypt(password)
    },
    // 通过Id获取机构详情
    // @param id 查询结构详情的Id
    getOrgByOrgId (id) {
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/organization/get', { id: id }).then(response => {
        if (this.$reponseStatus(response)) {
          this.domain = response.data.domain
          this.dataDivision = response.data.division
          if (this.editStaff.departmentId) {
            // if (this.editStaff.departmentId !== '' && this.editStaff.departmentId !== null) {
            // 初始化原有部门Id
            this.oldDeptId = this.editStaff.departmentId
            this.getDeptByDeptId(this.editStaff.departmentId)
          }
          // 获取机构列表
          this.getDepartTree(id)
        }
      })
    },
    // 通过Id获取部门详情
    // @param id 查询部门详情的Id
    getDeptByDeptId (id) {
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/department/get', { id: id }).then(response => {
        if (this.$reponseStatus(response)) {
          this.domain = response.data.domain
          this.dataDivision = response.data.division
        }
      })
    },
    // 通过Id用户详情
    // @param id 用户Id
    async getUserByUserId (id) {
      this.emptyText = '加载中··'
      const urlGet =
        this.urlPart === '/user'
          ? this.$apiConfig.managerPathPrefix + this.urlPart + '/get'
          : this.$apiConfig.managerPathPrefix + this.urlPart + '/getById'
      const response = await this.$aspHttps.asp_PostForm(urlGet, { id: id })
      if (this.$reponseStatus(response) && response.data) {
        this.domain = response.data.domain
        this.editStaff = { ...this.editStaff, ...response.data }
        this.selectedOrgText = this.getSelectedOrgText(response.data.managerOrgIds)
        if (response.data?.listDivisionName.length > 0) {
          this.editStaff.listDivisionName = response.data.listDivisionName.map(item => {
            if (item[0] === '{') {
              return JSON.parse(item)
            } else {
              return {
                pathName: item
              }
            }
          })
        } else {
          this.emptyText = '暂无数据'
        }
      }
    },
    // 获取所属机构
    async getOrganizationList () {
      let url = ''
      const params = {}
      // 部门管理新增成员 || 机构成员管理新增/编辑成员信息
      if (this.dialogParam.dataFrom === 'deptForDept' || this.dialogParam.dataFrom === 'staffForDept') {
        url = '/organization/listUserOrganizationForCombo'
      } else {
        params.domain = this.editStaff.domain ? this.editStaff.domain : this.userDomain
        url = '/organization/listForCombo'
      }
      const response = await this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + url, params)
      if (this.$reponseStatus(response)) {
        this.organizationList = response.data
      }
    },
    // 获取部门树形信息--默认查机构下部门树（若有传部门id,则查部门下部门树）
    // @param orgId 机构Id值
    getDepartTree (orgId) {
      let oId = orgId // 机构id
      let url = '/department/listDepartmentTree' // 查询机构下部门树
      let hander = this.$aspHttps.asp_PostForm
      // 当前节点是否是部门节点--部门节点传部门id，机构节点传机构id（接口url也是同理）
      const { currentData = {} } = this.dialogParam
      const { id, organizationId } = currentData
      if (id) oId = id // 机构或部门id
      if (id && organizationId && id !== organizationId) { // 当且仅当机构id、部门id都存在，且部门id不等于机构id时，当前节点id为部门id
        oId = id // 当前节点id（部门id）
        url = '/department/listDepartmentLeaf' // 当前部门树请求接口，默认部门
        hander = this.$aspHttps.asp_Post
      }
      hander(this.$apiConfig.managerPathPrefix + url + '?id=' + oId).then(response => {
        if (this.$reponseStatus(response)) {
          this.departmentTreeData = response.data
        }
      })
    },
    // 编辑提交
    operateEvent (param) {
      // 关闭弹窗 初始化信息
      const resetDialog = () => {
        this.submitStatus = false
        this.dialogParam.staffModelVisible = false
        // 点击取消 数据重置
        this.$refs.editStaff.resetFields()
        // 清空其他数据
        this.selectedOrgText = ''
      }
      if (param === 'save') {
        this.submitStatus = true
        this.$refs.editStaff.validate(valid => {
          if (this.userInforMaskItems.indexOf('division') === -1 &&
            (!this.editStaff.divisions || this.editStaff.divisions.length === 0) &&
            this.operateStatusCode !== 'resetPw'
          ) {
            this.divisionSModelVisible = true
            valid = false
          }
          if (valid) {
            // 先获取加密秘钥，再进行提交
            this.getConfig()
            // resetDialog()
          } else {
            this.submitStatus = false
          }
        })
      } else if (param === 'resetPw') {
        this.operateStatusCode = 'resetPw'
        this.dialogParam.title = '重置密码'
        this.editStaff = {}
        this.$set(this.editStaff, 'newPass' + 'word', VAR_INIT_STR) // 新密码
        this.$set(this.editStaff, 'pass' + 'word', VAR_INIT_STR) // 不确定是否为多余参数，暂时保留
        this.$set(this.editStaff, 'pwd' + 'Confirm', VAR_INIT_STR) // 确认密码
        // 先获取加密秘钥，再进行提交
        // this.getConfig() // 重置密码时出现重置密码输入框，保存时在提交
      } else {
        // 取消
        resetDialog()
        this.submitStatus = false
      }
    },
    // 提交操作
    submitOperStaff () {
      let operMessage = ''
      const oper = this.operateStatusCode
      let operUrl = ''
      let param = {}
      switch (oper) {
        case 'ADD':
          operUrl =
            this.$apiConfig.managerPathPrefix + this.urlPart + '/insert'
          param = {
            userName: this.editStaff.userName,
            realName: this.editStaff.realName,
            sex: this.editStaff.sex,
            mobile: this.editStaff.mobile,
            departmentId: this.editStaff.departmentId,
            organizationId: this.editStaff.organizationId,
            telephone: this.editStaff.telephone,
            email: this.editStaff.email,
            domain: this.editStaff.domain, // 机构归属域名，暂时默认admin
            password: this.addRSA(this.editStaff.newPassword),
            divisions: this.userInforMaskItems.indexOf('division') === -1 ? this.editStaff.divisions : ['000'],
            expireDateStr: this.userDomain === 'admin' ? this.editStaff.expireDateStr : this.initDateStr,
            manageOrgIds: this.dialogSelectOrgParam.data.selected
          }
          operMessage = '新增成功！'
          break
        case 'EDIT':
          operUrl =
            this.$apiConfig.managerPathPrefix + this.urlPart + '/update'
          param = {
            id: this.editStaff.id,
            realName: this.editStaff.realName,
            email: this.editStaff.email,
            mobile: this.editStaff.mobile,
            sex: this.editStaff.sex,
            departmentId: this.editStaff.departmentId,
            organizationId: this.editStaff.organizationId,
            telephone: this.editStaff.telephone,
            divisions: this.userInforMaskItems.indexOf('division') === -1 ? this.editStaff.divisions : ['000'],
            expireDateStr: this.userDomain === 'admin' ? this.editStaff.expireDateStr : this.initDateStr,
            manageOrgIds: this.dialogSelectOrgParam.data.selected
          }
          operMessage = '修改成功！'
          break
        case 'resetPw':
          operUrl =
            this.$apiConfig.managerPathPrefix + this.urlPart + '/resetPwd'
          param = {
            newPassword: this.addRSA(this.editStaff.newPassword),
            userId: this.dialogParam.editStaff.id
          }
          operMessage = '重置密码成功！'
          break
      }
      this.submitStatus = true
      this.editStaff.departmentId = this.editStaff.departmentId === null ? '' : this.editStaff.departmentId
      // 通过选择的部门或机构获取当前用户所属的域
      this.$aspHttps.asp_Post(operUrl, param).then(response => {
        if (this.$reponseStatus(response)) {
          this.$message.success(operMessage)
          this.$emit('search')
          this.operateEvent('cancel')
          // this.submitStatus = false
        } else {
          this.submitStatus = false
        }
      })
    },
    // 获取选中的机构信息
    // @param val 获取的选择的机构Id值
    getOrgGrm (val) {
      this.clearHasDivisionVal()
      this.dataDivision = ''
      this.checkOrganization.id = val
      this.editStaff.organizationId = val
      this.clearDeptVal()
      this.getOrgByOrgId(val)
    },
    // 获取部门信息保存值
    // @param data 选择的树形数据
    departTreeNodeClick (data) {
      if (data.id !== this.editStaff.departmentId) {
        // 修改部门数据，清空已选相关地域数据
        this.clearHasDivisionVal()
      }
      this.editStaff.departmentName = data.text
      this.editStaff.departmentId = data.id
      this.domain = data.domain
      this.popoverTreeVisible = false
      this.getDeptByDeptId(data.id)
    },
    // 清除已选部门数据
    clearDeptName () {
      this.editStaff.departmentName = ''
      this.editStaff.departmentId = ''
      // 清除已选地域相关信息
      this.clearHasDivisionVal()
      // 继承选择机构所属
      this.getOrgByOrgId(this.editStaff.organizationId)
    },
    // 操作地域--TODO-操作地域--条件必须至少选择机构
    operateArea () {
      if (this.dataDivision === null || this.dataDivision === '') {
        this.$message.warning('请至少要选择一个机构！')
        return
      }
      // 打开弹窗前获取数据--地域数据
      this.checkAreaParam.areaModelVisible = true
      this.checkAreaParam.parentDivision = this.dataDivision
      this.checkAreaParam.checkedIds = this.editStaff.divisions
      this.checkAreaParam.checkedDatas = this.editStaff.listDivisionName
    },
    // 获取/更新选择的所属地域数据
    updateCheckedArea (data) {
      this.emptyText = '加载中··'
      this.editStaff.listDivisionName = data.Nodes
      this.editStaff.divisions = data.Ids
      this.checkAreaParam = { // 并初始化弹窗数据
        areaModelVisible: false,
        parentDivision: '',
        title: '关联归属区域',
        checkedIds: [],
        checkedDatas: []
      }
      if (data.Ids.length > 0) {
        this.divisionSModelVisible = false
      }
    },
    // 在修改用户对话框框关闭前调用
    beforeCloseDo (done) {
      this.submitStatus = false
      done()
    },
    // 输出选中的机构id对应的文字
    getSelectedOrgText (data) {
      if (data && data.length > 0) {
        const dict = {}
        const res = []
        this.organizationList.map(item => {
          dict[item.id] = item.name
        })
        data.map(item => {
          if (dict[item]) {
            res.push(dict[item])
          }
        })
        return res.length === 0 ? '暂无' : res.join(',')
      } else {
        return '暂无'
      }
    },
    // 操作管理的机构
    operateSelectOrg () {
      this.dialogSelectOrgParam.modelVisible = true
      this.dialogSelectOrgParam.data = {
        total: this.organizationList,
        selected: this.editStaff.managerOrgIds
      }
    },
    // 获取选择的管理的机构数据
    updateSelectOrg (data) {
      this.dialogSelectOrgParam.data.selected = data
      this.dialogSelectOrgParam.modelVisible = false
      this.selectedOrgText = this.getSelectedOrgText(data)
    },
    // 获取归属机构数据
    async getOrganizationIds (val) {
      // 数据重置
      this.$refs.editStaff.resetFields()
      this.editStaff.blacklistTypes = []
      this.editStaff.domain = val
      await this.getOrganizationList()
    }
  }
}
</script>
