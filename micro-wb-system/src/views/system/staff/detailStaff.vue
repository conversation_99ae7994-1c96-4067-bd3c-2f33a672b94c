/** * 系统成员管理详情 */
<template>
  <asp-dialog v-model="dialogParam.modelVisible"
              :visible.sync="dialogParam.modelVisible"
              :title="dialogParam.title"
              class="detailStaff"
              width="60%">
    <template>
      <el-tabs v-model="activeName"
               type="border-card"
               @tab-click="handleTabClick">
        <el-tab-pane label="成员详情"
                     name="staffDetail">
          <detail ref="detailStaff"
                  :dialog-param.sync="detailParam"></detail>
        </el-tab-pane>
        <el-tab-pane v-if="
            authInfoList.indexOf('wb_030106') > -1 &&
              row.originType == '0' &&
              row.status !== 'EXPIRED'
          "
                     label="分配角色权限"
                     name="selectRole">
          <selectRole :dialog-param="dialogSelectRoleParam"
                      @changeStatus="changeStatus"
                      @updateSelectOrg="updateSelectOrg"></selectRole>
        </el-tab-pane>
        <el-tab-pane v-if="
            authInfoList.indexOf('wb_030118') > -1 &&
              row.originType == '0' &&
              row.status !== 'EXPIRED'
          "
                     label="重置密码"
                     name="resetPwd">
          <resetPwd ref="resetPwd"
                    :dialog-param="resetPwdParam"></resetPwd>
        </el-tab-pane>
        <el-tab-pane v-if="receiveStatus"
                     label="接收信息配置"
                     name="receiveMsg">
          <receiveInfo ref="receiveInfo"
                       :user-id="userId"></receiveInfo>
        </el-tab-pane>
      </el-tabs>
    </template>
    <template slot="footer-center">
      <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_030103' })"
                     v-if="row.originType == '0'"
                     name="变更"
                     @click="handleEdit">
      </asp-btn-solid>
      <!-- <asp-btn-solid
                v-hasAuth="doAuth({btnCode:'wb_030106'})"
                v-if="row.originType == '0' && row.status !== 'EXPIRED'"
                name="分配角色权限"
                @click="handleEditRole"
            >
            </asp-btn-solid> -->
      <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_030108' })"
                     v-if="
          row.originType == '0' &&
            (detailStaff.status === 'NORMAL' ||
              detailStaff.status === 'INACTIVE')
        "
                     :name="detailStaff.status === 'NORMAL' ? '禁用' : '启用'"
                     @click="handleUseOrStop">
      </asp-btn-solid>
      <!-- <asp-btn-solid
                v-hasAuth="doAuth({btnCode:'wb_030118'})"
                v-if="row.originType == '0' && row.status !== 'EXPIRED'"
                name="重置密码"
                @click="handleResetPassword"
            >
            </asp-btn-solid> -->
      <asp-btn-solid v-hasAuth="doAuthArr({ btnCodeArr: ['wb_030126'] })"
                     name="账号登录日志"
                     @click="handleLog">
      </asp-btn-solid>
      <asp-btn-hollow name="返回"
                      @click="handleClose"> </asp-btn-hollow>
    </template>
  </asp-dialog>
</template>

<script>
import selectRole from './selectRole'
import resetPwd from './resetPwd'
import detail from './detail'
import receiveInfo from './receiveInfo'
export default {
  name: 'DetailStaff',
  components: { selectRole, resetPwd, detail, receiveInfo },
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      activeName: 'staffDetail',
      detailStaff: {
        id: '',
        userName: '',
        mobile: '',
        email: '',
        expireDateStr: '',
        organizationId: '',
        departmentName: '',
        realName: '',
        sex: '',
        telephone: '',
        organizationName: '',
        status: '',
        statusText: '',
        roleNames: '',
        listDivisionName: '',
        managerOrgNames: ''
      },
      userInforMaskItems: [],
      statusList: {},
      // 管理的角色弹窗参数
      roleList: [],
      dialogSelectRoleParam: {
        data: {
          total: [],
          selected: []
        },
        roleType: '0' // 运营角色
      },
      // 重置密码弹窗
      resetPwdParam: {
        modelVisible: false,
        data: {
          id: '',
          urlPart: ''
        }
      },
      // 变更按钮需要
      row: {},
      urlPart: '/user',
      detailParam: {
        detailData: {}
      },
      isRoleFirst: true, // 用于区分是否是第一次打开分配角色权限页签
      authInfoList: [], // 当前用户所拥有的权限数据
      userId: '',
      receiveStatus: false
    }
  },
  computed: {},
  watch: {
    'dialogParam.modelVisible' (val) {
      if (val) {
        this.isRoleFirst = true
        if (this.dialogParam.data) {
          this.setStaff(this.dialogParam.data)
        }
        this.userId = this.dialogParam.data.id
        this.$nextTick(() => {
          this.getUserByUserId(this.dialogParam.data.id) // 获取用户详情数据
          this.getRoleList(this.dialogParam.data.id) // 获取角色列表
          this.resetPwdParam.data.urlPart = this.dialogParam.data.urlPart
        })
      } else {
        // 点击取消 数据重置
        this.detailStaff = {}
        this.activeName = 'staffDetail'
      }
    }
  },
  created () {
    const domain = this.$aspUtils.getDomainObject(this)
    this.userDomain = domain.userInfo.domain
    this.userInforMaskItems = domain.platformConfig.userInforMaskItems
      ? domain.platformConfig.userInforMaskItems.split('，')
      : []
    // this.userInforMaskItems = []
    this.authInfoList = domain.authInfo.wb_030101
      ? domain.authInfo.wb_030101
      : []
    // 是否展示接收信息配置
    const userForbidStatus = this.$aspUtils.getListAllObject(this).platformConfig.userForbidStatus
    this.receiveStatus = userForbidStatus && userForbidStatus !== 'disable'
  },
  mounted () {
    this.getStatusList()
  },
  methods: {
    doAuthArr (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCodeArr: opt.btnCodeArr }
    },
    // 按钮权限
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    // 设置内容
    setStaff (val) {
      this.detailStaff.id = val.id
      this.detailStaff.userName = val.userName
      this.detailStaff.mobile = val.mobile
      this.detailStaff.email = val.email
      this.detailStaff.expireDateStr = val.expireDateStr
      this.detailStaff.organizationId = val.organizationId
      this.detailStaff.departmentName = val.departmentName
      this.detailStaff.realName = val.realName
      this.detailStaff.sex = this.getDictSex(val.sex)
      this.detailStaff.telephone = val.telephone
      this.detailStaff.organizationName = val.organizationName
      this.detailStaff.status = val.status
      this.detailStaff.statusText = this.getDictStatus(val.status)
      this.row = val.row
    },
    // 状态翻译
    getDictStatus (val) {
      const item = this.statusList.find(status => status.code === val)
      return item ? item.name : ''
    },
    // 性别翻译
    getDictSex (val) {
      const dic = { 1: '男', 2: '女' }
      return dic[val]
    },
    // 通过Id获取用户详情(脱敏数据)
    getUserByUserId (id) {
      const urlGet =
        this.$apiConfig.managerPathPrefix +
        this.urlPart +
        '/getDesensitizationUserInfo'
      this.$aspHttps
        .asp_PostForm(urlGet, { id: id })
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.domain = response.data.domain
            const _data = response.data
            this.detailStaff = { ...this.detailStaff, ..._data }
            this.detailStaff.listDivisionName = ''
            if (_data.listDivisionName.length > 0) {
              this.detailStaff.listDivisionName = _data.listDivisionName.map(item => {
                if (item[0] === '{') {
                  return JSON.parse(item)
                } else {
                  return {
                    pathName: item
                  }
                }
              })
            }
            // this.detailStaff.listDivisionName = _data.listDivisionName
            //   ? _data.listDivisionName.join('，')
            //   : ''
            this.detailStaff.managerOrgNames = _data.managerOrgNames
              ? _data.managerOrgNames.join('，')
              : ''
            this.detailParam = {} // 先置空才能触发页面渲染
            this.detailParam.detailData = this.detailStaff
          }
        })
    },
    // 获取角色列表
    getRoleList (id) {
      this.$aspHttps
        .asp_PostForm(
          this.$apiConfig.managerPathPrefix + this.urlPart + '/listUserRoles',
          { id: id }
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.roleList = response.data
            if (!this.isRoleFirst) {
              this.$nextTick(() => {
                this.dialogSelectRoleParam.data.selected = this.roleList.filter(
                  val => val.check === 'true'
                )
              })
            }
          }
        })
    },
    // 禁用
    doInactive (id) {
      const lockedStaffUrl =
        this.$apiConfig.managerPathPrefix + this.urlPart + '/inactive'
      const updateStatusParams = {
        reason: 'lock',
        userId: id
      }
      this.$aspHttps
        .asp_Post(lockedStaffUrl, updateStatusParams)
        .then(response => {
          if (this.$reponseStatus(response)) {
            if (response.status === '200') {
              // 重置才能重新渲染页面
              const newDetailStaff = this.detailStaff
              this.detailStaff = {}
              this.detailStaff = newDetailStaff
              this.detailStaff.status = 'INACTIVE'
              this.detailStaff.statusText = this.getDictStatus('INACTIVE')
              this.$refs.detailStaff.setDetail(this.detailStaff)
              this.$message.success(response.message)
              this.emitUpdate()
            }
          }
        })
    },
    // 启用
    doUse (id) {
      this.$aspHttps
        .asp_PostForm(
          this.$apiConfig.managerPathPrefix + this.urlPart + '/active',
          { id: id }
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            if (response.status === '200') {
              // 重置才能重新渲染页面
              const newDetailStaff = this.detailStaff
              this.detailStaff = {}
              this.detailStaff = newDetailStaff
              this.detailStaff.status = 'NORMAL'
              this.detailStaff.statusText = this.getDictStatus('NORMAL')
              this.$refs.detailStaff.setDetail(this.detailStaff)
              this.$message.success(response.message)
              this.emitUpdate()
            }
          }
        })
    },
    // 获取状态字典
    getStatusList () {
      this.statusList = this.$aspUtils.getCodeValueByType(this, 'USER_STATUS')
    },
    // 通知外部变更
    emitUpdate () {
      this.$emit('updateStafdList')
    },
    // 权限关系下载
    handleDownload () {
      const id = this.dialogParam.data.id
      let url =
        this.$apiConfig.managerPathPrefix +
        this.urlPart +
        '/exportUserRight?userId=' +
        id
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    // 变更
    handleEdit () {
      this.$emit('handleEdit', this.row)
    },
    // 分配角色权限
    handleEditRole () {
      this.dialogSelectRoleParam.isRoleFirst = this.isRoleFirst
      this.dialogSelectRoleParam.data = {
        id: this.detailStaff.id,
        total: this.roleList,
        selected: this.roleList.filter(val => val.check === 'true')
      }
    },
    updateSelectOrg (data) {
      // console.log(data)
      this.getUserByUserId(this.dialogParam.data.id)
      this.getRoleList(this.dialogParam.data.id)
    },
    // 启用或禁用
    handleUseOrStop () {
      const disable = this.detailStaff.status === 'NORMAL'
      const title = disable ? '禁用' : '启用'
      this.$confirm(`是否${title}？`, '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: false
      })
        .then(async () => {
          disable
            ? this.doInactive(this.detailStaff.id)
            : this.doUse(this.detailStaff.id)
        })
        .catch(() => { })
    },
    // 重置密码
    handleResetPassword () {
      const id = this.detailStaff.id
      this.resetPwdParam.data.id = id
    },
    // 账号登录日志
    handleLog () {
      this.handleClose()
      this.$router.push({
        name: 'staffLog',
        query: { operatorId: this.detailStaff.id }
      })
    },
    // 返回
    handleClose () {
      this.dialogParam.modelVisible = false
    },
    handleTabClick (val) {
      switch (val.label) {
        case '分配角色权限':
          this.handleEditRole()
          break
        case '重置密码':
          this.handleResetPassword()
          break
        // case '接收信息配置':
        //     this.$refs.receiveInfo.$refs.receiveInfoConfigure.getDetail()
        //     break
        default:
      }
    },
    changeStatus () {
      this.isRoleFirst = false
    }
  }
}
</script>
