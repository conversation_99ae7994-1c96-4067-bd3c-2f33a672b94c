/** */

<template>
  <div>
    <ReceiveInfoConfigure
      ref="receiveInfoConfigure"
      :user-id="userId"
    ></ReceiveInfoConfigure>
    <el-row>
      <el-col>
        <center>
          <asp-btn-hollow icon="el-icon-refresh" name="重置" @click="resetData">
          </asp-btn-hollow>
          <asp-btn-solid
            :disabled="submitStatus"
            icon="el-icon-check"
            name="保存"
            @click="save"
          >
          </asp-btn-solid>
        </center>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ReceiveInfoConfigure from '@/views/setting/receiveInfoConfigure'
export default {
  name: 'StaffReceiveInfo',
  components: {
    ReceiveInfoConfigure
  },
  props: {
    userId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      submitStatus: false // 提交按钮限制
    }
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    resetData() {
      this.$refs.receiveInfoConfigure.setData()
    },
    save() {
      this.$refs.receiveInfoConfigure.$refs.baseForm.validate(valid => {
        if (valid) {
          const blacklistTypes = this.$refs.receiveInfoConfigure.baseForm
            .blacklistTypes
          const receiveData = this.$refs.receiveInfoConfigure.baseForm
            .receiveData
          const targetFo = blacklistTypes.map(val => {
            let forbidBusinessTypes = []
            const rowData = receiveData.filter(value => value.type === val)
            if (receiveData.length && rowData.length) {
              forbidBusinessTypes = rowData[0].forbidBusinessType.map(value => {
                return {
                  code: value,
                  name: this.$aspUtils.formatDict(
                    value,
                    this.$refs.receiveInfoConfigure[val + 'TypesList']
                  )
                }
              })
            }
            return {
              type: val,
              forbidBusinessTypes
            }
          })
          const param = {
            userId: this.userId,
            userBlacklistFos: targetFo
          }
          this.submitStatus = true
          this.$aspHttps
            .asp_Post(
              this.$apiConfig.managerPathPrefix +
                '/userBlacklist/saveUserBlacklist',
              param
            )
            .then(response => {
              this.submitStatus = false
              if (this.$reponseStatus(response)) {
                this.$refs.receiveInfoConfigure.resetData = {
                  blacklistTypes: JSON.parse(JSON.stringify(blacklistTypes)),
                  receiveData: JSON.parse(JSON.stringify(receiveData))
                } // 用于成员详情的数据重置
                this.$message.success('修改成功！')
              }
            })
        }
      })
    }
  }
}
</script>
