<!--
props说明
dialogParam = {
  modelVisible: true, // 显示
  data: {
    total: [],
    selected: []
  }
}
-->
<template>
  <asp-dialog
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :append-to-body="true"
    title="给成员分配机构"
    width="850px"
    class="webbas"
  >
    <template>
      <el-form
        ref="selectOrgForm"
        :model="selectOrgForm"
        label-position="left"
        size="small"
      >
        <el-row>
          <el-col :span="24">
            <el-transfer
              v-model="selectOrgForm.checkedList"
              :data="baseData"
              :render-content="renderFunc"
              :titles="['待关联机构', '已关联机构']"
              :button-texts="['删除', '添加']"
              filterable
              filter-placeholder="请输入关键字"
            />
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow icon="el-icon-close" name="取消" @click="handleColse">
      </asp-btn-hollow>
      <asp-btn-solid
        :loading="dialogLoading"
        icon="el-icon-check"
        name="保存"
        @click="submitStaffRole"
      >
      </asp-btn-solid>
    </template>
  </asp-dialog>
</template>

<script>
export default {
  name: 'SelectOrg',
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      dialogLoading: false,
      selectOrgForm: {
        checkedList: []
      }
    }
  },
  computed: {
    baseData() {
      const res = []
      if (
        this.dialogParam.data.total &&
        this.dialogParam.data.total.length > 0
      ) {
        this.dialogParam.data.total.map(item => {
          res.push({ key: item.id, label: item.name })
        })
      }
      return res
    }
  },
  watch: {
    'dialogParam.data.selected': {
      handler(val) {
        this.selectOrgForm.checkedList = val
      },
      deep: true
    }
  },
  methods: {
    // 为成员分配角色
    submitStaffRole() {
      this.$emit('updateSelectOrg', this.selectOrgForm.checkedList)
    },
    // 关闭窗口时
    handleColse() {
      this.dialogParam.modelVisible = false
    },
    // 加上右边tips
    renderFunc(h, option) {
      return (
        <el-tooltip
          content={option.label}
          class="item"
          effect="dark"
          placement="left"
        >
          <span class="singleRow">{option.label}</span>
        </el-tooltip>
      )
    }
  }
}
</script>
<style>
.el-transfer-panel__body .el-transfer-panel__list .el-transfer-panel__item {
  display: block !important;
}
</style>
