/** * 系统成员管理详情内嵌表单 */
<template>
  <div>
    <el-form ref="detailStaff"
             :model="detailStaff"
             class="el-collapse-110">
      <el-row>
        <el-col :span="12">
          <el-form-item label="登录名："
                        prop="userName">
            <span v-text="detailStaff.userName"></span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机："
                        prop="mobile">
            <span v-text="detailStaff.mobile"></span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="邮箱："
                        prop="email">
            <span v-text="detailStaff.email"></span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成员状态："
                        prop="status">
            <span v-text="detailStaff.statusText"></span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item v-if="userInforMaskItems.indexOf('division') == -1"
                        label="归属区域："
                        prop="listDivisionName">
            <span v-text="getPathName"></span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="归属部门："
                        prop="departmentName">
            <span v-text="detailStaff.departmentName"></span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="真实姓名："
                        prop="realName">
            <span v-text="detailStaff.realName"></span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别："
                        prop="sex">
            <span v-text="sexLable(detailStaff.sex)"></span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="固定电话："
                        prop="telephone">
            <span v-text="detailStaff.telephone"></span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账号有效期："
                        prop="expireDateStr">
            <span v-text="detailStaff.expireDateStr"></span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="归属机构："
                        prop="organizationName">
            <span v-text="detailStaff.organizationName"></span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账号权限：">
            <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_030123' })"
                          name="账号权限对应关系"
                          @click="handleDownload">
            </asp-btn-text>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="已有角色："
                        prop="roleNames">
            <span v-text="detailStaff.roleNames"></span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="跨机构权限："
                        prop="roleNames">
            <span v-text="detailStaff.managerOrgNames"></span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Detail',
  components: {},
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      detailStaff: {
        id: '',
        userName: '',
        mobile: '',
        email: '',
        expireDateStr: '',
        organizationId: '',
        departmentName: '',
        realName: '',
        sex: '',
        telephone: '',
        organizationName: '',
        status: '',
        statusText: '',
        roleNames: '',
        listDivisionName: '',
        managerOrgNames: ''
      },
      userInforMaskItems: [],
      statusList: {},
      // 变更按钮需要
      row: {},
      urlPart: '/user'
    }
  },
  computed: {
    sexLable () {
      return function (code) {
        const sexList = [
          { code: '1', name: '男' },
          { code: '2', name: '女' }
        ]
        let name = '未知'
        sexList.forEach(item => {
          if (item.code === code) { name = item.name }
        })
        return name
      }
    },
    getPathName () {
      if (this.detailStaff.listDivisionName.length > 0) {
        const name = this.detailStaff.listDivisionName.map(item => item.pathName).join('，')
        return name
      }
      return ''
    }
  },
  watch: {
    'dialogParam.detailData' (val) {
      if (val) {
        this.setDetail(val)
      }
    }
  },
  created () {
    const domain = this.$aspUtils.getDomainObject(this)
    this.userDomain = domain.userInfo.domain
    this.userInforMaskItems = domain.platformConfig.userInforMaskItems
      ? domain.platformConfig.userInforMaskItems.split('，')
      : []
    // this.userInforMaskItems = []
  },
  mounted () {
    this.getStatusList()
  },
  methods: {
    doAuthArr (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCodeArr: opt.btnCodeArr }
    },
    // 按钮权限
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    setDetail (val) {
      this.detailStaff = {
        id: val.id,
        userName: val.userName,
        mobile: val.mobile,
        email: val.email,
        expireDateStr: val.expireDateStr,
        organizationId: val.organizationId,
        departmentName: val.departmentName,
        realName: val.realName,
        sex: val.sex,
        telephone: val.telephone,
        organizationName: val.organizationName,
        status: val.status,
        statusText: val.statusText,
        roleNames: val.roleNames,
        listDivisionName: val.listDivisionName,
        managerOrgNames: val.managerOrgNames
      }
    },
    // 状态翻译
    getDictStatus (val) {
      const item = this.statusList.find(status => status.code === val)
      return item ? item.name : ''
    },
    // 性别翻译
    getDictSex (val) {
      const dic = { 1: '男', 2: '女' }
      return dic[val]
    },
    // 获取状态字典
    getStatusList () {
      this.statusList = this.$aspUtils.getCodeValueByType(this, 'USER_STATUS')
    },
    // 权限关系下载
    handleDownload () {
      const id = this.detailStaff.id
      let url =
        this.$apiConfig.managerPathPrefix +
        this.urlPart +
        '/exportUserRight?userId=' +
        id
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    }
  }
}
</script>
