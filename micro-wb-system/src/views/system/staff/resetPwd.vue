<template>
  <div>
    <el-form ref="resetPwd"
             :model="resetPwdForm"
             :inline="true"
             :rules="rules"
             class="el-collapse-90">
      <el-row>
        <el-col :span="12">
          <el-form-item label="新密码:"
                        prop="newPassword">
            <el-input v-model="resetPwdForm.newPassword"
                      type="password"
                      auto-complete="new-password"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="确认密码:"
                        prop="pwdConfirm">
            <el-input v-model="resetPwdForm.pwdConfirm"
                      type="password"
                      auto-complete="new-password"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <center>
            <asp-btn-hollow icon="el-icon-refresh"
                            name="重置"
                            @click="reset">
            </asp-btn-hollow>
            <asp-btn-solid :loading="dialogLoading"
                           :disabled="dialogLoading"
                           icon="el-icon-check"
                           name="保存"
                           @click="submitStaffRole">
            </asp-btn-solid>
          </center>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
const VAR_INIT_STR = ''
export default {
  name: 'ResetPwd',
  props: {
    dialogParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      dialogLoading: false,
      id: '',
      resetPwdForm: {
        newPassword: VAR_INIT_STR,
        pwdConfirm: VAR_INIT_STR
      },
      rules: {
        newPassword: [
          { required: true, message: '密码不能为空', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkPassword, trigger: 'blur' }
        ],
        pwdConfirm: [
          { required: true, message: '确认密码不能为空', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkPassword, trigger: 'blur' },
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value !== this.resetPwdForm.newPassword) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    // 重置密码
    submitStaffRole () {
      this.dialogLoading = true
      this.$refs.resetPwd.validate(valid => {
        if (!valid) {
          this.dialogLoading = false
          return false
        }
        const param = {
          newPassword: this.addRSA(this.resetPwdForm.newPassword),
          userId: this.dialogParam.data.id
        }
        this.$aspHttps
          .asp_Post(
            this.$apiConfig.managerPathPrefix +
            this.dialogParam.data.urlPart +
            '/resetPwd',
            param
          )
          .then(response => {
            if (this.$reponseStatus(response)) {
              this.$message.success('重置密码成功！')
              this.dialogLoading = false
              this.reset()
            } else {
              this.dialogLoading = false
            }
          })
      })
    },
    addRSA (password) {
      const { platformConfig } = this.$aspUtils.getListAllObject(this)
      const modulus = platformConfig ? platformConfig.modulus : ''
      const exponent = platformConfig ? platformConfig.exponent : ''
      // RSA
      asp_RSAKey.RSASetPublic(modulus, exponent)
      return asp_RSAKey.RSAEncrypt(password)
    },
    reset (formName) {
      this.$refs.resetPwd.resetFields()
      // this.$emit('changeaAtiveName') 返回详情页签
    }
  }
}
</script>
