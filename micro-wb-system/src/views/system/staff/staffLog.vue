/** * 账号登录日志 */
<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form ref="searchForm"
                 :inline="true"
                 :model="table.searchForm">
          <el-row>
            <el-col :span="12">
              <el-form-item label="操作时间：">
                <asp-date-range :labelWith="85"
                                :start-date.sync="table.searchForm.operationDateStart"
                                :end-date.sync="table.searchForm.operationDateEnd"></asp-date-range>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="query-area-btn-css">
                <asp-btn-solid v-loading
                               v-hasAuth="
                    doAuthArr({ btnCodeArr: ['wb_100125', 'wb_030126'] })
                  "
                               name="查询"
                               icon="el-icon-search"
                               @click="search()">
                </asp-btn-solid>
                <asp-btn-hollow icon="el-icon-refresh"
                                name="重置"
                                @click="reset()">
                </asp-btn-hollow>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <asp-table ref="table"
                 :url="table.url"
                 :param="table.searchForm"
                 :prefix="table.prefix"
                 type="">
        <asp-table-column prop="logTypeName"
                          min-width="110"
                          show-overflow-tooltip
                          label="日志类型">
        </asp-table-column>
        <asp-table-column prop="operatorName"
                          min-width="100"
                          show-overflow-tooltip
                          label="操作员">
        </asp-table-column>
        <asp-table-column prop="clientIp"
                          label="客户端IP"
                          min-width="100"
                          show-overflow-tooltip>
        </asp-table-column>
        <asp-table-column prop="businessName"
                          min-width="230"
                          show-overflow-tooltip
                          label="操作对象">
        </asp-table-column>
        <asp-table-column prop="description"
                          min-width="250"
                          label="操作说明">
        </asp-table-column>
        <asp-table-column prop="operationResult"
                          width="80"
                          label="操作结果"
                          show-overflow-tooltip>
        </asp-table-column>
        <asp-table-column sort-key="operationDate"
                          prop="operationDateStr"
                          width="155"
                          label="操作时间">
        </asp-table-column>
      </asp-table>
      <div class="center_button">
        <asp-btn-hollow icon="el-icon-back"
                        name="返回"
                        @click="goBack()">
        </asp-btn-hollow>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StaffLog',
  components: {},
  data () {
    return {
      table: {
        prefix: this.$apiConfig.supportPathPrefix,
        url: '/component/log/listLogByUserId',
        searchForm: {
          operatorId: this.$route.query.operatorId,
          operationDateStart: '',
          operationDateEnd: ''
        }
      },
      headerNavTitle: '账号登录日志'
    }
  },
  computed: {
    // TODO 面包屑中获取当前页标题 暂时屏蔽
    // title () {
    //   let currentPath = this.$main_tools.store.state.app.currentPath
    //   return currentPath[currentPath.length - 1].title
    // }
  },
  watch: {},
  created () {
    this.init()
  },
  methods: {
    /**
     * 按钮权限
     * @method doAuthArr
     * @param {object} opt
     * @return {object}
     */
    doAuthArr (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCodeArr: opt.btnCodeArr }
    },
    init () {
      // this.search() // TurboC
    },
    search () {
      this.$refs.table.asp_search()
    },
    reset () {
      this.$refs.table.asp_reset()
    },
    goBack () {
      this.$router.back()
    }
  }
}
</script>
