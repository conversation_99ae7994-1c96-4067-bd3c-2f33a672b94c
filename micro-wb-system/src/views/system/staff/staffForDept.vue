/** * 机构成员管理 */
<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form ref="searchForm"
                 :inline="true"
                 :model="table.searchForm">
          <el-row>
            <el-col :span="6">
              <el-form-item prop="keyword"
                            label="关键字：">
                <el-input v-model.trim="table.searchForm.keyword"
                          placeholder="请输入用户名/真实姓名"
                          name="keyword"
                          @keyup.enter.native="search()"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="归属部门："
                            prop="departmentName">
                <div class="icon-absolute-css">
                  <i class="el-select__caret el-input__icon el-icon-circle-close"
                     @click="clearDeptName"></i>
                </div>
                <el-popover placement="bottom"
                            width="160">
                  <div class="popover-tree">
                    <el-tree :data="departmentTreeData"
                             :props="defaultProps"
                             :expand-on-click-node="false"
                             @node-click="departTreeNodeClick"></el-tree>
                  </div>
                  <el-input slot="reference"
                            v-model="table.searchForm.departmentName"
                            readonly
                            auto-complete="off"></el-input>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="status"
                            label="账号状态：">
                <asp-select-all v-model="table.searchForm.status"
                                :code-list="statusList"></asp-select-all>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="mobile"
                            label="手机：">
                <el-input v-model.trim="table.searchForm.mobile"
                          placeholder="请输入手机"
                          name="mobile"
                          @keyup.enter.native="search(true)"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="email"
                            label="Email：">
                <el-input v-model.trim="table.searchForm.email"
                          placeholder="请输入Email"
                          name="email"
                          @keyup.enter.native="search(true)"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="keyword"
                            label="角色名称：">
                <el-input v-model.trim="roleName"
                          placeholder="请输入角色名称"
                          name="keyword"
                          @focus="showCheckbox"
                          @input="searchContent"></el-input>
              </el-form-item>
              <div v-if="flag"
                   class="dislog-div">
                <el-checkbox v-model="checkAll"
                             @change="handleCheckAllChange"
                             class="dislog-all">全选({{ roleList.length }})</el-checkbox>
                <div style="margin: 15px 0;"></div>
                <el-checkbox-group v-model="checkedRole"
                                   @change="handleCheckedCitiesChange"
                                   class="dislog-group">
                  <el-checkbox v-for="item in roleList"
                               :label="item.id"
                               :key="item.id">{{item.name}}</el-checkbox>
                </el-checkbox-group>
                <div class="block">
                  <el-pagination @size-change="handleSizeChange"
                                 @current-change="handleCurrentChange"
                                 :hide-on-single-page="true"
                                 :current-page.sync="currentPage"
                                 :pager-count="5"
                                 :page-sizes="[10]"
                                 :page-size="10"
                                 small
                                 layout="total, sizes, prev, pager, next, jumper"
                                 :total="roleTotal">
                  </el-pagination>
                </div>
                <div class="sure-btn">
                  <el-form-item class="query-area-btn-css">
                    <asp-btn-solid v-loading="searchStatus"
                                   name="确定"
                                   icon="el-icon-search"
                                   @click="makeSureBtn()"></asp-btn-solid>
                    <asp-btn-hollow icon="el-icon-refresh"
                                    name="取消"
                                    @click="closeDiv()"></asp-btn-hollow>
                  </el-form-item>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <el-form-item class="query-area-btn-css">
                <asp-btn-solid v-loading="searchStatus"
                               name="查询"
                               icon="el-icon-search"
                               @click="search()"></asp-btn-solid>
                <asp-btn-hollow icon="el-icon-refresh"
                                name="重置"
                                @click="reset()"></asp-btn-hollow>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <asp-table ref="table"
                 :url="table.url"
                 :param="table.searchForm"
                 :prefix="table.prefix"
                 type="">
        <template slot="header">
          <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_100119' })"
                         icon="el-icon-plus"
                         name="新增"
                         @click="addStaff">
          </asp-btn-solid>
          <asp-btn-hollow v-hasAuth="doAuth({ btnCode: 'wb_100122' })"
                          icon="el-icon-download"
                          name="导出"
                          @click="exportList">
          </asp-btn-hollow>
        </template>
        <asp-table-column sort-key="userName"
                          prop="userName"
                          min-width="100"
                          show-overflow-tooltip
                          label="用户名">
        </asp-table-column>
        <asp-table-column sort-key="realName"
                          prop="realName"
                          width="105"
                          label="真实姓名"
                          show-overflow-tooltip>
        </asp-table-column>
        <asp-table-column sort-key="organizationName"
                          prop="organizationName"
                          width="105"
                          label="归属机构"
                          show-overflow-tooltip>
          <template slot-scope="{ scope }">
            <span v-if="isExist"
                  class="staff-pointer"
                  @click="searchDepartment(scope.row.organizationId, '')">
              {{ scope.row.organizationName }}
            </span>
            <span v-else>
              {{ scope.row.organizationName }}
            </span>
          </template>
        </asp-table-column>
        <asp-table-column prop="departmentName"
                          min-width="90"
                          show-overflow-tooltip
                          label="归属部门">
          <template slot-scope="{ scope }">
            <span v-if="isExist"
                  class="staff-pointer"
                  @click="
                searchDepartment(
                  scope.row.organizationId,
                  scope.row.departmentId
                )
              ">
              {{ scope.row.departmentName }}
            </span>
            <span v-else>
              {{ scope.row.departmentName }}
            </span>
          </template>
        </asp-table-column>
        <asp-table-column :formatter="dictSex"
                          prop="sex"
                          label="性别"
                          width="50">
        </asp-table-column>
        <asp-table-column prop="mobile"
                          label="手机"
                          width="110">
        </asp-table-column>
        <asp-table-column prop="email"
                          label="Email"
                          show-overflow-tooltip
                          min-width="150">
        </asp-table-column>
        <asp-table-column label="状态"
                          width="95">
          <template slot-scope="{ scope }">
            <template v-if="scope.row.status === 'NORMAL'">
              <el-tag type="success">正常</el-tag>
            </template>
            <template v-else-if="scope.row.status === 'INACTIVE'">
              <el-tag type="warning">禁用</el-tag>
            </template>
            <template v-else-if="scope.row.status === 'PASSWORD_EXPIRED'">
              <el-tag type="danger">密码过期</el-tag>
            </template>
            <template v-else-if="scope.row.status === 'EXPIRED'">
              <el-tag type="danger">账号过期</el-tag>
            </template>
            <template v-else-if="scope.row.status === 'LOCKED'">
              <el-tag type="danger">锁定</el-tag>
            </template>
          </template>
        </asp-table-column>
        <asp-table-column :width="this.$aspFontSize.asp_ColButtonSize([2, 2, 2, 2, 2])"
                          label="操作"
                          fixed="right">
          <template slot-scope="{ scope }">
            <asp-btn-text name="查看"
                          @click="handleDetailStaff(scope.row)">
            </asp-btn-text>
            <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_100103' })"
                          v-show="
                scope.row.originType == '0' && scope.row.status !== 'EXPIRED'
              "
                          name="修改"
                          @click="editStaff(scope.row)">
            </asp-btn-text>
            <asp-btn-text v-loading="deleteStaffStatus"
                          v-hasAuth="doAuth({ btnCode: 'wb_100105' })"
                          v-show="scope.row.originType == '0'"
                          name="删除"
                          @click="delStaff(scope.row)">
            </asp-btn-text>
            <asp-btn-text v-loading="lockOrUnStatus"
                          v-hasAuth="doAuth({ btnCode: 'wb_100108' })"
                          v-show="
                scope.row.originType == '0' &&
                  (scope.row.status === 'NORMAL' ||
                    scope.row.status === 'INACTIVE' ||
                    scope.row.status === 'PASSWORD_EXPIRED')
              "
                          :name="scope.row.status === 'INACTIVE' ? '启用' : '禁用'"
                          @click="updateStatus(scope.row)">
            </asp-btn-text>
            <asp-btn-text v-hasAuth="doAuth({ btnCode: 'wb_100124' })"
                          v-show="scope.row.status === 'LOCKED'"
                          name="解锁"
                          @click="unlock(scope.row)">
            </asp-btn-text>
          </template>
        </asp-table-column>
      </asp-table>
    </div>
    <!-- 新增用户 -->
    <editStaff ref="editStaff"
               :dialog-param="staffModelParam"
               @search="search(true)"></editStaff>
    <!-- 查看用户 -->
    <detailStaff ref="detailStaff"
                 :dialog-param="detailStaffModelParam"
                 @updateStafdList="search"
                 @handleEdit="handleEdit"></detailStaff>
  </div>
</template>
<script>
import editStaff from './editStaff'
import detailStaff from './detailStaffForDept'
export default {
  name: 'StaffForDept',
  components: {
    editStaff,
    detailStaff
  },
  data () {
    return {
      flag: false, // 显示弹框
      checkAll: false, // 是否全选
      checkedRole: [], // 选中后的角色
      roleList: [], // 筛选后的数据
      roleDto: [], // 存储所有的复选框内容
      roleName: [], // 模糊查询条件
      roleParams: {
        page: 1,
        rows: 10,
        name: ''
      },
      arrNames: [], // 存储全选以后的id
      roleTotal: 0,
      currentPage: 1, // 当前页码
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/userOrganization/listPage',
        searchForm: {
          order: 'asc', // 顺序/倒序排列
          sortName: 'userName', // 排序名称
          keyword: '',
          status: '',
          organizationId: '',
          departmentId: '',
          departmentName: '',
          mobile: '',
          email: '',
          roleNames: []
        }
      },
      count: 0, // 计算显示详情的次数
      userDomain: '', // 当前登录用所属域
      organizationTreeData: [], // 组织列表
      searchStatus: false, // 点击查询加载按钮
      deleteStaffStatus: false, // 点击删除加载按钮
      lockOrUnStatus: false, // 点击禁用或启用加载按钮
      OrgLoading: false,
      staffModelParam: {},
      isExist: false,
      detailStaffModelParam: {},
      // 级联
      organizationList: [],
      departmentTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'text'
      },
      domainList: [], // 归属域
      urlPart: '/userOrganization',
      statusList: [
        // 因字典表数据不可用，因此在前端自定义
        { code: 'NORMAL', name: '正常' },
        { code: 'INITIAL', name: '初始' },
        { code: 'INACTIVE', name: '禁用' },
        { code: 'PASSWORD_EXPIRED', name: '密码过期' },
        { code: 'EXPIRED', name: '账号过期' },
        { code: 'LOCKED', name: '锁定' }
      ],
      sexList: [
        { code: '1', name: '男' },
        { code: '2', name: '女' }
      ]
    }
  },
  computed: {
    needShowDetail () {
      return !!this.$route.query.realName
    }
  },
  created () {
    const domain = this.$aspUtils.getDomainObject(this)
    this.userDomain = domain.userInfo.domain
    this.getDomainList()

    this.table.searchForm.organizationId = domain.userInfo.organizationId
    this.table.searchForm.organizationName = domain.userInfo.organizationName
    const organizationId = this.table.searchForm.organizationId
    organizationId && this.getDepartTree(organizationId)

    // let organizationArray = []
    // if (this.userDomain === 'admin') {
    //   organizationArray = domain.authInfo.wb_020101
    // } else if (this.userDomain === 'channel') {
    //   organizationArray = domain.authInfo.wb_chann_020101
    // } else if (this.userDomain === 'supplier') {
    //   organizationArray = domain.authInfo.wb_supp_020101
    // }

    if (domain.authInfo.wb_090101) {
      this.isExist = true
    }

    this.table.searchForm.keyword = this.$route.query.realName || ''
    // this.search(true) // TurboC
  },
  mounted () {
    // this.search() // TurboC
  },
  methods: {
    // 每页多少条
    handleSizeChange (val) {
      console.log(`每页 ${val} 条`)
    },
    // 当前页面
    handleCurrentChange (val) {
      this.checkAll = false
      this.roleParams.page = val
      this.currentPage = val
      this.getRoleNames()
      console.log(`当前页: ${val}`)
    },
    // 全选按钮
    handleCheckAllChange (val) {
      if (val) {
        this.roleList.forEach(item => {
          this.arrNames.push(item.id)
        })
        this.arrNames = [...new Set(this.arrNames)]
      } else {
        this.roleList.forEach(item => {
          const index = this.arrNames.indexOf(item.id)
          if (index > -1) {
            this.arrNames.splice(index, 1)
          }
        })
      }
      this.checkedRole = [...new Set(this.arrNames)]
      // this.isIndeterminate = false
    },
    // 复选框选择改变全选状态
    handleCheckedCitiesChange (value) {
      this.arrNames = value
      this.checkAll = true
      this.roleList.forEach(item => {
        if (value.indexOf(item.id) < 0) {
          this.checkAll = false
        }
      })
      // this.isIndeterminate = checkedCount > 0 && checkedCount < this.roleList.length
    },
    // 点击输入框显示复选框内容
    showCheckbox () {
      if (this.flag) return
      this.flag = true
      this.getRoleNames()
      // this.cities = this.cityOptions
    },
    // 关闭复选款弹框
    closeDiv () {
      this.flag = false
      if (this.table.searchForm.roleNames.length < 1) {
        this.checkedRole = []
        this.checkAll = false
        this.roleParams.page = 1
        this.currentPage = 1
        this.getRoleNames()
      }
    },
    // 确认后关闭弹框
    makeSureBtn () {
      this.flag = false
      this.table.searchForm.roleNames = this.checkedRole
      if (this.checkedRole.length < 1) {
        this.roleName = []
        return
      }
      this.roleName = []
      this.checkedRole.forEach(item => {
        this.roleDto.forEach(ite => {
          if (item === ite.id) {
            this.roleName.push(ite.name)
          }
        })
      })
      this.roleName = this.roleName.toString().length > 20 ? this.roleName.toString().substring(0, 20) + '...' : this.roleName.toString()
    },
    // 模糊搜索匹配复选框内容
    searchContent (query) {
      this.roleParams.name = query
      this.getRoleNames()
    },
    // 按钮权限
    doAuth (opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    /**
     * 查询组织详情
     * @param orgId
     * @param deptId
     */
    searchDepartment (orgId, deptId) {
      this.$router.push({
        path: '/system/deptForDept',
        query: {
          dataFrom: 'staff',
          orgId: orgId,
          deptId: deptId,
          title: '组织详情'
        }
      })
    },
    addStaff () {
      const domain = this.$aspUtils.getDomainObject(this)
      this.staffModelParam = {
        title: '新增成员',
        staffModelVisible: true,
        editStaff: {
          id: '',
          domainList: this.domainList
        },
        dataFrom: 'staffForDept'
      }
      this.staffModelParam.editStaff.organizationId =
        domain.userInfo.organizationId
    },
    handleDetailStaff (row) {
      this.detailStaffModelParam = {
        title: '成员详情',
        modelVisible: true,
        data: {
          id: row.id,
          userName: row.userName,
          realName: row.realName,
          sex: this.$aspUtils.initSex(row.sex),
          mobile: row.mobile,
          organizationId: row.organizationId,
          organizationName: row.organizationName,
          departmentId: row.departmentId,
          departmentName: row.departmentName,
          telephone: row.telephone,
          email: row.email,
          status: row.status,
          expireDateStr: row.expireDateStr,
          row: row,
          urlPart: this.urlPart
        }
      }
    },
    handleEdit (data) {
      this.detailStaffModelParam.modelVisible = false
      this.editStaff(data)
    },
    editStaff (row) {
      this.staffModelParam = {
        title: '修改用户信息',
        staffModelVisible: true,
        editStaff: {
          id: row.id,
          userName: row.userName,
          realName: row.realName,
          sex: this.$aspUtils.initSex(row.sex),
          mobile: row.mobile,
          organizationId: row.organizationId,
          organizationName: row.organizationName,
          departmentId: row.departmentId,
          departmentName: row.departmentName,
          telephone: row.telephone,
          email: row.email,
          status: row.status,
          expireDateStr: row.expireDateStr,
          urlPart: this.urlPart
        },
        dataFrom: 'staffForDept'
      }
    },
    delStaff (row) {
      const userId = this.$aspUtils.getDomainObject(this).userInfo.id
      if (row.id.toString() === '-999') {
        this.$msgbox({
          message: '该账号属于系统内置用户，不能删除',
          showCancelButton: false,
          confirmButtonText: '确定'
        })
        return
      } else if (row.id === userId) {
        this.$msgbox({
          message: '该账号为当前登录账号，不能删除',
          showCancelButton: false,
          confirmButtonText: '确定'
        })
        return
      }
      const showStr = '确认删除该账号？'
      this.$aspMsgbox.confirm(this, showStr, function (arg, instance) {
        // console.log(arg, instance)
        const url = this.$apiConfig.managerPathPrefix + this.urlPart + '/delete'
        this.$aspHttps
          .asp_PostForm(url, { id: row.id })
          .then(response => {
            if (this.$reponseStatus(response)) {
              this.$message.success('删除成功!')
              this.search()
            }
          })
      })
    },
    updateStatus (row) {
      if (row.id === '-999') {
        const showStr = '该用户为超级用户，不能禁用'
        this.$aspMsgbox.confirm(this, showStr, function (arg, instance) {
          // console.log(arg, instance)
        })
      } else {
        const oper = row.status === 'INACTIVE' ? '' : 'lock'
        let lockedStaffUrl = ''
        let updateStatusParams = {}
        const tips =
          oper === 'lock'
            ? `用户【${row.userName}】将被禁用，禁用后账号无法登陆系统，是否继续？`
            : `用户【${row.userName}】将被启用，启用后账号可正常登陆系统，是否继续？`
        this.$aspMsgbox.confirm(this, tips, function (arg, instance) {
          // console.log(arg, instance)
          if (oper === 'lock') {
            lockedStaffUrl =
              this.$apiConfig.managerPathPrefix + this.urlPart + '/inactive' // 禁用接口
            updateStatusParams = {
              reason: oper,
              userId: row.id
            }
            this.$aspHttps
              .asp_Post(lockedStaffUrl, updateStatusParams)
              .then(response => {
                if (this.$reponseStatus(response)) {
                  this.$message({
                    type: 'success',
                    message: '该用户已被禁用！'
                  })
                  this.search()
                }
              })
          } else {
            this.$aspHttps
              .asp_PostForm(
                this.$apiConfig.managerPathPrefix + this.urlPart + '/active',
                { id: row.id }
              )
              .then(response => {
                // 启用接口
                if (this.$reponseStatus(response)) {
                  this.$message({
                    type: 'success',
                    message: '该用户已被启用！'
                  })
                  this.search()
                }
              })
          }
        })
      }
    },
    unlock (row) {
      const showStr = '确认解锁该账号？'
      this.$aspMsgbox.confirm(this, showStr, function (arg, instance) {
        // console.log(arg, instance)
        const url = this.$apiConfig.managerPathPrefix + this.urlPart + '/unlock'
        this.$aspHttps
          .asp_PostForm(url, { id: row.id })
          .then(response => {
            if (this.$reponseStatus(response)) {
              this.$message.success('解锁成功!')
              this.search()
            }
          })
      })
    },
    search () {
      this.$nextTick(() => {
        this.$refs.table.asp_search()
        this.$nextTick(() => {
          // 需要直接显示详情
          if (this.needShowDetail && this.count === 0) {
            if (this.$refs.table.data.length > 0) {
              this.count++
              this.handleDetailStaff(this.$refs.table.data[0])
            }
          }
        })
      })
    },
    reset () {
      this.$refs.table.asp_reset()
      this.table.searchForm.departmentId = ''
      this.checkAll = false
      this.checkedRole = []
      this.arrNames = []
      this.flag = false
      this.roleParams.page = 1
      this.currentPage = 1
      this.table.searchForm.roleNames = []
      this.roleName = []
      this.roleDto = []
      this.roleParams.name = ''
      this.getRoleNames()
    },
    // 获取角色名称
    getRoleNames () {
      const url = '/roleOrganization/listPage'
      const params = this.roleParams
      this.$aspHttps
        .asp_Post(this.$apiConfig.managerPathPrefix + url, params)
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.roleTotal = response.total
            this.roleList = response.data
            const arr = Object.assign([], response.data)
            this.roleDto = this.delRepeat(this.roleDto, arr)
            if (this.checkedRole.length > 0) {
              this.checkAll = true
            }
            this.roleList.forEach(item => {
              if (this.checkedRole.indexOf(item.id) < 0) {
                this.checkAll = false
              }
            })
          }
        })
    },
    // 数组内对象去重
    delRepeat (arr1, arr2) {
      if (arr1.length < 1) return arr2
      arr1.forEach((item, index) => {
        arr2.forEach((ite, ind) => {
          if (item.id === ite.id) {
            arr2.splice(ind, 1)
          }
        })
      })
      return arr1.concat(arr2)
    },
    /**
     * 导出excel
     * @method exportList
     */
    exportList () {
      const listParams = {
        status:
          this.table.searchForm.status === null
            ? ''
            : this.table.searchForm.status,
        keyword: this.table.searchForm.keyword,
        order: this.table.searchForm.order,
        sortName: this.table.searchForm.sortName,
        departmentId: this.table.searchForm.departmentId,
        mobile: this.table.searchForm.mobile,
        email: this.table.searchForm.email,
        roleNames: this.table.searchForm.roleNames,
        page: this.$refs.table.page,
        rows: this.$refs.table.pageSize
      }
      const param = Object.keys(listParams)
        .map(key => {
          return key + '=' + listParams[key]
        })
        .join('&')
      let url =
        this.$apiConfig.managerPathPrefix + this.urlPart + '/export?' + param
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    getDepartTree (orgId) {
      this.$aspHttps
        .asp_PostForm(
          this.$apiConfig.managerPathPrefix + '/department/listDepartmentTree',
          { id: orgId }
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.departmentTreeData = response.data
          }
        })
    },
    departTreeNodeClick (data) {
      if (!data.id) return
      this.table.searchForm.departmentId = data.id
      this.table.searchForm.departmentName = data.text
    },
    clearDeptName () {
      this.table.searchForm.departmentId = ''
      this.table.searchForm.departmentName = ''
    },
    // 获取所属域
    getDomainList () {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    dictSex (row) {
      const item = this.sexList.find(val => val.code === row.sex)
      return item ? item.name : ''
    }
  }
}
</script>
<style lang="scss" scoped>
.dislog-div {
  position: absolute;
  width: 480px;
  height: 380px;
  background: #fff;
  z-index: 1003;
  box-shadow: 0px 6px 8px 0px rgb(0 0 0 / 8%);
  margin-top: 10px;
  .dislog-all {
    margin-left: 15px;
    margin-top: 20px;
  }
  .dislog-group {
    display: flex;
    flex-direction: column;
    margin-left: 15px;
  }
}
::v-deep.dislog-group .el-checkbox {
  padding-bottom: 5px;
}
::v-deep.block {
  bottom: 60px;
  left: 20px;
  position: absolute;
  .el-input {
    width: 50px;
  }
  .el-input.el-input--mini.el-input--suffix {
    width: 100px;
    margin-top: -4px;
  }
  .el-pagination .btn-prev {
    padding-right: 0;
    margin-left: -12px;
  }
  .el-input__suffix {
    top: 0;
  }
  .el-pagination__jump {
    margin-left: 10px;
    margin-top: -4px;
  }
  .el-pagination__total {
    margin-right: 0;
  }
}
::v-deep.sure-btn {
  width: 450px;
  position: absolute;
  bottom: 20px;
  .el-form-item .el-form-item__content {
    text-align: center;
  }
}
</style>