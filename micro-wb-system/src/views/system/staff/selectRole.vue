<!--
props说明
dialogParam = {
  modelVisible: true, // 显示
  data: {
    total: [],
    selected: []
  }
}
-->
<template>
  <div>
    <el-form
      ref="selectOrgForm"
      :model="selectOrgForm"
      label-position="left"
      size="small"
    >
      <el-row>
        <el-col :span="24">
          <el-transfer
            ref="selectOrgTranfer"
            v-model="selectOrgForm.checkedList"
            :data="baseData"
            :render-content="renderFunc"
            :titles="['待关联角色', '已关联角色']"
            :button-texts="['删除', '添加']"
            filterable
            filter-placeholder="请输入关键字"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <center>
            <asp-btn-hollow
              icon="el-icon-refresh"
              name="重置"
              @click="handleColse"
            >
            </asp-btn-hollow>
            <asp-btn-solid
              :loading="dialogLoading"
              icon="el-icon-check"
              name="保存"
              @click="submitStaffRole"
            >
            </asp-btn-solid>
          </center>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'SelectRole',
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      dialogLoading: false,
      selectOrgForm: {
        checkedList: []
      }
    }
  },
  computed: {
    baseData() {
      const res = []
      if (
        this.dialogParam.data.total &&
        this.dialogParam.data.total.length > 0
      ) {
        this.dialogParam.data.total.map(item => {
          // autoAssign: 1:默认角色 不可选中（勾选框置灰）删除
          res.push({ key: item.id, label: item.name, disabled: item.autoAssign === 1 })
        })
      }
      return res
    }
  },
  watch: {
    'dialogParam.data.selected'(val) {
      if (this.dialogParam.isRoleFirst) {
        const list = val.map(item => item.id)
        this.selectOrgForm.checkedList = list
      }
      this.$emit('changeStatus')
      // handler(val) {
      //     if (this.dialogParam.isRoleFirst) {
      //         const list = val.map(item => item.id)
      //         this.selectOrgForm.checkedList = list
      //     }
      //     this.$emit('changeStatus')
      // },
      // deep: true
    }
  },
  methods: {
    // 为成员分配角色
    submitStaffRole() {
      const param = {
        roleType: this.dialogParam.roleType,
        userRoleUpdateList: [
          {
            userId: this.dialogParam.data.id,
            roleList: this.selectOrgForm.checkedList
          }
        ]
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/userRole/updateUserRoles',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.$message.success('角色更新成功！')
            this.$emit('updateSelectOrg', this.selectOrgForm.checkedList)
          }
        })
    },
    // 关闭窗口时
    handleColse() {
      this.$refs.selectOrgTranfer.clearQuery('left')
      this.$refs.selectOrgTranfer.clearQuery('right')
      // 还原修改前的数据
      const list = this.dialogParam.data.selected.map(item => item.id)
      this.selectOrgForm.checkedList = list
      this.reset()
    },
    // 加上右边tips
    renderFunc(h, option) {
      return (
        <el-tooltip
          content={option.label}
          class="item"
          effect="dark"
          placement="left"
        >
          <span class="singleRow">{option.label}</span>
        </el-tooltip>
      )
    },
    reset() {
      // this.$emit('changeaAtiveName')
    }
  }
}
</script>
