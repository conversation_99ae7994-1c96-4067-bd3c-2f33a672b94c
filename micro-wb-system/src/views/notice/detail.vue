/** * 公告管理 */
<template>
  <div class="webbas">
    <section class="detail-css">
      <div class="detail-content-css">
        <el-collapse v-model="activeNames" class="pcc-collapse">
          <el-collapse-item name="1">
            <template slot="title" name="1">公告内容</template>
            <el-form :model="headContent">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="发布人：">
                    {{ createNameChange(headContent.createName) }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发布时间：">
                    {{ headContent.releaseTime }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="公告类型：">
                    {{ typeChange(headContent.type) }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="是否审核：">
                    {{ isExamineChange(headContent.isExamine) }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="提醒方式：">
                    <div>{{ remindWayChange(headContent.remindWay1) }}</div>
                    <div>{{ remind2WayChange(headContent.remindWay2) }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="是否回复：">
                    {{ isReplyChange(headContent.isReply) }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-model="activeNames" class="pcc-collapse">
          <el-collapse-item name="2">
            <template slot="title" name="2">公告正文</template>
            <el-form>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="">
                    <div style="text-align: center; margin-left: 110px">
                      {{ noticeTitle }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="border-top: none">
                <el-col :span="24">
                  <el-form-item label="">
                    <div
                      style="margin-left: 110px"
                      v-html="this.$aspUtils.htmlDecodeByRegExp(noticeContent)"
                    ></div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-model="activeNames" class="pcc-collapse">
          <el-collapse-item name="3">
            <template slot="title" name="3">公告接收对象</template>
            <el-row>
              <div class="list-area-css">
                <div class="list-top-btn-css">
                  <el-button
                    v-hasAuth="doAuth({ btnCode: 'wb_060126' })"
                    type="primary"
                    class="solid-with-icon-btn"
                    size="small"
                    icon="el-icon-upload2"
                    @click="exportReply()"
                    >导出回复详情</el-button
                  >
                </div>
                <div>
                  <el-table v-loading="tableLoading1" :data="objectData" stripe>
                    <el-table-column
                      prop="organizationName"
                      label="组织名称"
                      min-width="150"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      prop="readCount"
                      label="已查阅人数"
                      min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="noReadCount"
                      label="未查阅人数"
                      min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="division"
                      label="所属地域"
                      min-width="100"
                    >
                    </el-table-column>
                  </el-table>
                </div>
                <div class="list-pagination-css">
                  <el-pagination
                    :current-page.sync="paginationOne.page"
                    :page-sizes="paginationOne.pageSizes"
                    :page-size="paginationOne.pageSize"
                    :total="paginationOne.total"
                    layout="total, prev, pager, next, sizes, jumper"
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  ></el-pagination>
                </div>
              </div>
            </el-row>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-model="activeNames" class="pcc-collapse">
          <el-collapse-item name="4">
            <template slot="title" name="4">操作历史记录</template>
            <el-row>
              <div class="list-area-css">
                <div>
                  <el-table
                    v-loading="tableLoading2"
                    :data="historyTableData"
                    stripe
                  >
                    <el-table-column
                      prop="createDate"
                      label="操作时间"
                      min-width="120"
                    >
                      <!--<template slot-scope="scope">
                                                <span>{{ changeDateTime(scope.row.createDate) }}</span>
                                            </template>-->
                    </el-table-column>
                    <el-table-column
                      prop="operatorName"
                      label="操作人"
                      min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="result"
                      label="操作类型"
                      min-width="90"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="type"
                      label="操作结果"
                      min-width="90"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="opinion"
                      label="审批意见"
                      min-width="120"
                      show-overflow-tooltip
                    ></el-table-column>
                  </el-table>
                </div>
                <div class="list-pagination-css">
                  <el-pagination
                    :current-page.sync="paginationTwo.page"
                    :page-sizes="paginationTwo.pageSizes"
                    :page-size="paginationTwo.pageSize"
                    :total="paginationTwo.total"
                    layout="total, prev, pager, next, sizes, jumper"
                    background
                    @size-change="handleSizeChange1"
                    @current-change="handleCurrentChange1"
                  ></el-pagination>
                </div>
              </div>
            </el-row>
          </el-collapse-item>
        </el-collapse>
        <div class="center_button">
          <el-button size="small" class="hollow-with-icon-btn" @click="back()"
            >返回</el-button
          >
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'NoticeDetail',
  components: {},
  filters: {},
  data() {
    return {
      activeNames: ['1', '2', '3', '4'],
      headContent: {
        // 查询表单
        createName: '',
        releaseTime: '', // 紧急程度
        type: '', // 公告类型
        isExamine: '', // 是否需要审核
        remindWay1: '', // 提醒方式 邮件提醒
        remindWay2: '', // 提醒方式 短信提醒
        isReply: '' // 是否回复
      },
      createNameData: [],
      userId: '',
      detailId: '',
      noticeTitle: '', // 公告标题
      noticeContent: '', // 公告内容
      objectData: [], // 公告接收对象
      historyTableData: [], // 操作历史记录
      tableLoading1: true,
      tableLoading2: true,
      paginationOne: {
        // 分页数据
        page: 1,
        total: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 40]
      },
      paginationTwo: {
        // 分页数据
        page: 1,
        total: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 40]
      },
      prePageSize1: '',
      prePageSize2: ''
    }
  },
  watch: {},
  created() {},
  mounted() {
    if (this.$route.query.id) {
      this.detailId = this.$route.query.id
    } else {
      this.detailId = ''
    }
    this.init()
  },
  methods: {
    /**
     * 按钮权限
     * @method doAuth
     * @param {object} opt
     * @return {object}
     */
    doAuth(opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    init() {
      this.getDetailWithRead()
      this.listPageReadDetail()
      this.pageListHistory()
      this.getList()
    },
    // 切换
    createNameChange(data) {
      for (let i = 0; i < this.createNameData.length; i++) {
        const item = this.createNameData[i].id
        if (item === data) {
          return this.createNameData[i].userName
        }
      }
    },
    typeChange(data) {
      if (data === '0') {
        return '普通公告'
      } else if (data === '1') {
        return '首页公告'
      }
    },
    isExamineChange(data) {
      if (data === '0') {
        return '否'
      } else if (data === '1') {
        return '是'
      }
    },
    isReplyChange(data) {
      if (data === '0') {
        return '否'
      } else if (data === '1') {
        return '是'
      }
    },
    remindWayChange(data) {
      if (data === '1') {
        return '邮件提醒'
      } else {
        return ''
      }
    },
    remind2WayChange(data) {
      if (data === '1') {
        return '短信提醒'
      } else {
        return ''
      }
    },
    handleSizeChange(val) {
      this.paginationOne.pageSize = val
      this.listPageReadDetail()
    },
    handleCurrentChange(val) {
      this.paginationOne.page = val
      this.listPageReadDetail()
    },
    handleSizeChange1(val) {
      this.paginationTwo.pageSize = val
      this.pageListHistory()
    },
    handleCurrentChange1(val) {
      this.paginationTwo.page = val
      this.pageListHistory()
    },
    // 返回
    back() {
      this.$router.back()
    },
    // 查询公告详情，展示阅读详情
    getDetailWithRead() {
      const param = {
        id: this.detailId
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix +
            '/annoucement/getDetailWithReadById',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            // 公告内容
            this.headContent.createName = response.data.createUser
            this.headContent.releaseTime = response.data.publishDate
            this.headContent.type = response.data.type
            this.headContent.isExamine = response.data.reviewFlag
            this.headContent.remindWay1 = response.data.emailNotifyFlag
            this.headContent.remindWay2 = response.data.smsNotifyFlag
            this.headContent.isReply = response.data.replyFlag
            this.noticeTitle = response.data.title
            this.noticeContent = response.data.content
            this.userId = response.data.id
          }
        })
    },
    // 查询公告详情-分页获取公告阅读情况
    listPageReadDetail() {
      const param = {
        id: this.detailId,
        order: 'desc',
        sortName: 'title',
        page: this.paginationOne.page,
        rows: this.paginationOne.pageSize
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix +
            '/annoucement/listPageReadDetail',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            // 公告接收对象
            this.objectData = response.data
            this.tableLoading1 = false
            this.paginationOne.total = response.total
            this.prePageSize1 = response.records
          }
        })
    },
    // 查询公告详情，展示操作详情
    pageListHistory() {
      const param = {
        id: this.detailId,
        order: 'desc',
        sortName: 'title',
        page: this.paginationTwo.page,
        rows: this.paginationTwo.pageSize
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/annoucement/pageListHistory',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            // 操作历史记录
            this.historyTableData = response.data
            this.tableLoading2 = false
            this.paginationTwo.total = response.total
            this.prePageSize2 = response.records
          }
        })
    },
    // 获取发布人
    getList() {
      var param = {
        keyword: '',
        order: 'asc',
        organizationId: '',
        organizationName: '',
        page: 1,
        rows: 10,
        sortName: 'wbUser.user_name',
        status: 'NORMAL'
      }
      this.$aspHttps
        .asp_Post(this.$apiConfig.managerPathPrefix + '/user/list', param)
        .then(response => {
          if (this.$reponseStatus(response)) {
            const arr = []
            response.data.forEach(function(item) {
              arr.push({ id: item.id, userName: item.userName, show: false })
            })
            this.createNameData = arr
          }
        })
    },
    // 导出回复详情
    exportReply() {
      const url =
        this.$apiConfig.managerPathPrefix +
        '/annoucement/exportReply?id=' +
        this.detailId
      // 新开窗口下载
      this.$aspHttps.asp_ExportGetOpen(url)
    }
  }
}
</script>
