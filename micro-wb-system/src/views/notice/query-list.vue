/** * 公告管理 */
<template>
  <div class="webbas">
    <template>
      <div class="list-page-content-css">
        <section class="query-area-content-css">
          <el-form
            ref="searchForm"
            :inline="true"
            :model="searchForm"
            @submit.native.prevent
          >
            <el-row>
              <el-col :span="8">
                <el-form-item prop="name" label="公告标题：">
                  <el-input
                    v-model.trim="searchForm.noticeTitle"
                    placeholder="请输入标题"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="operatorType" label="紧急程度：">
                  <el-select
                    v-model="searchForm.noticeLevel"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in noticeLevelData"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="创建时间：" prop="date">
                  <el-date-picker
                    id="noticeInput1"
                    v-model="searchForm.createTime"
                    :clearable="false"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    class="noticeInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="type" label="公告类型：">
                  <el-select
                    v-model="searchForm.noticeType"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in noticeTypedata"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="发布时间："
                  class="el-form-item-list"
                  prop="date"
                >
                  <el-date-picker
                    id="noticeInput2"
                    v-model="searchForm.releaseTime"
                    :clearable="false"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    class="noticeInput"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item class="query-area-btn-css">
                  <el-button
                    v-loading
                    class="solid-with-icon-btn"
                    icon="el-icon-search"
                    @click="pageListAllPublish()"
                    >查询
                  </el-button>
                  <el-button
                    icon="el-icon-refresh"
                    class="hollow-with-icon-btn"
                    @click="reset()"
                    >重置
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </section>
        <section class="list-area-css">
          <div class="list-top-btn">
            <el-button
              v-hasAuth="doAuthArr({ btnCodeArr: ['wb_060107'] })"
              class="hollow-with-icon-btn"
              icon="el-icon-download"
              style="float: right;"
              @click="exportList()"
              >导出
            </el-button>
          </div>
          <div class="list-table-css">
            <el-table v-loading="tableLoading" :data="tableData" stripe>
              <el-table-column
                type="index"
                label="序号"
                width="70"
              ></el-table-column>
              <el-table-column prop="title" label="公告标题" min-width="130">
                <template slot-scope="scope">
                  <span
                    style="display: inherit;cursor: pointer;"
                    @click="viewDetail(scope.row.id)"
                    >{{ scope.row.title }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column label="公告类型" min-width="100">
                <template slot-scope="scope">
                  <template v-if="scope.row.type === '0'">普通公告</template>
                  <template v-else-if="scope.row.type === '1'"
                    >首页公告</template
                  >
                </template>
              </el-table-column>
              <el-table-column label="紧急程度" min-width="100">
                <template slot-scope="scope">
                  <template v-if="scope.row.urgency === '0'">普通</template>
                  <template v-else-if="scope.row.urgency === '1'"
                    >紧急</template
                  >
                  <template v-else-if="scope.row.urgency === '2'"
                    >非常紧急</template
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="createDate"
                label="创建时间"
                min-width="170"
              >
                <!--<template slot-scope="scope">
                                    <span>{{ changeDateTime(scope.row.createDate) }}</span>
                                </template>-->
              </el-table-column>
              <el-table-column
                prop="publishDate"
                label="发布时间"
                min-width="170"
              >
                <!--<template slot-scope="scope">
                                    <span>{{ changeDateTime(scope.row.publishDate) }}</span>
                                </template>-->
              </el-table-column>
              <el-table-column label="公告状态" min-width="120">
                <template slot-scope="scope">
                  <template v-if="scope.row.status === '0'">草稿</template>
                  <template v-if="scope.row.status === '1'">待审核</template>
                  <template v-else-if="scope.row.status === '2'"
                    >审核不通过</template
                  >
                  <template v-else-if="scope.row.status === '3'"
                    >已撤回</template
                  >
                  <template v-else-if="scope.row.status === '4'"
                    >已发布</template
                  >
                  <template v-else-if="scope.row.status === '5'"
                    >已删除</template
                  >
                  <template v-else-if="scope.row.status === '6'"
                    >取消发布</template
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="readNum"
                label="查阅人数"
                min-width="100"
              ></el-table-column>
              <el-table-column label="操作" min-width="120">
                <template slot-scope="scope">
                  <span class="table-edit" @click="viewDetail(scope.row.id)"
                    >详情</span
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="list-pagination-css">
            <el-pagination
              :current-page.sync="pagination.page"
              :page-sizes="pagination.pageSizes"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              layout="total, prev, pager, next, sizes, jumper"
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </section>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'QueryList',
  components: {},
  filters: {},
  data() {
    return {
      searchForm: {
        // 查询表单
        noticeTitle: '',
        noticeLevel: '', // 紧急程度
        noticeType: '', // 公告类型
        createTime: '', // 是否需要审核
        releaseTime: '' // 提醒方式
      },
      noticeTypedata: [
        {
          value: '0',
          label: '普通公告'
        },
        {
          value: '1',
          label: '首页公告'
        }
      ],
      noticeLevelData: [
        {
          value: '0',
          label: '普通'
        },
        {
          value: '1',
          label: '紧急'
        },
        {
          value: '2',
          label: '非常紧急'
        }
      ],
      tableLoading: false, // 等待状态
      tableData: [], // 表单数据
      pagination: {
        // 分页数据
        page: 1,
        total: 0,
        pageSize: 10,
        pageSizes: [10, 20, 30, 40]
      },
      prePageSize: 10
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.pageListAllPublish()
  },
  methods: {
    doAuthArr(opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCodeArr: opt.btnCodeArr }
    },
    // 查看详情
    viewDetail(id) {
      this.$router.push({ path: '/notice/simpleDetail', query: { id: id } })
    },
    // 行数切换
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pageListAllPublish()
    },
    // 翻页
    handleCurrentChange(val) {
      this.pagination.page = val
      this.pageListAllPublish()
    },
    // 重置
    reset() {
      this.searchForm.noticeTitle = ''
      this.searchForm.noticeLevel = ''
      this.searchForm.noticeType = ''
      this.searchForm.createTime = ''
      this.searchForm.releaseTime = ''
    },
    // 分页查询已发布公告
    pageListAllPublish() {
      this.tableLoading = true
      const param = {
        createDateStart: this.changeDate(this.searchForm.createTime[0]),
        createDateEnd: this.changeDate(this.searchForm.createTime[1]),
        publishDateStart: this.changeDate(this.searchForm.releaseTime[0]),
        publishDateEnd: this.changeDate(this.searchForm.releaseTime[1]),
        order: 'desc',
        sortName: 'title',
        page: this.pagination.page,
        rows: this.pagination.pageSize,
        title: this.searchForm.noticeTitle,
        type: this.searchForm.noticeType,
        urgency: this.searchForm.noticeLevel
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix +
            '/annoucement/pageListAllPublish',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            // table列表
            this.tableData = response.data
            this.tableLoading = false
            this.pagination.total = response.total
            this.prePageSize = response.records
          }
        })
    },
    // 公告查询列表看详情记录
    readDetail(data) {
      const param = {
        id: data
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/annoucement/read',
          param
        )
        .then(response => {
          this.$reponseStatus(response)
        })
    },
    // 导出
    exportList() {
      const param = {
        createDateStart: this.changeDate(this.searchForm.createTime[0]),
        createDateEnd: this.changeDate(this.searchForm.createTime[1]),
        publishDateStart: this.changeDate(this.searchForm.releaseTime[0]),
        publishDateEnd: this.changeDate(this.searchForm.releaseTime[1]),
        title: this.searchForm.noticeTitle,
        type: this.searchForm.noticeType,
        urgency: this.searchForm.noticeLevel
      }
      const arrs = []
      Object.keys(param).forEach(key => {
        param[key] ? arrs.push(key + '=' + param[key]) : delete param[key]
      })
      let url = this.$apiConfig.managerPathPrefix + '/annoucement/export'
      if (arrs.length > 0) {
        url = url + '?' + arrs.join('&')
      }
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      // 新开窗口下载
      this.$aspHttps.asp_ExportGetOpen(url)
    },
    // 日期转换
    changeDate(dateData) {
      if (dateData) {
        const months =
          dateData.getMonth() + 1 < 10
            ? '0' + (dateData.getMonth() + 1)
            : dateData.getMonth() + 1
        const days =
          dateData.getDate() < 10
            ? '0' + dateData.getDate()
            : dateData.getDate()
        const dateArr = dateData.getFullYear() + '-' + months + '-' + days
        return dateArr
      } else {
        return ''
      }
    }
  }
}
</script>
