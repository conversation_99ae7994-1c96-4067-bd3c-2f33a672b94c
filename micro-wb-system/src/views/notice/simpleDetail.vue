/** * 公告管理 */
<template>
  <div class="webbas">
    <!-- 主要模块入口 -->
    <section class="detail-css">
      <div class="detail-content-css">
        <el-collapse v-model="activeNames" class="pcc-collapse">
          <el-collapse-item name="1">
            <template slot="title" name="1">公告内容</template>
            <el-form :model="headContent">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="发布人：">
                    {{ createNameChange(headContent.createName) }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发布时间：">
                    {{ changeDateTime(headContent.releaseTime) }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="公告类型：">
                    {{ typeChange(headContent.type) }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="是否审核：">
                    {{ isExamineChange(headContent.isExamine) }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="提醒方式：">
                    <div>{{ remindWayChange(headContent.remindWay1) }}</div>
                    <div>{{ remind2WayChange(headContent.remindWay2) }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="是否回复：">
                    {{ isReplyChange(headContent.isReply) }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-model="activeNames" class="pcc-collapse">
          <el-collapse-item name="2">
            <template slot="title" name="2">公告正文</template>
            <el-form>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="">
                    <div style="text-align: center; margin-left: 110px">
                      {{ noticeTitle }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="border-top: none">
                <el-col :span="24">
                  <el-form-item label="">
                    <div
                      style="margin-left: 110px"
                      v-html="this.$aspUtils.htmlDecodeByRegExp(noticeContent)"
                    ></div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-model="activeNames" class="pcc-collapse">
          <el-collapse-item name="3">
            <template slot="title" name="3">回复详情</template>
            <el-row>
              <div class="list-area-css">
                <div class="list-top-btn-css">
                  <el-button
                    v-hasAuth="doAuth({ btnCode: 'wb_060130' })"
                    v-if="headContent.isReply === '1'"
                    type="primary"
                    class="solid-with-icon-btn"
                    size="small"
                    @click="reply()"
                    >回复</el-button
                  >
                </div>
                <div>
                  <el-table v-loading="tableLoading" :data="objectData" stripe>
                    <el-table-column
                      prop="replyDateStr"
                      label="回复时间"
                      min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="replyOpinion"
                      label="回复意见"
                      min-width="100"
                      show-overflow-tooltip
                    ></el-table-column>
                  </el-table>
                </div>
              </div>
            </el-row>
          </el-collapse-item>
        </el-collapse>
        <div class="center_button">
          <el-button size="small" class="hollow-with-icon-btn" @click="back()"
            >返回</el-button
          >
        </div>
        <el-dialog
          :visible.sync="replyVisible"
          custom-class="dialog-content-custom"
          title="提示"
          width="30%"
        >
          <span style="line-height: 30px;">请输入回复意见</span>
          <el-input
            v-model="replyValue"
            placeholder="请输入回复意见"
          ></el-input>
          <span slot="footer" class="dialog-footer">
            <el-button
              size="small"
              class="hollow-with-icon-btn"
              @click="replyVisible = false"
              >取 消</el-button
            >
            <el-button
              type="primary"
              size="small"
              class="solid-with-icon-btn"
              @click="sure()"
              >确 定</el-button
            >
          </span>
        </el-dialog>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'NoticeDetail',
  components: {},
  filters: {},
  data() {
    return {
      activeNames: ['1', '2', '3'],
      headContent: {
        // 查询表单
        createName: '',
        releaseTime: '', // 紧急程度
        type: '', // 公告类型
        isExamine: '', // 是否需要审核
        remindWay1: '', // 提醒方式 邮件提醒
        remindWay2: '', // 提醒方式 短信提醒
        isReply: '' // 是否回复
      },
      createNameData: [],
      userId: '',
      detailId: '',
      noticeTitle: '', // 公告标题
      noticeContent: '', // 公告内容
      objectData: [], // 回复公告记录
      tableLoading: true,
      replyVisible: false,
      replyValue: ''
    }
  },
  watch: {},
  created() {},
  mounted() {
    if (this.$route.query.id) {
      this.detailId = this.$route.query.id
    } else {
      this.detailId = ''
    }
    this.init()
  },
  methods: {
    /**
     * 按钮权限
     * @method doAuth
     * @param {object} opt
     * @return {object}
     */
    doAuth(opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    init() {
      this.getDetail()
      this.listPageReadDetail()
      this.getList()
    },

    // 切换
    createNameChange(data) {
      for (let i = 0; i < this.createNameData.length; i++) {
        const item = this.createNameData[i].id
        if (item === data) {
          return this.createNameData[i].userName
        }
      }
    },
    typeChange(data) {
      if (data === '0') {
        return '普通公告'
      } else if (data === '1') {
        return '首页公告'
      }
    },
    isExamineChange(data) {
      if (data === '0') {
        return '否'
      } else if (data === '1') {
        return '是'
      }
    },
    isReplyChange(data) {
      if (data === '0') {
        return '否'
      } else if (data === '1') {
        return '是'
      }
    },
    remindWayChange(data) {
      if (data === '1') {
        return '邮件提醒'
      } else {
        return ''
      }
    },
    remind2WayChange(data) {
      if (data === '1') {
        return '短信提醒'
      } else {
        return ''
      }
    },
    // 返回
    back() {
      this.$router.back()
    },
    // 查询公告详情
    getDetail() {
      const param = {
        id: this.detailId
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/annoucement/getDetailById',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            // 公告内容
            this.headContent.createName = response.data.createUser
            this.headContent.releaseTime = response.data.publishDate
            this.headContent.type = response.data.type
            this.headContent.isExamine = response.data.reviewFlag
            this.headContent.remindWay1 = response.data.emailNotifyFlag
            this.headContent.remindWay2 = response.data.smsNotifyFlag
            this.headContent.isReply = response.data.replyFlag
            this.noticeTitle = response.data.title
            this.noticeContent = response.data.content
            this.userId = response.data.id
          }
        })
    },
    // 查询回复公告列表
    listPageReadDetail() {
      const param = {
        id: this.detailId
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/annoucement/listReply',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            // 回复公告记录
            this.objectData = response.data
            this.tableLoading = false
          }
        })
    },
    // 日期时间转化
    changeDateTime(dateData) {
      if (dateData) {
        const date = dateData.split('T')[0]
        const time = dateData.split('T')[1].split('.')[0]
        return date + ' ' + time
      } else {
        return ''
      }
    },
    // 获取发布人
    getList() {
      var param = {
        keyword: '',
        order: 'asc',
        organizationId: '',
        organizationName: '',
        page: 1,
        rows: 10,
        sortName: 'wbUser.user_name',
        status: 'NORMAL'
      }
      this.$aspHttps
        .asp_Post(this.$apiConfig.managerPathPrefix + '/user/list', param)
        .then(response => {
          if (this.$reponseStatus(response)) {
            const arr = []
            response.data.forEach(function(item) {
              arr.push({
                id: item.id,
                userName: item.userName,
                show: false
              })
            })
            this.createNameData = arr
          }
        })
    },
    // 显示回复弹框
    reply() {
      this.replyVisible = true
    },
    // 确认回复
    sure() {
      const param = {
        annoucementId: this.detailId,
        replyOpinion: this.replyValue
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/annoucement/replyAnnoucement',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.replyVisible = false
            this.listPageReadDetail()
            this.$message({
              type: 'success',
              message: '回复成功'
            })
          }
        })
    }
  }
}
</script>
