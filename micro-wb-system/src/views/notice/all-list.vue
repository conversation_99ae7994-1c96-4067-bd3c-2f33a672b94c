/** * 公告管理 */
<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form ref="searchForm"
                 :inline="true"
                 :model="table.searchForm"
                 @submit.native.prevent>
          <el-row>
            <el-col :span="8">
              <el-form-item prop="noticeTitle"
                            label="公告标题：">
                <el-input v-model.trim="table.searchForm.noticeTitle"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="noticeLevel"
                            label="紧急程度：">
                <asp-select-opt v-model="table.searchForm.noticeLevel"
                                :code-list="noticeLevelData"></asp-select-opt>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="noticeType"
                            label="公告类型：">
                <asp-select-opt v-model="table.searchForm.noticeType"
                                :code-list="noticeTypedata"></asp-select-opt>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="创建时间：">
                <asp-date-range :start-date.sync="table.searchForm.createDateStart"
                                :end-date.sync="table.searchForm.createDateEnd" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发布时间：">
                <asp-date-range :start-date.sync="table.searchForm.publishDateStart"
                                :end-date.sync="table.searchForm.publishDateEnd" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item class="query-area-btn-css">
                <asp-btn-solid icon="el-icon-search"
                               name="查询"
                               @click="search()"></asp-btn-solid>
                <asp-btn-hollow icon="el-icon-refresh"
                                name="重置"
                                @click="reset()"></asp-btn-hollow>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <asp-table ref="table"
                 :url="table.url"
                 :param="table.searchForm"
                 :prefix="table.prefix"
                 type="">
        <template slot="header">
          <asp-btn-solid v-hasAuth="doAuth({ btnCode: 'wb_130105' })"
                         name="导出"
                         icon="el-icon-download"
                         @click="exportList"></asp-btn-solid>
        </template>
        <asp-table-column prop="title"
                          min-width="120"
                          label="公告标题"
                          sortable>
          <template slot-scope="scope">
            <el-button type="text"
                       @click="viewDetail(scope.row.id)">
              {{ scope.row.title }}
            </el-button>
          </template>
        </asp-table-column>
        <asp-table-column prop="type"
                          min-width="150"
                          label="公告类型"
                          sortable>
          <template slot-scope="scope">
            {{ this.$aspUtils.formatDict(scope.row.type, noticeTypedata) }}
          </template>
        </asp-table-column>
        <asp-table-column prop="urgency"
                          min-width="150"
                          label="紧急程度"
                          sortable>
          <template slot-scope="scope">
            {{ this.$aspUtils.formatDict(scope.row.urgency, noticeLevelData) }}
          </template>
        </asp-table-column>
        <asp-table-column :date-format="true"
                          prop="createDate"
                          label="创建时间">
        </asp-table-column>
        <asp-table-column :date-format="true"
                          prop="publishDate"
                          label="发布时间">
        </asp-table-column>
        <asp-table-column prop="status"
                          min-width="150"
                          label="公告状态"
                          sortable>
        </asp-table-column>
        <asp-table-column prop="readNum"
                          min-width="150"
                          label="查阅人数"
                          sortable>
        </asp-table-column>
      </asp-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QueryList',
  components: {},
  filters: {},
  data () {
    return {
      table: {
        prefix: this.$apiConfig.managerPathPrefix,
        url: '/annoucement/pageList',
        searchForm: {
          // 查询表单
          noticeTitle: '',
          noticeLevel: '', // 紧急程度
          noticeType: '', // 公告类型
          createDateStart: '', // 创建开始时间
          createDateEnd: '', // 创建结束时间
          publishDateStart: '', // 发布开始时间
          publishDateEnd: '' // 发布结束时间
        }
      },
      noticeTypedata: [
        {
          code: '0',
          name: '普通公告'
        },
        {
          code: '1',
          name: '首页公告'
        }
      ],
      noticeLevelData: [
        {
          code: '0',
          name: '普通'
        },
        {
          code: '1',
          name: '紧急'
        },
        {
          code: '2',
          name: '非常紧急'
        }
      ]
    }
  },
  watch: {},
  created () { },
  mounted () { },
  methods: {
    // 查看详情
    viewDetail (id) {
      this.$router.push({ path: '/notice/detail', query: { id: id } })
    },
    search () {
      this.$refs.table.asp_search()
    },
    reset () {
      this.$refs.table.asp_reset()
    },
    // 导出
    exportList () {
      const param = {
        createDateStart: this.changeDate(this.searchForm.createTime[0]),
        createDateEnd: this.changeDate(this.searchForm.createTime[1]),
        publishDateStart: this.changeDate(this.searchForm.releaseTime[0]),
        publishDateEnd: this.changeDate(this.searchForm.releaseTime[1]),
        title: this.searchForm.noticeTitle,
        type: this.searchForm.noticeType,
        urgency: this.searchForm.noticeLevel
      }
      this.$aspHttps
        .asp_Fetch(
          this.$apiConfig.managerPathPrefix + '/annoucement/export',
          param
        )
        .then(response => {
          if (!this.$reponseStatus(response)) {
            //
          }
        })
    }
  }
}
</script>
