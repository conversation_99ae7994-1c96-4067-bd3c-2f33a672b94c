/** * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/10/29. * @module editor *
新增/修改公告页面子组件_富文本编辑器 * */

<template>
  <textarea :id="id"
            :value="value"></textarea>
</template>

<script>
// Import TinyMCE
import tinymce from 'tinymce/tinymce'
// import "tinymce/themes/modern/theme";
import 'tinymce/plugins/advlist'
import 'tinymce/plugins/link'
import 'tinymce/plugins/image'
import 'tinymce/plugins/code'
import 'tinymce/plugins/table'
import 'tinymce/plugins/textcolor'
import 'tinymce/plugins/paste'
import 'tinymce/plugins/colorpicker'
const INIT = 0
const CHANGED = 2
// var EDITOR = null
export default {
  props: {
    value: {
      type: String,
      editor: null,
      required: true
    },
    // setting: {},
    url: {
      // 接口
      default: '',
      type: String
    },
    accept: {
      // 文件类型
      default: 'image/jpeg, image/png',
      type: String
    },
    maxSize: {
      // 大小
      default: 2097152,
      type: Number
    },
    withCredentials: {
      default: false,
      type: <PERSON><PERSON><PERSON>
    }
  },
  data () {
    return {
      status: INIT,
      id: 'editor-' + new Date().getMilliseconds()
    }
  },
  watch: {
    value: function (val) {
      if (this.status === INIT || tinymce.activeEditor.getContent() !== val) {
        tinymce.activeEditor.setContent(val)
      }
      this.status = CHANGED
    }
  },

  mounted () {
    const _this = this
    tinymce.init({
      selector: '#' + _this.id,
      upload_image_url: '/web/component/v1/attachment/add', // 配置的上传图片的路由
      language_url: './static/tinymce/zh_CN.js',
      language: 'zh_CN',
      skin_url: './static/tinymce/skins/lightgray',
      init_instance_callback: function (editor) {
        // EDITOR = editor
        editor.on('input change undo redo', () => {
          var content = editor.getContent()
          _this.$emit('show', content)
        })
      },
      content_style: `
    *                         { padding:0; margin:0; }
    html, body                { height:100%; }
    img                       { max-width:100%; display:block;height:auto; }
    a                         { text-decoration: none; }
    iframe                    { width: 100%; }
    p                         { line-height:1.6; margin: 0px; }
    table                     { word-wrap:break-word; word-break:break-all; max-width:100%; border:none; border-color:#999; }
    .mce-object-iframe        { width:100%; box-sizing:border-box; margin:0; padding:0; }
    ul,ol                     { list-style-position:inside; }
  `,
      insert_button_items: 'image link | inserttable',
      paste_retain_style_properties: 'all',
      paste_word_valid_elements: '*[*]', // word需要它
      paste_data_images: true, // 粘贴的同时能把内容里的图片自动上传，非常强力的功能
      paste_convert_word_fake_lists: false, // 插入word文档需要该属性
      paste_webkit_styles: 'all',
      paste_merge_formats: true,
      nonbreaking_force_tab: false,
      paste_auto_cleanup_on_paste: false,
      branding: false, // 去除右下角的'由tinymce驱动'
      elementpath: false, //  左下角的当前标签路径
      plugins: [
        'advlist link image',
        'code',
        'table textcolor paste textcolor colorpicker'
      ], // 配置
      width: 'auto',
      height: '500',
      theme_advanced_resizing: false,
      toolbar_items_size: 'small',
      block_formats:
        'Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;',
      toolbar1:
        'table |insertfile undo redo | formatselect | link unlink | uploadimg image media', // 工具栏1
      toolbar2:
        ' fontsizeselect | forecolor backcolor | fontselect bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | outdent indent | removeformat', // 工具栏2
      // 图片上传
      images_upload_handler: function (blobInfo, success, failure) {
        // failure(blobInfo)
        // _this.$emit('on-ready', blobInfo.blob().size, blobInfo.blob())
        /* 需求说明不需要校验图片大小
  if (blobInfo.blob().size > _this.maxSize) {
    failure('文件体积过大')
  } */
        success(
          'data:' + blobInfo.blob().type + ';base64,' + blobInfo.base64()
        )
      }
    })
    // Object.assign(setting, _this.setting)

    // tinymce.init(setting)
  },
  beforeDestroy: function () {
    tinymce.get(this.id).destroy()
  },

  methods: {}
}
</script>
