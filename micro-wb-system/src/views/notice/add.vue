/** * 公告管理 */
<template>
  <div class="webbas">
    <!-- 主要模块入口 -->
    <section class="add-css">
      <div class="add-content-css">
        <div>
          <el-form ref="noticeForm"
                   :model="noticeForm"
                   :rules="addFormRules"
                   :inline="true"
                   :inline-message="true">
            <el-row>
              <el-col :span="6">
                <el-form-item label="公告类型"
                              prop="noticeType">
                  <el-select v-model="noticeForm.noticeType"
                             placeholder="请选择">
                    <el-option v-for="item in noticeTypedata"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="紧急程度"
                              prop="noticeLevel">
                  <el-select v-model="noticeForm.noticeLevel"
                             placeholder="请选择">
                    <el-option v-for="item in noticeLevelData"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否需要审核"
                              prop="isExamine">
                  <div v-if="editStatus">
                    <el-radio v-model="noticeForm.isExamine"
                              :label="'1'"
                              disabled>是</el-radio>
                    <el-radio v-model="noticeForm.isExamine"
                              :label="'0'"
                              disabled>否</el-radio>
                  </div>
                  <div v-else>
                    <el-radio v-model="noticeForm.isExamine"
                              :label="'1'">是</el-radio>
                    <el-radio v-model="noticeForm.isExamine"
                              :label="'0'">否</el-radio>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="提醒方式"
                              prop="noticeWay">
                  <el-checkbox-group v-model="noticeForm.noticeWay">
                    <el-checkbox :label="'1'">邮件</el-checkbox>
                    <el-checkbox :label="'2'">短信</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="是否回复"
                              prop="isReply">
                  <el-radio v-model="noticeForm.isReply"
                            :label="'1'">是</el-radio>
                  <el-radio v-model="noticeForm.isReply"
                            :label="'0'">否</el-radio>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <div v-if="noticeTypeFlag">
                  <el-form-item label="公告有效期日期"
                                prop="noticeValidity">
                    <el-date-picker v-model="noticeForm.noticeValidity"
                                    type="date"
                                    placeholder="选择日期"
                                    value-format="yyyy-MM-dd"></el-date-picker>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="6">
                <div v-if="noticeTypeFlag">
                  <el-form-item label="展示方式"
                                prop="showWay">
                    <el-select v-model="noticeForm.showWay"
                               placeholder="请选择">
                      <el-option v-for="item in showWayData"
                                 :key="item.value"
                                 :label="item.label"
                                 :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="公告标题"
                              prop="noticeTitle">
                  <el-input v-model="noticeForm.noticeTitle"
                            placeholder="请输入标题"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="附件">
                  <el-upload id="noticeUpload"
                             :file-list="fileList"
                             :on-remove="handleRemove"
                             :on-success="onSuccess"
                             :before-upload="beforeUpload"
                             :on-error="onError"
                             :data="uploadData"
                             :action="uploadUrl"
                             class="upload-demo label-input">
                    <el-button type="primary"
                               class="solid-with-icon-btn"
                               size="small"
                               icon="el-icon-upload2">上传附件</el-button>
                    <span style="color: #606266">最大50M</span>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="发布对象">
                  <el-radio v-model="radioObject"
                            :label="'3'">选择域</el-radio>
                  <div style="display: inline-block">
                    <el-checkbox-group v-model="checkAdmin">
                      <el-checkbox v-for="(item, index) in checkAdminData"
                                   :key="index"
                                   :label="item.code"
                                   :disabled="isCheckField">{{ item.name }}</el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <div>
                    <el-radio v-model="radioObject"
                              :label="'1'">选择对象</el-radio>
                  </div>
                  <div>
                    <div class="transfer">
                      <div class="frameSearch">请选择对象</div>
                      <div class="frameSearch">
                        <el-input v-model="searchTreeData"
                                  class="search-box"
                                  placeholder="请输入搜索">
                          <i slot="suffix"
                             class="el-input__icon el-icon-search frameSearchIcon"></i>
                        </el-input>
                      </div>
                      <div class="frameContent">
                        <el-tree id="tree-change"
                                 ref="treeLeft"
                                 :data="treeData"
                                 :props="defaultProps"
                                 :filter-node-method="filterNode"
                                 :check-strictly="true"
                                 node-key="id"
                                 default-expand-all
                                 show-checkbox></el-tree>
                      </div>
                    </div>
                    <div class="transfer"
                         style="width: 10%; border: none">
                      <div class="center_button">
                        <el-button type="primary"
                                   class="solid-with-icon-btn"
                                   size="small"
                                   @click="addInfo()">添加<i class="el-icon-arrow-right el-icon--right"></i></el-button>
                      </div>
                      <div class="center_button">
                        <el-button :disabled="isDelete"
                                   type="primary"
                                   class="solid-with-icon-btn"
                                   size="small"
                                   icon="el-icon-arrow-left"
                                   @click="deleteInfo()">删除</el-button>
                      </div>
                    </div>
                    <div class="transfer">
                      <div class="frameSearch">已选择对象</div>
                      <div class="frameSearch">
                        <el-input v-model="searchObjectData"
                                  class="search-box"
                                  placeholder="请输入搜索">
                          <i slot="suffix"
                             class="el-input__icon el-icon-search frameSearchIcon"></i>
                        </el-input>
                      </div>
                      <div class="frameContent">
                        <div v-for="(item, index) in objectData"
                             :key="index">
                          <el-checkbox-group v-model="checkDatas">
                            <el-checkbox :label="item"
                                         @change="checkboxEvent(item, $event)"></el-checkbox>
                          </el-checkbox-group>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="公告正文">
                  <editor ref="tinymce"
                          :value="content"
                          :setting="editorSetting"
                          :url="Url"
                          :max-size="MaxSize"
                          :accept="Accept"
                          :with-credentials="withCredentials"
                          class="editor"
                          @on-upload-fail="onEditorReady"
                          @on-upload-success="onEditorUploadComplete"
                          @show="editors"></editor>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="抄送">
                  <div class="copy">
                    <span v-for="(item, index) in noticeCopy"
                          :key="index">
                      {{ item.userName }}
                      <i class="el-tag__close el-icon-close"
                         @click="delnoticeCopy(index)" />
                    </span>
                  </div>
                  <el-button type="primary"
                             class="solid-with-icon-btn"
                             size="small"
                             style="vertical-align: middle"
                             @click="choiceNoticeCopy()">选择</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="center_button">
          <el-button :disabled="referDraftStatus"
                     size="small"
                     class="hollow-with-icon-btn"
                     @click="referDraft()">存草稿</el-button>
          <el-button :disabled="submitStatus"
                     class="solid-with-icon-btn"
                     type="primary"
                     size="small"
                     @click="preventSubmission()">提交</el-button>
          <el-button size="small"
                     class="hollow-with-icon-btn"
                     @click="cancel()">取消</el-button>
        </div>
      </div>
    </section>

    <!-- 修改公告 -->
    <choiceNoticeCopyTemplate ref="editRole"
                              :dialog-param="copyModelParam"
                              @editSubmit="confirmSubmit"></choiceNoticeCopyTemplate>
  </div>
</template>

<script>
import choiceNoticeCopyTemplate from './components/choiceNoticeCopyTemplate'
// 文本编辑器
import editor from '@/views/notice/editor'
export function debounce (fn, delay) {
  const timeout = delay || 200
  var timer
  return function () {
    var th = this
    var args = arguments
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(function () {
      timer = null
      fn.call(th, args)
    }, timeout)
  }
}
export default {
  name: 'NoticeAdd',
  components: {
    choiceNoticeCopyTemplate,
    editor
  },
  filters: {},
  data () {
    return {
      noticeAttachmentType: {},
      uploadUrl: '',
      fileList: [], // 附件
      uploadData: null, // 附件上传附带参数
      gatAttachGroupId: '',
      preventSubmission: debounce(this.submission, 500),
      noticeForm: {
        // 查询表单
        noticeType: '', // 公告类型
        noticeLevel: '', // 紧急程度
        isExamine: '0', // 是否需要审核 0否1是
        noticeWay: ['1'], // 提醒方式 1 --邮件，2 --短信
        noticeValidity: '', // 公告有效期日期
        showWay: '', // 展示方式
        showPortal: '1', // 展示门户 1管理员2合作伙伴
        isReply: '0', // 是否回复
        noticeTitle: '', // 公告标题
        noticecontent: '', // 公告正文
        noticeCopy: [] // 抄送内容
      },
      noticeTypeFlag: true,
      noticeTypedata: [
        {
          value: '0',
          label: '普通公告'
        },
        {
          value: '1',
          label: '首页公告'
        }
      ],
      noticeLevelData: [
        {
          value: '0',
          label: '普通'
        },
        {
          value: '1',
          label: '紧急'
        },
        {
          value: '2',
          label: '非常紧急'
        }
      ],
      showWayData: [
        {
          value: '1',
          label: '登录页展示'
        },
        {
          value: '2',
          label: '当前页展示'
        }
      ],
      noticecontent: '', // 公告正文
      noticeCopy: [], // 抄送内容
      radioObject: '3', // 选择域 1-组织授权,3-域授权
      checkAdmin: [], // 发布对象 选择域
      checkAdminData: [], // 发布对象 选择域元数据
      searchTreeData: '', // 未选择对象搜索
      searchObjectData: '', // 已选择对象搜索
      objectData: [], // 对象元数据
      selObjectData: [], // 获取对象元数据
      objectMiddleData: [], // 对象数据过渡数据
      searchObjectMiddleData: [], // 已选择对象搜索过渡数据
      treeData: [], // 未选择对象元数据
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      deleteId: '', // 选择删除对象的id
      checkDatas: [], // 选择删除的对象
      copyModelParam: {},
      organizationData: [],
      isDrafts: '0', // 是否为草稿 1:是;0:否
      userId: '',
      copyData: [], // 获取抄送人元数据
      // 配置富文本编辑器高
      editorSetting: {
        height: 300
      },
      // 图片对应的上传地址
      Url: '',
      // 文件大小 10MB
      MaxSize: 10485760,
      // 文件格式
      Accept: 'image/jpeg, image/png',
      withCredentials: true,
      // 富文本编辑器双向绑定的内容
      content: '',
      isDelete: true,
      submitStatus: false, // 提交按钮禁用
      referDraftStatus: false, // 存草稿按钮禁用
      editStatus: false, // 判断修改的时候不可修改
      isCheckField: false, // 选择对象的时候不能选择域里面的选项
      addFormRules: {
        noticeType: [
          {
            required: true,
            message: '不能为空',
            trigger: ['blur', 'change']
          }
        ],
        noticeLevel: [
          {
            required: true,
            message: '不能为空',
            trigger: ['blur', 'change']
          }
        ],
        isExamine: [
          {
            required: true,
            message: '不能为空',
            trigger: ['blur', 'change']
          }
        ],
        noticeWay: [
          {
            required: true,
            message: '不能为空',
            trigger: ['blur', 'change']
          }
        ],
        noticeValidity: [
          {
            required: true,
            message: '不能为空',
            trigger: ['blur', 'change']
          }
        ],
        showWay: [
          {
            required: true,
            message: '不能为空',
            trigger: ['blur', 'change']
          }
        ],
        noticeTitle: [
          {
            required: true,
            message: '不能为空',
            trigger: ['blur', 'change']
          },
          {
            max: 200,
            message: '输入不能超过200个字符',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {},
  watch: {
    // 监听公告类型
    'noticeForm.noticeType' (val) {
      if (val === '0') {
        this.noticeTypeFlag = false
      } else {
        this.noticeTypeFlag = true
      }
    },
    // 监听发布对象
    radioObject (val) {
      if (val === '3') {
        this.checkAdmin = []
        this.objectData = []
        this.isCheckField = false
      } else if (val === '1') {
        this.checkAdmin = []
        this.isCheckField = true
      }
    },
    searchTreeData (val) {
      this.$refs.treeLeft.filter(val)
    },
    searchObjectData (val) {
      this.objectData = this.searchObjectMiddleData
      const arr = []
      for (let i = 0; i < this.objectData.length; i++) {
        const item = this.objectData[i]
        if (item.indexOf(val) > -1) {
          arr.push(item)
        }
      }
      this.objectData = arr
    },
    // 监听已选中发布对象
    checkDatas (val) {
      // 删除按钮是否可点击
      if (val.length > 0) {
        this.isDelete = false
      } else {
        this.isDelete = true
      }
    }
  },
  created () {
    this.getAttachmentType()
  },
  mounted () {
    this.Url = this.$aspHttps.asp_TokenUrl(
      '/web/system/v3.1/component/attachment/add'
    )
    this.init()
    if (this.$route.query.operate === 'Edit') {
      if (this.$route.query.id) {
        this.userId = this.$route.query.id
      }
      this.getList()
    }
    if (this.$route.query.operate === 'Edit') {
      if (this.$route.query.status === '2') {
        this.editStatus = true
      }
    }
  },
  methods: {
    filterNode (value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    init () {
      this.organizationList()
      this.getTypes()
    },
    // editor组件传过来的值赋给content
    editors (content) {
      this.content = content
    },
    // 上传失败的函数
    onEditorReady (ins, ina) {
      // console.log(ins, ina)
    },
    // 处理上传图片后返回数据，添加img标签到编辑框内
    onEditorUploadComplete (json) {
      this.content =
        this.content + "<img src='data:image/jpeg;base64," + json + '>'
    },
    // 提交
    submission () {
      this.submitStatus = true
      if (this.$route.query.operate === 'Add') {
        this.isDrafts = '0'
        this.insertNotice()
      } else {
        this.isDrafts = '0'
        this.updateNotice()
      }
    },
    // 存草稿
    referDraft () {
      this.referDraftStatus = true
      if (this.$route.query.operate === 'Add') {
        this.isDrafts = '1'
        this.insertNotice()
      } else {
        this.isDrafts = '1'
        this.updateNotice()
      }
    },
    // 取消
    cancel () {
      this.$router.back()
    },

    // 选择抄送人
    choiceNoticeCopy () {
      this.copyModelParam = {
        title: '人员名单',
        modelVisible: true
      }
    },
    // 响应抄送人
    confirmSubmit (val) {
      for (let i = 0; i < val.length; i++) {
        const item = val[i]
        this.noticeCopy.push({ id: item.id, userName: item.userName })
      }

      const arrData = []
      for (let i = 0; i < this.noticeCopy.length; i++) {
        for (let j = i + 1; j < this.noticeCopy.length; j++) {
          if (this.noticeCopy[i].id === this.noticeCopy[j].id) {
            ++i
          }
        }
        arrData.push(this.noticeCopy[i])
      }
      this.noticeCopy = arrData
    },
    // 抄送人转换
    noticeCopyChange (dataArr) {
      const arr = []
      dataArr.forEach((item, index) => {
        // console.log(index)
        arr.push(item.id)
      })
      // 抄送人转换去重
      const hash = []
      for (let i = 0; i < arr.length; i++) {
        if (hash.indexOf(arr[i]) < 0) {
          hash.push(arr[i])
        }
      }
      return hash.sort()
    },
    // 删除抄送人
    delnoticeCopy (index) {
      this.noticeCopy.splice(index, 1)
    },

    // 文件删除
    handleRemove (file, fileList) {
      // console.log(file, fileList)
    },
    // 获取附件上传配置信息
    getAttachmentType () {
      this.$aspHttps
        .asp_Post(this.$apiConfig.supportPathPrefix + '/attachment/viewType', {
          attachTypeId: '1'
        })
        .then(response => {
          if (
            this.$reponseStatus(response) &&
            Object.prototype.hasOwnProperty.call(response, 'data')
          ) {
            this.noticeAttachmentType = response.data
          }
        })
    },
    // 上传之前 上传附件附带参数
    beforeUpload (file) {
      // 校验附件
      if (!file) {
        this.$message.error('不能上传空文件！')
        return null
      }
      const fileSuffix = file.name.split('.')[1]
      if (
        this.noticeAttachmentType.fileSuffixLimit &&
        this.noticeAttachmentType.fileSuffixLimit
          .split(',')
          .indexOf(fileSuffix) < 0
      ) {
        this.$message.error(
          '文件后缀只能是' + this.noticeAttachmentType.fileSuffixLimit + '！'
        )
        return null
      }
      if (
        this.noticeAttachmentType.maxFileNameLength &&
        file.name.length > this.noticeAttachmentType.maxFileNameLength
      ) {
        this.$message.error('文件名称过长！')
        return null
      }
      if (
        this.noticeAttachmentType.singleSizeLimit &&
        file.size > this.noticeAttachmentType.singleSizeLimit
      ) {
        this.$message.error(
          '文件大小不能超过' +
          this.noticeAttachmentType.singleSizeLimit / 1024 / 1024 +
          'MB！'
        )
        return null
      }
      let url = this.$apiConfig.supportPathPrefix + '/attachment/add'
      url = this.$aspUtils.getRealUrl(url, this)
      this.uploadUrl = this.$aspHttps.asp_TokenUrl(url)

      this.uploadData = {
        attachGroupId: this.gatAttachGroupId,
        attachTypeId: '1'
      }
      const promise = new Promise(resolve => {
        this.$nextTick(function () {
          resolve(true)
        })
      })
      return promise // 通过返回一个promis对象解决
    },
    // onError (err, file, fileList) {
    onError () {
      // console.log(err, file, fileList)
      this.$message.error('文件上传失败！')
    },
    // 文件上传成功
    // onSuccess (response, file, fileList) {
    onSuccess (response) {
      // console.log(file, fileList)
      if (this.$reponseStatus(response)) {
        this.gatAttachGroupId = response.data.attachGroupId
        this.formalGroup(response.data.attachGroupId)
      }
    },

    // 添加已选择对象
    addInfo () {
      if (this.radioObject === '3') {
        this.$message.error('请勾选选择对象')
        return
      }

      this.objectData = []
      this.selObjectData = []
      this.objectMiddleData = []

      const nodeStr = this.$refs.treeLeft.getCheckedNodes(false, false)

      const arr = []
      for (let i = 0; i < nodeStr.length; i++) {
        const item = nodeStr[i]
        arr.push({
          id: item.id,
          label: item.label,
          layer: item.layer
        })
      }

      // 去重
      const hash = []
      for (let j = 0; j < arr.length; j++) {
        if (hash.indexOf(arr[j].id) < 0) {
          this.selObjectData.push(arr[j])
        }
      }

      this.getPath(this.treeData, [], [])

      this.getPropsDatas(this.objectMiddleData)
    },
    // 删除已选择对象
    deleteInfo () {
      this.checkDatas.forEach(e => {
        this.getIds(e, this.treeData)
        for (let i = 0; i < this.selObjectData.length; i++) {
          if (this.deleteId === this.selObjectData[i].id) {
            this.selObjectData.splice(i, 1)
          }
        }
        const index = this.objectData.indexOf(e)
        if (index > -1) {
          this.objectData.splice(index, 1)
        }
      })
      let ids = []
      this.selObjectData.forEach(k => {
        ids.push(k.id)
      })
      // 去重
      ids = this.clearRepeat(ids)
      this.checkDatas = []
      // 清除选中节点
      this.$refs.treeLeft.setCheckedKeys([])
      // 设置选中节点
      this.$refs.treeLeft.setCheckedKeys(ids)
    },
    // 获取已选择对象的id
    getIds (str, arr) {
      let arrs = []
      arrs = str.split('/')
      for (let i = 0; i < arr.length; i++) {
        if (arrs.length > 1 && arrs[0] === arr[i].label) {
          this.getIds(arrs[arrs.length - 1], arr[i].children)
        } else if (str === arr[i].label) {
          this.deleteId = arr[i].id
        } else {
          if (arr[i].children.length > 0) {
            this.getIds(str, arr[i].children)
          }
        }
      }
    },
    // 选择要删除数据
    checkboxEvent (data, event) {
      if (event) {
        // 选中
        this.checkDatas.push(data)
      } else {
        // 不选中
        const index = this.checkDatas.indexOf(data)
        if (index > -1) {
          this.checkDatas.splice(index, 1)
        }
      }
      // 去重
      this.checkDatas = this.clearRepeat(this.checkDatas)
    },
    // 遍历数据
    getPath (arr, index1, index2) {
      for (let i = 0; i < this.selObjectData.length; i++) {
        arr.forEach((item, key) => {
          const id = index1.concat(key)
          const name = index2.concat(item.label)
          if (item.id === this.selObjectData[i].id) {
            this.objectMiddleData.push(name)
            return name
          } else {
            if (item.children.length > 0) {
              this.getPath(item.children, id, name)
            }
          }
        })
      }
    },
    // 对最后的数据重组
    getPropsDatas (arr) {
      const arr1 = []
      for (let i = 0; i < arr.length; i++) {
        let arr2 = []
        for (let j = 0; j < arr[i].length; j++) {
          const item = arr[i][j]
          arr2 = arr2 + item + '/'
        }
        arr2 = arr2.substring(0, arr2.length - 1)
        arr1.push(arr2)
      }
      this.getSelObjectData(arr1)
    },
    // 对最后的数据去重
    getSelObjectData (arr) {
      let hash = []
      hash = this.clearRepeat(arr)
      this.searchObjectMiddleData = hash
      this.objectData = hash
    },
    // 去重
    clearRepeat (arr) {
      const hash = []
      for (let i = 0; i < arr.length; i++) {
        if (hash.indexOf(arr[i]) < 0) {
          hash.push(arr[i])
        }
      }
      hash.sort()
      return hash
    },

    // 已选择对象转换，给部门赋值
    deptIdsChange () {
      if (this.radioObject === '3') {
        return []
      } else {
        const nodeStr = this.$refs.treeLeft.getCheckedNodes(false, false)

        // 获取选中id
        const arr = []
        for (let i = 0; i < nodeStr.length; i++) {
          const item = nodeStr[i]
          arr.push(item.id)
        }
        // 去重
        const hash = []
        for (let j = 0; j < arr.length; j++) {
          if (hash.indexOf(arr[j]) < 0) {
            hash.push(arr[j])
          }
        }

        for (let l = 0; l < this.organizationData.length; l++) {
          for (let m = 0; m < hash.length; m++) {
            const item = this.organizationData[l]
            const item1 = hash[m]
            if (item.id === item1) {
              hash.splice(m, 1)
            }
          }
        }
        return hash
      }
    },
    // 已选择对象转换，给组织赋值
    organizationIdsChange () {
      if (this.radioObject === '3') {
        return []
      } else {
        const nodeStr = this.$refs.treeLeft.getCheckedNodes(false, false)

        // 获取选中id
        const arr = []
        for (let i = 0; i < nodeStr.length; i++) {
          const item = nodeStr[i]
          arr.push(item.id)
        }
        // 去重
        const hash = []
        for (let j = 0; j < arr.length; j++) {
          if (hash.indexOf(arr[j]) < 0) {
            hash.push(arr[j])
          }
        }

        const orgaData = []
        for (let l = 0; l < this.organizationData.length; l++) {
          for (let m = 0; m < hash.length; m++) {
            const item = this.organizationData[l]
            const item1 = hash[m]
            if (item.id === item1) {
              orgaData.push(item1)
            }
          }
        }
        return orgaData
      }
    },
    // 提醒方式转换
    emailNotifyFlagChange (dataArr) {
      const arr = []
      dataArr.forEach(item => {
        arr.push(item)
      })
      if (arr.indexOf('1') > -1) {
        return '1'
      } else {
        return '0'
      }
    },
    smsNotifyFlagChange (dataArr) {
      const arr = []
      dataArr.forEach(item => {
        arr.push(item)
      })
      if (arr.indexOf('2') > -1) {
        return '1'
      } else {
        return '0'
      }
    },
    // 选择域转换
    checkAdminChange (dataArr) {
      if (this.radioObject === '1') {
        return []
      } else {
        return dataArr
      }
    },

    // 获取组织机构
    organizationList () {
      const param = {
        domain: '',
        name: '',
        order: 'asc',
        sortable: 'org.name',
        page: 1,
        rows: 10,
        type: '',
        division: ''
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/organization/list',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            const arrData = response.data
            const arr = []
            for (let i = 0; i < arrData.length; i++) {
              const item = arrData[i]
              arr.push({
                id: item.id,
                label: item.name,
                layer: '0',
                children: []
              })
            }
            this.organizationData = arr

            this.queryDeptTreeData()
          }
        })
    },
    queryDeptTreeData () {
      for (let index = 0; index < this.organizationData.length; index++) {
        const items = this.organizationData[index]
        this.$aspHttps
          .asp_PostForm(
            this.$apiConfig.managerPathPrefix +
            '/department/listDepartmentTree',
            { id: items.id }
          )
          .then(response => {
            if (this.$reponseStatus(response)) {
              const arrData = response.data
              const arr = []
              for (let i = 0; i < arrData.length; i++) {
                const item1 = arrData[i]
                arr.push({
                  id: item1.id,
                  label: item1.text,
                  layer: '1',
                  children: []
                })

                if (item1.children) {
                  for (let j = 0; j < item1.children.length; j++) {
                    const item2 = item1.children[j]
                    arr[i].children.push({
                      id: item2.id,
                      label: item2.text,
                      upId: item2.parentId,
                      layer: '2',
                      children: []
                    })

                    if (item2.children) {
                      for (let k = 0; k < item2.children.length; k++) {
                        const item3 = item2.children[k]
                        arr[i].children[j].children.push({
                          id: item3.id,
                          label: item3.text,
                          upId: item3.parentId,
                          layer: '3',
                          children: []
                        })

                        if (item3.children) {
                          for (let l = 0; l < item3.children.length; l++) {
                            const item4 = item3.children[l]
                            arr[i].children[j].children[k].children.push({
                              id: item4.id,
                              label: item4.text,
                              upId: item4.parentId,
                              layer: '4',
                              children: []
                            })

                            if (item4.children) {
                              for (let m = 0; m < item4.children.length; m++) {
                                const item5 = item4.children[m]
                                arr[i].children[j].children[k].children.push({
                                  id: item5.id,
                                  label: item5.text,
                                  upId: item5.parentId,
                                  layer: '5',
                                  children: []
                                })
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
              items.children = arr
              this.treeData.push(items)
            }
          })
      }
    },

    // 创建公告
    insertNotice () {
      // 获取公告正文
      const afficheText = this.content
        .replace(/&nbsp;/g, '')
        .replace('<p>', '')
        .replace('</p>', '')
        .trim()
      if (afficheText === '') {
        this.$message.error('请输入公告正文')
        this.referDraftStatus = false
        this.submitStatus = false
        return
      }
      if (
        this.radioObject === '3' &&
        (!this.checkAdmin || this.checkAdmin.length === 0)
      ) {
        this.$message.error('请选择一个域')
        this.referDraftStatus = false
        this.submitStatus = false
        return
      }
      if (
        this.radioObject === '1' &&
        (!this.objectData || this.objectData.length === 0)
      ) {
        this.$message.error('请选择对象')
        this.referDraftStatus = false
        this.submitStatus = false
        return
      }
      this.$refs.noticeForm.validate(valid => {
        if (valid) {
          // html转码
          const content = this.$aspUtils.htmlEncodeByRegExp(this.content)
          const param = {
            attachGroupId: this.gatAttachGroupId,
            content: content,
            deptIds: this.deptIdsChange(),
            domains: this.checkAdminChange(this.checkAdmin),
            emailNotifyFlag: this.emailNotifyFlagChange(
              this.noticeForm.noticeWay
            ),
            homeShowType: this.noticeForm.showWay,
            isDraft: this.isDrafts,
            organizationIds: this.organizationIdsChange(),
            receiverType: this.radioObject,
            reviewFlag: this.noticeForm.isExamine,
            smsNotifyFlag: this.smsNotifyFlagChange(this.noticeForm.noticeWay),
            title: this.noticeForm.noticeTitle,
            type: this.noticeForm.noticeType,
            urgency: this.noticeForm.noticeLevel,
            userIds: this.noticeCopyChange(this.noticeCopy),
            homeEffectiveDate: this.noticeForm.noticeValidity,
            replyFlag: this.noticeForm.isReply
          }
          this.$aspHttps
            .asp_Post(
              this.$apiConfig.managerPathPrefix + '/annoucement/insert',
              param
            )
            .then(response => {
              if (this.$reponseStatus(response)) {
                this.$router.push('/system/annoucement/manager/create')
              } else {
                this.submitStatus = false
                this.referDraftStatus = false
              }
            })
        } else {
          this.submitStatus = false
          this.referDraftStatus = false
        }
      })
    },
    // 修改公告
    updateNotice () {
      // 获取公告正文
      const afficheText = this.content
        .replace(/&nbsp;/g, '')
        .replace('<p>', '')
        .replace('</p>', '')
        .trim()
      if (afficheText === '') {
        this.$message.error('请输入公告正文')
        this.referDraftStatus = false
        this.submitStatus = false
        return
      }
      if (
        this.radioObject === '3' &&
        (!this.checkAdmin || this.checkAdmin.length === 0)
      ) {
        this.$message.error('请选择一个域')
        this.referDraftStatus = false
        this.submitStatus = false
        return
      }
      if (
        this.radioObject === '1' &&
        (!this.objectData || this.objectData.length === 0)
      ) {
        this.$message.error('请选择对象')
        this.referDraftStatus = false
        this.submitStatus = false
        return
      }
      this.$refs.noticeForm.validate(valid => {
        if (valid) {
          // html转码
          const content = this.$aspUtils.htmlEncodeByRegExp(this.content)
          const param = {
            attachGroupId: this.gatAttachGroupId,
            content: content,
            deptIds: this.deptIdsChange(),
            domains: this.checkAdminChange(this.checkAdmin),
            emailNotifyFlag: this.emailNotifyFlagChange(
              this.noticeForm.noticeWay
            ),
            id: this.userId,
            homeShowType: this.noticeForm.showWay,
            isDraft: this.isDrafts,
            organizationIds: this.organizationIdsChange(),
            receiverType: this.radioObject,
            reviewFlag: this.noticeForm.isExamine,
            smsNotifyFlag: this.smsNotifyFlagChange(this.noticeForm.noticeWay),
            title: this.noticeForm.noticeTitle,
            type: this.noticeForm.noticeType,
            urgency: this.noticeForm.noticeLevel,
            userIds: this.noticeCopyChange(this.noticeCopy),
            homeEffectiveDate: this.noticeForm.noticeValidity,
            replyFlag: this.noticeForm.isReply
          }
          this.$aspHttps
            .asp_Post(
              this.$apiConfig.managerPathPrefix + '/annoucement/update',
              param
            )
            .then(response => {
              if (this.$reponseStatus(response)) {
                this.$router.push('/system/annoucement/manager/audit')
              } else {
                this.submitStatus = false
                this.referDraftStatus = false
              }
            })
        } else {
          this.submitStatus = false
          this.referDraftStatus = false
        }
      })
    },
    // 获取字典类型
    getTypes () {
      this.checkAdminData = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    // 获取抄送人
    getList () {
      const param = {
        keyword: '',
        order: 'asc',
        organizationId: '',
        organizationName: '',
        page: 1,
        rows: 10,
        sortName: 'wbUser.user_name',
        status: 'NORMAL'
      }
      this.$aspHttps
        .asp_Post(this.$apiConfig.managerPathPrefix + '/user/list', param)
        .then(response => {
          if (this.$reponseStatus(response)) {
            const arr = []
            response.data.forEach(function (item) {
              arr.push({ id: item.id, userName: item.userName })
            })
            this.copyData = arr
            this.get()
          }
        })
    },
    // 更新是获取绑定数据
    get () {
      const param = {
        id: this.userId
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/annoucement/get',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.gatAttachGroupId = response.data.attachGroupId
            // 富文本编辑器发布内容回显 content
            // 防止为Null
            if (response.data.content === null) {
              this.content = ''
            } else {
              this.content = this.$aspUtils.htmlDecodeByRegExp(
                response.data.content
              )
            }
            // this.content = response.data.content
            this.checkAdmin = response.data.domains
            if (response.data.emailNotifyFlag === '1') {
              this.noticeForm.noticeWay.push('1')
            }
            this.userId = response.data.id
            this.noticeForm.showWay = response.data.homeShowType
            this.isDrafts = response.data.isDraft
            // this.radioObject = response.data.receiverType
            this.radioObject =
              response.data.domains && response.data.domains.length > 0
                ? '3'
                : '1'
            this.noticeForm.isExamine = response.data.reviewFlag
            this.noticeForm.isReply = response.data.replyFlag
            if (response.data.smsNotifyFlag === '1') {
              this.noticeForm.noticeWay.push('2')
            }
            this.noticeForm.noticeTitle = response.data.title
            this.noticeForm.noticeType = response.data.type
            this.noticeForm.noticeLevel = response.data.urgency
            this.noticeCopy = this.changeCopyData(response.data.userIds)
            this.noticeForm.noticeValidity = response.data.homeEffectiveDate

            if (response.data.attachGroupId) {
              this.fileLists(response.data.attachGroupId)
            }

            this.getPropsData(
              response.data.deptIds,
              response.data.organizationIds
            )
          }
        })
    },
    // 转换抄送人
    changeCopyData (dataArr) {
      const arr = []
      for (let i = 0; i < this.copyData.length; i++) {
        for (let j = 0; j < dataArr.length; j++) {
          const item = this.copyData[i]
          const item1 = dataArr[j]
          if (item.id === item1) {
            arr.push(item)
          }
        }
      }
      return arr
    },
    // 获取发布对象
    getPropsData (data1, data2) {
      for (let i = 0; i < data2.length; i++) {
        const item = data2[i]
        data1.push(item)
      }

      this.$refs.treeLeft.setCheckedKeys(data1)
    },
    // 查询正式文件列表
    fileLists (attachGroupId) {
      const param = {
        attachGroupId: attachGroupId
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.supportPathPrefix + '/attachment/list',
          param
        )
        .then(response => {
          if (this.$reponseStatus(response)) {
            for (let i = 0; i < response.data.length; i++) {
              const item = response.data[i]
              this.fileList.push({
                name: item.fileName,
                url: item.fileSaveName
              })
            }
          }
        })
    },
    // 生效附件组集合
    formalGroup (attachGroupIdData) {
      const param = {
        attachGroupId: attachGroupIdData
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.supportPathPrefix + '/attachment/formalGroup',
          param
        )
        .then(response => {
          this.$reponseStatus(response)
        })
    }
  }
}
</script>
