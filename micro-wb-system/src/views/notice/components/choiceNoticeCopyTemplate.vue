/** */
<style lang="scss">
.copyList {
  display: inline-block;
  border-radius: 6px;
  line-height: 30px;
  background-color: #f0f2f5;
  margin: 5px;
  padding: 0 5px;
  vertical-align: middle;
}
.copyList.active {
  background-color: #cccccc;
}
</style>
<template>
  <div class="webbas">
    <el-dialog
      :title="dialogParam.title"
      :close-on-click-modal="false"
      :visible.sync="dialogParam.modelVisible"
      custom-class="dialog-content-custom"
      width="30%"
    >
      <div
        v-for="(item, index) in copyData"
        :key="index"
        :class="{ active: item.show }"
        class="copyList"
        @click="choiceList(item, index)"
      >
        {{ item.userName }}
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="small"
          class="hollow-with-icon-btn"
          @click="dialogParam.modelVisible = false"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          class="solid-with-icon-btn"
          @click="confirmSubmit()"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: '',
  components: {},
  props: {
    dialogParam: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      userDomain: '', // 当前登录用户所属的域
      copyData: [],
      noticeCopyData: []
    }
  },
  watch: {
    'dialogParam.modelVisible'(val) {
      if (val) {
        this.getList()
        this.noticeCopyData = []
      }
    }
  },
  created() {},
  methods: {
    // 获取抄送人
    getList() {
      var param = {
        keyword: '',
        order: 'asc',
        organizationId: '',
        organizationName: '',
        page: 1,
        rows: 10,
        sortName: 'wbUser.user_name',
        status: 'NORMAL'
      }
      this.$aspHttps
        .asp_Post(this.$apiConfig.managerPathPrefix + '/user/list', param)
        .then(response => {
          if (this.$reponseStatus(response)) {
            const arr = []
            response.data.forEach(function(item) {
              arr.push({ id: item.id, userName: item.userName, show: false })
            })
            this.copyData = arr
          }
        })
    },
    choiceList(item, index) {
      // console.log(index)
      item.show = !item.show
      if (item.show) {
        this.noticeCopyData.push(item)
      } else {
        for (let i = 0; i < this.noticeCopyData.length; i++) {
          const element = this.noticeCopyData[i]
          if (element.id.indexOf(item.id)) {
            this.noticeCopyData.splice(0, 1)
          }
        }
      }
    },
    confirmSubmit() {
      this.dialogParam.modelVisible = false
      this.$emit('editSubmit', this.noticeCopyData)
    }
  }
}
</script>
