/** */
<style>
#edit-role .el-form-item__label {
  text-align: left;
}
#edit-role .el-form-item__content {
  width: calc(100% - 100px);
}
#edit-role .el-form-item > label {
  position: relative;
}
#edit-role .el-form-item.is-required > .el-form-item__label:before {
  margin-left: 90px;
  top: 2px;
  position: absolute;
}
#edit-role .el-form-item.check-areas > .el-form-item__label:before {
  margin-left: 90px;
  top: 2px;
  position: absolute;
}
#edit-role .el-input input {
  width: 200px;
}
#edit-role .el-textarea {
  width: 200px;
}
#edit-role .el-dialog__body {
  margin-left: 40px;
}
#edit-role .el-dialog__header {
  border-bottom: 1px solid lightgray;
}
#edit-role .el-dialog__footer {
  border-top: 1px solid lightgray;
}
</style>
<template>
  <div id="edit-role" class="webbas">
    <el-dialog
      :title="dialogParam.title"
      :close-on-click-modal="false"
      :visible.sync="dialogParam.roleModelVisible"
      width="30%"
    >
      <el-form
        ref="editNotice"
        :model="editNotice"
        :rules="editNoticeRules"
        label-width="98px"
      >
        <el-form-item label="公告标题:" prop="title">
          <el-input
            v-model="editNotice.title"
            placeholder="不能超过20个字符"
            auto-complete="off"
          ></el-input>
        </el-form-item>

        <el-form-item label="公告类型:" prop="type">
          <el-select v-model="editNotice.type" placeholder="请选择">
            <el-option label="普通公告" value="0"></el-option>
            <el-option label="首页公告" value="1"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="紧急程度:" prop="urgency">
          <el-select v-model="editNotice.urgency" placeholder="请选择">
            <el-option label="普通" value="0"></el-option>
            <el-option label="紧急" value="1"></el-option>
            <el-option label="非常紧急" value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="查阅人数:" prop="readNum">
          <el-input
            v-model="editNotice.readNum"
            placeholder="不能超过20个字符"
            auto-complete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogParam.roleModelVisible = false"
          >取消</el-button
        >
        <el-button type="primary" size="small" @click="confirmSubmit()"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: '',
  components: {},
  props: {
    dialogParam: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      userDomain: '', // 当前登录用户所属的域
      editNotice: {},
      editNoticeRules: {
        title: [
          { required: true, message: '该字段不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个汉字', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' }
        ],
        type: [
          { required: true, message: '所属域不能为空', trigger: 'blur' },
          { max: 100, message: '输入不能超过100个汉字', trigger: 'blur' }
        ],
        urgency: [
          { required: true, message: '所属域不能为空', trigger: 'blur' },
          { max: 100, message: '输入不能超过100个汉字', trigger: 'blur' }
        ],
        readNum: [
          { required: true, message: '该字段不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个汉字', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkSpecial, trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    'dialogParam.roleModelVisible'(val) {
      if (val) {
        this.editNotice = {
          id: this.dialogParam.editNotice.id,
          title: this.dialogParam.editNotice.title,
          type: this.dialogParam.editNotice.type,
          urgency: this.dialogParam.editNotice.urgency,
          readNum: this.dialogParam.editNotice.readNum
        }
      }
    }
  },
  created() {},
  methods: {
    confirmSubmit() {
      this.$confirm('确认要修改该公告？', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // 请求体
            this.$aspHttps
              .asp_Post(
                this.$apiConfig.managerPathPrefix + '/annoucement/pageList',
                this.editNotice
              )
              .then(response => {
                if (this.$reponseStatus(response)) {
                  this.$emit('editSubmit', this.editNotice)
                  this.dialogParam.roleModelVisible = false
                  done() // 回调
                }
              })
          }
          if (action === 'cancel') {
            this.$message({ type: 'warning', message: '请求中•••' })
            done()
          }
        }
      })
        .then(() => {
          this.$message({ type: 'success', message: '修改成功!' })
        })
        .catch(() => {
          this.$message({ type: 'info', message: '已取消修改' })
        })
    }
  }
}
</script>
