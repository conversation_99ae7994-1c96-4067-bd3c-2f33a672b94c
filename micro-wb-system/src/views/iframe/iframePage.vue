/**
* appIframePage
* 为main界面下的Iframe的src加载外面界面提供main框架界面支持
* const param = {
    pageType: 'framePageBack',
    pageFrom: this.$route.query.dataPageFrom,
    param: {
        params: {},
        query: {},
        其他必要参数
    }
  }
* 发送格式:window.parent.postMessage(param, window.parent.location.origin)
* 通过Iframe进行跨平台交互信息
*/
<template>
  <div :style="contentHComputed">
    <iframe
      v-if="iFrameUrl"
      :src="iFrameUrl"
      frameborder="0"
      width="100%"
      height="100%"
    ></iframe>
  </div>
</template>

<script>
export default {
  name: 'FramePage',
  data: function() {
    return {
      iFrameUrl: ''
    }
  },
  watch: {
    '$route.query': {
      handler (val, oldVal) {
        if (val !== oldVal) {
          this.iFrameUrl = this.$route.query.iframe
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    // 计算区域高
    contentHComputed () {
      return { height: this.$contentH }
    }
  },
  mounted() {
    // 提供项目个性化--提供Iframe信息传输机制：[该方法慎用，需要提出给架构层面同意后才可开发]
    // --主要做iframe嵌套页面与当前系统应用间建立访问交互机制
    window.addEventListener('message', this.getMsgFromIframe, false)
  },
  created: function() {},
  methods: {
    /**
     * PCC项目个性化：接收来自Iframe框的数据传输，messageSend
     * 参数结构：e: { data: String/ Objec }
     * String: 主要做message信息通信；
     * Object: 主要就数据信息交互；
     * data: {
     *  pageType：页面交互方式，双方协议；
     *  param：{} 双方协议传递参数
     * }
     * 其中：
     * param: {
     *  params: 路由params参数；（加载页面需要时）
     *  query：路由地址参数；（加载页面需要时）
     *  其他必要参数；
     * }
     */
    getMsgFromIframe (e) {
      // console.log('接收到发送过来的信息e:', e)
      if (e.data) {
        // console.log('接收到发送过来的信息e.data:', e.data)
        if (JSON.stringify(e.data).indexOf('{') === -1) {
          // 通过Iframe控制框架进行菜单跳转的，可传递menuId进行控制对应菜单跳转
          this.$main_tools.menuTreeUtil.openMenuPageByIdOrUrl(e.data)
        } else {
          // console.log('main=====进入main页面路由跳转模式', JSON.stringify(e.data))
          // 获取子应用的bootStarp页面传递过来的数据，并做转换处理
          const data = JSON.parse(JSON.stringify(e.data))
          // 接收Iframe子应用项目发送登出消息，进行登出操作
          if (data && data.pageType && data.pageType === 'loginOut') {
            this.$main_tools.util.relogin()
          }
          // 接收Iframe子应用项目发送路由返回操作
          if (data && data.pageType && data.pageType === 'framePageBack') {
            this.$router.back()
          }
        }
      }
    }
  }
}
</script>
