<template>
  <div>
    <span class="date">统计时间：{{ params.startDate }} - {{ params.endDate }}</span>
    <div class="shopNumContent">
      <div>
        <p><i class="el-icon-question" title="PV：用户访问店铺的总次数"></i> 线上店访问总次数：<span>{{ indexLists.pvShow ? indexLists.pvShow : '0' }}</span> 次</p>
      </div>
      <div>
        <p><i class="el-icon-question" title="UV：访问店铺的总人数"></i> 线上店访问总人数：<span>{{ indexLists.uvShow ? indexLists.uvShow : '0' }}</span> 人</p>
      </div>
      <div>
        <p><i class="el-icon-question" title="店铺完成的订单总数"></i> 订单成交量：<span>{{ indexLists.orderNumShow ? indexLists.orderNumShow : '0' }}</span> 笔</p>
      </div>
      <div>
        <p><i class="el-icon-question" title="店铺完成的订单总金额"></i> 订单销售额：<span>{{ indexLists.orderMoneyShow ? indexLists.orderMoneyShow : '0' }}</span> 元</p>
      </div>
  </div>
  </div>
</template>
<script>
export default {
  props: ['indexList', 'params'],
  data () {
    return {}
  },
  computed: {
    // 防止数据没加载完成报错
    indexLists() {
      return this.indexList || {}
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-icon-question:before{
  color: #999999;
}
.date{
  font-size: 13px;
  color: #C0C2C6;
}
.shopNumContent{
  height: 80px;
  margin-top: 10px;
  border: 1px solid #E7E9EE;
  display: flex;
  align-items: center;
  justify-content: center;
  div{
    flex: 1;
    text-align: center;
    font-size: 17px;
    font-weight: 600;
    border-right: 1px solid #D9DADD;
    color: #333333;
    span{
      color: red;
    }
  }
  div:last-child{
    border: none;
  }
}
</style>
