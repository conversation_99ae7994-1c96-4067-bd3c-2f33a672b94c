<!--
 * @User: JOJO
 * @FilePath: \netshoposmweb\micro-wb-system\src\views\management\myaccount\index.vue
-->
<template>
  <div class="file-upload-status commonStyleContainer">
    <div class="webbas">
      <div class="list-page-content-css">
        <!--搜索部分 -->
        <div class="query-area-content-css">
          <div class="searchBlock" style="padding: 10px">
            <el-form ref="form" :model="form">
              <el-row>
                <el-col :span="2">
                  <el-select
                    v-model="table.searchForm.provinceCode"
                    size="mini"
                    placeholder="请选择"
                    @change = "probinceChanged(table.searchForm.provinceCode)"
                    @blur = "editCity"
                  >
                    <el-option
                      v-for="item in provinceList"
                      :key="item.id"
                      :value="item.provinceCode"
                      :label="item.provinceName"
                    >
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select
                    v-model="table.searchForm.cityCode"
                    size="mini"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in cityList"
                      :key="item.id"
                      :value="item.cityCode"
                      :label="item.cityName"
                    >
                    </el-option>
                  </el-select>
                </el-col>

                <el-col :span="5">
                  <el-input clearable placeholder="实体/线上渠道编码" v-model="table.searchForm.fuzzyConditions"></el-input>
                </el-col>

                <el-col :span="5">
                  <el-input clearable placeholder="店铺ID" v-model="table.searchForm.shopId"></el-input>
                </el-col>
                <!-- 状态 -->
                <el-col :span="2">
                  <el-select
                      ref="multiSelect"
                      v-model="table.searchForm.statusCode"
                      size="mini"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in form.statusOptions"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      >
                      </el-option>
                    </el-select>
                </el-col>
                <el-col :span="6">
                  <div style="display: flex">
                    <div style="width: 90px" v-if="shop_online_but_list">
                      <el-form-item label="">
                        <div style="margin-top: -1px;">
                          <asp-btn-solid
                          name="查询"
                          :disabled="aspbtndisabled"
                          icon="el-icon-search"
                          @click="getShopOnlineList"
                          ></asp-btn-solid>
                        </div>
                      </el-form-item>
                    </div>
                    <!-- <div style="width: 120px">
                      <el-form-item label="">
                        <asp-btn-hollow
                          name="导出明细"
                          icon="el-icon-download"
                          @click="exportSubsidiary"
                        ></asp-btn-hollow>
                      </el-form-item>
                    </div> -->
                  </div>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>

        <!-- 集团表格 -->
        <asp-table
          class="savecompany"
          ref="table"
          :url="table.url"
          :param="table.searchForm"
          :prefix="table.prefix"
          @tableSearchSuccess="tableSearchSuccess"
          :isMountedTableData="true"
          type=""
        >
        <template slot="header">
          <div class="tableHeader">
            <div class="btnStyle" v-if="shop_online_but_edib">
              <el-upload
                ref="upload"
                action="#"
                accept=".xls,.xlsx"
                :show-file-list="false"
                :http-request="handleModifyFileUpload"
                :auto-upload="true"
                >
                <span title="批量修改">批量修改</span>
              </el-upload>
              <i class="hx"></i>
              <span @click="modifyTemplate">
                <el-icon title="下载模板" name="download"></el-icon>
              </span>
            </div>
            <div class="btnStyle" v-if="shop_online_but_addb">
              <el-upload
                ref="upload"
                action="#"
                accept=".xls,.xlsx"
                :show-file-list="false"
                :http-request="handleAddFileUpload"
                :auto-upload="true"
                >
                <span title="批量添加">批量添加</span>
              </el-upload>
              <i class="hx"></i>
              <span @click="addTemplate">
                <el-icon title="下载模板" name="download"></el-icon>
              </span>
            </div>
            <!-- <div class="btnStyle">
              <span>添加</span>
            </div> -->
            <asp-btn-solid
              name="添加"
              @click="onlineAdd('add')"
              v-if="shop_online_but_add"
            ></asp-btn-solid>
          </div>
        </template>
          <asp-table-column
            prop="busiRegProvinceName"
            min-width="100"
            align="center"
            label="归属省"
            show-overflow-tooltip
          >
          </asp-table-column>
          <asp-table-column
            prop="busiRegCityName"
            min-width="100"
            align="center"
            label="归属市"
            show-overflow-tooltip
          >
          </asp-table-column>
          <asp-table-column
            prop="storeNumber"
            min-width="100"
            align="center"
            label="店铺ID"
            show-overflow-tooltip
          >
          </asp-table-column>
          <asp-table-column
            prop="onlineStoreName"
            min-width="100"
            align="center"
            label="店铺名称"
            show-overflow-tooltip
          >
          </asp-table-column>
          <asp-table-column
            prop="offlineChannelCode"
            min-width="120"
            align="center"
            show-overflow-tooltip
            label="实体渠道编码"
          >
          </asp-table-column>
          <asp-table-column
            prop="onlineChannelCode"
            min-width="120"
            align="center"
            label="线上渠道编码"
            show-overflow-tooltip
          >
          </asp-table-column>
          <asp-table-column
            prop="managerName"
            min-width="100"
            align="center"
            label="店长"
            show-overflow-tooltip
          >
          </asp-table-column>
          <asp-table-column
            prop="managerPhone"
            min-width="100"
            align="center"
            label="店长手机号"
            show-overflow-tooltip
          >
          </asp-table-column>
          <asp-table-column
            prop="createTime"
            min-width="150"
            label="入驻时间"
            align="center"
            show-overflow-tooltip
          >
          </asp-table-column>
          <asp-table-column
            prop="updateTime"
            min-width="150"
            align="center"
            label="店铺信息更新时间"
            show-overflow-tooltip
          >
          </asp-table-column>
          <asp-table-column
            prop="channelStatusCode"
            min-width="100"
            align="center"
            label="状态"
            show-overflow-tooltip
          >
            <template slot-scope="{ scope }">
              <span>{{ resetChannelStatusCode(scope.row.channelStatusCode) }}</span>
            </template>
          </asp-table-column>
          <asp-table-column
            min-width="100"
            align="center"
            label="查看"
          >
            <template slot-scope="{ scope }">
              <asp-btn-text
                name="详情"
                @click="detailConfig(scope.row)"
              >
              </asp-btn-text>
              &nbsp;
              <asp-btn-text
                v-if="shop_online_but_edit && scope.row.systemId.slice(-1) === '1'"
                name="编辑"
                @click="handleDetail(scope.row, 'edit')"
              >
              </asp-btn-text>
            </template>
          </asp-table-column>
        </asp-table>
      </div>
      <!-- 查看详情 -->
      <onlineDetails :dialog-param="detailParam"></onlineDetails>
    </div>


    <!-- 新增 -->
  <asp-dialog
    v-model="dialogParam.onlineVisible"
    :visible.sync="dialogParam.onlineVisible"
    :title="dialogParam.title"
    width="70%">
    <template style="text-align: center;">
      <p class="explanatory">*注：仅支持添加自建店，一级云店暂不支持手动添加</p>
      <el-form :model="addForm" ref="ruleForm" :rules="rules" class="el-collapse-120">
        <el-form-item label-width="200px" prop="busiRegProvinceName" label="省份：">
          <span>{{ addForm.busiRegProvinceName }}</span>
        </el-form-item>

        <el-form-item label-width="200px" prop="busiRegCityCode" label="地市：">
          <el-select size="small" @change="changeCity" v-model="addForm.busiRegCityName" placeholder="请选择">
            <el-option v-for="item in cityListAdd" v-bind:key="item.cityCode" :label="item.cityName" :value="item.cityCode + ',' + item.cityName"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label-width="200px" label="区县：">
          <el-input maxlength="32" style="width: 70%;" size="small" placeholder="区县名称" v-model="addForm.busiRegCountyName"></el-input>
        </el-form-item>

        <el-form-item label-width="200px" prop="offlineChannelCode" label="19位渠道编码：">
          <el-input maxlength="19" :disabled="isStatus === 'edit'" style="width: 70%;" size="small" placeholder="实体、直销、随销、泛渠道19位渠道标识编码" v-model="addForm.offlineChannelCode"></el-input>
        </el-form-item>
        <!-- prop="onlineChannelCode" -->
        <el-form-item v-if="isStatus === 'edit'" label-width="200px" label="线上线下结合渠道编码：">
          <el-input :disabled="isStatus === 'edit'" style="width: 70%;" size="small" v-model="addForm.onlineChannelCode"></el-input>
        </el-form-item>

        <el-form-item label-width="200px" prop="storeNumber" label="店铺ID：">
          <el-input maxlength="20" :disabled="isStatus === 'edit'" style="width: 70%;" size="small" placeholder="省侧自行为店铺分配序号，后续不可变更" v-model="addForm.storeNumber"></el-input>
        </el-form-item>

        <el-form-item label-width="200px" prop="onlineStoreName" label="线上店铺名称：">
          <el-input maxlength="512" style="width: 70%;" size="small" placeholder="可填写中英文、数字、下划线" v-model="addForm.onlineStoreName"></el-input>
        </el-form-item>

        <el-form-item label-width="200px" label="门店客服电话：">
          <el-input maxlength="16" style="width: 70%;" size="small" v-model="addForm.storeContactNumber"></el-input>
        </el-form-item>

        <el-form-item readonly label-width="200px" label="员工：" prop="roleNum">
          <el-input-number :disabled="isBlur" size='mini' @focus="xx" @blur="addYgBlur"  @change="handleAddOrReduce" controls-position="right" v-model="addForm.roleNum" :min="1"></el-input-number>
          <span> &nbsp;&nbsp;&nbsp;人</span>
        </el-form-item>

        <div class="employees" v-for="(item,index) in addForm.employees" :key="index">
          <el-form-item label-width="200px" label="员工身份" :rules="rules.identity" :prop="'employees.' + index + '.identity'">
            <el-radio-group @change="changeRole($event,index)" v-model="item.identity">
              <el-radio :label='0'>店长</el-radio>
              <el-radio :label='1'>店员</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label-width="200px" label="姓名：" :rules="rules.employeesName" :prop="'employees.' + index + '.name'">
            <el-input maxlength="32" size="small" style="width: 50%;" v-model="item.name"></el-input>
          </el-form-item>
          <el-form-item label-width="200px" label="手机号：" :rules="rules.contactNumber" :prop="'employees.' + index + '.contactNumber'">
            <el-input maxlength="32" size="small" style="width: 50%;" v-model="item.contactNumber"></el-input>
          </el-form-item>
          <el-form-item label-width="200px" label="员工工号：">
            <el-input maxlength="256" size="small" style="width: 50%;"  placeholder="Boss系统工号" v-model="item.employeeNumber"></el-input>
          </el-form-item>
          <span @click="deleteRole(index)">删除</span>
        </div>

        <el-form-item label-width="200px" label="线上店铺入口或者网址：" prop="onlineStoreEntranceUrl">
          <el-input maxlength="512" style="width: 70%;" size="small" v-model="addForm.onlineStoreEntranceUrl"></el-input>
        </el-form-item>

        <el-form-item label-width="205px" label="渠道门店投放网络交易平台：">
          <el-checkbox-group v-model="addForm.platformType" @change="changeOnlinePay">
            <el-checkbox label="0" name="type">企业自建</el-checkbox>
            <el-checkbox label="1" name="type">淘宝</el-checkbox>
            <el-checkbox label="2" name="type">天猫</el-checkbox>
            <el-checkbox label="3" name="type">京东</el-checkbox>
            <el-checkbox label="4" name="type">拼多多</el-checkbox>
            <el-checkbox label="5" name="type">抖音</el-checkbox>
            <el-checkbox label="6" name="type">快手</el-checkbox>
            <el-checkbox label="7" name="type">微信</el-checkbox>
            <el-checkbox label="8" name="type">支付宝</el-checkbox>
            <el-checkbox label="9" name="type">淘特</el-checkbox>
            <el-checkbox label="10" name="type">其他</el-checkbox>
            <el-checkbox label="11" name="type">今日头条</el-checkbox>
            <el-checkbox label="12" name="type">百度营销 <i class="el-icon-question" title="信息流"></i></el-checkbox>
            <el-checkbox label="13" name="type">广点通 <i class="el-icon-question" title="信息流"></i></el-checkbox>
            <el-checkbox label="14" name="type">巨量引擎 <i class="el-icon-question" title="信息流"></i></el-checkbox>
            <el-checkbox label="15" name="type">磁力引擎 <i class="el-icon-question" title="信息流"></i></el-checkbox>
            <el-checkbox label="16" name="type">阿里超级汇川 <i class="el-icon-question" title="信息流"></i></el-checkbox>
            <el-checkbox label="17" name="type">其他信息流</el-checkbox>
            <el-checkbox label="18" name="type">垂类</el-checkbox>
            <el-checkbox label="19" name="type">小红书</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label-width="205px" label="电子渠道门店类型：" prop="electronicChannelType">
          <el-checkbox-group v-model="addForm.electronicChannelType" @change="changeOnlinePay">
            <el-checkbox label="1" name="type">中国移动APP</el-checkbox>
            <el-checkbox label="2" name="type">公众号（微信）</el-checkbox>
            <el-checkbox label="3" name="type">小程序</el-checkbox>
            <el-checkbox label="4" name="type">其他</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label-width="205px" label="线上店铺的营业范围：" prop="businessScope">
          <span>【查询服务】：</span>
          <el-checkbox-group style="margin-left: 30px;" v-model="addForm.businessScope" @change="changeOnlinePay">
            <el-checkbox label="0101" name="type">话费查询</el-checkbox>
            <el-checkbox label="0102" name="type">积分查询</el-checkbox>
            <el-checkbox label="0103" name="type">流量查询</el-checkbox>
            <el-checkbox label="0104" name="type">其他查询</el-checkbox>
          </el-checkbox-group>
          <span>【充值服务】：</span>
          <el-checkbox-group style="margin-left: 30px;" v-model="addForm.businessScope" @change="changeOnlinePay">
            <el-checkbox label="0201" name="type">充值缴费</el-checkbox>
            <el-checkbox label="0202" name="type">流量充值</el-checkbox>
          </el-checkbox-group>
          <span>【服务办理】：</span>
          <el-checkbox-group style="margin-left: 30px;" v-model="addForm.businessScope" @change="changeOnlinePay">
            <el-checkbox label="0301" name="type">入网 <i class="el-icon-question" title="16周岁以上用户凭二代入网"></i></el-checkbox>
            <el-checkbox label="0302" name="type">过户 <i class="el-icon-question" title="普通号码及吉祥号码"></i></el-checkbox>
            <el-checkbox label="0303" name="type">资料完善 <i class="el-icon-question" title="已登记及未登记状态的原机主信息错误或资料缺失的本地及省内异地普通号码及吉祥号码"></i></el-checkbox>
            <el-checkbox label="0304" name="type">改资料 <i class="el-icon-question" title="修改除姓名、证件号码、证件类型以外的客户信息"></i></el-checkbox>
            <el-checkbox label="0305" name="type">密码重置 <i class="el-icon-question" title="二代证及非二代证入网用户"></i></el-checkbox>
            <el-checkbox label="0306" name="type">补卡 <i class="el-icon-question" title="本地本人普通及吉祥号码，代办本地普通及吉祥号码，省内异地二代证和非二代证入网普通及吉祥号码，16周岁以下用户"></i></el-checkbox>
            <el-checkbox label="0307" name="type">销户 <i class="el-icon-question" title="本人本地普通及吉祥号码，代办本地普通及吉祥号码，省内异地二代证和非二代证入网普通及吉祥号码，16周岁以下用户"></i></el-checkbox>
            <el-checkbox label="0308" name="type">销户重开 <i class="el-icon-question" title="本人本地普通及吉祥号码，代办本地普通及吉祥号码，16周岁以下用户"></i></el-checkbox>
            <el-checkbox label="0309" name="type">强制复机 <i class="el-icon-question" title="本地号码"></i></el-checkbox>
            <el-checkbox label="0310" name="type">销户转账</el-checkbox>
            <el-checkbox label="0311" name="type">销户取现</el-checkbox>
            <el-checkbox label="0312" name="type">黑名单出库</el-checkbox>
            <el-checkbox label="0313" name="type">退预交款</el-checkbox>
            <el-checkbox label="0314" name="type">集团V网关闭</el-checkbox>
            <el-checkbox label="0315" name="type">8元套餐</el-checkbox>
            <el-checkbox label="0316" name="type">小额充值卡销售</el-checkbox>
            <el-checkbox label="0317" name="type">托收取消</el-checkbox>
            <el-checkbox label="0318" name="type">跨省换卡、跨区密码重置、跨省销户</el-checkbox>
            <el-checkbox label="0319" name="type">打印服务</el-checkbox>
            <el-checkbox label="0320" name="type">解合约</el-checkbox>
            <el-checkbox label="0321" name="type">降档</el-checkbox>
            <el-checkbox label="0322" name="type">携号转网（转出）</el-checkbox>
            <el-checkbox label="0323" name="type">携号转网（转入）</el-checkbox>
            <el-checkbox label="0399" name="type">服务办理其他</el-checkbox>
          </el-checkbox-group>
          <span>【业务办理】：</span>
          <el-checkbox-group style="margin-left: 30px;" v-model="addForm.businessScope" @change="changeOnlinePay">
            <el-checkbox label="0401" name="type">号卡</el-checkbox>
            <el-checkbox label="0402" name="type">固话</el-checkbox>
            <el-checkbox label="0403" name="type">宽带</el-checkbox>
            <el-checkbox label="0404" name="type">5G套包</el-checkbox>
            <el-checkbox label="0405" name="type">权益</el-checkbox>
            <el-checkbox label="0406" name="type">物联网卡</el-checkbox>
            <el-checkbox label="0499" name="type">业务办理其他</el-checkbox>
          </el-checkbox-group>
          <span>【实物销售】：</span>
          <el-checkbox-group style="margin-left: 30px;" v-model="addForm.businessScope" @change="changeOnlinePay">
            <el-checkbox label="0501" name="type">终端销售(不含接机维修和以旧换新)</el-checkbox>
            <el-checkbox label="0502" name="type">终端销售(含以旧换新)</el-checkbox>
            <el-checkbox label="0503" name="type">终端销售(含接机维修)</el-checkbox>
            <el-checkbox label="0504" name="type">终端销售(含接机维修和以旧换新)</el-checkbox>
            <el-checkbox label="0505" name="type">儿童手表</el-checkbox>
            <el-checkbox label="0506" name="type">智能家居</el-checkbox>
            <el-checkbox label="0507" name="type">智能网关</el-checkbox>
            <el-checkbox label="0508" name="type">其他终端（PC、平板、学习机）</el-checkbox>
          </el-checkbox-group>
          <span>【宣传类】：</span>
          <el-checkbox-group style="margin-left: 30px;" v-model="addForm.businessScope" @change="changeOnlinePay">
            <el-checkbox label="0601" name="type">产品宣传</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item style="margin-bottom: 0;" label-width="200px" label="店铺状态：" prop="channelStatusCode">
          <el-radio-group v-model="addForm.channelStatusCode" @change="changeChannelStatus">
            <el-radio :disabled="addForm.channelStatusCode === '4'" label="1">正常</el-radio>
            <el-radio :disabled="addForm.channelStatusCode === '4'" label="2">冻结</el-radio>
            <el-radio :disabled="addForm.channelStatusCode === '4'" label="3">终止</el-radio>
            <el-radio :disabled="isStatus === 'add'" label="4">清退</el-radio>
          </el-radio-group>
        </el-form-item>

        <div style="margin-top: 10px;" v-if="addForm.channelStatusCode == 2 || addForm.channelStatusCode == 3">
          <el-form-item label-width="260px" :label="addForm.channelStatusCode == 2 ? '冻结原因' : '终止原因'" prop="exitReason">
            <el-radio-group v-model="addForm.exitReason" >
              <el-radio style="margin-top: 13px;" label="1">授权到期</el-radio><br/>
              <el-radio style="margin-top: 13px;" label="2">因渠道违规等问题企业与渠道解除合作</el-radio><br/>
              <div style="display: flex;align-items: flex-end;">
                <el-radio style="margin-top: 15px;margin-right: 0;" label="3">其他，请填写原因：</el-radio>
                <div style="margin-top: 10px;">
                  <el-form-item v-if="addForm.exitReason === '3'" prop="exitReasonDesc">
                    <el-input maxlength="128" style="padding-top: 10px;" size="mini" v-model="addForm.exitReasonDesc"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-radio-group>
          </el-form-item>
        </div>

        <el-form-item style="margin-top: 20px;" label-width="200px" label="店铺经度：">
          <el-input maxlength="32" style="width: 70%;" size="small" placeholder="经度，例：165.2446934" v-model="addForm.longitude"></el-input>
        </el-form-item>

        <el-form-item label-width="200px" label="店铺纬度：">
          <el-input maxlength="32" style="width: 70%;" size="small" placeholder="纬度，例：44.1794393" v-model="addForm.latitude"></el-input>
        </el-form-item>

        <el-form-item label-width="200px" label="店铺介绍：">
          <el-input maxlength="256" style="width: 70%;" size="small" placeholder="店铺介绍，用于企微渠道线上开店" v-model="addForm.shopDesc"></el-input>
        </el-form-item>

        <el-form-item label-width="200px" label="店铺头像地址：">
          <el-input maxlength="256" style="width: 70%;" size="small" placeholder="店铺头像链接，用于企微渠道线上开店" v-model="addForm.shopAvatar"></el-input>
        </el-form-item>
      </el-form>
    </template>
    <template slot="footer-center">
      <div class="webbas footerCon">
      <asp-btn-hollow
        icon="el-icon-close"
        name="取消"
        @click="closeDialog"
      >
      </asp-btn-hollow>
      <asp-btn-solid
        v-if="isStatus === 'add' && isStatus !== 'detail'"
        icon="el-icon-check"
        name="保存"
        @click="saveAdd('ruleForm')"
      >
      </asp-btn-solid>
      <asp-btn-solid
        v-if="isStatus === 'edit' && isStatus !== 'detail'"
        icon="el-icon-check"
        name="修改"
        @click="updateOnline('ruleForm')"
      >
      </asp-btn-solid>
      </div>
    </template>
  </asp-dialog>
  </div>
</template>

<script>
import onlineDetails from './details'
import axios from 'axios'
import { ab2Json, ExportFile } from '../../../../utils/index.js'
export default {
  components: {
    onlineDetails
  },
  data() {
    return {
      shop_online_but_list: JSON.parse(sessionStorage.getItem('buttonInfo')).shop_online_but_list, // 查询按钮
      shop_online_but_add: JSON.parse(sessionStorage.getItem('buttonInfo')).shop_online_but_add, // 新增按钮
      shop_online_but_edit: JSON.parse(sessionStorage.getItem('buttonInfo')).shop_online_but_edit, // 编辑按钮
      shop_online_but_addb: JSON.parse(sessionStorage.getItem('buttonInfo')).shop_online_but_addb, // 批量添加按钮
      shop_online_but_edib: JSON.parse(sessionStorage.getItem('buttonInfo')).shop_online_but_edib, // 批量修改按钮
      NoAdminGroup: true, // 是否为集团账号 默认不是集团
      isBlur: false,
      isStatus: '', // 按钮文字
      uploadUrl: '',
      uploadData: null, // 附件上传附带参数
      form: {
        /* 文件状态下拉框 */
        statusOptions: [
          { label: '请选择', value: '' },
          { label: '正常', value: 1 },
          { label: '冻结', value: 2 },
          { label: '终止', value: 3 },
          { label: '清退', value: 4 }
        ]
      },
      addForm: {
        systemId: '',
        reqNo: '',
        offlineChannelCode: '',
        storeNumber: '',
        onlineStoreName: '',
        storeContactNumber: '',
        onlineChannelCode: '',
        onlineStoreEntranceUrl: '',
        platformType: [],
        electronicChannelType: [],
        businessScope: [],
        channelStatusCode: '',
        longitude: '',
        latitude: '',
        shopDesc: '',
        shopAvatar: '',
        exitReason: '', // 冻结原因
        exitReasonDesc: '', // 冻结原因input
        busiRegProvinceCode: '', // 业务区域省份编码
        busiRegProvinceName: '', // 业务区域省份名称
        busiRegCityCode: '', // 业务区域地市编码
        busiRegCityName: '', // 业务区域地市名称
        busiRegCountyCode: '', // 业务区域区县编码
        busiRegCountyName: '', // 业务区域区县名称
        roleNum: '1',
        employees: [
          {
            identity: '', // 员工身份
            name: '',
            contactNumber: '',
            employeeNumber: ''
          }
        ]
      },
      cityListAdd: [],
      ruleForm: {
        offlineChannelCode: '',
        busiRegCityCode: '',
        prefectureCity: '',
        storeNumber: '',
        onlineStoreName: '',
        roleNum: '',
        identity: '',
        employeesName: '',
        contactNumber: '',
        onlineStoreEntranceUrl: '',
        electronicChannelType: '',
        businessScope: '',
        channelStatusCode: '',
        exitReason: '',
        exitReasonDesc: ''
      },
      rules: {
        offlineChannelCode: [
          { required: true, message: '请输入19位渠道编码', trigger: 'blur' },
          { min: 19, max: 19, message: '请确定渠道编码是否为19位', trigger: 'blur' }
        ],
        // onlineChannelCode: [
        //   { required: true, message: '请输入线上线下渠道编码', trigger: 'blur' }
        // ],
        busiRegCityCode: [
          { required: true, message: '请选择地市', trigger: 'change' }
        ],
        prefectureCity: [
          { required: true, message: '请选择地市', trigger: 'change' }
        ],
        storeNumber: [
          { required: true, message: '请输入店铺ID', trigger: 'blur' }
        ],
        onlineStoreName: [
          { required: true, message: '请输入线上店铺名称', trigger: 'blur' }
        ],
        roleNum: [
          { required: true, message: '请添加员工', trigger: 'change' }
        ],
        identity: [
          { required: true, message: '请选择员工身份', trigger: 'change' }
        ],
        employeesName: [
          { required: true, message: '请输入员工姓名', trigger: 'blur' }
        ],
        contactNumber: [
          { required: true, message: '请输入员工电话号码', trigger: 'blur' }
        ],
        onlineStoreEntranceUrl: [
          { required: true, message: '请输入线上店铺入口或网址', trigger: 'blur' }
        ],
        // platformType: [
        //   { type: 'array', required: true, message: '请至少选择一个交易平台', trigger: 'change' }
        // ],
        electronicChannelType: [
          { type: 'array', required: true, message: '请至少选择一个门店类型', trigger: 'change' }
        ],
        businessScope: [
          { type: 'array', required: true, message: '请至少选择一个营业范围', trigger: 'change' }
        ],
        channelStatusCode: [
          { required: true, message: '请选择店铺状态', trigger: 'change' }
        ],
        exitReason: [
          { required: true, message: '请选择冻结/终止原因', trigger: 'change' }
        ],
        exitReasonDesc: [
          { required: true, message: '请填写店铺状态变更原因', trigger: 'change' }
        ]
      },
      provinceList: [],
      screenPro: [],
      cityList: [],
      /* 表格配置 */
      table: {
        prefix: this.$apiConfig.level1cloudstorePathPreFix,
        url: '/proxyData/api/shop/list',
        searchForm: {
          isPage: 1,
          shopId: '',
          provinceCode: '',
          cityCode: '',
          fuzzyConditions: '',
          statusCode: ''
        }
      },

      // dialog数据
      dialogParam: {
        onlineVisible: false
        // title: '新增'
      },
      detailParam: { modelVisible: false },

      aspbtndisabled: false,

      /* 已上传 */
      uploadNumber: 0,

      /* 未上传 */
      nouploadNumber: 0,

      /* 表格数据 */
      tableData: [],

      /* 表格loading */
      loading: true,

      /* 集团变量 */
      departmentId: '',

      /* 需要传到导出接口的具体参数 */
      exportParams: {
        fileStatus: '',
        pickerValue: ''
      },

      /* 已上传按钮颜色 */
      commonColor: '',
      groupList: []
    }
  },
  created() {
    // 判断账号 地区/集团
    this.isGroup()
  },
  methods: {
    resetChannelStatusCode(val) {
      if (val === '1') {
        return '正常'
      } else if (val === '2') {
        return '冻结'
      } else if (val === '3') {
        return '终止'
      } else if (val === '4') {
        return '清退'
      }
    },
    xx() {
      this.isBlur = true
    },
    addYgBlur() {
      this.isBlur = false
    },
    // 添加 --- 角色选择
    changeRole(role, i) {
      if (!role) {
        this.addForm.employees.forEach((item, index) => {
          if (index !== i) {
            item.identity = 1
          }
        })
      }
    },
    // 删除角色
    deleteRole(index) {
      if (this.addForm.employees.length > 1) {
        this.addForm.employees.splice(index, 1)
        this.addForm.roleNum = this.addForm.employees.length
      } else {
        this.$message.error('删除失败，店铺人员至少有一位')
      }
    },
    // 传递参数 市区域编码 市名称
    changeCity(val) {
      this.addForm.busiRegCityCode = val.split(',')[0]
      this.addForm.busiRegCityName = val.split(',')[1]
    },
    /* 是否是集团账户 */
    isGroup() {
      return new Promise((resolve) => {
        this.$aspHttps
          .asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/getCurrent')
          .then((response) => {
            if (this.$reponseStatus(response)) {
              this.$nextTick(() => {
                this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/shop/common/regionInfo', {}).then(res => {
                  this.provinceList = res.data
                  // 处理省市select
                  this.groupList = response.data
                  const newProvinceList = this.provinceList.filter((item) => {
                    return item.provinceCode === this.groupList.divisions[0].substring(0, 3)
                  })
                  if (newProvinceList[0].provinceCode === '000') {
                    // 集团用户 隐藏按钮
                    this.NoAdminGroup = false
                    // 初始化页面 省市默认值展示
                    this.$nextTick(() => {
                      this.cityList = newProvinceList[0].cities
                      this.cityListAdd = newProvinceList[0].cities.splice(1, 1)
                      this.table.searchForm.provinceCode = '000'
                      this.table.searchForm.cityCode = '000'
                    })
                  } else {
                    this.provinceList = newProvinceList
                    this.table.searchForm.provinceCode = newProvinceList[0].provinceCode
                    // 省份-地市
                    if (this.provinceList[0].cities.length === 1) {
                      this.table.searchForm.cityCode = this.provinceList[0].cities[0].cityCode
                    } else {
                      this.table.searchForm.cityCode = '000'
                    }
                    console.log(this.table.searchForm, 'this.table.searchForm')
                    console.log(this.provinceList, 'this.table.searchForm')
                    this.cityList = newProvinceList[0].cities
                    this.cityListAdd = newProvinceList[0].cities.filter((item) => {
                      return item.cityCode !== '000'
                    })
                    // 非集团用户 显示按钮
                    this.NoAdminGroup = true
                    // 添加列表赋值
                    this.addForm.busiRegProvinceName = newProvinceList[0].provinceName
                    this.addForm.busiRegProvinceCode = newProvinceList[0].provinceCode
                  }
                })
              })
              resolve()
            }
          })
          .catch(() => {
            this.loading = false
            resolve()
          })
      })
    },
    // 重置数据
    reset() {
      this.addForm.offlineChannelCode = ''
      this.addForm.storeNumber = ''
      this.addForm.onlineStoreName = ''
      this.addForm.storeContactNumber = ''
      this.addForm.onlineChannelCode = ''
      this.addForm.onlineStoreEntranceUrl = ''
      this.addForm.platformType = []
      this.addForm.electronicChannelType = []
      this.addForm.businessScope = []
      this.addForm.channelStatusCode = ''
      this.addForm.longitude = ''
      this.addForm.latitude = ''
      this.addForm.shopDesc = ''
      this.addForm.shopAvatar = ''
      this.addForm.exitReason = ''
      this.addForm.exitReasonDesc = ''
      this.addForm.busiRegCityCode = ''
      this.addForm.busiRegCityName = ''
      this.addForm.busiRegCountyCode = ''
      this.addForm.busiRegCountyName = ''
      this.addForm.roleNum = '1'
      this.addForm.employees = [
        {
          identity: '', // 员工身份
          name: '',
          contactNumber: '',
          employeeNumber: ''
        }
      ]
    },
    // 添加
    onlineAdd(val) {
      this.isStatus = val
      this.dialogParam.onlineVisible = true
      this.reset()
    },
    // 添加 --- 关闭dialog
    closeDialog() {
      this.dialogParam.onlineVisible = false
    },
    // 保存添加
    saveAdd(formName) {
      // 复制对象
      var addForm = JSON.parse(JSON.stringify(this.addForm))
      addForm.platformType = this.addForm.platformType.join(',')
      addForm.electronicChannelType = this.addForm.electronicChannelType.join(',')
      addForm.businessScope = this.addForm.businessScope.join(',')
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 接口调用
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/shop/add', addForm).then((res) => {
            if (this.$reponseStatus(res)) {
              this.$message.success('添加成功')
              this.dialogParam.onlineVisible = false
              this.$nextTick(() => {
                this.$refs.table.asp_search()
              })
              // 刷新列表
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          this.$message.error('请完善必填信息')
          return false
        }
      })
    },
    // 编辑
    handleDetail(row, val) {
      this.isStatus = val
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/shop/select', { reqNo: row.reqNo }).then((res) => {
        if (res.status === '200') {
          this.dialogParam.onlineVisible = true
          this.$nextTick(() => {
            this.addForm.businessScope = res.data.businessScope.split(',')
            this.addForm.platformType = res.data.platformType.split(',')
            this.addForm.electronicChannelType = res.data.electronicChannelType.split(',')
            this.addForm.busiRegProvinceName = res.data.busiRegProvinceName
            // this.$set(this.addForm, 'busiRegProvinceName', )
            this.$set(this.addForm, 'roleNum', res.data.employees.length)
          })
          this.addForm = res.data
        }
      })
    },
    // 编辑 --- 修改按钮
    updateOnline(formName) {
      // 复制对象
      var addForm = JSON.parse(JSON.stringify(this.addForm))
      addForm.platformType = this.addForm.platformType.join(',')
      addForm.electronicChannelType = this.addForm.electronicChannelType.join(',')
      addForm.businessScope = this.addForm.businessScope.join(',')
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 接口调用
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/shop/update', addForm).then((res) => {
            if (this.$reponseStatus(res)) {
              this.$message.success('修改成功')
              this.dialogParam.onlineVisible = false
              this.$nextTick(() => {
                this.$refs.table.asp_search()
              })
              // 刷新列表
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          this.$message.error('请完善必填信息')
          return false
        }
      })
    },
    // 批量修改
    handleModifyFileUpload(fileObject) {
    // 校验附件
      if (!fileObject.file) {
        this.$message.error('不能上传空文件！')
        return false
      }
      // const fileSuffix = fileObject.file.name.split('.')[1]
      // if (fileSuffix !== 'xls' && fileSuffix !== 'xlsx') {
      //   this.$message.error('文件后缀只能是xls或者xlsx')
      //   return false
      // }
      const fd = new FormData()
      fd.append('file', fileObject.file)
      axios({
        method: 'post',
        url: this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/shop/updateImportExcel',
        data: fd,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }).then((response) => {
        let res = response.data
        if (response.config.responseType === 'arraybuffer') {
          if (response.data.byteLength < 1024) {
            res = ab2Json(response.data)
          } else {
            res = {
              code: '200',
              status: '200',
              data: {
                headers: response.headers,
                body: response.data
              }
            }
          }
        }
        if (res.status !== '200') {
          this.$message.error(res.message)
        } else {
          this.$message({
            showClose: true,
            message: '正在执行中，请耐心等待。',
            type: 'warning',
            duration: 0
          })
          if (res.data && res.data.body) {
            ExportFile(res.data)
          } else {
            this.$message.success('修改成功')
          }
        }
      })
    },
    // 批量添加
    handleAddFileUpload(fileObject) {
      // 校验附件
      if (!fileObject.file) {
        this.$message.error('不能上传空文件！')
        return false
      }
      // const fileSuffix = fileObject.file.name.split('.')[1]
      // if (fileSuffix !== 'xls' && fileSuffix !== 'xlsx') {
      //   this.$message.error('文件后缀只能是xls或者xlsx')
      //   return false
      // }
      const fd = new FormData()
      fd.append('file', fileObject.file)
      axios({
        method: 'post',
        url: this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/shop/addImportExcel',
        data: fd,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }).then((response) => {
        let res = response.data
        if (response.config.responseType === 'arraybuffer') {
          if (response.data.byteLength < 1024) {
            res = ab2Json(response.data)
          } else {
            res = {
              code: '200',
              status: '200',
              data: {
                headers: response.headers,
                body: response.data
              }
            }
          }
        }
        if (res.status !== '200') {
          this.$message.error(res.message)
        } else {
          this.$message({
            showClose: true,
            message: '正在执行中，请耐心等待。',
            type: 'warning',
            duration: 0
          })
          if (res.data && res.data.body) {
            ExportFile(res.data)
          } else {
            this.$message.success('添加成功')
          }
        }
      })
    },
    // 查看参数详情
    // 获取详情
    detailConfig(row) {
      this.detailParam = {
        title: '线上店管理详情',
        modelVisible: true,
        data: row
      }
    },
    // 省份下拉框
    probinceChanged(data) {
      this.provinceList.filter((item) => {
        if (item.provinceCode === data) {
          this.cityList = item.cities
        }
      })
    },
    // 清空市级下拉框
    editCity() {
      this.table.searchForm.cityCode = ''
    },
    handleChange() {},
    changeOnlinePay(a) {
    },
    // 选择店铺状态
    changeChannelStatus() {},
    // 批量修改 --- 下载模板
    modifyTemplate() {
      this.$aspHttps.asp_ExportGet(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/shop/getUpdateImportDemo'
      )
    },
    // 批量添加 --- 下载模板
    addTemplate() {
      this.$aspHttps.asp_ExportGet(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/shop/getAddImportDemo'
      )
    },
    // 添加---点击增加或者减少店员结构
    handleAddOrReduce(value) {
      // push
      if (this.addForm.employees.length < value) {
        this.addForm.employees.push({
          identity: '',
          name: '',
          contactNumber: '',
          employeeNumber: ''
        })
      } else {
        this.addForm.employees.pop()
      }
    },
    /* 点击查看全部 */
    // handleRow(item) {
    //   let url = `/management/fileuploadstatus/lookall?name=${item.proName}&proId=${item.proId}`
    //   if (this.exportParams.pickerValue[0]) {
    //     url += `&startDate=${this.exportParams.pickerValue[0]}`
    //   }
    //   if (this.exportParams.pickerValue[1]) {
    //     url += `&endDate=${this.exportParams.pickerValue[1]}`
    //   }
    //   if (this.exportParams.fileStatus || this.exportParams.fileStatus === 0) {
    //     url += `&status=${String(this.exportParams.fileStatus)}`
    //   }
    //   this.$router.push({
    //     path: url
    //   })
    // },

    /* 点击导出 */
    // exportSubsidiary() {
    //   this.$aspHttps.asp_FileDownload(
    //     this.$apiConfig.level1cloudstorePathPreFix +
    //       '/databoard/upload/exportDataList',
    //     {
    //       params: {
    //         startDate: this.exportParams.pickerValue[0],
    //         endDate: this.exportParams.pickerValue[1],
    //         status: this.exportParams.fileStatus
    //       }
    //     },
    //     '文件上传情况明细'
    //   )
    // },
    /* 格式化时间 */
    // formatTimer: function (value) {
    //   value = String(value)
    //   const date = new Date(value)
    //   const y = date.getFullYear()
    //   let MM = date.getMonth() + 1
    //   MM = MM < 10 ? '0' + MM : MM
    //   let d = date.getDate()
    //   d = d < 10 ? '0' + d : d
    //   return y + '-' + MM + '-' + d
    // },


    /* 查询成功回调 */
    tableSearchSuccess(data) {
      this.$refs.table.listData = data
    },
    // 列表查询
    getShopOnlineList() {
      this.$nextTick(() => {
        if (this.table.searchForm.fuzzyConditions) {
          this.table.searchForm.fuzzyConditionsList = this.table.searchForm.fuzzyConditions.split(',')
        } else {
          this.table.searchForm.fuzzyConditionsList = []
        }
        this.$refs.table.asp_search()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../scss/common.scss";
.explanatory{
  color: red;
  text-align: center;
}
::v-deep .el-form-item__label {
  font-weight: bold;
  color: #333333;
}
::v-deep .el-dialog__footer{
  background-color: #f1f5fa;
}
/* ::v-deep .el-dialog__header{
  height: 30px;
  background-color: #f1f5fa;
} */
.employees{
  width: 600px;
  border: 1px dashed #999999;
  margin-left: 10%;
  margin-bottom: 10px;
  position: relative;
  span {
    position: absolute;
    right: -45px;
    bottom: 0;
    cursor: pointer;
  }
}
.tableHeader{
  display: flex;
  padding:5px 0;
  align-items: center;
  justify-content: flex-end;
}
.btnStyle{
  padding:5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border:1px solid #DCDFE6;
  border-radius:5px;
  margin-left: 10px;
  padding:5px 10px;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
  margin-right: 10px;
  background: linear-gradient(360deg, #4576e4 0%, #6f9ef0 100%);
  color: #ffffff;
  .hx{
    width:1px;
    height:14px;
    display: block;
    background: #DCDFE6;
    margin:0 8px;
  }
  span{
    color: #ffffff;
  }
}

.file-upload-status {
  .searchBlock {
    padding: 10px;
  }

  .content {
    display: flex;
    flex-direction: column;

    .rap {
      flex: 1;
      min-height: 0;
      padding: 0 20px;
      padding-bottom: 20px;
    }

    .searchBlock {
      padding: 10px;
      ul {
        padding: 0 20px;
        padding-top: 20px;
        display: flex;
        flex-flow: wrap;
        margin: 0;
        li {
          list-style: none;
          margin-right: 20px;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          & > span {
            display: block;
            font-size: 13px;

            margin-right: 10px;
          }
        }
      }
    }

    .tablePane {
      padding: 0 20px;
    }
  }
}
</style>
