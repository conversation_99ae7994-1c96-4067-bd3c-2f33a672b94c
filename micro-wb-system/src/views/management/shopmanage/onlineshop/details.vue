<template>
  <asp-dialog
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="50%"
  >
    <el-form :model="onlineDetails" ref="onlineDetails">
      <el-form-item label="入驻时间：">
        {{ dialogParam.data ? dialogParam.data.createTime : '' }}
      </el-form-item>
      <el-form-item label="实体/直销/随销渠道编码：">
        {{ dialogParam.data ? dialogParam.data.offlineChannelCode : '' }}
      </el-form-item>
      <el-form-item label="线上线下结合渠道编码：">
        {{ dialogParam.data ? dialogParam.data.onlineChannelCode : '' }}
      </el-form-item>
      <el-form-item label="归属省：">
        {{ dialogParam.data ? dialogParam.data.busiRegProvinceName : '' }}
      </el-form-item>
      <el-form-item label="归属市：">
        {{ dialogParam.data ? dialogParam.data.busiRegCityName : '' }}
      </el-form-item>
      <el-form-item label="店铺ID：">
        {{ dialogParam.data ? dialogParam.data.storeNumber : '' }}
      </el-form-item>
      <el-form-item label="店铺名称：">
        {{ dialogParam.data ? dialogParam.data.onlineStoreName : '' }}
      </el-form-item>
      <el-form-item label="门店客服电话：">
        {{ dialogParam.data ? dialogParam.data.storeContactNumber : '' }}
      </el-form-item>
      <el-form-item label="店长：">
        {{ dialogParam.data ? `${dialogParam.data.managerName}&nbsp;&nbsp;&nbsp;${dialogParam.data.managerPhone}` : '' }}
      </el-form-item>
      <div v-for="item in isClerk" :key="item.contactNumber">
        <el-form-item label="店员：">
          {{ `${item.name}&nbsp;&nbsp;&nbsp;${item.contactNumber}` }}
        </el-form-item>
      </div>
      <el-form-item label="线上店铺入口或网址：">
        {{ dialogParam.data ? dialogParam.data.onlineStoreEntranceUrl : '' }}
      </el-form-item>
      <el-form-item label="渠道门店投放网络交易平台：">
        {{ dialogParam.data ? platformTypelist : '' }}
      </el-form-item>
      <el-form-item label="电子渠道门店类型：">
        {{ dialogParam.data ? electronicChannelList : '' }}
      </el-form-item>
      <el-form-item label="线上店铺营业范围：">
        {{ dialogParam.data ? businessScopeList : '' }}
      </el-form-item>
      <el-form-item label="状态：">
        {{ isStatus + '&nbsp;&nbsp;&nbsp;' + reason }}
      </el-form-item>
      <el-form-item label="状态变更时间：">
        {{ dialogParam.data ? dialogParam.data.updateTime : '' }}
      </el-form-item>
      <el-form-item label="经纬度：">
        {{ longitude ? `经度：${dialogParam.data.longitude || ''} ， 纬度：${dialogParam.data.latitude || ''} `: '' }}
      </el-form-item>
      <el-form-item label="店铺介绍：">
        {{ dialogParam.data ? dialogParam.data.shopDesc : '' }}
      </el-form-item>
      <el-form-item label="店铺头像地址：">
        {{ dialogParam.data ? dialogParam.data.shopAvatar : '' }}
      </el-form-item>
    </el-form>
    <template slot="footer-center">
      <asp-btn-solid
        name="返回"
        @click="cancel()"
      ></asp-btn-solid>
    </template>
  </asp-dialog>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  computed: {
    // 店员数据处理
    // isClerk() {
    //   return this.dialogParam.data.employees.filter(item => {
    //     return item.identity === 1
    //   })
    // },
    // 状态数据处理
    // isStatus() {
    //   let isStatus = ''

    //   return isStatus
    // }
  },
  watch: {
    'dialogParam.data': {
      handler(val) {
        // 店员数据处理
        this.isClerk = this.dialogParam.data.employees.filter(item => {
          return item.identity === 1
        })
        this.longitude = this.dialogParam.data.longitude + this.dialogParam.data.latitude
        // 店铺状态数据处理
        if (this.dialogParam.data.channelStatusCode === '1') {
          this.isStatus = '正常'
        } else if (this.dialogParam.data.channelStatusCode === '2') {
          this.isStatus = '冻结'
        } else if (this.dialogParam.data.channelStatusCode === '3') {
          this.isStatus = '终止'
        } else if (this.dialogParam.data.channelStatusCode === '4') {
          this.isStatus = '清退'
        }
        // 渠道门店投放网络交易平台数据处理
        if (this.dialogParam.data.platformType) {
          const platformArr = this.dialogParam.data.platformType.split(',')
          const newPlatformArr = []
          platformArr.map(item => {
            this.platformTypeData.map(i => {
              if (item === String(i.value)) {
                newPlatformArr.push(i.name)
              }
            })
          })
          this.platformTypelist = newPlatformArr.join(',')
        }
        // 电子渠道门店类型数据处理
        if (this.dialogParam.data.electronicChannelType) {
          const electronicChannelArr = this.dialogParam.data.electronicChannelType.split(',')
          const newElectronicChannelArr = []
          electronicChannelArr.map(item => {
            this.electronicChannelData.map(i => {
              if (item === i.value) {
                newElectronicChannelArr.push(i.name)
              }
            })
          })
          this.electronicChannelList = newElectronicChannelArr.join(',')
        }
        // 线上店铺的营业范围数据处理
        if (this.dialogParam.data.businessScope) {
          const businessScopeArr = this.dialogParam.data.businessScope.split(',')
          const newbusinessScopeArr = []
          businessScopeArr.map(item => {
            this.businessScopeData.map(i => {
              if (item === i.value) {
                newbusinessScopeArr.push(i.name)
              }
            })
          })
          this.businessScopeList = newbusinessScopeArr.join(',')
          this.reqNo = val.reqNo
          if (this.reqNo) {
            this.getDetails()
          }
        }
      }
    }
  },
  data() {
    return {
      longitude: '',
      reason: '',
      isClerk: [],
      isStatus: '',
      reqNo: '',
      detailsList: {},
      onlineDetails: {
        id: ''
      },
      platformTypelist: '', // 渠道门店投放网络交易平台展示数据
      electronicChannelList: '', // 电子渠道门店类型展示数据
      businessScopeList: '', // 线上店铺的营业范围展示数据
      platformTypeData: [
        {
          value: 0,
          name: '企业自建'
        },
        {
          value: 1,
          name: '淘宝'
        },
        {
          value: 2,
          name: '天猫'
        },
        {
          value: 3,
          name: '京东'
        },
        {
          value: 4,
          name: '拼多多'
        },
        {
          value: 5,
          name: '抖音'
        },
        {
          value: 6,
          name: '快手'
        },
        {
          value: 7,
          name: '微信'
        },
        {
          value: 8,
          name: '支付宝'
        },
        {
          value: 9,
          name: '淘特'
        },
        {
          value: 10,
          name: '其他'
        },
        {
          value: 11,
          name: '今日头条'
        },
        {
          value: 12,
          name: '百度营销'
        },
        {
          value: 13,
          name: '广点通'
        },
        {
          value: 14,
          name: '巨量引擎'
        },
        {
          value: 15,
          name: '磁力引擎'
        },
        {
          value: 16,
          name: '阿里超级汇川'
        },
        {
          value: 17,
          name: '其他信息流'
        },
        {
          value: 18,
          name: '垂类'
        },
        {
          value: 19,
          name: '小红书'
        }
      ],
      electronicChannelData: [
        {
          value: '1',
          name: '中国移动APP'
        },
        {
          value: '2',
          name: '公众号（微信）'
        },
        {
          value: '3',
          name: '小程序'
        },
        {
          value: '4',
          name: '其他'
        }
      ],
      businessScopeData: [
        {
          value: '0101',
          name: '话费查询'
        },
        {
          value: '0102',
          name: '积分查询'
        },
        {
          value: '0103',
          name: '流量查询'
        },
        {
          value: '0104',
          name: '其他查询'
        },
        {
          value: '0201',
          name: '充值缴费'
        },
        {
          value: '0202',
          name: '流量充值'
        },
        {
          value: '0301',
          name: '入网'
        },
        {
          value: '0302',
          name: '过户'
        },
        {
          value: '0303',
          name: '资料完善'
        },
        {
          value: '0304',
          name: '改资料'
        },
        {
          value: '0305',
          name: '密码重置'
        },
        {
          value: '0306',
          name: '补卡'
        },
        {
          value: '0307',
          name: '销户'
        },
        {
          value: '0308',
          name: '销户重开'
        },
        {
          value: '0309',
          name: '强制复机'
        },
        {
          value: '0310',
          name: '销户转账'
        },
        {
          value: '0311',
          name: '销户取现'
        },
        {
          value: '0312',
          name: '黑名单出库'
        },
        {
          value: '0313',
          name: '退预交款'
        },
        {
          value: '0314',
          name: '集团V网关闭'
        },
        {
          value: '0315',
          name: '8元套餐'
        },
        {
          value: '0316',
          name: '小额充值卡销售'
        },
        {
          value: '0317',
          name: '托收取消'
        },
        {
          value: '0318',
          name: '跨省换卡、跨区密码重置、跨省销户'
        },
        {
          value: '0319',
          name: '打印服务'
        },
        {
          value: '0320',
          name: '解合约'
        },
        {
          value: '0321',
          name: '降档'
        },
        {
          value: '0322',
          name: '携号转网(转出)'
        },
        {
          value: '0323',
          name: '携号转网(转入)'
        },
        {
          value: '0399',
          name: '其他'
        },
        {
          value: '0401',
          name: '号卡'
        },
        {
          value: '0402',
          name: '固话'
        },
        {
          value: '0403',
          name: '宽带'
        },
        {
          value: '0404',
          name: '5G套包'
        },
        {
          value: '0405',
          name: '权益'
        },
        {
          value: '0406',
          name: '物联网卡'
        },
        {
          value: '0499',
          name: '其他'
        },
        {
          value: '0501',
          name: '终端销售'
        },
        {
          value: '0502',
          name: '儿童手表'
        },
        {
          value: '0503',
          name: '智能家居'
        },
        {
          value: '0504',
          name: '智能网关'
        },
        {
          value: '0505',
          name: '其他终端'
        },
        {
          value: '0601',
          name: '产品宣传'
        }
      ]
    }
  },
  methods: {
    // 获取详情
    getDetails() {
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/shop/select', {
        reqNo: this.reqNo
      }).then((res) => {
        if (this.$reponseStatus(res)) {
          this.detailsList = res.data
          // 冻结/终止原因数据处理
          if (res.data.exitReason && res.data.exitReason === '1') {
            this.reason = '授权到期'
          } else if (res.data.exitReason && res.data.exitReason === '2') {
            this.reason = '因渠道违规等问题企业与渠道解除合作'
          } else if (res.data.exitReason && res.data.exitReason === '3') {
            this.reason = res.data.exitReasonDesc
          } else {
            this.reason = ''
          }
        }
      })
    },
    cancel() {
      this.dialogParam.modelVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>

::v-deep .el-form-item__content {
  width: calc(100% - 200px);
}
::v-deep .el-form-item__label {
  width: 200px;
}
</style>
