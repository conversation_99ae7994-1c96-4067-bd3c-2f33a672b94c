{"list": [{"type": "form", "label": "表单容器", "columnName": "shopoverviewform", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1701227204627", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 3, "align": "left", "list": [{"type": "radio", "label": "", "columnName": "dateType", "defaultValue": "day", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont iconradio-checked", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "{}", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "月", "value": "month"}, {"label": "日", "value": "day"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "月", "value": "month", "disabled": false}, {"label": "日", "value": "day", "disabled": false}], "hasParentForm": true, "labelWidth": 0, "targetName": "label_radio_1701227255412", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "styleBtn": true, "isBreak": false, "oldColumnName": "dateStatus", "network$$$tag": false, "dynamic": {"single_dy_query_area_list": [{"source": {"label": "", "columnName": "dateType"}, "target": [{"columnName": "startDate", "label": " startDate", "props": {"type": "date", "placeholder": "请选择", "format": "yyyy-MM-dd", "value-format": "yyyy-MM-dd", "range-separator": "至", "start-placeholder": "", "end-placeholder": "", "disabled": false, "readonly": false, "clearable": false, "picker-options": {}}, "type": "datePicker"}], "condition": [{"columnName": "dateType", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "day", "type": ["hidden"], "result": false, "value": "", "status": []}, {"columnName": "dateType", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "month", "type": ["hidden"], "result": true, "value": "", "status": []}]}, {"source": {"label": "", "columnName": "dateType"}, "target": [{"columnName": "startDate1", "label": " startDate1", "props": {"type": "month", "placeholder": "请选择", "format": "yyyy-MM", "value-format": "yyyy-MM", "range-separator": "至", "start-placeholder": "", "end-placeholder": "", "disabled": false, "readonly": false, "clearable": false, "picker-options": {}}, "type": "datePicker"}], "condition": [{"columnName": "dateType", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "day", "type": ["hidden"], "result": true, "value": "", "status": []}, {"columnName": "dateType", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "month", "type": ["hidden"], "result": false, "value": "", "status": []}]}]}}]}, {"span": 4, "align": "left", "list": [{"label": "", "type": "datePicker", "columnName": "startDate", "defaultValue": "", "icon": "", "width": "100%", "asideIcon": "iconfont iconriqi", "customLabelWidth": true, "required": false, "rules": [], "classify": "form", "class": "", "aspProps": {"dateRange": 2}, "props": {"type": "date", "placeholder": "请选择", "format": "yyyy-MM-dd", "value-format": "yyyy-MM-dd", "range-separator": "至", "start-placeholder": "", "end-placeholder": "", "disabled": false, "readonly": false, "clearable": false, "picker-options": {}}, "hidden": false, "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": "", "defaultSwitch": true, "hiddenStatus": [], "dynamic": {"single_dy_query_area_list": []}, "oldColumnName": "dateMonth", "defaultRangeValue": -1}, {"label": "", "type": "datePicker", "columnName": "startDate1", "defaultValue": "", "icon": "", "width": "100%", "asideIcon": "iconfont iconriqi", "customLabelWidth": true, "required": false, "rules": [], "classify": "form", "class": "", "aspProps": {"dateRange": 6}, "props": {"type": "month", "placeholder": "请选择", "format": "yyyy-MM", "value-format": "yyyy-MM", "range-separator": "", "start-placeholder": "", "end-placeholder": "", "disabled": false, "readonly": false, "clearable": false, "picker-options": {}}, "hidden": true, "hasParentForm": true, "labelWidth": 0, "oldColumnName": "startDate", "bpmFlowImageCloumnName": "", "hiddenStatus": [], "defaultSwitch": false, "defaultSwitch_New_switch": true, "defaultSwitch_New": true, "defaultValueNum1": -1, "defaultFromTime": "00:00:00"}]}, {"span": 4, "align": "left", "list": [{"type": "select", "label": "", "columnName": "provinceCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "provinceName", "option-value": "provinceCode", "option-alias": "", "option-disabled": "", "searchProps": {"need": "0", "apiName": "", "isShowInput": true, "labelAlias": "", "remotePropName": "condition", "apiType": "post+json"}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/shop/common/regionInfo", "apiType": "post+json", "apiParam": "{\"provinceCode\":\"$provinceCode$\"}", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": true, "clearableStatus": [], "readonlyStatus": []}, "options": [{"label": "", "vlaue": "new_option1704268149766", "value": ""}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "", "vlaue": "new_option1704268149766", "value": "", "": false}], "hasParentForm": true, "labelWidth": 0, "targetName": "label_select_1701227544235", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "network$$$tag": false, "oldColumnName": "proId", "filterOption": "", "isMutexSwitch": false, "isClearOtherValue": true, "clearOtherValueList": ["cityCode"], "convertLabel": false, "convertLabelStatus": [], "hiddenStatus": [], "dynamic": {"single_option_select": [{"source": {"label": "", "columnName": "provinceCode"}, "target": [{"columnName": "cityCode", "label": " cityCode", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "type": "select"}], "optionType": "2", "condition": [{"columnName": "provinceCode", "condition": "", "valueType": "change", "compareValueType": "", "columnValue": "", "apiType": "post+json", "apiName": "/yundian/osm/api/shop/common/regionCitiesInfo", "apiParam": "{\"provinceCode\":\"$provinceCode$\"}", "sessionKey": "", "dicKey": "", "status": []}]}]}}]}, {"span": 4, "align": "left", "list": [{"type": "select", "label": "", "columnName": "cityCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "cityName", "option-value": "cityCode", "option-alias": "", "option-disabled": "", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/shop/common/regionCitiesInfo", "apiType": "post+json", "apiParam": "{\"provinceCode\":\"$provinceCode$\"}", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [], "hidden": false, "isAddOption": false, "showOptions": [], "hasParentForm": true, "labelWidth": 0, "targetName": "label_select_1704440870495", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1704440870495", "dynamic": {}, "network$$$tag": false}]}, {"span": 9, "align": "left", "list": [{"type": "button", "label": "查询", "columnName": "button_1704440865318", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"type": "primary", "plain": false, "round": false, "icon": "el-icon-search"}, "class": "solid-with-icon-btn ", "classify": "button", "hasParentForm": true, "labelWidth": 1, "bpmFlowImageCloumnName": "", "formId": "shopoverviewform", "tableId": "table_1701758682112", "oldColumnName": "button", "event": "submit", "authSwitch": false, "dynamic": {"single_dy_query_area_list": [{"source": {"label": "查询", "columnName": "button"}, "target": [{"columnName": "customArea_1701227725002", "label": " customArea_1701227725002", "props": {"placeholder": ""}, "type": "customArea", "children": []}], "condition": []}]}}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false}, {"type": "empty", "label": "空容器", "columnName": "empty_1701761278139", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "form", "label": "表单容器", "columnName": "form_1701227722079", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"label": "", "type": "customArea", "columnName": "customArea_1701227725002", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont iconquyu", "customLabelWidth": false, "classify": "form", "class": "", "slotName": "showNum", "props": {"placeholder": ""}, "hidden": false, "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "isBreak": false, "authSwitch": false}], "isBorder": true, "isOverspread": false, "classify": "layout", "hidden": false}, {"label": "线上店概况", "type": "headline", "button": false, "isLabelWidth": false, "columnName": "headline_1701761287401", "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "showSubTitle": false, "subTitle": "", "align": null, "hidden": false, "operation": [], "asideIcon": "iconfont iconzhedie<PERSON>ban", "list": [{"type": "LRGrid", "label": "左右布局", "columnName": "LRGrid_1703759429257", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 24, "align": "right", "list": [{"type": "button", "label": "导出明细表", "columnName": "gkexport", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"type": "primary", "disabled": false, "disabledStatus": []}, "class": "solid-with-icon-btn ", "classify": "button", "hasParentForm": false, "labelWidth": 1, "bpmFlowImageCloumnName": "", "event": "export", "treasuryConfig": {"isEnableTreasury": false, "treasuryResType": "session", "treasuryResKey": "", "treasuryResDict": "codeData.GOLD_BANK_APPROVAL_CODE", "treasuryActionApi": "", "treasuryAccessApi": "/web/business/v1/base/goldBank/getGoldBankParam", "treasuryAccessApiMethod": "post+json", "treasuryAccessApiParams": {"goldBankApprovalCode": "操作标识Key，不用修改自动匹配", "businessName": "", "businessType": ""}, "treasuryWriteLogApi": "/web/business/v1/base/goldBank/saveGoldBankLog", "treasuryWriteLogApiMethod": "post+json", "treasuryWriteLogApiParmas": {"goldBankApprovalResult": "金库的审批结果，不用修改自动匹配", "goldBankApprovalCode": "操作标识Key，不用修改自动匹配", "businessName": "", "businessType": ""}, "treasuryView": "asp-treasury-approval", "treasuryTitle": "金库审批", "treasuryDialogWidth": "60%", "treasuryDialogHeight": "auto", "treasuryDialogTitleClass": "dialog-title-default", "treasuryHints": "正在等待金库管理员审批", "treasuryConfirmMsg": "待金库管理员反馈审批结果，确定关闭当前的金库审批弹框吗？关闭后将取消本次操作。"}, "exportAsyncConfig": {"syncApiUrl": "", "syncApiMethod": "post+blob", "syncApiParams": {}, "exportFileName": "", "exportFileNamePrefix": "", "exportFileNameSuffix": "", "exportFileMineType": "", "exportAsyncApiUrl": "", "exportAsyncApiMethod": "post+json", "exportAsyncApiParams": {}, "exportAsyncHints": "", "exportAsyncResType": "session", "exportAsyncResKey": "", "exportAsyncResDict": "", "exportAsyncDefaultTimout": 50}, "exportType": "request", "apiMethod": "post+arraybuffer", "apiName": "/yundian/osm/api/shop/shopManager/export/shopOverviewList", "apiParam": {"startDate": "$startDate$", "startDate1": "$startDate1$", "endDate": "$endDate$", "provinceCode": "$provinceCode$", "cityCode": "$cityCode$", "dateType": "$dateType$", "downLoadName": "店铺概况"}, "tableId": "table_1701758682112", "formId": "shopoverviewform", "exportUrl": "/yundian/osm/api/shop/shopManager/export/shopOverviewList", "exportFileName": "店铺概况", "oldColumnName": "button_1703759443364", "authSwitch": false, "dynamic": {}, "isBreak": false, "hiddenStatus": []}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": false, "labelWidth": 1}, {"type": "table", "label": "表格", "columnName": "table_1701758682112", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": true, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [{"timeStamp": 1701227799046, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "filterColumn": false}, {"label": "省份", "columnName": "provinceName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "canSortConvert": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "地市", "columnName": "cityName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "开通线上店总数", "columnName": "openShopNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "活跃厅店数", "columnName": "activeShopNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "登记员工数", "columnName": "worker<PERSON>um", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "线上店粉丝数", "columnName": "fansNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": true, "layout": "total, prev, pager, next, sizes, jumper", "pageSizes": [10, 20, 30, 40, 50], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/shopManager/shopOverviewList", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading", "apiFailTip": true}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1701227799046, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "filterColumn": false}, {"label": "省份", "columnName": "provinceName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "canSortConvert": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "地市", "columnName": "cityName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "开通线上店总数", "columnName": "openShopNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "活跃厅店数", "columnName": "activeShopNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "登记员工数", "columnName": "worker<PERSON>um", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "线上店粉丝数", "columnName": "fansNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}], "paginationStatus": [], "router": {}, "formId": "shopoverviewform", "hasParentForm": false, "labelWidth": 100, "expandRowKeys": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "allIsEditFlag": false, "allIsEditStatus": [], "httpHeader": {"initHttp": true, "methods": "", "type": "post", "apiParam": {}}, "dynamicColumnCfg": {"columnCode": "code", "columnName": "name", "width": "auto"}}], "titleHiddenStatusList": [], "titleHidden": false, "open": true, "toolList": [], "title-background-color": "rgb(255,255,255,1)", "title-font-size": 15, "title-color": "rgb(48,49,51,1)", "hasParentForm": false, "labelWidth": 1, "hiddenStatus": []}], "classify": "empty", "hidden": false}], "model": {"dateType": "day", "startDate": "2024-01-30", "startDate1": "2023-12", "provinceCode": "", "cityCode": "", "customArea_1701227725002": ""}, "tableModel": {"childList": []}, "pagination": {}, "publicConfig": {"exportName": "shopoverview", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 1, "labelPosition": "right", "size": "small", "class": "", "layoutType": "flex", "statusList": [], "localProxy": "", "nigxProxy": "", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": "", "pageNumKey": "page", "pageSizeKey": "rows", "totalKey": "total"}, "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "店铺概况", "advancedQuery": false, "lineNumber": 0}, "dataConfig": {}}