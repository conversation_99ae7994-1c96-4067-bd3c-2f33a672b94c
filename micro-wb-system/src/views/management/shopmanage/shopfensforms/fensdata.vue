<template>
  <div>
    <span class="date">统计时间：{{ indexLists.startDate ? indexLists.startDate : '' }}</span>
    <div class="shopNumContent">
      <div>
        <p>线上店粉丝总量：<span>{{ indexLists.fansNumShow }}</span> 人</p>
      </div>
  </div>
  </div>
</template>
<script>
export default {
  props: ['indexList', 'params'],
  data () {
    return {
      // date: ''
    }
  },
  mounted() {
    // this.Date()
  },
  computed: {
    // 防止数据没加载完成报错
    indexLists() {
      return this.indexList || {}
    }
  },
  methods: {
    // Date() {
    //   // 创建当前时间的 Date 对象
    //   const currentDate = new Date()
    //   // 将日期设为前一天
    //   currentDate.setDate(currentDate.getDate() - 1)
    //   // 格式化日期输出（示例）
    //   const year = currentDate.getFullYear() // 年份
    //   const month = (currentDate.getMonth() + 1).toString().padStart(2, '0') // 月份（需要加上1并转换为字符串）
    //   const day = currentDate.getDate().toString().padStart(2, '0') // 日期（需要转换为字符串）
    //   this.date = `${year}-${month}-${day}`
    // }
  }
}
</script>
<style lang="scss" scoped>
.date{
  font-size: 13px;
  color: #C0C2C6;
}
.shopNumContent{
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: left;
  div{
    text-align: center;
    font-size: 17px;
    font-weight: 600;
    border-right: 1px solid #D9DADD;
    color: #333333;
    span{
      color: red;
    }
  }
  div:last-child{
    border: none;
  }
}
</style>
