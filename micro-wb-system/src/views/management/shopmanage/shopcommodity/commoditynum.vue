<template>
  <div>
    <span class="date">统计时间：{{ indexList.startDate ? indexList.startDate : '' }}</span>
    <div class="shopNumContent">
      <div>
        <p><i class="el-icon-question" title="店铺中上线的商品总数"></i> 线上店商品总量：<span>{{ indexList.goodsNum ? indexList.goodsNum : 0 }}</span> 件</p>
      </div>
  </div>
  </div>
</template>
<script>
export default {
  props: ['indexList'],
  data () {
    return {
      // date: ''
    }
  },
  mounted() {
    // this.Date()
  },
  methods: {
    // Date() {
    //   // 创建当前时间的 Date 对象
    //   const currentDate = new Date()
    //   // 将日期设为前一天
    //   currentDate.setDate(currentDate.getDate() - 1)
    //   // 格式化日期输出（示例）
    //   const year = currentDate.getFullYear() // 年份
    //   const month = (currentDate.getMonth() + 1).toString().padStart(2, '0') // 月份（需要加上1并转换为字符串）
    //   const day = currentDate.getDate().toString().padStart(2, '0') // 日期（需要转换为字符串）
    //   this.date = `${year}-${month}-${day}`
    // }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-icon-question:before{
  color: #999999;
}
.date{
  font-size: 13px;
  color: #C0C2C6;
}
.shopNumContent{
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: left;
  div{
    text-align: center;
    font-size: 17px;
    font-weight: 600;
    border-right: 1px solid #D9DADD;
    color: #333333;
    span{
      color: red;
    }
  }
  div:last-child{
    border: none;
  }
}
</style>
