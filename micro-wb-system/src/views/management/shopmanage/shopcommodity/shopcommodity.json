{"list": [{"type": "form", "label": "表单容器", "columnName": "shopListFrom", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1702362505032", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 4, "align": "left", "list": [{"type": "select", "label": "", "columnName": "provinceCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "provinceName", "option-value": "provinceCode", "option-alias": "", "option-disabled": "", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/shop/common/regionInfo", "apiType": "post+json", "apiParam": "{\"provinceCode\":\"$provinceCode$\"}", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "Option 1", "value": "1", "": false}, {"label": "Option 2", "value": "2", "": false}], "hasParentForm": true, "labelWidth": 0, "targetName": "label_select_1702362555335", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1702362555335", "network$$$tag": false, "dynamic": {"single_option_select": [{"source": {"label": "", "columnName": "provinceCode"}, "target": [{"columnName": "cityCode", "label": " cityCode", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "type": "select"}], "optionType": "2", "condition": [{"columnName": "provinceCode", "condition": "", "valueType": "change", "compareValueType": "", "columnValue": "", "apiType": "post+json", "apiName": "/yundian/osm/api/shop/common/regionCitiesInfo", "apiParam": "{\"provinceCode\":\"$provinceCode$\"}", "sessionKey": "", "dicKey": "", "status": []}]}]}, "isClearOtherValue": true, "clearOtherValueList": ["cityCode"]}]}, {"span": 4, "align": "left", "list": [{"type": "select", "label": "", "columnName": "cityCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "cityName", "option-value": "cityCode", "option-alias": "", "option-disabled": "", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "{}", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "Option 1", "value": "1", "": false}, {"label": "Option 2", "value": "2", "": false}], "hasParentForm": true, "labelWidth": 0, "targetName": "label_select_1702362556503", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1702362556503", "network$$$tag": false, "isClearOtherValue": false, "clearOtherValueList": []}]}, {"span": 6, "align": "left", "list": [{"type": "input", "label": "", "columnName": "channelCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "实体/线上渠道编码", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "clearableStatus": []}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 0, "oldColumnName": "onlineChannelCode", "bpmFlowImageCloumnName": ""}]}, {"span": 6, "align": "left", "list": [{"type": "input", "label": "", "columnName": "shopId", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "店铺ID", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "clearableStatus": []}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 0, "oldColumnName": "input_1702362565247", "bpmFlowImageCloumnName": ""}]}, {"span": 4, "align": "left", "list": [{"type": "button", "label": "查询", "columnName": "button_1702362568781", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-search", "type": "primary"}, "class": "solid-with-icon-btn ", "classify": "button", "hasParentForm": true, "labelWidth": 100, "event": "submit", "treasuryConfig": {"isEnableTreasury": false, "treasuryResType": "session", "treasuryResKey": "", "treasuryResDict": "codeData.GOLD_BANK_APPROVAL_CODE", "treasuryActionApi": "", "treasuryAccessApi": "/web/business/v1/base/goldBank/getGoldBankParam", "treasuryAccessApiMethod": "post+json", "treasuryAccessApiParams": {"goldBankApprovalCode": "操作标识Key，不用修改自动匹配", "businessName": "", "businessType": ""}, "treasuryWriteLogApi": "/web/business/v1/base/goldBank/saveGoldBankLog", "treasuryWriteLogApiMethod": "post+json", "treasuryWriteLogApiParmas": {"goldBankApprovalResult": "金库的审批结果，不用修改自动匹配", "goldBankApprovalCode": "操作标识Key，不用修改自动匹配", "businessName": "", "businessType": ""}, "treasuryView": "asp-treasury-approval", "treasuryTitle": "金库审批", "treasuryDialogWidth": "60%", "treasuryDialogHeight": "auto", "treasuryDialogTitleClass": "dialog-title-default", "treasuryHints": "正在等待金库管理员审批", "treasuryConfirmMsg": "待金库管理员反馈审批结果，确定关闭当前的金库审批弹框吗？关闭后将取消本次操作。"}, "exportAsyncConfig": {"syncApiUrl": "", "syncApiMethod": "post+blob", "syncApiParams": {}, "exportFileName": "", "exportFileNamePrefix": "", "exportFileNameSuffix": "", "exportFileMineType": "", "exportAsyncApiUrl": "", "exportAsyncApiMethod": "post+json", "exportAsyncApiParams": {}, "exportAsyncHints": "", "exportAsyncResType": "session", "exportAsyncResKey": "", "exportAsyncResDict": "", "exportAsyncDefaultTimout": 50}, "bpmFlowImageCloumnName": "", "tableId": "shopInfoList", "formId": "shopListFrom"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false}, {"type": "empty", "label": "空容器", "columnName": "empty_1702365433822", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "form", "label": "表单容器", "columnName": "form_1704442214614", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1704708928256", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 24, "align": "right", "list": [{"type": "button", "label": "导出明细表", "columnName": "button_1704708944312", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"type": "primary"}, "class": "solid-with-icon-btn ", "classify": "button", "hasParentForm": false, "labelWidth": 100, "bpmFlowImageCloumnName": "", "event": "export", "treasuryConfig": {"isEnableTreasury": false, "treasuryResType": "session", "treasuryResKey": "", "treasuryResDict": "codeData.GOLD_BANK_APPROVAL_CODE", "treasuryActionApi": "", "treasuryAccessApi": "/web/business/v1/base/goldBank/getGoldBankParam", "treasuryAccessApiMethod": "post+json", "treasuryAccessApiParams": {"goldBankApprovalCode": "操作标识Key，不用修改自动匹配", "businessName": "", "businessType": ""}, "treasuryWriteLogApi": "/web/business/v1/base/goldBank/saveGoldBankLog", "treasuryWriteLogApiMethod": "post+json", "treasuryWriteLogApiParmas": {"goldBankApprovalResult": "金库的审批结果，不用修改自动匹配", "goldBankApprovalCode": "操作标识Key，不用修改自动匹配", "businessName": "", "businessType": ""}, "treasuryView": "asp-treasury-approval", "treasuryTitle": "金库审批", "treasuryDialogWidth": "60%", "treasuryDialogHeight": "auto", "treasuryDialogTitleClass": "dialog-title-default", "treasuryHints": "正在等待金库管理员审批", "treasuryConfirmMsg": "待金库管理员反馈审批结果，确定关闭当前的金库审批弹框吗？关闭后将取消本次操作。"}, "exportAsyncConfig": {"syncApiUrl": "", "syncApiMethod": "post+blob", "syncApiParams": {}, "exportFileName": "", "exportFileNamePrefix": "", "exportFileNameSuffix": "", "exportFileMineType": "", "exportAsyncApiUrl": "", "exportAsyncApiMethod": "post+json", "exportAsyncApiParams": {}, "exportAsyncHints": "", "exportAsyncResType": "session", "exportAsyncResKey": "", "exportAsyncResDict": "", "exportAsyncDefaultTimout": 50}, "exportType": "request", "apiMethod": "post+arraybuffer", "apiName": "/yundian/osm/api/shop/shopManager/export/shopGoodsList", "apiParam": {"provinceCode": "$provinceCode$", "cityCode": "$cityCode$", "channelCode": "$channelCode$", "shopId": "$shopId$", "downLoadName": "商品总量商品列表"}}]}], "isTableGrid": false, "classify": "layout", "hidden": false}, {"label": "", "type": "customArea", "columnName": "customArea_1702362754992", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont iconquyu", "customLabelWidth": true, "classify": "form", "class": "", "slotName": "commoditynum", "props": {"placeholder": ""}, "hidden": false, "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": ""}], "isBorder": true, "isOverspread": false, "classify": "layout", "hidden": false, "hasParentForm": false, "labelWidth": 100}, {"label": "在售商品类型排名", "type": "headline", "button": false, "isLabelWidth": false, "columnName": "headline_1702365441629", "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "showSubTitle": false, "subTitle": "", "align": null, "hidden": false, "operation": [], "asideIcon": "iconfont iconzhedie<PERSON>ban", "list": [{"type": "table", "label": "表格", "columnName": "table_1702363008554", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [{"timeStamp": 1702362837915, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "排名", "columnName": "sortNo", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "商品类型", "columnName": "goodsTypeShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "商品总数", "columnName": "goodsNumShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/shopManager/shopGoodsNumGoodsTypeTop", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1702362837915, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "排名", "columnName": "sortNo", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "商品类型", "columnName": "goodsTypeShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "商品总数", "columnName": "goodsNumShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "hasParentForm": false, "labelWidth": 100, "paginationStatus": [], "router": {}, "fixed-pagination": true, "tableHeight": "200px"}], "titleHiddenStatusList": [], "titleHidden": false, "open": true, "toolList": [], "title-background-color": "rgb(255,255,255,1)", "title-font-size": 15, "title-color": "rgb(48,49,51,1)", "hasParentForm": false, "labelWidth": 100}, {"label": "地区排名", "type": "headline", "button": false, "isLabelWidth": false, "columnName": "headline_1702365478316", "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "showSubTitle": false, "subTitle": "", "align": null, "hidden": false, "operation": [], "asideIcon": "iconfont iconzhedie<PERSON>ban", "list": [{"type": "table", "label": "表格", "columnName": "table_1702363045628", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [{"timeStamp": 1702362917246, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "排名", "columnName": "sortNo", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "地区", "columnName": "provinceName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "商品总数", "columnName": "goodsNumShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/shopManager/shopGoodsNumGoodsRegionTop", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1702362917246, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "排名", "columnName": "sortNo", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "省份", "columnName": "provinceName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "商品总数", "columnName": "goodsNumShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "hasParentForm": false, "labelWidth": 100, "paginationStatus": [], "router": {}, "fixed-pagination": true, "tableHeight": "200px"}], "titleHiddenStatusList": [], "titleHidden": false, "open": true, "toolList": [], "title-background-color": "rgb(255,255,255,1)", "title-font-size": 15, "title-color": "rgb(48,49,51,1)", "hasParentForm": false, "labelWidth": 100, "hiddenStatus": []}, {"label": "线上店商品信息报表", "type": "headline", "button": false, "isLabelWidth": false, "columnName": "headline_1702371104920", "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "showSubTitle": false, "subTitle": "", "align": null, "hidden": false, "operation": [], "asideIcon": "iconfont iconzhedie<PERSON>ban", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1704442328944", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 24, "align": "right", "list": [{"type": "button", "label": "导出明细表", "columnName": "button_1704442305177", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"type": "primary"}, "class": "solid-with-icon-btn ", "classify": "button", "hasParentForm": false, "labelWidth": 100, "event": "export", "bpmFlowImageCloumnName": "", "treasuryConfig": {"isEnableTreasury": false, "treasuryResType": "session", "treasuryResKey": "", "treasuryResDict": "codeData.GOLD_BANK_APPROVAL_CODE", "treasuryActionApi": "", "treasuryAccessApi": "/web/business/v1/base/goldBank/getGoldBankParam", "treasuryAccessApiMethod": "post+json", "treasuryAccessApiParams": {"goldBankApprovalCode": "操作标识Key，不用修改自动匹配", "businessName": "", "businessType": ""}, "treasuryWriteLogApi": "/web/business/v1/base/goldBank/saveGoldBankLog", "treasuryWriteLogApiMethod": "post+json", "treasuryWriteLogApiParmas": {"goldBankApprovalResult": "金库的审批结果，不用修改自动匹配", "goldBankApprovalCode": "操作标识Key，不用修改自动匹配", "businessName": "", "businessType": ""}, "treasuryView": "asp-treasury-approval", "treasuryTitle": "金库审批", "treasuryDialogWidth": "60%", "treasuryDialogHeight": "auto", "treasuryDialogTitleClass": "dialog-title-default", "treasuryHints": "正在等待金库管理员审批", "treasuryConfirmMsg": "待金库管理员反馈审批结果，确定关闭当前的金库审批弹框吗？关闭后将取消本次操作。"}, "exportAsyncConfig": {"syncApiUrl": "", "syncApiMethod": "post+blob", "syncApiParams": {}, "exportFileName": "", "exportFileNamePrefix": "", "exportFileNameSuffix": "", "exportFileMineType": "", "exportAsyncApiUrl": "", "exportAsyncApiMethod": "post+json", "exportAsyncApiParams": {}, "exportAsyncHints": "", "exportAsyncResType": "session", "exportAsyncResKey": "", "exportAsyncResDict": "", "exportAsyncDefaultTimout": 50}, "apiParam": {"provinceCode": "$provinceCode$", "cityCode": "$cityCode$", "channelCode": "$channelCode$", "shopId": "$shopId$", "downLoadName": "店铺商品报表"}, "exportType": "request", "apiMethod": "post+arraybuffer", "apiName": "/yundian/osm/api/shop/shopManager/export/shopGoodsNumList"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": false, "labelWidth": 100}, {"type": "table", "label": "表格", "columnName": "shopInfoList", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": false, "show-pagination": true, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [{"timeStamp": 1702363099765, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "省份", "columnName": "provinceName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "地市", "columnName": "cityName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "店铺ID", "columnName": "shopId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "原19位渠道编码", "columnName": "offlineChannelCode", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "线上线下结合渠道编码", "columnName": "onlineChannelCode", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "商品数量", "columnName": "goodsNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [{"type": "text", "timestamp": 1704705010314, "columnName": "shopcommodityLook", "label": "查看详情", "align": "center", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "dialog-form", "confirmationSwitch": "", "dialogConfig": {"bindName": "shopcommodityFrom", "title": "商品详情", "subTitle": "", "titleClass": "dialog-title-default", "width": "900px", "height": "500px", "toolList": []}, "parentName": "shopInfoList", "parentItem": {"cacheMultipleSelection": false, "multipleSelectionColumnName": [], "show-multiple-selection": false}, "isShowBadge": false, "badgePropName": "", "badgeMaxValue": ""}], "operation-width": "100", "pagination": {"background": false, "layout": "total, prev, pager, next, sizes, jumper", "pageSizes": [10, 20, 30, 40, 50], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/shopManager/shopGoodsNumList", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1702363099765, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "省份", "columnName": "provinceName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "地市", "columnName": "cityName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "店铺ID", "columnName": "shopId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "原19位渠道编码", "columnName": "offlineChannelCode", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "线上线下结合渠道编码", "columnName": "onlineChannelCode", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "商品数量", "columnName": "goodsNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "hasParentForm": false, "labelWidth": 100, "paginationStatus": [], "router": {}, "multipleSelectionStatus": [], "multipleSelectionSwitch": false, "fixed-pagination": true, "tableHeight": "400px", "formId": "shopListFrom", "hiddenStatus": [], "show-operation-status": [], "operation-exceed": 1, "toolList-position": "center"}], "titleHiddenStatusList": [], "titleHidden": false, "open": true, "toolList": [], "title-background-color": "rgb(255,255,255,1)", "title-font-size": 15, "title-color": "rgb(48,49,51,1)", "hasParentForm": false, "labelWidth": 100}], "classify": "empty", "hidden": false, "hasParentForm": false}], "model": {"provinceCode": "", "cityCode": "", "shopId": "", "customArea_1702362754992": "", "channelCode": ""}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "shopcommodity", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "small", "class": "", "layoutType": "flex", "statusList": [], "localProxy": "", "nigxProxy": "", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": "", "pageNumKey": "page", "pageSizeKey": "rows", "totalKey": "total"}, "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "店铺商品报表", "advancedQuery": false, "lineNumber": 0}}