{"formList": [{"label": "原19位渠道编码", "classify": "basic", "type": "input", "columnName": "offlineChannelCode", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": "", "operations": [{"status": ["info"], "attr": "label", "formatter": ""}]}, {"label": "线上线下结合渠道编码", "classify": "basic", "type": "input", "columnName": "onlineChannelCode", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": "", "operations": [{"status": ["info"], "attr": "label", "formatter": ""}]}, {"label": "店铺id", "classify": "basic", "type": "input", "columnName": "shopId", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": "", "operations": [{"status": ["info"], "attr": "label", "formatter": ""}]}, {"label": "省份", "classify": "basic", "type": "input", "columnName": "provinceName", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": "", "operations": [{"status": ["info"], "attr": "label", "formatter": ""}]}, {"label": "地市", "classify": "basic", "type": "input", "columnName": "cityName", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": true, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": "", "isBreak": false, "closeFormItemBorder": false, "operations": [{"status": ["info"], "attr": "label", "formatter": ""}]}, {"type": "table", "label": "表格", "columnName": "table_1705040035893", "classify": "layout", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "props": {"border": true, "stripe": true, "highlight-current-row": false, "show-header": true, "default-sort": ""}, "btnGroupDynamic": [], "list": [{"timeStamp": 1705040073324, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true}, {"label": "商品类型", "columnName": "goodsTypeName", "type": "text", "sort": false, "fixed": false, "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "align": "center"}, {"label": "商品id", "columnName": "goodsId", "type": "text", "sort": false, "fixed": false, "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "align": "center"}, {"label": "商品名称", "columnName": "goodsName", "type": "text", "sort": false, "fixed": false, "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "align": "center"}, {"label": "SKU", "columnName": "skuInfo", "type": "text", "sort": false, "fixed": false, "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "align": "center"}, {"label": "最高价格（单位：元）", "columnName": "maxPrice", "type": "text", "sort": false, "fixed": false, "width": "", "min-width": "115", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "align": "center"}, {"label": "最低价格（单位：元）", "columnName": "minPrice", "type": "text", "sort": false, "fixed": false, "width": "", "min-width": "115", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "align": "center"}, {"label": "状态", "columnName": "goodsStatusShow", "type": "text", "sort": false, "fixed": false, "width": "", "min-width": "70", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "align": "center"}], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "hide-on-single-page": false, "default_pageSize": 10}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/shopManager/shopGoodsInfoList", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading", "httpParam": "{\"provinceCode\":\"$provinceCode$\",\"cityCode\":\"$cityCode$\",\"offlineChannelCode\":\"$offlineChannelCode$\",\"shopId\":\"$shopId$\"}"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "showHeader": true, "name": "layout", "labelWidth": 170, "paginationStatus": [], "router": {}}], "formConfig": {"labelWidth": 170, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "shopcommodityFrom", "defaultClass": "webbas", "size": "small", "statusList": ["info", "edit"], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "店铺商品报表详情"}, "dataConfig": {}, "virtual_model": {}, "model": {"offlineChannelCode": "", "shopId": "", "provinceName": "", "cityName": "", "onlineChannelCode": ""}, "pre_loading_request_list": [{"apiId": 1705044279191, "isAuto": 1, "apiType": "post+json", "sendTime": "mounted", "apiName": "/yundian/osm/api/shop/shopManager/shopGoodsInfo", "apiParam": "{\n  \"provinceCode\":\"$provinceCode$\",\n  \"cityCode\":\"$cityCode$\",\n  \"offlineChannelCode\":\"$offlineChannelCode$\",\n  \"shopId\":\"$shopId$\"\n}", "status": []}]}