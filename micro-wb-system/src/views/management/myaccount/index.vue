<!--
 * @User: JOJO
 * @FilePath: \netshoposmweb\micro-wb-system\src\views\management\myaccount\index.vue
-->
<template>
  <section class="my-account">
    <el-card
      class="box-card"
      v-loading="loading"
      element-loading-background="rgba(255,255,255,1)"
    >
      <ul v-if="isData">
        <li v-for="(item, index) in dataArr" :key="index" ref="li">
          <span>{{ item.title }}</span> :
          <span :style="{ width: spanWidth }">{{ item.value }}</span>
        </li>
      </ul>
    </el-card>
  </section>
</template>

<script>
export default {
  data() {
    return {
      /* 展示数据 */
      dataArr: [
        { title: '用户角色', value: '' },
        { title: '用户名', value: '' },
        { title: '用户ID', value: '' },
        { title: '手机号', value: '' }
      ],

      /* 第二个span的最大宽度 */
      spanWidth: 'auto',

      /* 加载框 */
      loading: true,

      departmentId: ''
    }
  },
  computed: {
    /* 判断是否有数据 */
    isData() {
      return this.dataArr.some((item) => item.value)
    }
  },
  methods: {
    /* 更新第二个span值的宽度, 取最大 */
    updateSpan() {
      this.$nextTick(() => {
        // 找到li
        const liAll = this.$refs.li

        // 找到li中span里面宽度最大的
        const maxWidth = liAll
          .map((item) => item.children[1].clientWidth)
          .reduce((a, b) => {
            return b > a ? b : a
          })

        this.spanWidth = maxWidth + 'px'
      })
    },

    getUserByUserId() {
      this.loading = true

      this.$aspHttps
        .asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/getCurrent')
        .then((response) => {
          if (this.$reponseStatus(response)) {
            const data = response.data
            // this.dataArr[0].value = data.roleNames
            this.dataArr[1].value = data.userName
            this.dataArr[2].value = data.id
            this.dataArr[3].value = data.mobile

            this.dataArr[0].value = response.data.departmentId
              ? '省公司'
              : '集团'

            this.updateSpan()

            setTimeout(() => {
              this.loading = false
            }, 100)
          }
        })
        .catch(() => {
          this.loading = false
        })
    }
  },
  created() {},
  mounted() {
    this.getUserByUserId()
  },
  watch: {},
  destroyed() {
    this.loading = true
  }
}
</script>

<style lang="scss" scoped>
.my-account {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  transition: 0.3s ease all;
  >>> .el-loading-mask {
  }
  .box-card {
    width: 80%;
    height: 80%;
    padding: 0px 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  ul {
    padding: 0;
    li {
      list-style: none;
      margin: 20px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: justify;
      text-align-last: justify;
      span {
        &:nth-of-type(1) {
          width: 75px;
          margin-right: 10px;
          text-align: justify;
          i {
            padding-left: 100%;
            display: inline-block;
          }
        }
        &:nth-of-type(2) {
          margin-left: 10px;
          text-align: left;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
