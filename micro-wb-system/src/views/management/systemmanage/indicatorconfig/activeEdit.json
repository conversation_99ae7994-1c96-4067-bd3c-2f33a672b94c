{"formList": [{"label": "UV", "type": "text", "columnName": "text_1712731751324", "defaultValue": "", "isModel": true, "icon-dev": "iconfont icontext", "classify": "basic", "isLabelWidth": false, "span": 24, "width": "100%", "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": true, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "isCompare": true, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "defValueType": "userDefined", "isHelpTipText": "", "content-font-color-switch": false, "closeFormItemBorder": true, "authSwitch": false, "operations": [], "isBreak": false}, {"label": "每日>=", "classify": "basic", "type": "input", "columnName": "dayValue", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "该指标不能为空", "rules": [{"required": true, "message": "该指标不能为空", "trigger": "blur"}, {"message": "请填写不超过100,000的自然数", "pattern": "^[1-9]\\d{0,4}$|^100000$|^0$", "trigger": "blur"}], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 270, "isHelpTipText": "", "ruleType": "", "numberTypeRequired": true, "isTrim": false, "authSwitch": false}, {"label": "每月>=", "classify": "basic", "type": "input", "columnName": "monthValue", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "该指标不能为空", "rules": [{"required": true, "message": "该指标不能为空", "trigger": "blur"}, {"message": "请填写不超过100,000的自然数", "pattern": "^[1-9]\\d{0,4}$|^100000$|^0$", "trigger": "blur"}], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 270, "isTrim": true, "numberTypeRequired": true, "ruleType": "", "isHelpTipText": ""}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1709000771018", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"0": "hidden", "1": "show", "columnName": "activeSubmit", "type": "primary", "icon": "", "label": "保存", "interactive": "button_group_submit_all_validate", "validateProp": ["dayValue", "monthValue"], "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/activeUpdate", "class": "solid-with-icon-btn", "default": "hidden", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": [], "exportName": "activeEdit"}, "apiCloseDialogWithResposne": true, "confirmStatus": ["qy"], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": "", "authSwitch": false, "actionWithPrePageList": [], "confirmationSwitch": true, "confirmationMessage": "保存后第二日，该指标将作为判定活跃厅店的关键指标，是否确认保存？", "qy": "show", "jy": "hidden", "ty": "hidden", "bpmButtonList": []}, {"0": "show", "1": "hidden", "is-table-column": false, "columnName": "activeSubmits", "label": "保存", "type": "primary", "class": "solid-with-icon-btn", "icon": "", "bpmFlowImageCloumnName": "", "default": "hidden", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/activeUpdate", "apiCloseDialogWithResposne": true, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "bpmButtonList": [], "activeType": "button_group_form_interface", "apiParam": {}, "apiIsRefresh": "", "apiIsReturn": "", "qy": "hidden", "jy": "show", "ty": "show"}], "name": "layout", "labelWidth": 160, "isDialogFooterBtns": false, "dynamic": {}, "hiddenListStatus": [], "operations": [{"status": ["qy"], "show": true}, {"status": ["jy"], "show": false}], "titleHidden": false, "titleHiddenStatusList": [], "copyOldVal": ""}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "activeEdit", "defaultClass": "webbas", "size": "small", "statusList": ["qy", "ty", "xse", "ddl", "pv", "uv"], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "活跃度指标编辑", "isOpenExportPDFSwitch": false, "exportPDFProps": {"eleId": "", "eleClassName": "", "markProps": {"pdfFileName": "", "markTitle": "", "markImgUrl": "", "imgFileName": "", "opacity": 100, "imgUp": false, "pStyle": {"font-size": "44px", "margin": "40px 0px", "display": "flex", "justify-content": "center"}}}}, "dataConfig": {}, "virtual_model": {}, "model": {"dayValue": "", "monthValue": "", "text_1712731751324": ""}}