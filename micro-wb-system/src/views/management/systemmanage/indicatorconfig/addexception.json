{"formList": [{"label": "异常类型：", "classify": "basic", "type": "input", "columnName": "name", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "请填写异常类型", "rules": [{"required": true, "message": "请填写异常类型", "trigger": "blur"}, {"message": "请输入正确的异常类型", "pattern": "^\\S+$", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "10", "show-word-limit": true, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": "", "numberTypeRequired": false}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1710313260119", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "", "label": "确定", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/unusualTypeAdd", "class": "solid-with-icon-btn", "default": "hidden", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": true, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {"name": "$name$"}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": "", "addexception": "show", "editexception": "hidden"}, {"columnName": "cacel", "type": "warning", "icon": "", "label": "取消", "interactive": "", "validateProp": "", "apiName": "", "class": "hollow-with-icon-btn", "default": "show", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_close_dialog"}, {"is-table-column": false, "columnName": "edit", "label": "修改", "type": "primary", "class": "solid-with-icon-btn", "icon": "", "bpmFlowImageCloumnName": "", "default": "hidden", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/unusualTypeUpdate", "apiCloseDialogWithResposne": true, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "addexception": "hidden", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "editexception": "show", "activeType": "button_group_form_interface", "apiParam": {"id": "$id$", "name": "$name$"}, "apiIsRefresh": "", "apiIsReturn": ""}], "name": "layout", "labelWidth": 160}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "addexception", "defaultClass": "webbas", "size": "small", "statusList": ["addexception", "editexception"], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "新增异常指标类型", "isOpenExportPDFSwitch": false, "exportPDFProps": {"eleId": "", "eleClassName": "", "markProps": {"pdfFileName": "", "markTitle": "", "markImgUrl": "", "imgFileName": "", "opacity": 100, "imgUp": false, "pStyle": {"font-size": "44px", "margin": "40px 0px", "display": "flex", "justify-content": "center"}}}}, "dataConfig": {}, "virtual_model": {}, "model": {"name": ""}}