{"list": [{"type": "empty", "label": "空容器", "columnName": "empty_1712456818983", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"label": "标签页", "type": "tabs", "isLabelWidth": false, "columnName": "tabs_1712456821260", "asideIcon": "iconfont icontabs1", "span": 24, "isModel": true, "hidden": false, "operation": [], "toolList": [], "list": [{"label": "活跃度指标", "columnName": "tab_1", "name": "tab_1", "toolList": [], "list": [{"type": "form", "label": "表单容器", "columnName": "form_1712456528757", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1712456387828", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 16, "align": "left", "list": [{"label": "", "type": "customArea", "columnName": "selectAllData", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont iconquyu", "customLabelWidth": true, "classify": "form", "class": "", "slotName": "selectAllData", "props": {"placeholder": ""}, "hidden": false, "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": "", "oldColumnName": "customArea_1707122703968"}]}, {"span": 4, "align": "left", "list": [{"type": "input", "label": "", "columnName": "metricName", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "输入指标名称", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": "", "oldColumnName": "input_1707103128187"}]}, {"span": 4, "align": "left", "list": [{"type": "button", "label": "搜索", "columnName": "button_1707103129746", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"type": "primary", "round": false, "icon": "el-icon-search"}, "class": "solid-with-icon-btn ", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "tableId": "dynamicTable_1712456519669", "formId": "form_1712456528757", "event": "submit"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}, {"type": "empty", "label": "空容器", "columnName": "empty_1712456644258", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "table", "label": "表格", "columnName": "dynamicTable_1712456519669", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": "", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [{"label": "button_1703586862083", "columnName": "button_1703586862083", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "statusShow", "compare": "=", "compareType": "staticData", "targetValue": "启用", "authSwitch": false, "authId": "", "if": "&&"}]}, {"label": "button_1708683437372", "columnName": "button_1708683437372", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "statusShow", "compare": "=", "compareType": "staticData", "targetValue": "停用", "authSwitch": false, "authId": "", "if": "&&"}]}], "list": [{"timeStamp": 1703586943126, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "filterColumn": false}, {"label": "指标", "columnName": "metricName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "每日", "columnName": "dayValueShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "每月", "columnName": "monthValueShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "是否启用", "columnName": "statusShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}], "operation": [], "toolList": [{"type": "text", "timestamp": 1703586862083, "columnName": "button_1703586862083", "label": "停用", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "hidden", "state": "show", "dataLinkage": [], "confirmationStatus": [], "confirmationSwitch": true, "parentName": "table_1703586825572", "parentItem": {"cacheMultipleSelection": false, "multipleSelectionColumnName": [], "show-multiple-selection": false}, "activeType": "table_row_api", "refreshTableData": true, "isShowBadge": true, "authSwitch": false, "authId": "", "confirmationMessage": "停用后第二日，该指标将不再作为判定活跃厅店的关键指标，是否确认停用？", "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/activeUpdateStatus", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"metricCode\":\"$metricCode$\",\"status\":\"0\"}", "_hidden_state": true}, {"type": "text", "timestamp": 1708683437372, "columnName": "button_1708683437372", "label": "启用", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "hidden", "state": "show", "dataLinkage": [], "info": "", "confirmationStatus": [], "activeType": "table_row_api", "refreshTableData": true, "isShowBadge": true, "parentName": "table_1703586825572", "parentItem": {"cacheMultipleSelection": false, "multipleSelectionColumnName": [], "show-multiple-selection": false}, "confirmationSwitch": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/activeUpdateStatus", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "confirmationMessage": "启用后第二日，该指标将作为判定活跃厅店的关键指标，是否确认启用？", "apiParam": "{\"metricCode\":\"$metricCode$\",\"status\":\"1\"}", "_hidden_state": true}, {"type": "text", "timestamp": 1703586850340, "columnName": "button_1703586850340", "label": "编辑", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "info": "show", "parentName": "table_1703586825572", "parentItem": {"cacheMultipleSelection": false, "multipleSelectionColumnName": [], "show-multiple-selection": false}, "activeType": "dialog-form", "dialogConfig": {"bindName": "activeEdit", "title": "指标编辑", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "200px", "toolList": []}, "_hidden_state": false}, {"type": "text", "timestamp": 1708683451230, "columnName": "button_1708683451230", "label": "操作记录", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "info": "show", "confirmationStatus": [], "parentName": "table_1703586825572", "parentItem": {"cacheMultipleSelection": false, "multipleSelectionColumnName": [], "show-multiple-selection": false}, "activeType": "dialog-table", "dialogConfig": {"bindName": "activeoperate", "title": "操作记录", "subTitle": "", "titleClass": "dialog-title-default", "width": "800px", "height": "auto", "toolList": []}, "_hidden_state": false}], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/systemManager/metricConfig/activeList", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1703586943126, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "filterColumn": false}, {"label": "指标", "columnName": "metricName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "每日", "columnName": "dayValueShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "每月", "columnName": "monthValueShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "是否启用", "columnName": "statusShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}], "hasParentForm": true, "labelWidth": 100, "paginationStatus": [], "show-operation-status": [], "router": {}, "fixed-pagination": true, "tableHeight": "450px", "formId": "form_1712456528757", "httpHeader": {"initHttp": true, "methods": "", "type": "post", "apiParam": {}}, "dynamicColumnCfg": {"columnCode": "code", "columnName": "name", "width": "auto"}}], "classify": "empty", "hidden": false, "hasParentForm": true, "labelWidth": 100}]}, {"label": "店铺清退指标", "columnName": "tab_2", "name": "tab_1712456545781", "list": [{"type": "empty", "label": "空容器", "columnName": "empty_1712456572204", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "form", "label": "表单容器", "columnName": "form_1712456576381", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"label": "", "type": "customArea", "columnName": "customArea_1709087336565", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont iconquyu", "customLabelWidth": true, "classify": "form", "class": "", "slotName": "clearslot", "props": {"placeholder": ""}, "hidden": false, "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": ""}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}, {"type": "table", "label": "表格", "columnName": "table_1703667971697", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [{"label": "button_1703667533650", "columnName": "button_1703667533650", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "statusShow", "compare": "=", "compareType": "staticData", "targetValue": "停用", "authSwitch": false, "authId": "", "if": "&&"}]}, {"label": "button_1709115515538", "columnName": "button_1709115515538", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "statusShow", "compare": "=", "compareType": "staticData", "targetValue": "启用", "authSwitch": false, "authId": "", "if": "&&"}]}], "list": [{"timeStamp": 1703665230301, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "filterColumn": false}, {"label": "指标", "columnName": "metricName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "时间", "columnName": "metricTimeShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "数量", "columnName": "metricValueShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "是否启用", "columnName": "statusShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [{"type": "text", "timestamp": 1703667533650, "columnName": "button_1703667533650", "label": "启用", "align": "center", "class": "", "icon": "", "justShowIcon": false, "defaultState": "hidden", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/shopClearUpdateStatus", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"metricCode\":\"$metricCode$\",\"status\":\"1\"}", "confirmationSwitch": true, "confirmationMessage": "启用后第二日，该指标将作为下发清退预警的关键指标，是否确认启用？", "_hidden_state": true}, {"type": "text", "timestamp": 1709115515538, "columnName": "button_1709115515538", "label": "停用", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "hidden", "state": "show", "dataLinkage": [], "info": "show", "edit": "show", "confirmationStatus": [], "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/shopClearUpdateStatus", "apiSuccessTip": true, "apiFailTip": false, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"metricCode\":\"$metricCode$\",\"status\":\"0\"}", "confirmationSwitch": true, "confirmationMessage": "停用后第二日，该指标将不再作为下发清退预警的关键指标，是否确认停用？", "_hidden_state": true}, {"type": "text", "timestamp": 1703667519730, "columnName": "button_1703667519730", "label": "编辑", "align": "center", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "confirmationSwitch": "", "activeType": "dialog-form", "dialogConfig": {"bindName": "clearedit", "title": "指标编辑", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "200px", "toolList": []}, "_hidden_state": false}, {"type": "text", "timestamp": 1709115524082, "columnName": "button_1709115524082", "label": "操作记录", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "info": "show", "edit": "show", "confirmationStatus": [], "parentName": "table_1703667971697", "parentItem": {"cacheMultipleSelection": false, "multipleSelectionColumnName": [], "show-multiple-selection": false}, "activeType": "dialog-table", "refreshTableData": false, "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/shopClearLog", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "dialogConfig": {"bindName": "clearoperate", "title": "操作记录", "subTitle": "", "titleClass": "dialog-title-default", "width": "800px", "height": "auto", "toolList": []}, "_hidden_state": false}], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/systemManager/metricConfig/shopClearList", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1703665230301, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "filterColumn": false}, {"label": "指标", "columnName": "metricName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "时间", "columnName": "modifyTime", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "数量", "columnName": "metricValueShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "是否启用", "columnName": "statusShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "paginationStatus": [], "router": {}, "show-operation-status": [], "hasParentForm": true, "labelWidth": 100, "fixed-pagination": true, "tableHeight": "450px"}], "classify": "empty", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "toolList": []}, {"label": "异常指标", "columnName": "tab_3", "name": "tab_1712456584267", "list": [{"type": "form", "label": "表单容器", "columnName": "form_1712456614906", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1703668497263", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 18, "align": "left", "list": [{"label": "当满足至少一项启用条件，被判定为异常厅店", "type": "text", "columnName": "text_1703668834502", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "classify": "form", "isLabelWidth": false, "span": 24, "width": "100%", "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "hasParentForm": true, "labelWidth": 295, "bpmFlowImageCloumnName": "", "customLabelWidth": true}]}, {"span": 3, "align": "left", "list": [{"type": "button", "label": "异常类型管理", "columnName": "button_1703669190276", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "exception", "title": "异常类型管理", "subTitle": "", "titleClass": "dialog-title-default", "width": "800px", "height": "auto", "toolList": []}, "props": {"type": "primary"}, "class": "solid-with-icon-btn ", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "event": "router", "routerType": "path", "routerKey": "/management/systemmanage/exception", "routerParamType": "query", "sf_pageType": "table", "sf_routerId": "exception", "sf_routerParam": "", "routerParamValue": ""}]}, {"span": 3, "align": "left", "list": [{"type": "button", "label": "新增", "columnName": "button_1703669192143", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "addanomaly", "title": "新增异常指标", "subTitle": "", "titleClass": "dialog-title-default", "width": "500px", "height": "auto", "toolList": []}, "props": {"type": "primary"}, "class": "solid-with-icon-btn ", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "event": "dialog-form"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}, {"type": "empty", "label": "空容器", "columnName": "empty_1712456609848", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "table", "label": "表格", "columnName": "table_1703670248380", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": false, "show-pagination": true, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [{"label": "button_1703670324032", "columnName": "button_1703670324032", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "statusShow", "compare": "=", "compareType": "staticData", "targetValue": "停用", "authSwitch": false, "authId": "", "if": "&&"}]}, {"label": "button_1710319991899", "columnName": "button_1710319991899", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "statusShow", "compare": "=", "compareType": "staticData", "targetValue": "启用", "authSwitch": false, "authId": "", "if": "&&"}]}], "list": [{"timeStamp": 1703670265809, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "filterColumn": false}, {"label": "异常类型", "columnName": "unusualTypeName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "店铺状态", "columnName": "channelStatusShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "时间", "columnName": "metricTimeShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "指标", "columnName": "metricShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "是否启用", "columnName": "statusShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [{"type": "text", "timestamp": 1703670317944, "columnName": "button_1703670317944", "label": "编辑", "align": "center", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "dialog-form", "dialogConfig": {"bindName": "addanomaly", "title": "编辑异常指标", "subTitle": "", "titleClass": "dialog-title-default", "width": "500px", "height": "auto", "toolList": []}}, {"type": "text", "timestamp": 1703670324032, "columnName": "button_1703670324032", "label": "启用", "align": "center", "class": "", "icon": "", "justShowIcon": false, "defaultState": "hidden", "state": "show", "dataLinkage": [], "confirmationStatus": [], "authSwitch": false, "authId": "", "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/unusualUpdateStatus", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"id\":\"$id$\",\"status\":\"1\"}", "confirmationSwitch": true, "confirmationMessage": "启用后第二日，该指标将作为判定异常厅店的关键指标，是否确认启用？"}, {"type": "text", "timestamp": 1710319991899, "columnName": "button_1710319991899", "label": "停用", "align": "center", "class": "", "icon": "", "justShowIcon": false, "defaultState": "hidden", "state": "show", "dataLinkage": [], "info": "", "edit": "", "clearedit": "", "operate": "", "exception": "", "addexception": "", "confirmationStatus": [], "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/unusualUpdateStatus", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"id\":\"$id$\",\"status\":\"0\"}", "confirmationSwitch": true, "confirmationMessage": "停用后第二日，该指标将不再作为判定异常厅店的关键指标，是否确认停用？"}, {"type": "text", "timestamp": 1703670324745, "columnName": "button_1703670324745", "label": "删除", "align": "center", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/unusualDelete", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"id\":\"$id$\"}", "confirmationSwitch": true, "confirmationMessage": "确定删除吗？"}, {"type": "text", "timestamp": 1703670325362, "columnName": "button_1703670325362", "label": "操作记录", "align": "center", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "dialog-table", "dialogConfig": {"bindName": "anomalyoperate", "title": "操作记录", "subTitle": "", "titleClass": "dialog-title-default", "width": "800px", "height": "auto", "toolList": []}}], "operation-width": "150", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/systemManager/metricConfig/unusualList", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1703670265809, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "filterColumn": false}, {"label": "异常类型", "columnName": "unusualTypeName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "店铺状态", "columnName": "channelStatusShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "时间", "columnName": "metricTimeShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "指标", "columnName": "metricShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "是否启用", "columnName": "statusShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "hasParentForm": true, "labelWidth": 100, "paginationStatus": [], "fixed-pagination": true, "tableHeight": "450px", "router": {}, "show-operation-status": []}], "classify": "empty", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "toolList": []}], "props": {"type": "card", "tab-position": "top", "tabs-height": ""}, "hasParentForm": true, "tabsValue": "tab_2", "labelWidth": 100}], "classify": "empty", "hidden": false, "hasParentForm": true}], "model": {"select_1703585926405": "1", "text_1703585946904": "", "input_1703586389161": "", "select_1703664670850": "", "text_1703664689076": "", "text_1703668834502": "", "select1": "1", "select_1707103042895": "", "text_1707103044511": "", "selectAllData": "", "customArea_1709087336565": "", "metricName": ""}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "indicatorconfig", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "small", "class": "", "layoutType": "flex", "statusList": ["info", "edit", "clearedit", "operate", "exception", "addexception", "addanomaly"], "localProxy": "", "nigxProxy": "", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": "", "pageNumKey": "page", "pageSizeKey": "rows", "totalKey": "total"}, "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "指标配置", "routerInfo": {}}, "dataConfig": {}}