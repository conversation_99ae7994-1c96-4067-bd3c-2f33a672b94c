{"formList": [{"label": "栅格布局", "type": "row", "columnName": "row_1710235536342", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "classify": "layout", "rowTypeFlex": true, "formFields": [{"span": 24, "childList": [{"label": "距统计当日（天）", "classify": "basic", "type": "input", "columnName": "metricTime", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "不能为空", "trigger": "blur"}, {"message": "请输入正确的天数", "pattern": "^(?:0|(?:-?[1-9]\\d*))$", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": "^(?:0|(?:-?[1-9]\\d*))$"}]}], "name": "layout", "labelWidth": 160, "rowGutter": true}, {"label": "订单量<=", "classify": "basic", "type": "input", "columnName": "metricValue", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "订单量不能为空", "rules": [{"required": true, "message": "订单量不能为空", "trigger": "blur"}, {"message": "请填写不超过100,000的自然数", "pattern": "^[1-9]\\d{0,4}$|^100000$|^0$", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": "", "numberTypeRequired": false}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1710235726457", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "", "label": "保存", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/shopClearUpdate", "class": "solid-with-icon-btn", "default": "hidden", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": true, "confirmStatus": ["qy"], "bpmButtonList": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": "", "confirmationSwitch": true, "confirmationMessage": "保存后第二日，该指标将作为下发清退预警的关键指标，是否确认保存？", "qy": "show", "ty": "hidden"}, {"is-table-column": false, "columnName": "submits", "label": "保存", "type": "primary", "class": "solid-with-icon-btn", "icon": "", "bpmFlowImageCloumnName": "", "default": "hidden", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/shopClearUpdate", "apiCloseDialogWithResposne": true, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "clearedit": "", "qy": "hidden", "ty": "show", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "activeType": "button_group_form_interface", "apiParam": {}, "apiIsRefresh": "", "apiIsReturn": ""}], "name": "layout", "labelWidth": 160}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "clearedit", "defaultClass": "webbas", "size": "small", "statusList": ["clearedit", "qy", "ty"], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "清退指标编辑", "isOpenExportPDFSwitch": false, "exportPDFProps": {"eleId": "", "eleClassName": "", "markProps": {"pdfFileName": "", "markTitle": "", "markImgUrl": "", "imgFileName": "", "opacity": 100, "imgUp": false, "pStyle": {"font-size": "44px", "margin": "40px 0px", "display": "flex", "justify-content": "center"}}}}, "dataConfig": {}, "virtual_model": {}, "model": {"text": "", "metricValue": "", "metricTime": ""}}