/**
* 业务实体
*/
<template>
  <div style="height: 100%; ">
    <asp-smart-table ref="aspSmartTable"
                     v-model="model"
                     :table-json="tableJson"
                     :size-change="sizeChange"
                     :current-change="currentChange"
                     :before-http="beforeHttp"
                     :after-http="afterHttp"
                     :before-button="beforeButton"
                     :before-router="beforeRouter"
                     :before-table-row-render="beforeTableRowRender"
                     :before-table-render="beforeTableRender"
                     :before-click-table-cell="beforeClickTableCell"
                     :dialog-config="dialogConfig"
                     :render-table="renderTable"
                     @on="onbind">
      <template slot="selectAllData">
        <div class="slotDiv"  style="margin-top: -13px;">
          <p>当满足&nbsp;
          <el-select v-model="selectValue" style="width: 100px;" :disabled="isEdit" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>&nbsp;启用条件，被判定为活跃厅店</p>
          <el-button class="solid-with-icon-btn editBtn" type="primary" @click="edit" v-if="isEdit">编辑</el-button>
          <div v-else>
            <el-button class="solid-with-icon-btn editBtn" type="primary" @click="save">保存</el-button>
            <el-button class="hollow-with-icon-btn editBtn" type="primary" @click="cancel(selectValue)">取消</el-button>
          </div>
        </div>
      </template>
      <template slot="clearslot">
        <div class="slotDiv">
          <div>当满足&nbsp;
            <el-select v-model="selectClearValue" style="width: 100px;" :disabled="isClearEdit" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div style="display: flex;">
            <p>&nbsp;启用条件，下发清退预警。预警后&nbsp;<el-input v-model="checkday" :disabled="isClearEdit"></el-input>&nbsp;天未修改，执行清退</p>
          </div>
          <el-button class="solid-with-icon-btn editBtn" type="primary" @click="clearEdit" v-if="isClearEdit">编辑</el-button>
          <div v-else>
            <el-button class="solid-with-icon-btn editBtn" type="primary" @click="clearSave">保存</el-button>
            <el-button class="hollow-with-icon-btn editBtn" type="primary" @click="clearCancel(selectClearValue)">取消</el-button>
          </div>
        </div>
      </template>
    </asp-smart-table>
  </div>
</template>

<script>
// import reportnum from './reportnum.vue'
export default {
  name: 'shopoverview',
  // components: {
  //   reportnum
  // },
  data () {
    return {
      checkday: '',
      checkdays: '',
      isEdit: true,
      isClearEdit: true,
      options: [{
        value: 'and',
        label: '以下全部'
      }, {
        value: 'or',
        label: '至少一项'
      }],
      selectValue: '',
      selectValues: '',
      selectClearValue: '',
      selectClearValues: '',
      tableJson: null,
      model: {},
      testData: [],
      renderTable: {
        xxdy: (h, item, scope, tableData) => { // xxdy是列名columnName, 请根据实际情况修改
          return (<span style="color: red; background: #f2dede;">{scope.row[item.columnName]}</span>)
        }
      },
      rowId: '',
      dialogStatus: 'edit',
      dialogConfig: {
        activeEdit: {
          _this: this, // 获取当前弹窗所在的表格页面的this对象；使用方式this._this
          name: 'activeEdit',
          mounted: async ({ $_dialogData }) => {
            const modelHtml = JSON.parse(
              JSON.stringify(require('./activeEdit.json'))
            ) // 修改模板名称
            let modelData1 = modelHtml.model
            if ($_dialogData.PreTableRow) {
              // 如果带表单几个数据过来
              // 表单所在行所有数据 $_dialogData.PreTableRow
              modelData1 = {
                ...modelHtml.data,
                ...$_dialogData.PreTableRow
              }
              if (modelData1.monthValue) {
                modelData1.monthValue = modelData1.monthValue.toString()
              }
              if (modelData1.dayValue) {
                modelData1.dayValue = modelData1.dayValue.toString()
              }
            }
            modelData1.status = modelData1.status + ''
            const modelData = modelData1
            const oldModel = $_dialogData.PreTableRow || {}
            if (modelData.metricCode === 'money') {
              modelHtml.formList[0].label = modelData.metricName
            } else if (modelData.metricCode === 'order') {
              modelHtml.formList[0].label = modelData.metricName
            } else if (modelData.metricCode === 'pv') {
              modelHtml.formList[0].label = modelData.metricName
            } else if (modelData.metricCode === 'uv') {
              modelHtml.formList[0].label = modelData.metricName
            }
            if (modelData.status === '0') {
              this.dialogStatus = 'ty'
            } else if (modelData.status === '1') {
              this.dialogStatus = 'qy'
            }
            return {
              modelHtml,
              modelData,
              oldModel,
              status: this.dialogStatus
            }
          },
          // 数据安装后执行函数
          setup (data) {
          },
          onbind (data) {
          },
          beforeHttpPro ({ item, parent, index, model }, httpObject, callback) {
            // 活跃度指标编辑
            if (item.columnName === 'activeSubmit') {
              delete httpObject.httpBody.dayValueShow
              delete httpObject.httpBody.monthValueShow
            } else if (item.columnName === 'activeSubmits') {
              delete httpObject.httpBody.dayValueShow
              delete httpObject.httpBody.monthValueShow
            }
            callback(httpObject)
          },
          afterHttpPro (data, responseBody, callback) {
            // 活跃度指标编辑后 刷新列表
            if (data.item.columnName === 'activeSubmit') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('修改成功')
                : this._this.$message.error(responseBody.message || '修改失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'activeSubmits') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('修改成功')
                : this._this.$message.error(responseBody.message || '修改失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'createTab' && '' + responseBody.status === '200') {
              data.$_this.$refs.smartForm.asp_updateModel({
                createdTable: '1',
                createdTableName: '是'
              })
              data.$_this.$refs.smartForm.asp_setHidden('buttonGroup_create', true)
            }
            callback(responseBody)
          },
          beforeLoadingHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          beforeBpmHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterBpmHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          compDataChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          compDataActiveChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            let isContinue = true
            if (data.index === 0) {
              this._this.$message.error('主键不允许删除')
              isContinue = false
            }
            callback(isContinue, data.rowData)
          },
          afterButtonPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, rowData)
          },
          beforeAuthPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, isContinue)
          },
          beforeRouterPro ({ item, row, routerObj, next }) { next(routerObj) },
          beforeColumnValidatePro ({ tableItem, model, value, type }, callback) {
            const isContinue = true
            const hasContinue = false
            callback(isContinue, hasContinue, undefined)
          },
          beforeDrawTableRowPro ({ item, model, row, rowClassName }, callback) { callback(rowClassName) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        },
        clearedit: {
          _this: this, // 获取当前弹窗所在的表格页面的this对象；使用方式this._this
          name: 'clearedit',
          mounted: async ({ $_dialogData }) => {
            const modelHtml = JSON.parse(
              JSON.stringify(require('./clearedit.json'))
            ) // 修改模板名称
            let modelData1 = modelHtml.model
            if ($_dialogData.PreTableRow) {
              // 如果带表单几个数据过来
              // 表单所在行所有数据 $_dialogData.PreTableRow
              modelData1 = {
                ...modelHtml.data,
                ...$_dialogData.PreTableRow
              }
            }
            modelData1.status = modelData1.status + ''
            delete modelData1.metricValueShow
            const modelData = modelData1
            const oldModel = $_dialogData.PreTableRow || {}
            if (modelData.metricCode === 'money') {
              modelHtml.formList[1].label = modelData.metricName + '<='
            } else if (modelData.metricCode === 'order') {
              modelHtml.formList[1].label = modelData.metricName + '<='
            } else if (modelData.metricCode === 'pv') {
              modelHtml.formList[1].label = modelData.metricName + '<='
            } else if (modelData.metricCode === 'uv') {
              modelHtml.formList[1].label = modelData.metricName + '<='
            }
            if (modelData.status === '0') {
              this.dialogStatus = 'ty'
            } else if (modelData.status === '1') {
              this.dialogStatus = 'qy'
            }
            return {
              modelHtml,
              modelData,
              oldModel,
              status: this.dialogStatus
            }
          },
          // 数据安装后执行函数
          setup (data) {
          },
          onbind (data) {
          },
          beforeHttpPro ({ item, parent, index, model }, httpObject, callback) {
            callback(httpObject)
          },
          afterHttpPro (data, responseBody, callback) {
            // 活跃度指标编辑后 刷新列表
            if (data.item.columnName === 'submit') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('修改成功')
                : this._this.$message.error(responseBody.message || '修改失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'submits') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('修改成功')
                : this._this.$message.error(responseBody.message || '修改失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'createTab' && '' + responseBody.status === '200') {
              data.$_this.$refs.smartForm.asp_updateModel({
                createdTable: '1',
                createdTableName: '是'
              })
              data.$_this.$refs.smartForm.asp_setHidden('buttonGroup_create', true)
            }
            callback(responseBody)
          },
          beforeLoadingHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          beforeBpmHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterBpmHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          compDataChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          compDataActiveChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            let isContinue = true
            if (data.index === 0) {
              this._this.$message.error('主键不允许删除')
              isContinue = false
            }
            callback(isContinue, data.rowData)
          },
          afterButtonPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, rowData)
          },
          beforeAuthPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, isContinue)
          },
          beforeRouterPro ({ item, row, routerObj, next }) { next(routerObj) },
          beforeColumnValidatePro ({ tableItem, model, value, type }, callback) {
            const isContinue = true
            const hasContinue = false
            callback(isContinue, hasContinue, undefined)
          },
          beforeDrawTableRowPro ({ item, model, row, rowClassName }, callback) { callback(rowClassName) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        },
        // 活跃度指标 --- 操作记录
        activeoperate: {
          _this: this, // 获取当前弹窗所在的表格页面的this对象；使用方式this._this
          name: 'activeoperate',
          mounted: async ({ $_dialogData }) => {
            const modelHtml = JSON.parse(
              JSON.stringify(require('./activeoperate.json'))
            ) // 修改模板名称
            let modelData1 = modelHtml.model
            if ($_dialogData.PreTableRow) {
              // 如果带表单几个数据过来
              // 表单所在行所有数据 $_dialogData.PreTableRow
              modelData1 = {
                ...modelHtml.data,
                ...$_dialogData.PreTableRow
              }
            }
            delete modelData1.dayValueShow
            delete modelData1.monthValueShow
            const modelData = modelData1
            const oldModel = $_dialogData.PreTableRow || {}
            return {
              modelHtml,
              model: modelData,
              oldModel,
              status: 'operate',
              tableJson: modelHtml
            }
          },
          // 数据安装后执行函数
          setup (data) {
          },
          onbind (data) {
          },
          beforeHttpPro ({ item, parent, index, model }, httpObject, callback) {
            callback(httpObject)
          },
          afterHttpPro (data, responseBody, callback) {
            // 活跃度指标编辑后 刷新列表
            if (data.item.columnName === 'activeSubmit') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'activeSubmits') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'createTab' && '' + responseBody.status === '200') {
              data.$_this.$refs.smartForm.asp_updateModel({
                createdTable: '1',
                createdTableName: '是'
              })
              data.$_this.$refs.smartForm.asp_setHidden('buttonGroup_create', true)
            }
            callback(responseBody)
          },
          beforeLoadingHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          beforeBpmHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterBpmHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          compDataChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          compDataActiveChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            let isContinue = true
            if (data.index === 0) {
              this._this.$message.error('主键不允许删除')
              isContinue = false
            }
            callback(isContinue, data.rowData)
          },
          afterButtonPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, rowData)
          },
          beforeAuthPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, isContinue)
          },
          beforeRouterPro ({ item, row, routerObj, next }) { next(routerObj) },
          beforeColumnValidatePro ({ tableItem, model, value, type }, callback) {
            const isContinue = true
            const hasContinue = false
            callback(isContinue, hasContinue, undefined)
          },
          beforeDrawTableRowPro ({ item, model, row, rowClassName }, callback) { callback(rowClassName) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        },
        // 清退指标 --- 操作记录
        clearoperate: {
          _this: this, // 获取当前弹窗所在的表格页面的this对象；使用方式this._this
          name: 'clearoperate',
          mounted: async ({ $_dialogData }) => {
            const modelHtml = JSON.parse(
              JSON.stringify(require('./clearoperate.json'))
            ) // 修改模板名称
            let modelData1 = modelHtml.model
            if ($_dialogData.PreTableRow) {
              // 如果带表单几个数据过来
              // 表单所在行所有数据 $_dialogData.PreTableRow
              modelData1 = {
                ...modelHtml.data,
                ...$_dialogData.PreTableRow
              }
            }
            delete modelData1.metricValueShow
            const modelData = modelData1
            const oldModel = $_dialogData.PreTableRow || {}
            return {
              modelHtml,
              model: modelData,
              oldModel,
              status: 'operate',
              tableJson: modelHtml
            }
          },
          // 数据安装后执行函数
          setup (data) {
          },
          onbind (data) {
          },
          beforeHttpPro ({ item, parent, index, model }, httpObject, callback) {
            callback(httpObject)
          },
          afterHttpPro (data, responseBody, callback) {
            // 活跃度指标编辑后 刷新列表
            if (data.item.columnName === 'activeSubmit') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'activeSubmits') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'createTab' && '' + responseBody.status === '200') {
              data.$_this.$refs.smartForm.asp_updateModel({
                createdTable: '1',
                createdTableName: '是'
              })
              data.$_this.$refs.smartForm.asp_setHidden('buttonGroup_create', true)
            }
            callback(responseBody)
          },
          beforeLoadingHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          beforeBpmHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterBpmHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          compDataChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          compDataActiveChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            let isContinue = true
            if (data.index === 0) {
              this._this.$message.error('主键不允许删除')
              isContinue = false
            }
            callback(isContinue, data.rowData)
          },
          afterButtonPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, rowData)
          },
          beforeAuthPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, isContinue)
          },
          beforeRouterPro ({ item, row, routerObj, next }) { next(routerObj) },
          beforeColumnValidatePro ({ tableItem, model, value, type }, callback) {
            const isContinue = true
            const hasContinue = false
            callback(isContinue, hasContinue, undefined)
          },
          beforeDrawTableRowPro ({ item, model, row, rowClassName }, callback) { callback(rowClassName) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        },
        // 异常指标 --- 操作记录
        anomalyoperate: {
          _this: this, // 获取当前弹窗所在的表格页面的this对象；使用方式this._this
          name: 'anomalyoperate',
          mounted: async ({ $_dialogData }) => {
            const modelHtml = JSON.parse(
              JSON.stringify(require('./anomalyoperate.json'))
            ) // 修改模板名称
            let modelData1 = modelHtml.model
            if ($_dialogData.PreTableRow) {
              // 如果带表单几个数据过来
              // 表单所在行所有数据 $_dialogData.PreTableRow
              modelData1 = {
                ...modelHtml.data,
                ...$_dialogData.PreTableRow
              }
            }
            delete modelData1.metricShow
            delete modelData1.metricComparators
            const modelData = modelData1
            const oldModel = $_dialogData.PreTableRow || {}
            return {
              modelHtml,
              model: modelData,
              oldModel,
              status: 'operate',
              tableJson: modelHtml
            }
          },
          // 数据安装后执行函数
          setup (data) {
          },
          onbind (data) {
          },
          beforeHttpPro ({ item, parent, index, model }, httpObject, callback) {
            callback(httpObject)
          },
          afterHttpPro (data, responseBody, callback) {
            // 活跃度指标编辑后 刷新列表
            if (data.item.columnName === 'activeSubmit') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'activeSubmits') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'createTab' && '' + responseBody.status === '200') {
              data.$_this.$refs.smartForm.asp_updateModel({
                createdTable: '1',
                createdTableName: '是'
              })
              data.$_this.$refs.smartForm.asp_setHidden('buttonGroup_create', true)
            }
            callback(responseBody)
          },
          beforeLoadingHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          beforeBpmHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterBpmHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          compDataChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          compDataActiveChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            let isContinue = true
            if (data.index === 0) {
              this._this.$message.error('主键不允许删除')
              isContinue = false
            }
            callback(isContinue, data.rowData)
          },
          afterButtonPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, rowData)
          },
          beforeAuthPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, isContinue)
          },
          beforeRouterPro ({ item, row, routerObj, next }) { next(routerObj) },
          beforeColumnValidatePro ({ tableItem, model, value, type }, callback) {
            const isContinue = true
            const hasContinue = false
            callback(isContinue, hasContinue, undefined)
          },
          beforeDrawTableRowPro ({ item, model, row, rowClassName }, callback) { callback(rowClassName) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        },
        exception: {
          _this: this, // 获取当前弹窗所在的表格页面的this对象；使用方式this._this
          name: 'exception',
          mounted: async ({ $_dialogData }) => {
            const modelHtml = JSON.parse(
              JSON.stringify(require('./exception.json'))
            ) // 修改模板名称
            let modelData1 = modelHtml.model
            if ($_dialogData.PreTableRow) {
              // 如果带表单几个数据过来
              // 表单所在行所有数据 $_dialogData.PreTableRow
              modelData1 = {
                ...modelHtml.data,
                ...$_dialogData.PreTableRow
              }
            }
            const modelData = modelData1
            const oldModel = $_dialogData.PreTableRow || {}
            return {
              model: modelData,
              tableJson: modelHtml,
              oldModel,
              status: 'exception'
            }
          },
          // 数据安装后执行函数
          setup (data) {
          },
          onbind (data) {
          },
          beforeHttpPro ({ item, parent, index, model }, httpObject, callback) {
            callback(httpObject)
          },
          afterHttpPro (data, responseBody, callback) {
            // 活跃度指标编辑后 刷新列表
            if (data.item.columnName === 'activeSubmit') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'activeSubmits') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'createTab' && '' + responseBody.status === '200') {
              data.$_this.$refs.smartForm.asp_updateModel({
                createdTable: '1',
                createdTableName: '是'
              })
              data.$_this.$refs.smartForm.asp_setHidden('buttonGroup_create', true)
            }
            callback(responseBody)
          },
          beforeLoadingHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          beforeBpmHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterBpmHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          compDataChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          compDataActiveChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            let isContinue = true
            if (data.index === 0) {
              this._this.$message.error('主键不允许删除')
              isContinue = false
            }
            callback(isContinue, data.rowData)
          },
          afterButtonPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, rowData)
          },
          beforeAuthPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, isContinue)
          },
          beforeRouterPro ({ item, row, routerObj, next }) { next(routerObj) },
          beforeColumnValidatePro ({ tableItem, model, value, type }, callback) {
            const isContinue = true
            const hasContinue = false
            callback(isContinue, hasContinue, undefined)
          },
          beforeDrawTableRowPro ({ item, model, row, rowClassName }, callback) { callback(rowClassName) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        },
        addanomaly: {
          _this: this, // 获取当前弹窗所在的表格页面的this对象；使用方式this._this
          name: 'addanomaly',
          mounted: async ({ $_dialogData }) => {
            const modelHtml = JSON.parse(
              JSON.stringify(require('./addanomaly.json'))
            ) // 修改模板名称
            let modelData1 = modelHtml.model
            if ($_dialogData.PreTableRow) {
              // 如果带表单几个数据过来
              // 表单所在行所有数据 $_dialogData.PreTableRow
              modelData1 = {
                ...modelHtml.data,
                ...$_dialogData.PreTableRow
              }
            }
            const modelData = modelData1
            modelData.channelStatus = modelData.channelStatus.toString()
            modelData.metricTime = modelData.metricTime.toString()
            modelData.metricValue = modelData.metricValue.toString()
            if (modelData.status === 0) {
              this.dialogStatus = 'ty'
            } else if (modelData.status === 1) {
              this.dialogStatus = 'qy'
            } else {
              this.dialogStatus = 'adds'
            }
            const oldModel = $_dialogData.PreTableRow || {}
            return {
              modelHtml,
              modelData,
              oldModel,
              status: this.dialogStatus
            }
          },
          // 数据安装后执行函数
          setup (data) {
          },
          onbind (data) {
          },
          beforeHttpPro ({ item, parent, index, model }, httpObject, callback) {
            callback(httpObject)
          },
          afterHttpPro (data, responseBody, callback) {
            // 活跃度指标编辑后 刷新列表
            if (data.item.columnName === 'submit') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'edit' || data.item.columnName === 'edits') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('修改成功')
                : this._this.$message.error(responseBody.message || '修改失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'createTab' && '' + responseBody.status === '200') {
              data.$_this.$refs.smartForm.asp_updateModel({
                createdTable: '1',
                createdTableName: '是'
              })
              data.$_this.$refs.smartForm.asp_setHidden('buttonGroup_create', true)
            }
            callback(responseBody)
          },
          beforeLoadingHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          beforeBpmHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterBpmHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          compDataChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          compDataActiveChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            let isContinue = true
            if (data.index === 0) {
              this._this.$message.error('主键不允许删除')
              isContinue = false
            }
            callback(isContinue, data.rowData)
          },
          afterButtonPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, rowData)
          },
          beforeAuthPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, isContinue)
          },
          beforeRouterPro ({ item, row, routerObj, next }) { next(routerObj) },
          beforeColumnValidatePro ({ tableItem, model, value, type }, callback) {
            const isContinue = true
            const hasContinue = false
            callback(isContinue, hasContinue, undefined)
          },
          beforeDrawTableRowPro ({ item, model, row, rowClassName }, callback) { callback(rowClassName) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        },
        table: {
          _this: this, // 获取当前弹窗所在的表格页面的this对象；使用方式this._this
          name: 'table',
          mounted: async (data) => {
            const tableJson = JSON.parse(JSON.stringify(require('./indicatorconfig.json'))) // 修改模板名称
            const model = tableJson.model
            return {
              tableJson, model, status: 'create'
            }
          },
          setup (data) { }, // 数据安装后执行函数
          destroyed (item) { },
          onbind (item) {
          },
          beforeHttp ({ tableItem, params, httpMethod, row }) { },
          afterHttp ({ tableItem, responseBody }) { },
          beforeButton ({ item, rowObj, next }) { next(item, rowObj) },
          beforeRouter ({ item, row, routerObj, next }) { next(routerObj) },
          beforeTableRender ({ tableName, tableData, columnItem, scope }, callBack) { },
          beforeTableRowRender ({ item, tableData, row, rowClassName }) { return rowClassName },
          beforeClickTableCell ({ item, row, tableData }) { },
          sizeChange ({ tableItem, pageSize, next }) { next(true) },
          currentChange ({ tableItem, currentPage, next }) { next(true) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        }
      }
    }
  },
  async mounted () {
    this.tableJson = JSON.parse(JSON.stringify(require('./indicatorconfig.json')))
    this.model = this.tableJson.model
  },
  methods: {
    /**
     * 智能表格监听所有组件的交互事件操作：监听、捕捉事件响应
     * @param item 响应组件对象属性集（类型、组件Id，控件内元数属性），columnName每个组件单元的唯一码（组件单元Id）
     * @param type 事件类型（click/blur/onblur等）
     * @param index 当是表格组件时，返回组件的行号
     * @param model 查询区域表单数据模型
     * @param tableModel 表格组件数据模型
     * @param row 表格行数据
     * @param multipleSelection 表格多选数据（当出现列表复选时才有，包括跨页数据，整行数据）
     * @param sortProps 排序属性
     * @returns {Promise<void>}
     */
    edit() {
      this.isEdit = false
    },
    clearEdit() {
      this.isClearEdit = false
    },
    save() {
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/shop/systemManager/metricConfig/activeUpdateStatus',
        {
          metricCode: this.selectValue
        }
      ).then((res) => {
        if (res.status === '200') {
          this.$alert('保存成功，明日起生效', {
            confirmButtonText: '确定',
            center: true,
            callback: action => {
            }
          })
          this.isEdit = true
        } else {
          this.$message.error({
            message: res.message
          })
        }
      })
    },
    clearSave() {
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/shop/systemManager/metricConfig/shopClearUpdateStatus',
        {
          metricCode: this.selectClearValue,
          metricTime: this.checkday
        }
      ).then((res) => {
        if (res.status === '200') {
          this.$alert('保存成功，明日起生效', {
            confirmButtonText: '确定',
            center: true,
            callback: action => {
            }
          })
          this.isClearEdit = true
        } else {
          this.$message.error({
            message: res.message
          })
        }
      })
    },
    cancel() {
      this.isEdit = true
      this.selectValue = this.selectValues
    },
    clearCancel() {
      this.isClearEdit = true
      this.selectClearValue = this.selectClearValues
      this.checkday = this.checkdays
    },
    async onbind ({ item, type, index, model, tableModel, row, subFormSelectData, sortProps }) {
      if (item.columnName === 'tab_3') {
        this.$refs.aspSmartTable.asp_sendTableQuery('table_1703670248380')
      }
    },
    /**
     * 智能表格页面所有请求前的前置操作
     * 例如：修改请求参数、修改请求方式、修改请求URL、或者请求条件不满足不给发送请求
     * @param tableItem 组件对象属性集
     * @param params 请求参数body，数据格式如下(字段格式不一致的需要自行转换)如下:
     *                                         {
     *                                             page：1， // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                             rows: 10，// 分页属性(页大小)，数字类型 （不是分页接口，没有这个字段）
     *                                             .......   // 业务属性字段
     *                                          }
     * @param httpMethod.url 请求地址URL
     * @param httpMethod.type 请求方式，目前主要六种：'post+json', 'post+form', 'get'，'put+json'，'delete+json'，'patch+json'
     * @param row 当组件为表格并且是表格操作列触发的请求，此参数返回表格行数据，其它返回undefined
     */
    beforeHttp ({ tableItem, params, httpMethod, row }) {
    },
    /**
     * 智能表格页面所有请求后置操作
     * 例如：请求后的数据包体需要做二次处理
     * @param tableItem 组件对象属性集
     * @param responseBody 响应数据body, 数据包格式(字段格式不一致的需要自行转换)如下：
     *                                              {
     *                                                status: "200", // 业务状态码，字符串类型，成功返回"200"，失败返回其它数据
     *                                                message: "",   // 业务提示语，字符串类型，给业务的提示语属性
     *                                                page：1，      // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                                total: 53，    // 分页属性(总记录大小)，数字类型 （不是分页接口，没有这个字段）
     *                                                data: {}或者[] // 业务数据区，对象或数组类型，用于各业务逻辑处理
     *                                               }
     */
    afterHttp ({ tableItem, responseBody }) {
      // 活跃度
      if (tableItem.columnName === 'dynamicTable_1712456519669') {
        this.selectValues = responseBody.otherData.metricCode
        this.selectValue = this.selectValues
      }
      // 清退
      if (tableItem.columnName === 'table_1703667971697') {
        this.selectClearValues = responseBody.otherData.metricCode
        this.selectClearValue = this.selectClearValues
        this.checkdays = responseBody.otherData.metricTime
        this.checkday = this.checkdays
      }
    },
    /**
     * 智能表格页面上的按钮的前置操作：包括不限于查询区域，表格顶部、表格操作列
     * 例如：对操作按钮进行处理的数据进行预处理，或者对按钮请求进行个性胡逻辑判断等
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param item 组件对象属性集
     * @param rowObj 当组件为表格操作列中的按钮，此参数返回表格行数据，其它返回undefined
     * @param next 回调函数
     */
    beforeButton ({ item, rowObj, next }) {
      next(item, rowObj) // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 智能表格页面路由跳转的前置操作
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param item 响应组件对象属性集
     * @param row 当响应组件为表格操作列中的按钮时，此参数返回表格行数据，其它返回undefined
     * @param routerObj.routerType: 路由类型
     * @param routerObj.routerParamType 路由参数类型
     * @param routerObj.routerUrl 路由地址或名称
     * @param routerObj.routerParamValue 路由参数
     * @param next 回调函数
     */
    beforeRouter ({ item, row, routerObj, next }) {
      next(routerObj) // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 表格内容渲染之前的前置动作，
     * @param tableName 当前表格名称
     * @param tableData 表格当页的数据
     * @param columnItem 表格当前列的信息
     * @param scope 表格行信息包含属性 $index row等
     * @param callback 回调事件，用于改变指定列的显示内容
     * @param callback 参数说明如下
     * 参数一：指定修改的表格名称 tableName
     * 参数二：指定修改的列名 columnName
     * 参数三：指定索引集合，整列生效则传空数组[],指定某几行生效则传索引集合[1,3] indexList
     * 参数四：显示内容{ content: 可以是文本也可以是html代码片段}
     * 示例：callBack('aspSmartTable', 'name', [], { content: `【附加标题】<a @click="detial(${scope.row})">${scope.row.name}</a>` })
     */
    beforeTableRender ({ tableName, tableData, columnItem, scope }, callBack) { },
    /**
     * 智能表格监听所有行绘制的前置回调响应
     * @param item 组件对象属性集(类型、组件columnName，组件内元数属性)，columnName是每个组件的唯一标识码
     * @param tableData 表格数据模型
     * @param row:  表格组件当前绘制的行数据
     * @param rowClassName: 子表单组件当前行绘制class name
     * @param callback: 回调api
     * @param           callback回调api参数: rowClassName: 子表单组件当前行绘制class name
     */
    beforeTableRowRender ({ item, tableData, row, rowClassName }) {
      return rowClassName // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 智能表格单元格点击的前置操作
     * @param item 响应组件对象属性集
     * @param row 此参数返回表格行数据
     * @param tableData: 表格数据模型
     */
    beforeClickTableCell ({ item, row, tableData }) { },
    /**
     * 表格页码大小发生变化时触发的前置事件
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param pageSize 表格页码大小
     * @param next 回调函数
     */
    sizeChange ({ tableItem, pageSize, next }) {
      next(true) // 允许继续运行传true, 否则传false  // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 表格当前页发生变化时触发的前置事件，包括点翻页、上一页、下一页、刷新页、重置页
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param currentPage 当前页码号
     * @param next 回调函数
     */
    currentChange ({ tableItem, currentPage, next }) {
      next(true) // 允许继续运行传true, 否则传false // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-input {
  width: 80px;
}
.slotDiv{
  display: flex;
  align-items: center;
  .editBtn{
    margin-left: 5px;
  }
}
::v-deep .create-tab-css {
  margin: 4px 4px;
}
.asp-pagination {
  background-color: #fff;
}
::v-deep .asp-smart-table .widget-box-button{
  padding-top: 0 !important;
}
.date{
  font-size: 13px;
  color: #C0C2C6;
}
</style>
