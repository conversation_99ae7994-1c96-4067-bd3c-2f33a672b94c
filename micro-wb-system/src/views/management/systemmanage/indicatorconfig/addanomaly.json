{"formList": [{"label": "异常类型：", "type": "select", "isLabelWidth": true, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "请选择异常类型", "rules": [{"required": true, "message": "请选择异常类型", "trigger": "blur"}], "columnName": "unusualTypeId", "targetName": "label_select_1712135538520", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "name", "option-value": "id", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/unusualTypeList", "apiType": "post+json", "apiParam": "", "separator": ","}, "options": [], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "showOptions": [], "labelWidth": 170, "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": "", "copyNewVal": "", "isHelpTipText": ""}, {"label": "店铺状态：", "type": "select", "isLabelWidth": true, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "请选择店铺状态", "rules": [{"required": true, "message": "请选择店铺状态", "trigger": "blur"}], "columnName": "channelStatus", "targetName": "label_select_1712135671669", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "正常", "value": "1"}, {"label": "冻结", "value": "2"}, {"label": "终止", "value": "3"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "showOptions": [{"label": "正常", "value": "1", "disabled": false}, {"label": "冻结", "value": "2", "disabled": false}, {"label": "终止", "value": "3", "disabled": false}], "labelWidth": 170, "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": "", "oldColumnName": "select_1712135671669", "copyNewVal": "", "isHelpTipText": ""}, {"label": "距统计当日（天）：", "classify": "basic", "type": "input", "columnName": "metricTime", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "请填写必要参数", "rules": [{"required": true, "message": "请填写必要参数", "trigger": "blur"}, {"message": "请输入正确的天数", "pattern": "^(?:0|(?:-?[1-9]\\d*))$", "trigger": "blur"}], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 170, "ruleType": "^(?:0|(?:-?[1-9]\\d*))$", "isHelpTipText": ""}, {"label": "栅格布局", "type": "row", "columnName": "row_1710473059910", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "classify": "layout", "rowTypeFlex": true, "formFields": [{"span": 9, "childList": [{"label": "", "type": "select", "isLabelWidth": true, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "请填写必填的指标", "rules": [{"required": true, "message": "请填写必填的指标", "trigger": "blur"}], "columnName": "metricCode", "targetName": "metricCode_copy", "defaultValue": "order", "defaultContent": "订单量", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "订单量", "value": "order"}, {"label": "销售额", "value": "money"}, {"label": "PV", "value": "pv"}, {"label": "UV", "value": "uv"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "order", "showOptions": [{"label": "订单量", "value": "order", "disabled": false}, {"label": "销售额", "value": "money", "disabled": false}, {"label": "PV", "value": "pv", "disabled": false}, {"label": "UV", "value": "uv", "disabled": false}], "labelWidth": 0, "isHelpTipText": "", "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": "", "copyOldVal": "", "statusList": []}]}, {"span": 6, "childList": [{"label": "", "type": "select", "isLabelWidth": true, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "请填写必填的指标", "rules": [{"required": true, "message": "请填写必填的指标", "trigger": "blur"}], "columnName": "metricComparators", "targetName": "label_select_1710473093135", "defaultValue": ">=", "defaultContent": ">=", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "=", "value": "="}, {"label": "<=", "value": "<="}, {"label": ">=", "value": ">="}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": ">=", "showOptions": [{"label": "=", "value": "=", "disabled": false}, {"label": "<=", "value": "<=", "disabled": false}, {"label": ">=", "value": ">=", "disabled": false}], "labelWidth": 0, "isHelpTipText": "", "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": "", "copyOldVal": "", "statusList": []}]}, {"span": 9, "childList": [{"label": "", "classify": "basic", "type": "input", "columnName": "metricValue", "defaultValue": "1", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "请填写必填指标", "rules": [{"required": true, "message": "请填写必填指标", "trigger": "blur"}, {"message": "请填写不超过100,000的自然数", "pattern": "^[1-9]\\d{0,4}$|^100000$|^0$", "trigger": "blur"}], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": "1"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "1", "labelWidth": 0, "ruleType": "", "isHelpTipText": "", "numberTypeRequired": true, "copyOldVal": "", "statusList": []}]}], "name": "layout", "labelWidth": 160}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1710473533245", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "", "label": "保存", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/unusualAdd", "class": "solid-with-icon-btn", "default": "hidden", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": true, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": "", "adds": "show", "edits": "hidden", "qy": "hidden", "ty": "hidden"}, {"is-table-column": false, "columnName": "edit", "label": "修改", "type": "primary", "class": "solid-with-icon-btn", "icon": "", "bpmFlowImageCloumnName": "", "default": "hidden", "authSwitch": false, "authId": "", "confirmationSwitch": true, "confirmationMessage": "保存后第二日，该指标将作为判定异常厅店的关键指标，是否确认保存？", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/unusualUpdate", "apiCloseDialogWithResposne": true, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "activeType": "button_group_form_interface", "apiParam": {}, "apiIsRefresh": "", "apiIsReturn": "", "adds": "hidden", "edits": "hidden", "qy": "show", "ty": "hidden"}, {"is-table-column": false, "columnName": "edits", "label": "修改", "type": "primary", "class": "solid-with-icon-btn", "icon": "", "bpmFlowImageCloumnName": "", "default": "hidden", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/metricConfig/unusualUpdate", "apiCloseDialogWithResposne": true, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "adds": "hidden", "edits": "hidden", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "activeType": "button_group_form_interface", "apiParam": {}, "apiIsRefresh": "", "apiIsReturn": "", "qy": "hidden", "ty": "show"}], "name": "layout", "labelWidth": 160}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "addanomaly", "defaultClass": "webbas", "size": "small", "statusList": ["adds", "edits", "qy", "ty"], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "isOpenExportPDFSwitch": false, "exportPDFProps": {"eleId": "", "eleClassName": "", "markProps": {"pdfFileName": "", "markTitle": "", "markImgUrl": "", "imgFileName": "", "opacity": 100, "imgUp": false, "pStyle": {"font-size": "44px", "margin": "40px 0px", "display": "flex", "justify-content": "center"}}}, "titleName": "新增异常指标"}, "dataConfig": {}, "virtual_model": {}, "model": {"select_1710473088815": "", "select_1712135671669": "", "label_select_1712135671669": "终止", "channelStatus": "", "metricTime": "", "unusualTypeId": "", "metricComparators": ">=", "label_select_1710473093135": ">=", "metricValue": "1", "metricCode": "order", "metricCode_copy": "订单量"}}