{"list": [{"type": "empty", "label": "空容器", "columnName": "empty_1710743648077", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "table", "label": "表格", "columnName": "table_1710743665794", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [{"timeStamp": 1710743682780, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true}, {"label": "操作时间", "columnName": "logTime", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "align": "center"}, {"label": "操作用户", "columnName": "operateUser", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "align": "center"}, {"label": "操作内容", "columnName": "operateShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "align": "center"}], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/systemManager/contactManage/log"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [], "hasParentForm": false, "labelWidth": 100, "router": {}}], "classify": "empty", "hidden": false}], "model": {}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "mini", "class": "", "layoutType": "flex", "statusList": ["operate"], "localProxy": "", "nigxProxy": "", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": ""}}}