{"formList": [{"label": "省份：", "type": "select", "isLabelWidth": false, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "请选择省份", "rules": [{"required": true, "message": "请选择省份", "trigger": "blur"}], "columnName": "provinceCode", "targetName": "label_select_1710728964567", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "provinceName", "option-value": "provinceCode", "option-alias": "", "option-disabled": "", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "text": "", "wordLimitStyle": "transparent"}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/shop/common/regionInfo", "apiType": "post+json", "apiParam": "", "separator": ","}, "options": [], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "", "showOptions": [], "labelWidth": 160, "isHelpTipText": "", "filterOptionStatus": [], "disabledOptionStatus": []}, {"label": "接口人姓名：", "classify": "basic", "type": "input", "columnName": "name", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "请填写接口人姓名", "rules": [{"required": true, "message": "请填写接口人姓名", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "姓名", "maxlength": "15", "show-word-limit": true, "clearable": true, "disabled": false, "readonly": false, "minlength": "1"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "numberTypeRequired": false}, {"label": "接口人手机号：", "classify": "basic", "type": "input", "columnName": "phone", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "请填写接口人手机号", "rules": [{"required": true, "message": "请填写接口人手机号", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "手机号", "maxlength": "11", "show-word-limit": true, "clearable": true, "disabled": false, "readonly": false, "minlength": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160}, {"label": "接口人邮箱：", "classify": "basic", "type": "input", "columnName": "email", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "请填写接口人邮箱", "rules": [{"required": true, "message": "请填写接口人邮箱", "trigger": "blur"}, {"message": "邮箱不能超过255个字符", "pattern": "^.{1,255}$", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "邮箱", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "copyOldVal": "邮箱", "statusList": [], "isHelpTipText": ""}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1710729260160", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "", "label": "提交", "interactive": "button_group_submit_all_validate", "validateProp": [], "apiName": "/yundian/osm/api/shop/systemManager/contactManage/add", "class": "solid-with-icon-btn", "default": "hidden", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": [], "exportName": ""}, "apiCloseDialogWithResposne": true, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {"provinceCode": "$provinceCode$", "name": "$name$", "phone": "$phone$", "email": "$email$"}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": "", "add": "show", "edit": "hidden"}, {"is-table-column": false, "columnName": "edit", "label": "修改", "type": "primary", "class": "solid-with-icon-btn", "icon": "", "bpmFlowImageCloumnName": "", "default": "hidden", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/systemManager/contactManage/update", "apiCloseDialogWithResposne": true, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "add": "hidden", "edit": "show", "activeType": "button_group_form_interface", "apiParam": {}, "apiIsRefresh": "", "apiIsReturn": ""}], "name": "layout", "labelWidth": 160}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "", "defaultClass": "webbas", "size": "small", "statusList": ["add", "edit"], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}}, "dataConfig": {}, "virtual_model": {}, "model": {"provinceCode": "", "name": "", "phone": "", "email": ""}}