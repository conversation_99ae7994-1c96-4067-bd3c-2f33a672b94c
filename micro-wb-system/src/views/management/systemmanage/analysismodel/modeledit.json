{"formList": [{"label": "模型名称：", "classify": "basic", "type": "input", "columnName": "name", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "模型名称不能为空", "rules": [{"required": true, "message": "模型名称不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "10", "show-word-limit": true, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent", "controls": true}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": ""}, {"label": "应用场景：", "type": "textarea", "columnName": "desc", "classify": "basic", "defaultValue": "", "isModel": true, "required": true, "requiredCutomizeTips": "应用场景不能为空", "rules": [{"required": true, "message": "应用场景不能为空", "trigger": "blur"}], "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": "", "shortcut": false, "shortcutOptions": [{"label": "option1", "value": "0"}], "class": "", "operation": [], "hidden": false, "props": {"placeholder": "", "maxlength": "35", "show-word-limit": true, "wordLimitStyle": "transparent", "clearable": true, "disabled": false, "readonly": false, "rows": 2, "controls": true, "text": ""}, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": ""}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1711362309772", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "", "label": "确定", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/yundian/osm/api/shop/systemManager/model/update", "class": "solid-with-icon-btn", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": ""}], "name": "layout", "labelWidth": 160}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "modeledit", "defaultClass": "webbas", "size": "small", "statusList": [], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "isOpenExportPDFSwitch": false, "exportPDFProps": {"eleId": "", "eleClassName": "", "markProps": {"pdfFileName": "", "markTitle": "", "markImgUrl": "", "imgFileName": "", "opacity": 100, "imgUp": false, "pStyle": {"font-size": "44px", "margin": "40px 0px", "display": "flex", "justify-content": "center"}}}, "titleName": "分析模型管理-编辑"}, "dataConfig": {}, "virtual_model": {}, "model": {"input_1711362237103": "", "name": "", "desc": ""}}