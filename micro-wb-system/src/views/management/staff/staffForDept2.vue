/** * 机构成员管理 */
<template>
  <div class="webbas">
    <div class="list-page-content-css">
      <div class="query-area-content-css">
        <el-form ref="searchForm" :inline="true" :model="table.searchForm">
          <el-row>
            <el-col :span="6">
              <el-form-item prop="keyword" label="关键字：">
                <el-input
                  v-model.trim="table.searchForm.keyword"
                  placeholder="请输入用户名/真实姓名"
                  name="keyword"
                  @keyup.enter.native="search()"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="!departmentIdwindow">
              <el-form-item label="归属区域：" prop="departmentName">
                <div class="icon-absolute-css">
                  <i
                    class="el-select__caret el-input__icon el-icon-circle-close"
                    @click="clearDeptName"
                  ></i>
                </div>
                <el-popover placement="bottom" width="160">
                  <div class="popover-tree">
                    <el-tree
                      :data="departmentTreeData"
                      :props="defaultProps"
                      :expand-on-click-node="false"
                      @node-click="departTreeNodeClick"
                    ></el-tree>
                  </div>
                  <el-input
                    slot="reference"
                    v-model="table.searchForm.departmentName"
                    readonly
                    auto-complete="off"
                  ></el-input>
                </el-popover>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="6">
              <el-form-item prop="status" label="账号状态：">
                <asp-select-all
                  v-model="table.searchForm.status"
                  :code-list="statusList"
                ></asp-select-all>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="6">
              <el-form-item prop="status" label="用户角色：">
                <asp-select-all
                  v-model="table.searchForm.userRoles"
                  :code-list="userRolesList"
                ></asp-select-all>
              </el-form-item>
            </el-col> -->
            <el-col :span="6">
              <el-form-item prop="mobile" label="手机：">
                <el-input
                  v-model.trim="table.searchForm.mobile"
                  placeholder="请输入手机"
                  name="mobile"
                  @keyup.enter.native="search(true)"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!-- <el-col :span="6">
              <el-form-item prop="email" label="Email：">
                <el-input
                  v-model.trim="table.searchForm.email"
                  placeholder="请输入Email"
                  name="email"
                  @keyup.enter.native="search(true)"
                ></el-input>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="6">
              <el-form-item prop="role" label="用户角色：">
                <el-input
                  v-model.trim="table.searchForm.email"
                  placeholder="请输入Email"
                  name="email"
                  @keyup.enter.native="search(true)"
                ></el-input>
              </el-form-item>
            </el-col> -->
            <el-form-item class="query-area-btn-css">
              <asp-btn-solid
                v-loading="searchStatus"
                name="查询"
                icon="el-icon-search"
                @click="search()"
              ></asp-btn-solid>
              <asp-btn-hollow
                icon="el-icon-refresh"
                name="重置"
                @click="reset()"
              ></asp-btn-hollow>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <asp-table
        ref="table"
        :url="table.url"
        :param="table.searchForm"
        :isMountedTableData="false"
        :prefix="table.prefix"
        @tableSearchSuccess="tableSearchSuccess"
        type=""
      >
        <!-- <template slot="header">
          <asp-btn-solid
            v-hasAuth="doAuth({ btnCode: 'wb_100119' })"
            icon="el-icon-plus"
            name="新增"
            @click="addStaff"
          >
          </asp-btn-solid>
          <asp-btn-hollow
            v-hasAuth="doAuth({ btnCode: 'wb_100122' })"
            icon="el-icon-download"
            name="导出"
            @click="exportList"
          >
          </asp-btn-hollow>
        </template> -->
        <asp-table-column
          sort-key="userName"
          prop="userName"
          min-width="100"
          show-overflow-tooltip
          label="用户ID"
        >
        </asp-table-column>
        <asp-table-column
          sort-key="realName"
          prop="realName"
          width="105"
          label="姓名"
          show-overflow-tooltip
        >
        </asp-table-column>
        <!-- <asp-table-column
          sort-key="organizationName"
          prop="organizationName"
          width="105"
          label="归属机构"
          show-overflow-tooltip
        >
          <template slot-scope="{ scope }">
            <span
              v-if="isExist"
              class="staff-pointer"
              @click="searchDepartment(scope.row.organizationId, '')"
            >
              {{ scope.row.organizationName }}
            </span>
            <span v-else>
              {{ scope.row.organizationName }}
            </span>
          </template>
        </asp-table-column> -->
        <!-- <asp-table-column
          sort-key="organizationName"
          prop="organizationName"
          width="105"
          label="用户角色"
          show-overflow-tooltip
        >
          <template slot-scope="{ scope }">
            {{ scope.row.departmentId ? '省公司' : '集团' }}
          </template>
        </asp-table-column> -->
        <asp-table-column
          prop="departmentName"
          min-width="90"
          show-overflow-tooltip
          label="归属区域"
        >
          <template slot-scope="{ scope }">
            <span
              v-if="isExist"
              class="staff-pointer"
            >
              {{ scope.row.departmentName ? scope.row.departmentName : '全国' }}
            </span>
            <span v-else>
              {{ scope.row.departmentName ? scope.row.departmentName : '全国' }}
            </span>
          </template>
        </asp-table-column>
        <asp-table-column prop="roleNames" label="角色名称" min-width="110" show-overflow-tooltip>
        </asp-table-column>

        <asp-table-column prop="mobile" label="手机号" width="110">
        </asp-table-column>

        <asp-table-column
          prop="createDate"
          min-width="90"
          show-overflow-tooltip
          label="创建时间"
        >
          <template slot-scope="{ scope }">
            {{ formatTimer(scope.row.createDate) }}
          </template>
        </asp-table-column>
      </asp-table>
    </div>
  </div>
</template>
<script>
export default {
  name: 'StaffForDept',
  components: {
  },
  data() {
    return {
      departmentIdwindow: '',
      table: {
        prefix: this.$apiConfig.level1cloudstorePathPreFix,
        url: '/shop/orgUser/listPage',
        searchForm: {
          order: 'asc', // 顺序/倒序排列
          sortName: 'userName', // 排序名称
          keyword: '',
          status: '',
          organizationId: '',
          departmentId: '',
          departmentName: '',
          mobile: '',
          email: '',
          userRoles: ''
        }
      },
      count: 0, // 计算显示详情的次数
      userDomain: '', // 当前登录用所属域
      organizationTreeData: [], // 组织列表
      searchStatus: false, // 点击查询加载按钮
      deleteStaffStatus: false, // 点击删除加载按钮
      lockOrUnStatus: false, // 点击禁用或启用加载按钮
      OrgLoading: false,
      staffModelParam: {},
      isExist: false,
      detailStaffModelParam: {},
      // 级联
      organizationList: [],
      departmentTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'text'
      },
      domainList: [], // 归属域
      urlPart: '/userOrganization',
      statusList: [
        // 因字典表数据不可用，因此在前端自定义
        { code: 'NORMAL', name: '正常' },
        { code: 'INITIAL', name: '初始' },
        { code: 'INACTIVE', name: '禁用' },
        { code: 'PASSWORD_EXPIRED', name: '密码过期' },
        { code: 'EXPIRED', name: '账号过期' },
        { code: 'LOCKED', name: '锁定' }
      ],
      userRolesList: [
        { code: '1', name: '集团' },
        { code: '2', name: '省公司' }
      ],
      sexList: [
        { code: '1', name: '男' },
        { code: '2', name: '女' }
      ]
    }
  },
  computed: {
    needShowDetail() {
      return !!this.$route.query.realName
    }
  },
  async created() {
    await this.$aspHttps
      .asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/getCurrent')
      .then((response) => {
        if (this.$reponseStatus(response)) {
          if (response.data.departmentId) {
            this.table.searchForm.departmentId = response.data.departmentId
            this.departmentIdwindow = response.data.departmentId

            this.search()
          } else {
            this.search()
          }
        } else {
          this.search()
        }
      })
      .catch(() => {
        this.search()
      })

    const domain = this.$aspUtils.getDomainObject(this)
    this.userDomain = domain.userInfo.domain
    this.getDomainList()

    this.table.searchForm.organizationId = domain.userInfo.organizationId
    this.table.searchForm.organizationName = domain.userInfo.organizationName
    const organizationId = this.table.searchForm.organizationId
    organizationId && this.getDepartTree(organizationId)

    // let organizationArray = []
    // if (this.userDomain === 'admin') {
    //   organizationArray = domain.authInfo.wb_020101
    // } else if (this.userDomain === 'channel') {
    //   organizationArray = domain.authInfo.wb_chann_020101
    // } else if (this.userDomain === 'supplier') {
    //   organizationArray = domain.authInfo.wb_supp_020101
    // }

    if (domain.authInfo.wb_090101) {
      this.isExist = true
    }

    this.table.searchForm.keyword = this.$route.query.realName || ''

    // this.search(true) // TurboC
  },
  mounted() {
    // this.search() // TurboC
  },
  methods: {
    /* 格式化时间 */
    formatTimer: function (value) {
      const date = new Date(value) // 转换为Date对象
      const year = date.getFullYear().toString().padStart(4, '0') // 年份补零
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份补零
      const day = date.getDate().toString().padStart(2, '0') // 日期补零
      const hours = date.getHours().toString().padStart(2, '0') // 小时补零
      const minutes = date.getMinutes().toString().padStart(2, '0') // 分钟补零
      const seconds = date.getSeconds().toString().padStart(2, '0') // 秒数补零
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    departmentId() {
      const departmentId = JSON.parse(sessionStorage.getItem('domain')).userInfo
        .departmentId
      // developmentId
      return !departmentId
    },

    tableSearchSuccess(listData) {
      // let newlist = []
      // if (this.table.searchForm.userRoles === '1') {
      //   newlist = listData.filter((item) => !item.departmentId)
      // } else if (this.table.searchForm.userRoles === '2') {
      //   newlist = listData.filter((item) => item.departmentId)
      // } else {
      //   newlist = listData
      // }
      this.$refs.table.listData = listData
    },

    // 按钮权限
    doAuth(opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCode: opt.btnCode }
    },
    /**
     * 查询组织详情
     * @param orgId
     * @param deptId
     */
    searchDepartment(orgId, deptId) {
      this.$router.push({
        path: '/system/deptForDept',
        query: {
          dataFrom: 'staff',
          orgId: orgId,
          deptId: deptId,
          title: '组织详情'
        }
      })
    },
    search() {
      this.$nextTick(() => {
        if (this.departmentIdwindow) {
          this.table.searchForm.departmentId = this.departmentIdwindow
        }

        this.$refs.table.asp_search()
        this.$nextTick(() => {
          // 需要直接显示详情
          if (this.needShowDetail && this.count === 0) {
            if (this.$refs.table.data.length > 0) {
              this.count++
              this.handleDetailStaff(this.$refs.table.data[0])
            }
          }
        })
      })
    },
    reset() {
      this.$refs.table.asp_reset()
      this.table.searchForm.departmentId = ''
    },
    /**
     * 导出excel
     * @method exportList
     */
    exportList() {
      const listParams = {
        status:
          this.table.searchForm.status === null
            ? ''
            : this.table.searchForm.status,
        keyword: this.table.searchForm.keyword,
        order: this.table.searchForm.order,
        sortName: this.table.searchForm.sortName,
        departmentId: this.table.searchForm.departmentId,
        mobile: this.table.searchForm.mobile,
        email: this.table.searchForm.email,
        page: this.$refs.table.page,
        rows: this.$refs.table.pageSize
      }
      const param = Object.keys(listParams)
        .map((key) => {
          return key + '=' + listParams[key]
        })
        .join('&')
      let url =
        this.$apiConfig.managerPathPrefix + this.urlPart + '/export?' + param
      url = url.replace(/undefined/g, '')
      url = this.$aspUtils.getRealUrl(url, this)
      this.$aspHttps.asp_ExportGet(url)
    },
    getDepartTree(orgId) {
      this.$aspHttps
        .asp_PostForm(
          this.$apiConfig.managerPathPrefix + '/department/listDepartmentTree',
          { id: orgId }
        )
        .then((response) => {
          if (this.$reponseStatus(response)) {
            this.departmentTreeData = response.data
          }
        })
    },
    departTreeNodeClick(data) {
      if (!data.id) return
      this.table.searchForm.departmentId = data.id
      this.table.searchForm.departmentName = data.text
    },
    clearDeptName() {
      this.table.searchForm.departmentId = ''
      this.table.searchForm.departmentName = ''
    },
    // 获取所属域
    getDomainList() {
      this.domainList = this.$aspUtils.getCodeValueByType(this, 'DOMAIN_NAME')
    },
    dictSex(row) {
      const item = this.sexList.find((val) => val.code === row.sex)
      return item ? item.name : ''
    }
  }
}
</script>
