
<template>
  <div class="container">
    <el-form ref="form" :model="form" label-width="80px" label-position="top">
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="1.选泽省份：">
            <el-select v-model="form.provinceCode" placeholder="请选择">
              <el-option
                v-for="item in provinceList"
                :key="item.provinceCode"
                :label="item.provinceName"
                :value="item.provinceCode"
                @click.native="labelClick(item.provinceName)"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="2.选择厅店：">
            <el-tree
              v-loading="loading"
              :props="props"
              :data='treeData'
              show-checkbox
              ref="tree">
            </el-tree>
          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="2">
          <el-form-item label="设置对接方式：" prop="bussinessIdleCalling">
            <el-select v-model="form.integrationMode" placeholder="请选择">
              <el-option v-for="(item,index) in integrationModeJson" :key="item+index" :label="item" :value="index"></el-option>
            </el-select>
          </el-form-item>
          <el-row type="flex" justify="end">
            <el-col :span="8">
              <el-form-item>
                <el-button type="primary" @click="onSubmit">立即创建</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import {
  integrationModeJson
} from '../configmanagement/appointment//appointmentconfig.js'
import { ZXSID_LIST } from '@/components/aspire/asp-config/asp-cfg-datas.json'
export default {
  props: {
    provinceList: {
      type: Array,
      default: (data) => data
    }
  },
  data() {
    return {
      integrationModeJson: integrationModeJson,
      form: {
        provinceCode: '',
        shopIdList: [],
        integrationMode: 0
      },
      props: {
        label: 'text',
        children: 'children'
      },
      count: 1,
      treeData: [],
      zxsidList: ZXSID_LIST,
      loading: false
    }
  },
  watch: {
    'form.provinceCode': function(newVal) {
      if (newVal && newVal !== '000') {
        this.getNode()
      }
    }
  },
  methods: {
    getNode() {
      this.loading = true
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/queryProvinceShopList', { provinceCode: this.form.provinceCode })
        .then(res => {
          if (res.status === '200') {
            // 判断是否在直辖市列表中
            if (this.zxsidList.includes(this.form.provinceCode)) {
              this.treeData = res.data
            } else {
              this.treeData = [{
                text: this.provinceName,
                id: this.form.provinceCode,
                children: res.data
              }]
            }
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          this.loading = false
        })
    },
    getShopList() {
      const nodeList = this.$refs.tree.getCheckedNodes()
      this.form.shopIdList = nodeList.filter(data => {
        return !!data.shopId && data.shopId !== ''
      }).map(data => data.shopId)
    },
    labelClick(label) {
      this.provinceName = label
    },
    onSubmit() {
      this.getShopList() // 获取选中的店铺列表
      if (!this.form.provinceCode) {
        this.$message({
          message: '请选择省份',
          type: 'warning',
          duration: '6000'
        })
        return
      }
      if (this.form.shopIdList.length === 0) {
        this.$message({
          message: '请至少选择一家营业厅',
          type: 'warning',
          duration: '6000'
        })
        return
      }
      // 提示用户确认操作
      this.$confirm(`确定对【${this.form.shopIdList.length}家】厅店的对接方式设置为【${this.integrationModeJson[this.form.integrationMode]}】?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: '提交中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/batchUpdateIntegrationMode', this.form)
          .then(res => {
            if (res.status === '200') {
              this.$message.success('保存成功')
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            loading.close()
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消设置'
        })
      })
    }
  }
}
</script>
<style lang="scss">
  .container{
    padding:20px;
    .el-form-item .el-select{
      display: block!important;
    }
    .el-table .el-form-item{
      margin-bottom: 0px;
    }
  }
</style>