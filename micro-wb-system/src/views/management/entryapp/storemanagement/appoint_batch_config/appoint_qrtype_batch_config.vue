<template>
  <div class="container">
    <el-row :gutter="100">
      <el-col :span="8">
        <el-form ref="form" :model="form" label-width="80px" label-position="top">
          <el-form-item label="1.选泽省份：">
            <el-select v-model="form.provinceCode" placeholder="请选择">
              <el-option
                v-for="item in provinceList"
                :key="item.provinceCode"
                :label="item.provinceName"
                :value="item.provinceCode"
                @click.native="labelClick(item.provinceName)"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="2.选择厅店：">
            <el-tree
              v-loading="loading"
              :props="props"
              :data='treeData'
              show-checkbox
              ref="tree">
            </el-tree>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="12">
        <el-form ref="form1" :model="form1" label-width="120px" label-position="right">
          <el-form-item label="二维码类型" required>
            <el-select v-model="form1.qrType" placeholder="">
              <el-option v-for="(item,index) in qrTypeJson" :key="item+index" :label="item" :value="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="form1.qrType === 1" label="小程序类型" required>
            <el-select v-model="form1.qrChannel" placeholder="默认">
              <el-option label="云店小程序" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="form1.qrType === 1" label="生成小程序" required>
            <el-select v-model="form1.isCreated" placeholder="默认">
              <el-option label="不生成" :value="0"></el-option>
              <el-option label="生成" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-row type="flex" justify="end">
            <el-col :span="12">
              <el-form-item>
                <el-button type="primary" @click="onSubmit">立即创建</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import {
  qrTypeJson
} from '../configmanagement/appointment//appointmentconfig.js'
import { ZXSID_LIST } from '@/components/aspire/asp-config/asp-cfg-datas.json'
export default {
  props: {
    provinceList: {
      type: Array,
      default: (data) => data
    }
  },
  data() {
    return {
      qrTypeJson: qrTypeJson,
      zxsidList: ZXSID_LIST,
      form: {
        provinceCode: '',
        shopIdList: []
      },
      form1: {
        qrType: 0,
        qrChannel: 0,
        isCreated: 0
      },
      treeData: [],
      props: {
        label: 'text',
        children: 'children'
      },
      loading: false
    }
  },
  watch: {
    'form.provinceCode': function(newVal) {
      if (newVal && newVal !== '000') {
        this.getNode()
      }
    }
  },
  methods: {
    getNode() {
      this.loading = true
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/queryProvinceShopList',
        { provinceCode: this.form.provinceCode, isUseUserCityCode: true })
        .then(res => {
          if (res.status === '200') {
            // 判断是否在直辖市列表中
            if (this.zxsidList.includes(this.form.provinceCode)) {
              this.treeData = res.data
            } else {
              this.treeData = [{
                text: this.provinceName,
                id: this.form.provinceCode,
                children: res.data
              }]
            }
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          this.loading = false
        })
    },
    labelClick(label) {
      this.provinceName = label
    },
    getShopList() {
      const nodeList = this.$refs.tree.getCheckedNodes()
      this.form.shopIdList = nodeList.filter(data => {
        return !!data.shopId && data.shopId !== ''
      }).map(data => data.shopId)
    },
    onSubmit() {
      this.getShopList() // 获取选中的店铺列表
      if (!this.form.provinceCode) {
        this.$message({
          message: '请选择省份',
          type: 'warning',
          duration: '6000'
        })
        return
      }
      if (this.form.shopIdList.length === 0) {
        this.$message({
          message: '请至少选择一家营业厅',
          type: 'warning',
          duration: '6000'
        })
        return
      }
      const sendData = {
        ...this.form,
        ...this.form1
      }
      // 提示用户确认操作
      this.$confirm(`确定对【${this.form.shopIdList.length}家】厅店的二维码类型设置为【${this.qrTypeJson[this.form1.qrType]}】?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: '提交中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/batchUpdateQrType', sendData)
          .then(res => {
            if (res.status === '200') {
              this.$message.success('保存成功')
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            loading.close()
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消设置'
        })
      })
    }
  }
}
</script>
<style lang="scss">
  .container{
    padding:20px;
    .el-form-item .el-select{
      display: block!important;
    }
  }
</style>