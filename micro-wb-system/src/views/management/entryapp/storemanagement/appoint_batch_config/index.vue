<template>
  <el-tabs v-if="provinceList && provinceList.length > 0" v-model="activeName">
    <template v-for="item in componentsCfg">
      <el-tab-pane v-if="item.isShow" :key="item.label" :label="item.label" :name="item.name">
        <component
          :is="item.component"
          :provinceList="provinceList"
        ></component>
      </el-tab-pane>
    </template>
  </el-tabs>
</template>
<script>
import AppointDockingBatchConfig from './appoint_docking_batch_config.vue'
import AppointStatusBatchConfig from './appoint_status_batch_config.vue'
import AppointQrtypeBatchConfig from './appoint_qrtype_batch_config.vue'
import AppointMsgtypeBatchConfig from './appoint_msgtype_batch_config.vue'
import AppointBusscopeBatchConfig from './appoint_busscope_batch_config.vue'
export default {
  components: {
    AppointDockingBatchConfig,
    AppointStatusBatchConfig,
    AppointQrtypeBatchConfig,
    AppointMsgtypeBatchConfig,
    AppointBusscopeBatchConfig
  },
  data() {
    return {
      activeName: '0',
      provinceList: null, // 省份数据初始化为空数组
      componentsCfg: [
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).batch_business_scope, // 查询批量设置业务范围按钮权限
          component: AppointBusscopeBatchConfig,
          label: '批量设置业务范围',
          name: 'first'
        },
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).batch_queue_status, // 查询批量设置厅店取号状态按钮权限
          component: AppointStatusBatchConfig,
          label: '批量设置厅店取号状态',
          name: 'second'
        },
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).batch_qrcode_type, // 批量设置取号二维码类型
          component: AppointQrtypeBatchConfig,
          label: '批量设置取号二维码类型',
          name: 'third'
        },
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).batch_docking, // 查询批量设置对接方式权限
          component: AppointDockingBatchConfig,
          label: '批量设置对接方式',
          name: 'fourth'
        },
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).batch_msg_type, // 查询批量设置消息类型权限
          component: AppointMsgtypeBatchConfig,
          label: '批量设置消息类型',
          name: 'fifth'
        }
      ]
    }
  },
  created() {
    const visibleComponents = this.componentsCfg.filter(item => item.isShow)
    this.activeName = visibleComponents[0].name
    this.getProvince() // 获取省份数据
  },
  methods: {
    getProvince() {
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/shop/common/regionInfo', {}).then(res => {
        if (res.status === '200') {
          // this.provinceList = res.data
          this.provinceList = res.data.filter(data => {
            return !!(data.provinceCode !== '000')
          })
        }
      })
    }
  }
}
</script>