<template>
  <div class="container">
    <el-form ref="form" :model="form" label-width="80px" label-position="top">
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="1.选泽省份：">
            <el-select v-model="form.provinceCode" placeholder="请选择">
              <el-option
                v-for="item in provinceList"
                :key="item.provinceCode"
                :label="item.provinceName"
                :value="item.provinceCode"
                @click.native="labelClick(item.provinceName)"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="2.选择厅店：">
            <el-tree
              v-loading="loading"
              :props="props"
              :data='treeData'
              show-checkbox
              ref="tree">
            </el-tree>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-table
            v-loading="loadingScope"
            ref="multipleTable"
            :data="tableData"
            :header-cell-style="{textAlign: 'center'}"
            style="width: 100%"
            border
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="200"
              :selectable="checkSelectable"
              align="center">
            </el-table-column>
            <el-table-column
              prop="type"
              label="业务编码"
              width="300"
              align="center">
            </el-table-column>
            <el-table-column
              label="业务范围名称"
              prop="title"
              align="center">
            </el-table-column>
          </el-table>
          <el-row type="flex" justify="end">
            <el-col :span="6">
              <el-form-item>
                <el-button type="primary" class="btn_batch" @click="onSubmit">批量修改</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { ZXSID_LIST } from '@/components/aspire/asp-config/asp-cfg-datas.json'
export default {
  props: {
    provinceList: {
      type: Array,
      default: (data) => data
    }
  },
  data() {
    return {
      form: {
        provinceCode: '',
        shopIdList: [],
        scopeIds: []
      },
      provinceName: '',
      tableData: [],
      props: {
        label: 'text',
        children: 'children'
      },
      treeData: [],
      zxsidList: ZXSID_LIST,
      loading: false,
      loadingScope: false,
      selectedTableData: []
    }
  },
  watch: {
    'form.provinceCode': function(newVal) {
      if (newVal && newVal !== '000') {
        this.getNode()
        this.getBusinessList()
      }
    }
  },
  methods: {
    /** 获取业务类型配置范围 */
    getBusinessList() {
      this.loadingScope = true
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/businessScope/query', { provinceCode: this.form.provinceCode })
        .then(res => {
          if (res.status === '200') {
            this.tableData = res.data
            this.selectedTableData = this.tableData.filter(item => item.channelDefault === 1)
            this.$nextTick(() => {
              this.toggleSelection(this.selectedTableData)
            })
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          this.loadingScope = false
        })
    },
    toggleSelection(rows) {
      rows.forEach(row => {
        this.$refs.multipleTable.toggleRowSelection(row)
      })
    },
    checkSelectable(row) {
      return !(row.channelDefault && row.channelDefault === 1)
    },
    /** 获取选中省份名称 */
    labelClick(label) {
      this.provinceName = label
    },
    /** 获取tree节点数据 */
    getNode() {
      this.loading = true
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/queryProvinceShopList',
        { provinceCode: this.form.provinceCode, isUseUserCityCode: true })
        .then(res => {
          if (res.status === '200') {
            // 判断是否在直辖市列表中
            if (this.zxsidList.includes(this.form.provinceCode)) {
              this.treeData = res.data
            } else {
              this.treeData = [{
                text: this.provinceName,
                id: this.form.provinceCode,
                children: res.data
              }]
            }
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          this.loading = false
        })
    },
    /** 获取选中营业厅列表 */
    getShopList() {
      const nodeList = this.$refs.tree.getCheckedNodes()
      this.form.shopIdList = nodeList.filter(data => {
        return !!data.shopId && data.shopId !== ''
      }).map(data => data.shopId)
    },
    handleSelectionChange(val) {
      this.form.scopeIds = val.map(data => data.scopeId)
    },
    onSubmit() {
      this.getShopList() // 获取选中的店铺列表
      if (!this.form.provinceCode) {
        this.$message({
          message: '请选择省份',
          type: 'warning',
          duration: '6000'
        })
        return
      }
      if (this.form.shopIdList.length === 0) {
        this.$message({
          message: '请至少选择一家营业厅',
          type: 'warning',
          duration: '6000'
        })
        return
      }
      if (this.form.scopeIds.length === 0) {
        this.$message({
          message: '请至少勾选一个业务类型',
          type: 'warning',
          duration: '6000'
        })
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '提交中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/businessScope/binding/batchBind', this.form)
        .then(res => {
          if (res.status === '200') {
            this.$message.success('保存成功')
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          loading.close()
        })
    }
  }
}
</script>
<style lang="scss">
  .container{
    padding:20px;
    .el-form-item .el-select{
      display: block!important;
    }
  }
  .btn_batch{
    margin-top:22px;
  }
</style>