<template>
  <div class="container">
    <el-form ref="form" :model="form" label-width="80px" label-position="top">
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="1.选泽省份：">
            <el-select v-model="form.provinceCode" placeholder="请选择">
              <el-option
                v-for="item in provinceList"
                :key="item.provinceCode"
                :label="item.provinceName"
                :value="item.provinceCode"
                @click.native="labelClick(item.provinceName)"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="2.选择厅店：">
            <el-tree
              v-loading="loading"
              :props="props"
              :data='treeData'
              show-checkbox
              ref="tree">
            </el-tree>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="">
            <el-radio-group v-model="form.hallStatus">
              <el-table
                ref="multipleTable"
                :data="tableData"
                :header-cell-style="{textAlign: 'center'}"
                style="width: 100%"
                border>
                <el-table-column
                  prop="id"
                  label="序号"
                  align="center"
                  width="40">
                </el-table-column>
                <el-table-column
                  label="营业厅状态"
                  align="center"
                  width="200">
                  <template slot-scope="scope">
                    <el-radio :label="scope.row.status">
                      {{ scope.row.radiaLabel }}
                    </el-radio>
                  </template>
                </el-table-column>
              </el-table>
            </el-radio-group>
          </el-form-item>
          <el-row type="flex" justify="end">
            <el-col :span="6">
              <el-form-item>
                <el-button type="primary" @click="onSubmit">批量修改</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { ZXSID_LIST } from '@/components/aspire/asp-config/asp-cfg-datas.json'
export default {
  props: {
    provinceList: {
      type: Array,
      default: (data) => data
    }
  },
  data() {
    return {
      form: {
        provinceCode: '',
        shopIdList: [],
        hallStatus: ''
      },
      provinceName: '',
      tableData: [
        {
          status: 0,
          radiaLabel: '正常',
          id: 1
        },
        {
          status: 2,
          radiaLabel: '停用',
          id: 2
        },
        {
          status: 1,
          radiaLabel: '测试',
          id: 3
        }
      ],
      props: {
        label: 'text',
        children: 'children'
      },
      treeData: [],
      zxsidList: ZXSID_LIST,
      loading: false
    }
  },
  watch: {
    'form.provinceCode': function(newVal) {
      if (newVal && newVal !== '000') {
        this.getNode()
      }
    }
  },
  methods: {
    getShopList() {
      const nodeList = this.$refs.tree.getCheckedNodes()
      this.form.shopIdList = nodeList.filter(data => {
        return !!data.shopId && data.shopId !== ''
      }).map(data => data.shopId)
    },
    onSubmit() {
      this.getShopList() // 获取选中的店铺列表
      if (!this.form.provinceCode) {
        this.$message({
          message: '请选择省份',
          type: 'warning',
          duration: '6000'
        })
        return
      }
      if (this.form.shopIdList.length === 0) {
        this.$message({
          message: '请至少选择一家营业厅',
          type: 'warning',
          duration: '6000'
        })
        return
      }
      if (this.form.hallStatus === '') {
        this.$message({
          message: '请勾选一个营业状态',
          type: 'warning',
          duration: '6000'
        })
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '提交中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/batchUpdateHallStatus', this.form)
        .then(res => {
          if (res.status === '200') {
            this.$message.success('保存成功')
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          loading.close()
        })
    },
    getNode() {
      this.loading = true
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/queryProvinceShopList',
        { provinceCode: this.form.provinceCode, isUseUserCityCode: true })
        .then(res => {
          if (res.status === '200') {
            // 判断是否在直辖市列表中
            if (this.zxsidList.includes(this.form.provinceCode)) {
              this.treeData = res.data
            } else {
              this.treeData = [{
                text: this.provinceName,
                id: this.form.provinceCode,
                children: res.data
              }]
            }
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          this.loading = false
        })
    },
    labelClick(label) {
      this.provinceName = label
    }
  }
}
</script>
<style lang="scss">
  .container{
    padding:20px;
    .el-form-item .el-select{
      display: block!important;
    }
  }
</style>