<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="45%"
  >
  <div v-if="type === 'examine'">
    <h3>全网渠道编码变更提示</h3>
    <div class="content">
      <div v-for="item in data" :key=item.unifiedChannelId>
        <p>该厅店的全网渠道编码已变更为：{{ item.unifiedChannelId }}</p>
        <p>是否变更当前{{ item.reqNo }}审核单中的全网渠道编码？</p>
      </div>
    </div>
  </div>
  <div v-else>
    <h3>全网渠道编码批量变更提示</h3>
    <div class="content">
      <div v-for="item in data" :key=item.reqNo>
        <p>审核单{{ item.reqNo }}的全网渠道编码已变更为：{{ item.unifiedChannelId }}</p>
      </div>
      <p>是否批量变更这些审核单中的全网渠道编码？</p>
    </div>
  </div>
  <template slot="footer-center">
      <div class="webbas footerCon">
        <asp-btn-solid
          v-if="type === 'examine'"
          name="确定变更，并审核厅店信息"
          @click="changeChannel"
        >
        </asp-btn-solid>
        <asp-btn-solid
          v-if="type === 'batch_review'"
          name="确定变更，并审核厅店信息"
          @click="plChange"
        >
        </asp-btn-solid>
        <asp-btn-hollow
          @click="closeAuditOfficeMark"
          name="取消，并取消审核厅店信息"
        >
        </asp-btn-hollow>
      </div>
    </template>
  </asp-dialog>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        this.type = val.type
        this.data = val.data
        this.id = val.id
        console.log(this.data, 'dataaa')
      }
    }
  },
  data () {
    return {
      type: '',
      data: [],
      id: []
    }
  },
  methods: {
    // 取消审核厅弹层
    closeAuditOfficeMark () {
      this.dialogParam.modelVisible = false
    },
    // 单独变更
    changeChannel () {
      const params = [this.data[0].reqNo]
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/updateUnifiedChannelId', { reqNos: params }).then(res => {
        if (res.status === '200') {
          this.dialogParam.modelVisible = false
          this.$parent.auditMark(res.data)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 批量变更
    plChange () {
      const params = []
      this.data.forEach(item => {
        params.push(item.reqNo)
      })
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/updateUnifiedChannelId', { reqNos: params }).then(res => {
        if (res.status === '200') {
          this.dialogParam.modelVisible = false
          this.$parent.plConfig((this.id).toString())
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
h3 {
  text-align: center;
}
.content {
  text-align: center;
  height: 150px;
  overflow: scroll;
  p {
    line-height: 18px;
  }
}
</style>
