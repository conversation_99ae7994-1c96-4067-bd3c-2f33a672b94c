<!-- 用户支付渠道拦截报表 -->
/**
* 业务实体
*/
<template>
  <div style="height: 100%; ">
    <asp-smart-table ref="aspSmartTable"
                     v-model="model"
                     :table-json="tableJson"
                     :size-change="sizeChange"
                     :current-change="currentChange"
                     :before-http="beforeHttp"
                     :after-http="afterHttp"
                     :before-button="beforeButton"
                     :before-router="beforeRouter"
                     :before-table-row-render="beforeTableRowRender"
                     :before-table-render="beforeTableRender"
                     :before-click-table-cell="beforeClickTableCell"
                     :dialog-config="dialogConfig"
                     :render-table="renderTable"
                     @on="onbind">
                     <template slot="picture" slot-scope="scope">
                      <img style="width: 50px; height: 50px;" :src="$apiConfig.level1cloudstorePathPreFix + '/proxyFs/show?file_id=' + scope.data.picture" alt="">
                     </template>
                     <template slot="button_import">
                      <div class="btnStyle">
                        <el-upload
                          ref="upload"
                          action="#"
                          accept=".xls,.xlsx"
                          :show-file-list="false"
                          :http-request="handleModifyFileUpload"
                          :auto-upload="true"
                          >
                          <span title="导入">导入</span>
                        </el-upload>
                      </div>
                     </template>
    </asp-smart-table>
    <editreview ref="editreview" @refreshList = "refreshList" @handNopass="handNopass" :dialog-param="editReviewParam"></editreview>
    <changenotification :dialog-param="changeParam"></changenotification>
    <ai :dialog-param="aiParam"></ai>
    <!-- 审核不通过弹窗 -->
    <el-dialog
      :visible.sync="nopass"
      width="30%"
      :show-close="false">
      <el-form ref="nopassForm" :rules="rules" :model="nopassForm">
        <el-form-item label="未通过审核原因：" prop="nopasscontent">
          <el-input
            type="textarea"
            maxlength="150"
            :rows="2"
            placeholder="请输入未通过审核原因"
            v-model="nopassForm.nopasscontent">
      </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelNopass">取消</el-button>
        <el-button type="primary" @click="sendNopassMessage">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import editreview from './editreview.vue'
import changenotification from './changenotification.vue'
import ai from './ai.vue'
export default {
  name: 'reviewandedit',
  components: {
    editreview,
    changenotification,
    ai
  },
  data () {
    return {
      nopass: false,
      nopassForm: {
        nopasscontent: ''
      },
      applyId: null,
      rules: {
        nopasscontent: [
          { required: true, message: '请填写原因', trigger: 'blur' }
        ]
      },
      editReviewParam: {},
      changeParam: {},
      aiParam: {},
      tableJson: null,
      model: {},
      testData: [],
      tempSelect: [],
      ids: '',
      exportids: [],
      renderTable: {
        picture: (h, item, scope, tableData) => { // xxdy是列名columnName, 请根据实际情况修改
          return (<span style="color: red; background: #f2dede;">{scope.row[item.columnName]}</span>)
        }
      },
      rowId: '',
      dialogStatus: '',
      dialogConfig: {
        // 审核记录
        examineoperate: {
          name: 'examineoperate',
          mounted: async ({ $_dialogData }) => {
            console.log($_dialogData)
            const modelHtml = JSON.parse(
              JSON.stringify(require('./examineoperate.json'))
            ) // 修改模板名称
            let modelData1 = modelHtml.model
            if ($_dialogData.PreTableRow) {
              // 如果带表单几个数据过来
              // 表单所在行所有数据 $_dialogData.PreTableRow
              modelData1 = {
                ...modelHtml.data,
                applyId: $_dialogData.PreTableRow.applyId
              }
            }
            const modelData = modelData1
            const oldModel = $_dialogData.PreTableRow || {}
            return {
              modelHtml,
              model: modelData,
              oldModel,
              status: 'info',
              tableJson: modelHtml
            }
          },
          // 数据安装后执行函数
          setup (data) {
          },
          onbind (data) {
          },
          beforeHttpPro ({ item, parent, index, model }, httpObject, callback) {
            console.log(item, 'ioio')
            /* if (item.columnName === 'submit') {
              httpObject.httpBody.businessColumn[0].primary = '1'
            }
            callback(httpObject) */
          },
          afterHttpPro (data, responseBody, callback) {
            /* if (data.item.columnName === 'submit') {
            const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'createTab' && '' + responseBody.status === '200') {
              data.$_this.$refs.smartForm.asp_updateModel({
                createdTable: '1',
                createdTableName: '是'
              })
              data.$_this.$refs.smartForm.asp_setHidden('buttonGroup_create', true)
            }
            callback(responseBody) */
          },
          beforeLoadingHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          beforeBpmHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterBpmHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          compDataChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          compDataActiveChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            let isContinue = true
            if (data.index === 0) {
              this._this.$message.error('主键不允许删除')
              isContinue = false
            }
            callback(isContinue, data.rowData)
          },
          afterButtonPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, rowData)
          },
          beforeAuthPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, isContinue)
          },
          beforeRouterPro ({ item, row, routerObj, next }) { next(routerObj) },
          beforeColumnValidatePro ({ tableItem, model, value, type }, callback) {
            const isContinue = true
            const hasContinue = false
            callback(isContinue, hasContinue, undefined)
          },
          beforeDrawTableRowPro ({ item, model, row, rowClassName }, callback) { callback(rowClassName) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        }
      }
    }
  },
  async mounted () {
    this.tableJson = JSON.parse(JSON.stringify(require('./reviewandedit.json')))
    this.model = this.tableJson.model
    // 省市下拉框默认展示
    this.$aspHttps
      .asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/getCurrent')
      .then((response) => {
        if (this.$reponseStatus(response)) {
          this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/shop/common/regionInfo', {}).then(res => {
            if (res.status === '200') {
              if (res.data[0].cities.length === 1) {
                this.model.cityCode = res.data[0].cities[0].cityCode
              } else {
                this.model.cityCode = '000'
              }
            }
          })
          this.model.provinceCode = response.data.divisions[0].substring(0, 3)
        }
      })
  },
  methods: {
    // 审核弹窗
    auditMark(rowObj) {
      this.editReviewParam = {
        title: '审核',
        modelVisible: true,
        data: rowObj,
        type: 'examine'
      }
    },
    // 批量修改
    handleModifyFileUpload(fileObject) {
    // 校验附件
      if (!fileObject.file) {
        this.$message.error('不能上传空文件！')
        return false
      }
      // const fileSuffix = fileObject.file.name.split('.')[1]
      // if (fileSuffix !== 'xls' && fileSuffix !== 'xlsx') {
      //   this.$message.error('文件后缀只能是xls或者xlsx')
      //   return false
      // }
      const fd = new FormData()
      fd.append('file', fileObject.file)
      this.$aspHttps.asp_FileUpload(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/upload/api/apply/import/1',
        fd,
        1
      ).then((response) => {
        if (response.status !== '200') {
          this.$message.error(response.message)
        } else {
          this.$message.success('创建导入任务成功')
        }
      })
    },
    // AI稽查 继续提交 审核
    ailConfig(ids, aiCheck) {
      const params = {
        applyIds: ids,
        auditStatus: 1,
        aiCheck: aiCheck
      }
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/audit', params).then((res) => {
        if (res.status === '200') {
          this.editReviewParam.modelVisible = false
          this.$message.success('审核成功')
          // 刷新列表
          this.refreshList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 批量审核
    plConfig(ids, aiCheck) {
      this.$confirm('经审核，营业厅信息已确认无误', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定，审核通过',
        cancelButtonText: '审核不通过'
      }).then(() => {
        const params = {
          applyIds: ids,
          auditStatus: 1,
          aiCheck: aiCheck
        }
        this.$aspHttps.asp_Post(
          this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/audit', params).then((res) => {
          if (res.status === '200') {
            if (res.data && res.data.checkMsgList) {
              this.aiParam = {
                title: 'AI厅店信息数智稽核提醒',
                modelVisible: true,
                data: res.data,
                type: 'plConfig',
                params: params
              }
            } else {
              this.editReviewParam.modelVisible = false
              this.$message.success('审核成功')
              // 刷新列表
              this.refreshList()
            }
          } else {
            this.$message.error(res.message)
          }
        })
      }).catch((e) => {
        if (e === 'cancel') {
          this.nopass = true
        }
      })
    },
    handNopass(applyId) {
      this.ids = ''
      this.nopass = true
      this.applyId = applyId
    },
    // 未通过审核原因提交
    sendNopassMessage() {
      this.$refs.nopassForm.validate((valid) => {
        if (valid) {
          const params = {
            applyId: this.applyId,
            applyIds: this.ids.toString(),
            auditOpinion: this.nopassForm.nopasscontent,
            auditStatus: 2
          }
          // 审核不通过 判断是批量还是单独审核  传参改变
          if (params.applyId === null) {
            delete params.applyId
          } else if (params.applyIds === '') {
            delete params.applyIds
          }
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/audit', params).then((res) => {
          // 要改status值。
            if (res.status === '200') {
              this.nopass = false
              this.editReviewParam.modelVisible = false
              this.nopassForm.nopasscontent = ''
              this.$message.success('成功')
              // 刷新列表
              this.refreshList()
            } else {
              this.nopassForm.nopasscontent = ''
              this.$message.error(res.message)
            }
          })
        } else {
          this.nopassForm.nopasscontent = ''
          return false
        }
      })
    },
    // 未通过审核原因取消
    cancelNopass() {
      this.nopass = false
      this.nopassForm.nopasscontent = ''
    },
    // 刷新列表
    refreshList () {
      this.$refs.aspSmartTable.asp_sendTableQuery('editAndAuditList')
    },
    /**
     * 智能表格监听所有组件的交互事件操作：监听、捕捉事件响应
     * @param item 响应组件对象属性集（类型、组件Id，控件内元数属性），columnName每个组件单元的唯一码（组件单元Id）
     * @param type 事件类型（click/blur/onblur等）
     * @param index 当是表格组件时，返回组件的行号
     * @param model 查询区域表单数据模型
     * @param tableModel 表格组件数据模型
     * @param row 表格行数据
     * @param multipleSelection 表格多选数据（当出现列表复选时才有，包括跨页数据，整行数据）
     * @param sortProps 排序属性
     * @returns {Promise<void>}
     */
    async onbind ({ item, type, index, model, tableModel, row, subFormSelectData, sortProps }) {
      if (subFormSelectData) {
        this.tempSelect = subFormSelectData
      }
      if (item.columnName === 'button_export') {
        const ids = []
        this.tempSelect.forEach(item => {
          ids.push(item.applyId)
        })
        this.exportids = [...new Set(ids)]
        const params = {
          channelCategorys: model.channelCategorys,
          cityCode: model.cityCode,
          openStatus: model.openStatus,
          provinceCode: model.provinceCode,
          shopAdminNameOrPhone: model.shopAdminNameOrPhone,
          shopNameOrShortName: model.shopNameOrShortName,
          unifiedChannelId: model.unifiedChannelId,
          applyStatus: model.applyStatus,
          applyIdList: this.exportids,
          downLoadName: '待审核工单'
        }
        this.$aspHttps.asp_Post_file(
          this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/export',
          params
        ).then(res => {
          if (res.status === '200') {
            this.$message.success('导出成功')
          } else {
            this.$message.error(res.message)
          }
        })
      }
      switch (item.columnName) {
        // case 'batch_review':
        //   this.aiParam = {
        //     title: 'AI厅店信息数智稽核提醒',
        //     modelVisible: true,
        //     data: item
        //   }
        //   break
        // 批量审核
        case 'batch_review':
          if (this.tempSelect.length === 0) {
            this.$message.error('请选择要审核的数据')
          } else {
            this.applyId = null
            const ids = []
            this.tempSelect.forEach(item => {
              ids.push(item.applyId)
            })
            this.ids = [...new Set(ids)]
            // 全网渠道编码变更提示接口
            this.$aspHttps.asp_Post(
              this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/queryNewUnifiedChannelId', { applyIds: this.ids }).then(res => {
              if (res.status === '200') {
                if (Object.keys(res.data).length !== 0) {
                  // 全网渠道编码变更弹窗
                  this.changeParam = {
                    title: '变更提示',
                    modelVisible: true,
                    data: res.data,
                    type: 'batch_review',
                    id: this.ids
                  }
                } else {
                  // 批量审核 清空单独的apply
                  this.applyId = null
                  const ids = []
                  this.tempSelect.forEach(item => {
                    ids.push(item.applyId)
                  })
                  this.ids = [...new Set(ids)].join()
                  this.plConfig(this.ids, true)
                }
              } else {
                this.$message.error(res.message)
              }
            })
          }
          break
        case 'edit':
          this.rowId = row.id
          this.dialogStatus = 'edit'
          break
      }
    },
    /**
     * 智能表格页面所有请求前的前置操作
     * 例如：修改请求参数、修改请求方式、修改请求URL、或者请求条件不满足不给发送请求
     * @param tableItem 组件对象属性集
     * @param params 请求参数body，数据格式如下(字段格式不一致的需要自行转换)如下:
     *                                         {
     *                                             page：1， // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                             rows: 10，// 分页属性(页大小)，数字类型 （不是分页接口，没有这个字段）
     *                                             .......   // 业务属性字段
     *                                          }
     * @param httpMethod.url 请求地址URL
     * @param httpMethod.type 请求方式，目前主要六种：'post+json', 'post+form', 'get'，'put+json'，'delete+json'，'patch+json'
     * @param row 当组件为表格并且是表格操作列触发的请求，此参数返回表格行数据，其它返回undefined
     */
    beforeHttp ({ tableItem, params, httpMethod, row }) {
      if (tableItem.columnName === 'editAndAuditList') {
        if (params.unifiedChannelId) {
          params.unifiedChannelIdList = params.unifiedChannelId.split(',')
        } else {
          params.unifiedChannelIdList = []
        }
      }
    },
    /**
     * 智能表格页面所有请求后置操作
     * 例如：请求后的数据包体需要做二次处理
     * @param tableItem 组件对象属性集
     * @param responseBody 响应数据body, 数据包格式(字段格式不一致的需要自行转换)如下：
     *                                              {
     *                                                status: "200", // 业务状态码，字符串类型，成功返回"200"，失败返回其它数据
     *                                                message: "",   // 业务提示语，字符串类型，给业务的提示语属性
     *                                                page：1，      // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                                total: 53，    // 分页属性(总记录大小)，数字类型 （不是分页接口，没有这个字段）
     *                                                data: {}或者[] // 业务数据区，对象或数组类型，用于各业务逻辑处理
     *                                               }
     */
    afterHttp ({ tableItem, responseBody }) {
    },
    /**
     * 智能表格页面上的按钮的前置操作：包括不限于查询区域，表格顶部、表格操作列
     * 例如：对操作按钮进行处理的数据进行预处理，或者对按钮请求进行个性胡逻辑判断等
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param item 组件对象属性集
     * @param rowObj 当组件为表格操作列中的按钮，此参数返回表格行数据，其它返回undefined
     * @param next 回调函数
     */
    beforeButton ({ item, rowObj, next }) {
      if (item.columnName === 'operationEdit' || item.columnName === 'restart') {
        // 营业范围列表接口
        this.$aspHttps.asp_Post(
          this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/getBusinessScopeList', {}).then((res) => {
          if (res.status === '200') {
            // 成功后赋值
            rowObj.businessScopeAll = res.data
            // AI稽核传参
            rowObj.aiCheck = true
            // 弹层
            this.editReviewParam = {
              title: '编辑',
              modelVisible: true,
              data: rowObj,
              mapData: rowObj,
              type: rowObj.openStatusCode === 2 ? 'rzfail' : item.columnName
            }
          } else {
            this.$message.error(res.message)
          }
        })
      }
      if (item.columnName === 'examine') {
        const params = [rowObj.applyId]
        // 全网渠道编码变更提示接口
        this.$aspHttps.asp_Post(
          this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/queryNewUnifiedChannelId', { applyIds: params }).then(res => {
          if (res.status === '200') {
            if (Object.keys(res.data).length !== 0) {
              this.changeParam = {
                title: '变更提示',
                modelVisible: true,
                data: res.data,
                type: 'examine'
              }
            } else {
              this.auditMark(rowObj)
            }
          } else {
            this.$message.error(res.message)
          }
        })
      }
      next(item, rowObj) // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 智能表格页面路由跳转的前置操作
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param item 响应组件对象属性集
     * @param row 当响应组件为表格操作列中的按钮时，此参数返回表格行数据，其它返回undefined
     * @param routerObj.routerType: 路由类型
     * @param routerObj.routerParamType 路由参数类型
     * @param routerObj.routerUrl 路由地址或名称
     * @param routerObj.routerParamValue 路由参数
     * @param next 回调函数
     */
    beforeRouter ({ item, row, routerObj, next }) {
      next(routerObj) // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 表格内容渲染之前的前置动作，
     * @param tableName 当前表格名称
     * @param tableData 表格当页的数据
     * @param columnItem 表格当前列的信息
     * @param scope 表格行信息包含属性 $index row等
     * @param callback 回调事件，用于改变指定列的显示内容
     * @param callback 参数说明如下
     * 参数一：指定修改的表格名称 tableName
     * 参数二：指定修改的列名 columnName
     * 参数三：指定索引集合，整列生效则传空数组[],指定某几行生效则传索引集合[1,3] indexList
     * 参数四：显示内容{ content: 可以是文本也可以是html代码片段}
     * 示例：callBack('aspSmartTable', 'name', [], { content: `【附加标题】<a @click="detial(${scope.row})">${scope.row.name}</a>` })
     */
    beforeTableRender ({ tableName, tableData, columnItem, scope }, callBack) { },
    /**
     * 智能表格监听所有行绘制的前置回调响应
     * @param item 组件对象属性集(类型、组件columnName，组件内元数属性)，columnName是每个组件的唯一标识码
     * @param tableData 表格数据模型
     * @param row:  表格组件当前绘制的行数据
     * @param rowClassName: 子表单组件当前行绘制class name
     * @param callback: 回调api
     * @param           callback回调api参数: rowClassName: 子表单组件当前行绘制class name
     */
    beforeTableRowRender ({ item, tableData, row, rowClassName }) {
      return rowClassName // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 智能表格单元格点击的前置操作
     * @param item 响应组件对象属性集
     * @param row 此参数返回表格行数据
     * @param tableData: 表格数据模型
     */
    beforeClickTableCell ({ item, row, tableData }) { },
    /**
     * 表格页码大小发生变化时触发的前置事件
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param pageSize 表格页码大小
     * @param next 回调函数
     */
    sizeChange ({ tableItem, pageSize, next }) {
      next(true) // 允许继续运行传true, 否则传false  // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 表格当前页发生变化时触发的前置事件，包括点翻页、上一页、下一页、刷新页、重置页
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param currentPage 当前页码号
     * @param next 回调函数
     */
    currentChange ({ tableItem, currentPage, next }) {
      next(true) // 允许继续运行传true, 否则传false // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-table th.el-table__cell>.cell {
  text-align: center;
}
::v-deep .el-table .cell {
  text-align: center;
}
::v-deep .asp-smart-table {
  height: 120vh;
}
::v-deep .create-tab-css {
  margin: 4px 4px;
}
.asp-pagination {
  background-color: #fff;
}
::v-deep .asp-smart-table .widget-box-button{
  padding-top: 0 !important;
}
::v-deep .asp-smart-table .solid-with-icon-btn {
  padding: 7px 25px;
}
.btnStyle{
  display: flex;
  align-items: center;
  justify-content: center;
  border:1px solid #DCDFE6;
  border-radius:5px;
  padding:4px -2px;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
  background: linear-gradient(360deg, #4576e4 0%, #6f9ef0 100%);
  color: #ffffff;
  span{
    color: #ffffff;
  }
}
</style>
