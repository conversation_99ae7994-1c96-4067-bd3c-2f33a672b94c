<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="50%"
  >
    <div class="diaBox">
      <h2>AI厅店信息数智稽核提醒</h2>
      <p class="aitip">您本次操作，系统稽核发现有疑似风险点 {{ aiData.riskMsgTotal }} 项，涉及 {{ aiData.riskTotal }} 家店铺，请接口检查修改！</p>
      <p class="fx"><i class="el-icon-warning" style="color: #F4C706;font-size: 20px;"></i><span>疑似风险<b> {{ aiData.riskMsgTotal }} </b>项</span></p>
      <p class="jg"></p>
      <div class="fxBox">
        <div class="fxitem" v-for="(item,index) in aiData.checkMsgList" :key="index" >
          <p class="fxtitle">{{ index+1 }}.全网渠道ID：{{ item.unifiedChannelCode }} <span>检测到疑似风险 {{ item.riskMsgList.length }} 项</span></p>
          <div>
            <p v-for="(quesitem,index) in item.riskMsgList" :key="index"><i class="el-icon-close" style="color: #E86C09;"></i>&nbsp;&nbsp;&nbsp;{{ quesitem.exMsg }}</p>
          </div>
        </div>
      </div>
    </div>
    <template slot="footer-center">
      <div class="webbas footerCon">
        <asp-btn-solid
          name="返回看看"
          @click="close"
          v-if="type !== 'plConfig'"
        >
        </asp-btn-solid>
        <asp-btn-solid
          name="继续提交"
          @click="continueSubmitting"
          v-if="type === 'editForm'"
        >
        </asp-btn-solid>
        <asp-btn-solid
          name="继续提交"
          @click="continuePl"
          v-if="type === 'plConfig' || type === 'onlyConfig'"
        >
        </asp-btn-solid>
      </div>
    </template>
  </asp-dialog>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        this.aiData = val.data
        this.type = val.type
        this.params = val.params
      }
    }
  },
  data() {
    return {
      aiData: {},
      params: {},
      type: ''
    }
  },
  methods: {
    // 返回修改
    close () {
      this.dialogParam.modelVisible = false
    },
    // 继续提交
    continueSubmitting () {
      this.params.aiCheck = false
      this.$parent.appAndEditSave(this.type, this.params)
      this.close()
    },
    // 批量审核继续提交
    continuePl () {
      if (this.type === 'onlyConfig') {
        this.params.aiCheck = false
        this.$parent.isPass(false)
        this.close()
      } else {
        this.params.aiCheck = false
        this.$parent.ailConfig(this.params.applyIds, false)
        this.close()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  height: 100%;
  margin-top: 0 !important;
  margin-right: 0 !important;
  margin-bottom: 0 !important;
  border-radius: 0;
  overflow: scroll;
  position: relative;
}
::v-deep .el-dialog__footer {
  position: fixed !important;
  bottom: 0 !important;
  left: 50%;
  width: 100% !important;
}
::v-deep .el-dialog__body {
  padding: 0 !important;
}
.diaBox {
  padding-bottom: 50px;
  h2 {
    text-align: center;
    color: black;
    margin-top: 20px;
  }
  .aitip {
    color: red;
    text-align: center;
  }
  .fx {
    align-items: center;
    margin-left: 20px;
    b {
      color: #FA1A17;
    }
    span {
      margin-left: 10px;
      color: black;
      font-weight:bold;
    }
  }
  .jg {
    width: 100%;
    height: 5px;
    background-color: #F1F5FA;
  }
  .fxBox{
    position: relative;
    padding: 0 10px;
    .fxitem {
      border-bottom: 1px solid #D4D4D4;
    }
    div {
      .fxtitle {
        color: black;
        span {
          margin-left: 15px;
          color: #E86C09;
        }
      }
    }
  }
}
.footerCon {
  margin-left: -50%;
}
</style>
