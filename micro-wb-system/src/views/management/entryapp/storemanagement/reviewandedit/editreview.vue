<template>
  <div>
    <asp-dialog
      class="webbas"
      v-model="dialogParam.modelVisible"
      :visible.sync="dialogParam.modelVisible"
      :title="dialogParam.title"
      width="70%"
    >
    <el-form ref="editForm" :rules="rules" :model="editForm">
      <el-form-item label="当前营业厅：">
        <span>{{ editForm.unifiedChannelId }}</span>
      </el-form-item>
      <el-form-item label="实体营业厅名称：" prop="shopName">
        <span v-if="type === 'examine' || type === 'button_details'">{{ editForm.shopName }}</span>
        <el-input v-model="editForm.shopName" show-word-limit maxlength="30" v-else></el-input>
      </el-form-item>
      <el-form-item label="实体营业厅简称：" prop="shopShortName">
        <span v-if="type === 'examine' || type === 'button_details'">{{ editForm.shopShortName }}</span>
        <el-input v-model="editForm.shopShortName" show-word-limit maxlength="16" v-else></el-input>
      </el-form-item>
      <p style="color: red;font-size: 13px;margin-left: 21%;">当前在中国移动APP中展示的厅店名称为“实体营业厅简称”</p>
      <el-form-item label="门店客服电话：">
        <span v-if="type === 'examine' || type === 'button_details'">{{ editForm.shopContactPhone ? editForm.shopContactPhone : '无' }}</span>
        <el-input v-model.trim="editForm.shopContactPhone" v-else></el-input>
      </el-form-item>
      <p style="color: red;font-size: 13px;margin-left: 21%;">座机格式为:区号+电话号码，如0371-666666;手机必须为11位数字</p>

      <el-form-item readonly label="员工：" prop="roleNum">
        <span v-if="type === 'examine' || type === 'button_details'">{{ editForm.roleNum }}</span>
        <el-input-number size='mini' v-else :disabled="isBlur" @focus="xx" @blur="addYgBlur" @change="handleAddOrReduce" controls-position="right" v-model="editForm.roleNum" :min="1"></el-input-number>
        <span> &nbsp;&nbsp;&nbsp;人</span>
      </el-form-item>

      <div class="employees" v-for="(item,index) in editForm.staffInfo" :key="index">
        <el-form-item label="员工身份：" :rules="rules.identity" :prop="'staffInfo.' + index + '.identity'">
          <el-radio-group :disabled="type === 'examine' || type === 'button_details'" @change="changeRole($event,index)" v-model="item.identity">
            <el-radio :label='0'>店长</el-radio>
            <el-radio :label='1'>店员</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="姓名：">
          <el-input :readonly="type === 'examine' || type === 'button_details'" show-word-limit maxlength="32" size="small" style="width: 50%;" v-model="item.name"></el-input>
        </el-form-item>
        <el-form-item label="电话号：">
          <el-input :readonly="type === 'examine' || type === 'button_details'" show-word-limit maxlength="11" size="small" style="width: 50%;" v-model="item.phone"></el-input>
        </el-form-item>
        <el-form-item label="员工工号：">
          <el-input :readonly="type === 'examine' || type === 'button_details'" show-word-limit maxlength="256" size="small" style="width: 50%;"  placeholder="Boss系统工号" v-model="item.staffNumber"></el-input>
        </el-form-item>
        <span @click="deleteRole(index)" v-if="type !== 'examine' && type !== 'button_details'">删除</span>
      </div>
    <el-form-item label="营业范围：" prop="businessScope">
      <span v-if="type === 'examine' || type === 'button_details'">{{ editForm.businessScopeTitles ? editForm.businessScopeTitles : '无' }}</span>
      <el-checkbox-group @change="aaa" v-model="editForm.businessScope" v-else>
        <el-checkbox v-for="item in editForm.businessScopeAll" :key="item.scopeId" :label="item.scopeId" name="type">{{ item.title }}</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <!-- <el-form-item label="图片：">
      <img class="setpicture" :src="editForm.pictureShow" alt="" v-if="type === 'examine' || type === 'button_details'">
      <uploader
        v-else
        v-model="editForm.pictureShow"
        verify-picture="160x160"
        tips="图片尺寸160*160"
        @input="input"
      ></uploader>
    </el-form-item> -->
    <el-form-item label="地址：" prop="address">
      <span v-if="type === 'examine' || type === 'button_details'">{{ editForm.address }}</span>
      <template v-else>
        <el-row>
          <el-col :span="20">
            <el-input v-model="editForm.address" placeholder="支持地图选址或手动输入" show-word-limit maxlength="30"></el-input>
          </el-col>
          <el-col :span="2" :offset="1">
            <asp-btn-solid
              name="地图选址"
              @click="mapDialog.modelVisible = true"
            />
          </el-col>
        </el-row>
      </template>
    </el-form-item>

    <el-row>
      <el-col :span="10">
        <el-form-item label="店铺经度：" prop="longitude">
          <span v-if="type === 'examine' || type === 'button_details'">{{ editForm.longitude }}</span>
          <el-input v-model="editForm.longitude" placeholder="请填写WGS84坐标系经度" v-else></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="店铺纬度：" prop="latitude">
          <span v-if="type === 'examine' || type === 'button_details'">{{ editForm.latitude }}</span>
          <el-input v-model="editForm.latitude" placeholder="请填写WGS84坐标系纬度" v-else></el-input>
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item :label="'\u3000'" style="color:red">注意：经纬度信息只能填写 WGS-84 坐标系
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="移动优选厅：" v-if="type === 'button_details'">
      <span>{{ editForm.chinaMobileFirst === 1 ? '是' : '否' }}</span>
    </el-form-item>
    <el-form-item label="附近厅推荐等级：" v-if="type === 'button_details'">
      <span>{{ editForm.recommendLevel === 1 ? '高' : '中' }}</span>
    </el-form-item>
    <el-form-item label="营业时间：" prop="businessHours">
      <span v-if="type === 'examine' || type === 'button_details'">{{ editForm.businessHours ? editForm.businessHours : '无' }}</span>
      <el-input v-model="editForm.businessHours" v-else></el-input>
      <p style="color: #999;">备注：七个时间段分别表示周一至周日的厅店营业时间，逗号分隔，为空代表今日打烊休息（样例如下）<br>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        周六休请输入： &nbsp;“09:00-17:30,09:00-17:30,09:00-17:30,09:00-17:30,09:00-17:30,,09:00-16:30”<br>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        周三休请输入： &nbsp;“09:00-17:30,09:00-17:30,,09:00-17:30,09:00-17:30,09:00-16:30,09:00-16:30”<br>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        周日休请输入： &nbsp;“09:00-17:30,09:00-17:30,09:00-17:30,09:00-17:30,09:00-17:30,09:00-16:30,”<br>
      </p>
      <p style="color: red;">（注意：只填双引号内的数字）</p>
    </el-form-item>
    <el-form-item label="父级厅店信息：" v-if="type === 'button_details'">
      <span>{{ editForm.parentUnifiedChannelId ? editForm.parentUnifiedChannelId : '无' }}</span>
    </el-form-item>
    <el-form-item label="营业厅状态：">
      <span>{{ editForm.status }}</span>
    </el-form-item>
  </el-form>
  <p class="tip" v-if="type === 'examine'">经审核，营业厅信息已准确无误</p>
  <template slot="footer-center">
      <div class="webbas footerCon" v-if="type === 'examine'">
        <asp-btn-solid
          name="确定，审核通过"
          @click="auditSuccess"
        >
        </asp-btn-solid>
        <asp-btn-hollow
          name="审核不通过"
          @click="handNopass"
        >
        </asp-btn-hollow>
      </div>
      <div class="webbas footerCon" v-else-if="type === 'button_details'">
        <asp-btn-hollow
          @click="closeEditMark"
          name="确定"
        >
        </asp-btn-hollow>
      </div>
      <div class="webbas footerCon" v-else>
        <asp-btn-solid
          name="保存"
          @click="appAndEditSave('editForm', editForm)"
        >
        </asp-btn-solid>
        <asp-btn-hollow
          @click="closeEditMark"
          name="取消"
        >
        </asp-btn-hollow>
      </div>
  </template>
    </asp-dialog>

    <ai :dialog-param="aiParam"></ai>
    <asp-dialog
      v-model="mapDialog.modelVisible"
      :visible.sync="mapDialog.modelVisible"
      :title="mapDialog.title"
      width="1040px"
    >
      <map-picker :city="`${this.mapData.province}${this.mapData.city}`" :default-search-txt="editForm.shopName" @selected="recieveMap"></map-picker>
    </asp-dialog>
  </div>
</template>
<script>
// import Uploader from '@/components/uploader.vue'
import ai from './ai.vue'
import MapPicker from '@/components/mappicker/index.vue'
export default {
  components: {
    // Uploader,
    ai,
    MapPicker
  },
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        this.editForm = JSON.parse(JSON.stringify(val.data))
        if (val.mapData) {
          this.mapData = JSON.parse(JSON.stringify(val.mapData))
        }
        this.editForm.roleNum = val.data.staffInfo.length
        this.editForm.pictureShow = this.$apiConfig.level1cloudstorePathPreFix + '/proxyFs/show?file_id=' + val.data.picture
        this.type = val.type
        // 处理营业范围数据
        if (this.editForm.businessScope) {
          this.editForm.businessScope = this.editForm.businessScope.split(',')
        } else {
          this.$set(this.editForm, 'businessScope', [])
        }
      }
    }
  },
  data () {
    return {
      aiParam: {},
      isBlur: false,
      type: '', // 类型 审核或编辑
      rules: {
        shopName: [
          { required: true, message: '请填写实体营业厅名称', trigger: 'change' }
        ],
        shopShortName: [
          { required: true, message: '请填写实体营业厅简称', trigger: 'change' }
        ],
        roleNum: [
          { required: true, message: '请添加员工', trigger: 'change' }
        ],
        identity: [
          { required: true, message: '请选择员工身份', trigger: 'change' }
        ],
        // name: [
        //   { required: true, message: '请输入员工姓名', trigger: 'blur' }
        // ],
        // phone: [
        //   { required: true, message: '请输入员工电话号码', trigger: 'blur' }
        // ],
        businessScope: [
          { type: 'array', required: true, message: '请选择营业范围', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请填写地址', trigger: 'change' }
        ],
        longitude: [
          { required: true, message: '请填写经度', trigger: 'change' }
        ],
        latitude: [
          { required: true, message: '请填写纬度', trigger: 'change' }
        ],
        businessHours: [
          { required: true, message: '请填营业时间', trigger: 'change' }
        ]
        // picture: [
        //   { required: true, message: '请上传图片', trigger: 'change' }
        // ]
      },
      editForm: {
        aiCheck: true,
        unifiedChannelId: '',
        shopName: '',
        shopShortName: '',
        shopContactPhone: '',
        picture: '',
        roleNum: null,
        businessScopeAll: [],
        businessScope: []
      },
      mapData: {},
      mapDialog: {
        modelVisible: false,
        title: '地图'
      }
    }
  },
  methods: {
    recieveMap(marker) {
      if (marker) {
        console.log(marker)
        this.editForm.latitude = marker.latitude
        this.editForm.longitude = marker.longitude
        this.editForm.address = marker.address
      }
      this.mapDialog.modelVisible = false
    },
    // AI稽核继续提交
    isSubmitting(value) {
      console.log(value, '--')
    },
    aaa(value) {
      setTimeout(() => {
        console.log(value)
      }, 1000)
    },
    xx() {
      this.isBlur = true
    },
    addYgBlur() {
      this.isBlur = false
    },
    // 处理图片传参
    input(q) {
      this.editForm.picture = q.fileId
      this.editForm.pictureShow = this.$apiConfig.level1cloudstorePathPreFix + '/proxyFs/show?file_id=' + q.fileId
      this.editForm.fileId = q.fileId
    },
    handNopass() {
      this.$emit('handNopass', this.editForm.applyId)
    },
    // 取消
    closeEditMark() {
      this.dialogParam.modelVisible = false
    },
    // 审核通过按钮 --- 校验是否存在覆盖的数据
    auditSuccess() {
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/isExistsPaiDuiApply', { applyId: this.editForm.applyId }).then(res => {
        if (res.status === '200') {
          if (Object.keys(res.data).length !== 0) {
            this.$alert('<p>该厅店的信息已更新，更新数据源来自：一级排队取号系统</p><p>请确认是否要覆盖一级排队系统同步的更新数据？</p><p style="color:red;font-size: 13px;">注：覆盖后附近厅将展示本次审核通过的信息，取消覆盖附近厅展示一级排队系统同步更新的厅店信息。</p>', '厅店信息变更提示', {
              dangerouslyUseHTMLString: true,
              distinguishCancelAndClose: true,
              center: true,
              customClass: 'custom-message-box',
              cancelButtonClass: 'btn-custom-cancel',
              showCancelButton: true,
              confirmButtonText: '确定覆盖，并审核通过',
              cancelButtonText: '取消覆盖，并审核不通过'
            }).then(() => {
              this.isPass(true)
            }).catch((e) => {
              if (e === 'cancel') {
                this.handNopass()
              }
            })
          } else {
            this.isPass(true)
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 审核通过
    isPass (aiCheck) {
      const params = {
        applyId: this.editForm.applyId,
        auditStatus: 1,
        aiCheck: aiCheck
      }
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/audit', params).then((res) => {
        if (res.status === '200') {
          if (res.data && res.data.checkMsgList) {
            this.aiParam = {
              title: 'AI厅店信息数智稽核提醒',
              modelVisible: true,
              data: res.data,
              type: 'onlyConfig',
              params: params
            }
          } else {
            this.dialogParam.modelVisible = false
            this.$message.success('审核成功')
            // 刷新列表
            this.$parent.refreshList()
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 添加---点击增加或者减少店员结构
    handleAddOrReduce(value) {
      // push
      if (this.editForm.staffInfo.length < value) {
        this.editForm.staffInfo.push({
          identity: 2,
          name: '',
          phone: '',
          staffNumber: ''
        })
      } else {
        this.editForm.staffInfo.pop()
      }
    },
    // 添加 --- 角色选择
    changeRole(role, i) {
      if (!role) {
        this.editForm.staffInfo.forEach((item, index) => {
          console.log(item.identity, 'item.identity')
          if (index !== i) {
            item.identity = 1
          }
        })
      }
    },
    removeSpaces(a) {
      return a.replace(/\s+/g, '')
    },
    // 删除角色
    deleteRole(index) {
      if (this.editForm.staffInfo.length > 1) {
        this.editForm.staffInfo.splice(index, 1)
        this.editForm.roleNum = this.editForm.staffInfo.length
      } else {
        this.$message.error('删除失败，店铺人员至少有一位')
      }
    },
    // 编辑 --- 保存
    appAndEditSave(editForm, params) {
      this.$refs[editForm].validate((valid) => {
        if (valid) {
          const sendParams = Object.assign({}, params)
          sendParams.businessScope = sendParams.businessScope.toString()
          if (this.type === 'iszg') {
            sendParams.operType = 2
          } else if (this.type === 'manageEdit') {
            sendParams.operType = 3
          } else if (this.type === 'restart') {
            sendParams.operType = 1
          } else if (this.type === 'rzfail') {
            sendParams.operType = 4
          } else {
            sendParams.operType = 0
          }
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/edit', sendParams).then((res) => {
            if (res.status === '200') {
              if (res.data && res.data.checkMsgList) {
                this.aiParam = {
                  title: 'AI厅店信息数智稽核提醒',
                  modelVisible: true,
                  data: res.data,
                  type: 'editForm',
                  params: params
                }
              } else {
                console.log('123')
                this.$message.success('保存成功')
                this.dialogParam.modelVisible = false
                // 刷新列表
                switch (this.type) {
                  case 'manageEdit':
                    this.$parent.refreshListTwo()
                    break
                  case 'restart':
                    this.$parent.refreshList()
                    break
                  case 'operationEdit':
                    this.$parent.refreshList()
                    break
                  case 'rzfail':
                    this.$parent.refreshList()
                    break
                }
              }
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form-item {
  margin-top: 10px;
}
.el-input-number {
  width: 200px;
}
.el-form-item__content {
  margin-left: 10px;
}
.setpicture {
  width: 80px;
  height: 80px;
}
.employees{
  width: 600px;
  border: 1px dashed #999999;
  margin-left: 10%;
  margin-top: 10px;
  padding-bottom: 10px;
  position: relative;
  span {
    position: absolute;
    right: -45px;
    bottom: 0;
    cursor: pointer;
  }
}
.tip {
  font-size: 17px;
  color: red;
  font-weight: bold;
  text-align: center;
  margin-top: 30px;
}
</style>
<style lang="scss">
.custom-message-box {
  width: 700px;
}
</style>
