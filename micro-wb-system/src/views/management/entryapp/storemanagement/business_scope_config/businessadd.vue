<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="60%"
  >
    <el-form ref="businessForm" :rules="rules" :model="businessForm">
      <el-form-item label="省份：">
        <span>{{ businessForm.provinceName }}</span>
      </el-form-item>
      <el-form-item label="业务编码：" prop="type" required>
        <el-select v-model="businessForm.type" placeholder="请选择" @change='$forceUpdate()' :disabled="type==='edit'">
          <el-option v-for="item in engList" :key="item" :value="item" :label="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="营业范围名称：" prop="title">
        <el-input v-model="businessForm.title"></el-input>
      </el-form-item>
      <el-form-item label="是否必选：" prop="channelDefault" required>
        <el-select v-model="businessForm.channelDefault" placeholder="请选择">
          <el-option :value="0" label="否"></el-option>
          <el-option :value="1" label="是"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item prop="channelDefault">
        <span style="font-weight: 600;"><span style="color: red;">* </span>是否设为以下渠道默认值：</span>
        <el-radio-group v-model="businessForm.channelDefault" @input="changeIsSetup">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
        <p style="font-size: 13px;margin-left: 25%;color: red;">设置后，此类渠道下的所有营业厅必须配置该营业范围</p>
        <div style="margin-left: 27%;">
          <p style="font-weight: 600;">【直营店】</p>
          <el-checkbox-group :disabled="businessForm.channelDefault === '0'" v-model="businessForm.channelCategorys">
            <el-checkbox label="1001" name="type">旗舰店1001</el-checkbox>
            <el-checkbox label="1002" name="type">标准店1002</el-checkbox>
            <el-checkbox label="1003" name="type">社区店1003</el-checkbox>
            <el-checkbox label="1004" name="type">自建手机卖场1004</el-checkbox>
            <el-checkbox label="1005" name="type">校园店1005</el-checkbox>
          </el-checkbox-group>
          <p style="font-weight: 600;">【加盟店】</p>
          <el-checkbox-group :disabled="businessForm.channelDefault === '0'" v-model="businessForm.channelCategorys">
            <el-checkbox label="2002" name="type">委托加盟2002</el-checkbox>
            <el-checkbox label="2003" name="type">带店加盟2003</el-checkbox>
          </el-checkbox-group>
          <p style="font-weight: 600;">【授权店】</p>
          <el-checkbox-group :disabled="businessForm.channelDefault === '0'" v-model="businessForm.channelCategorys">
            <el-checkbox label="2101" name="type">手机卖场2101</el-checkbox>
            <el-checkbox label="2102" name="type">手机专卖店2102</el-checkbox>
            <el-checkbox label="2103" name="type">授权代理店2103</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item> -->
    </el-form>
    <template slot="footer-center">
      <div class="webbas footerCon" v-if="type === 'edit'">
        <asp-btn-solid
          name="确定"
          @click="updateBusiness"
        >
        </asp-btn-solid>
      </div>
      <div class="webbas footerCon" v-else>
        <asp-btn-solid
          name="确定"
          @click="saveBusiness"
        >
        </asp-btn-solid>
        <asp-btn-hollow
          name="取消"
          @click="closeMark"
        >
        </asp-btn-hollow>
      </div>
    </template>
  </asp-dialog>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        this.type = val.type
        if (val.type === 'add') {
          this.businessForm.provinceCode = val.data[0].provinceCode
          this.businessForm.provinceName = val.data[0].provinceName
          this.getScopeList(this.businessForm.provinceCode) // 获取营业范围列表
        }
        if (val.type === 'edit') {
          this.businessForm.provinceCode = val.data.provinceCode
          this.businessForm.provinceName = val.data.province
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/businessScope/detail', { scopeId: val.data.scopeId }).then(res => {
            if (res.status === '200') {
              // res.data.channelDefault = res.data.channelDefault.toString()
              this.businessForm = Object.assign({}, res.data)
              this.businessForm.provinceName = res.data.province
            }
          })
        }
      }
    }
  },
  data () {
    return {
      type: '',
      options: [{
        value: 'A',
        label: 'A'
      }, {
        value: 'B',
        label: 'B'
      }, {
        value: 'C',
        label: 'C'
      }, {
        value: 'D',
        label: 'D'
      }, {
        value: 'E',
        label: 'E'
      }, {
        value: 'F',
        label: 'F'
      }, {
        value: 'G',
        label: 'G'
      }, {
        value: 'H',
        label: 'H'
      }, {
        value: 'I',
        label: 'I'
      }, {
        value: 'J',
        label: 'J'
      }, {
        value: 'K',
        label: 'K'
      }, {
        value: 'L',
        label: 'L'
      }, {
        value: 'M',
        label: 'M'
      }, {
        value: 'N',
        label: 'N'
      }, {
        value: 'O',
        label: 'O'
      }, {
        value: 'P',
        label: 'P'
      }, {
        value: 'Q',
        label: 'Q'
      }, {
        value: 'R',
        label: 'R'
      }, {
        value: 'S',
        label: 'S'
      }, {
        value: 'T',
        label: 'T'
      }, {
        value: 'U',
        label: 'U'
      }, {
        value: 'V',
        label: 'V'
      }, {
        value: 'W',
        label: 'W'
      }, {
        value: 'X',
        label: 'X'
      }, {
        value: 'Y',
        label: 'Y'
      }, {
        value: 'Z',
        label: 'Z'
      }],
      rules: {
        title: [
          { required: true, message: '请填写营业范围名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '业务编码不能为空', trigger: 'change' }],
        value: [
          { required: true, message: '请选择业务编码', trigger: 'change' }
        ],
        ischeck: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      businessForm: {
        provinceCode: '',
        provinceName: '',
        title: '',
        channelDefault: 0,
        channelCategorys: [],
        type: ''
      },
      engList: []
    }
  },
  methods: {
    changeIsSetup(a) {
      this.businessForm.channelDefault = a
      this.businessForm.channelCategorys = []
    },
    /** 获取营业范围列表 */
    getScopeList(provinceCode) {
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/businessScope/type/list', { provinceCode }).then(res => {
        if (res.status === '200') {
          this.engList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 新增 --- 确定
    saveBusiness() {
      const aa = 'businessForm'
      this.$refs[aa].validate((valid) => {
        if (valid) {
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/businessScope/add', this.businessForm).then(res => {
            if (res.status === '200') {
              this.$message.success(res.message)
              this.$parent.refreshList()
              this.closeMark()
              this.reset()
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 关闭弹窗
    closeMark() {
      this.dialogParam.modelVisible = false
      this.reset()
    },
    reset() {
      this.businessForm = {
        provinceCode: '',
        provinceName: '',
        title: '',
        channelDefault: 0,
        channelCategorys: [],
        type: ''
      }
    },
    // 修改 --- 按钮
    updateBusiness() {
      console.log(this.businessForm)
      const aa = 'businessForm'
      this.$refs[aa].validate((valid) => {
        if (valid) {
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/businessScope/edit', this.businessForm).then(res => {
            if (res.status === '200') {
              this.$message.success(res.message)
              this.$parent.refreshList()
              this.closeMark()
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.el-form-item {
  margin-top: 10px;
}
.el-form-item__content {
  margin-left: 10px;
}
</style>
