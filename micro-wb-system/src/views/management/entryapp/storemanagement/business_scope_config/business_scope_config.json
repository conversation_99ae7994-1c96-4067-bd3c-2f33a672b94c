{"list": [{"type": "form", "label": "表单容器", "columnName": "form_1727592272025", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1727592301268", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 3, "align": "left", "list": [{"type": "select", "label": "", "columnName": "provinceCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "provinceName", "option-value": "provinceCode", "option-alias": "", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/shop/common/regionInfo", "apiType": "post+json", "apiParam": "{}", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "Option 1", "value": "1", "disabled": false}, {"label": "Option 2", "value": "2", "disabled": false}], "hasParentForm": true, "labelWidth": 0, "targetName": "label_select_1727592381280", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1727592381280", "network$$$tag": false}]}, {"span": 4, "align": "left", "list": [{"type": "input", "label": "", "columnName": "scopeIdOrName", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "营业范围名称/ID", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 0, "oldColumnName": "input_1727592886872"}]}, {"span": 4, "align": "left", "list": [{"type": "select", "label": "状态：", "columnName": "status", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "草稿", "value": "1"}, {"label": "正常", "vlaue": "new_option1728632071916", "value": "2"}, {"label": "停用", "vlaue": "new_option1728632077460", "value": "3"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "草稿", "value": "1", "disabled": false}, {"label": "正常", "vlaue": "new_option1728632071916", "value": "2", "disabled": false}, {"label": "停用", "vlaue": "new_option1728632077460", "value": "3", "disabled": false}], "hasParentForm": true, "labelWidth": 60, "targetName": "label_select_1727594319971", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1727594319971"}]}, {"span": 4, "align": "left", "list": [{"type": "select", "label": "默认营业范围：", "columnName": "channelDefault", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "是", "value": "1"}, {"label": "否", "vlaue": "new_option1728632114348", "value": "2"}], "hidden": true, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "是", "value": "1", "disabled": false}, {"label": "否", "vlaue": "new_option1728632114348", "value": "2", "disabled": false}], "hasParentForm": true, "labelWidth": 115, "targetName": "label_select_1727592956752", "filterOptionStatus": [], "disabledOptionStatus": [], "oldColumnName": "select_1727592956752", "bpmFlowImageCloumnName": "", "hiddenStatus": []}]}, {"span": 7, "align": "left", "list": [{"type": "select", "label": "渠道默认营业范围：", "columnName": "channelCategorys", "defaultValue": [""], "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": true, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "旗舰店", "value": "1001"}, {"label": "标准店", "vlaue": "new_option1728632195261", "value": "1002"}, {"label": "社区店", "vlaue": "new_option1728632207908", "value": "1003"}, {"label": "自建手机卖场", "vlaue": "new_option1728632216676", "value": "1004"}, {"label": "校园店", "vlaue": "new_option1728632226124", "value": "1005"}, {"label": "委托加盟", "vlaue": "new_option1728632234940", "value": "2002"}, {"label": "带店加盟", "vlaue": "new_option1728632243372", "value": "2003"}, {"label": "手机卖场", "vlaue": "new_option1728632255460", "value": "2101"}, {"label": "手机专卖店", "vlaue": "new_option1728632264004", "value": "2102"}, {"label": "授权代理店", "vlaue": "new_option1728632273555", "value": "2103"}], "hidden": true, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "旗舰店", "value": "1001", "disabled": false}, {"label": "标准店", "vlaue": "new_option1728632195261", "value": "1002", "disabled": false}, {"label": "社区店", "vlaue": "new_option1728632207908", "value": "1003", "disabled": false}, {"label": "自建手机卖场", "vlaue": "new_option1728632216676", "value": "1004", "disabled": false}, {"label": "校园店", "vlaue": "new_option1728632226124", "value": "1005", "disabled": false}, {"label": "委托加盟", "vlaue": "new_option1728632234940", "value": "2002", "disabled": false}, {"label": "带店加盟", "vlaue": "new_option1728632243372", "value": "2003", "disabled": false}, {"label": "手机卖场", "vlaue": "new_option1728632255460", "value": "2101", "disabled": false}, {"label": "手机专卖店", "vlaue": "new_option1728632264004", "value": "2102", "disabled": false}, {"label": "授权代理店", "vlaue": "new_option1728632273555", "value": "2103", "disabled": false}], "hasParentForm": true, "labelWidth": 150, "targetName": "label_select_1727594190703", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1727594190703", "hiddenStatus": []}]}, {"span": 2, "align": "left", "list": [{"type": "button", "label": "查询", "columnName": "button_1727594196934", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "event": "submit", "tableId": "table_1727593275516", "formId": "form_1727592272025", "authSwitch": true, "authId": "business_but_query"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false}, {"type": "empty", "label": "空容器", "columnName": "empty_1727593133687", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1727593144149", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 24, "align": "right", "list": [{"type": "button", "label": "新增", "columnName": "business_add", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": false, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "bbuss", "authSwitch": true, "authId": "business_but_add"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": false, "labelWidth": 100}, {"type": "table", "label": "表格", "columnName": "table_1727593275516", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": true, "show-pagination": true, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [{"label": "button_1727593891202", "columnName": "button_1727593891202", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "status", "compare": "=", "compareType": "staticData", "targetValue": "1||3", "authSwitch": false, "authId": "", "if": "||"}]}, {"label": "button_1727594050630", "columnName": "button_1727594050630", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "status", "compare": "=", "compareType": "staticData", "targetValue": "2", "authSwitch": false, "authId": "", "if": "||"}]}, {"label": "button_1727594067822", "columnName": "button_1727594067822", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "status", "compare": "=", "compareType": "staticData", "targetValue": "1||3", "authSwitch": false, "authId": "", "if": "||"}]}], "list": [{"timeStamp": 1727593767787, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "filterColumn": false}, {"label": "ID", "columnName": "scopeId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "业务编码", "columnName": "type", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center"}, {"label": "营业范围名称", "columnName": "title", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "是否必选", "columnName": "channelDefault", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "否", "value": "0"}, {"label": "是", "value": "1"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ",", "switchValueType": "boolean"}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center"}, {"label": "省份", "columnName": "province", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "150", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "添加时间", "columnName": "createdTime", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "150", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "默认营业范围", "columnName": "channelDefault", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "150", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "否", "value": "0"}, {"label": "是", "value": "1"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": false, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "默认渠道", "columnName": "channelCategory", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "状态", "columnName": "status", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "草稿", "value": "1"}, {"label": "正常", "value": "2"}, {"label": "停用", "value": "3"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [{"type": "text", "timestamp": 1727593891202, "columnName": "button_1727593891202", "label": "启用", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/businessScope/status", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"scopeId\":\"$scopeId$\",\"provinceCode\":\"$provinceCode$\",\"status\":2}", "confirmationSwitch": true, "confirmationMessage": "确认启用吗？", "authSwitch": true, "authId": "business_but_sors", "_hidden_state": true}, {"type": "text", "timestamp": 1727594050630, "columnName": "button_1727594050630", "label": "停用", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "confirmationSwitch": true, "confirmationMessage": "停用后，所有营业厅均不再展示该营业范围。是否确认停用？", "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/businessScope/status", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"scopeId\":\"$scopeId$\",\"provinceCode\":\"$provinceCode$\",\"status\":3}", "authSwitch": true, "authId": "business_but_sors", "_hidden_state": true}, {"type": "text", "timestamp": 1727594055118, "columnName": "button_edit", "label": "编辑", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "authSwitch": true, "authId": "business_but_edit", "_hidden_state": true}, {"type": "text", "timestamp": 1727594067822, "columnName": "button_1727594067822", "label": "删除", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/businessScope/delete", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"scopeId\":\"$scopeId$\"}", "confirmationSwitch": true, "confirmationMessage": "是否确认删除？", "authSwitch": true, "authId": "business_but_delete", "_hidden_state": true}, {"type": "text", "timestamp": 1727594071110, "columnName": "button_1727594071110", "label": "查看操作记录", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "dialog-table", "dialogConfig": {"bindName": "businessoperate", "title": "操作记录", "subTitle": "", "titleClass": "dialog-title-default", "width": "800px", "height": "auto", "toolList": []}, "authSwitch": true, "authId": "business_but_look", "_hidden_state": true}], "operation-width": "200", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 20, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/proxyData/api/proxy/o2o/businessScope/page", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1727593767787, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "filterColumn": false}, {"label": "ID", "columnName": "scopeId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "业务编码", "columnName": "scopeId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center"}, {"label": "营业范围名称", "columnName": "title", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "是否必选", "columnName": "scopeId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center"}, {"label": "省份", "columnName": "province", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "150", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "添加时间", "columnName": "createdTime", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "150", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "默认营业范围", "columnName": "channelDefault", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "150", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "否", "value": "0"}, {"label": "是", "value": "1"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": false, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "默认渠道", "columnName": "channelCategory", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "状态", "columnName": "status", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "草稿", "value": "1"}, {"label": "正常", "value": "2"}, {"label": "停用", "value": "3"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "hasParentForm": false, "labelWidth": 100, "paginationStatus": [], "router": {}, "show-operation-status": [], "formId": "form_1727592272025"}], "classify": "empty", "hidden": false}], "model": {"provinceCode": "", "scopeIdOrName": "", "status": "", "channelDefault": "", "channelCategorys": [""]}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "business_scope_config", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "mini", "class": "", "layoutType": "flex", "statusList": [], "localProxy": "", "nigxProxy": "", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": ""}, "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "营业范围配置"}}