<!-- 用户支付渠道拦截报表 -->
/**
* 业务实体
*/
<template>
  <div style="height: 100%; ">
    <asp-smart-table ref="aspSmartTable"
                     v-model="model"
                     :table-json="tableJson"
                     :size-change="sizeChange"
                     :current-change="currentChange"
                     :before-http="beforeHttp"
                     :after-http="afterHttp"
                     :before-button="beforeButton"
                     :before-router="beforeRouter"
                     :before-table-row-render="beforeTableRowRender"
                     :before-table-render="beforeTableRender"
                     :before-click-table-cell="beforeClickTableCell"
                     :dialog-config="dialogConfig"
                     :render-table="renderTable"
                     @on="onbind">
                     <template slot="upload_btn">
                      <div class="btnStyle">
          <div class="btnitem">
            <el-upload
              ref="upload"
              :action="uploadUrl"
              accept=".ppt,.pdf,.pptx"
              :before-upload="beforeUpload"
              :on-error="handleErrors"
              :show-file-list="false"
              :on-success="handleChangeShopInfos"
              :auto-upload="true"
              >
              <span title="导入">上传文件</span>
            </el-upload>
          </div>
          <div class="btnitem" v-show="false">
            <el-upload
              ref="uploads"
              :action="uploadUrls"
              accept=".ppt,.pdf,.pptx"
              :before-upload="beforeUpload"
              :show-file-list="false"
              :on-success="handleChangeShopInfo"
              :auto-upload="true"
              :on-error="handleError"
              >
              <span title="导入">上传文件</span>
            </el-upload>
          </div>
        </div>
        </template>
    </asp-smart-table>
  </div>
</template>

<script>
import aspUtil from '@/components/aspire/asp-api/asp-Utils.js'
export default {
  name: 'operatie_manual',
  data () {
    return {
      updateid: '',
      applyId: null,
      tableJson: null,
      model: {},
      renderTable: {
        picture: (h, item, scope, tableData) => { // xxdy是列名columnName, 请根据实际情况修改
          return (<span style="color: red; background: #f2dede;">{scope.row[item.columnName]}</span>)
        }
      },
      uploadUrl: aspUtil.getRealUrl(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/file/upload', this),
      uploadUrls: '',
      dialogStatus: '',
      dialogConfig: {
      }
    }
  },
  async mounted () {
    this.tableJson = JSON.parse(JSON.stringify(require('./operatie_manual.json')))
    this.model = this.tableJson.model
  },
  methods: {
    handleErrors (err, file, fileList) {
      console.log(err, 'err')
      if (err.status === 413) {
        this.$message.error('文件不能超过10M，请重新上传')
      }
    },
    handleError (err, file, fileList) {
      console.log(err, 'err')
      if (err.status === 413) {
        this.$message.error('文件不能超过10M，请重新上传')
      }
    },
    // 刷新列表
    refreshList () {
      this.$refs.aspSmartTable.asp_sendTableQuery('table_1740973160020')
    },
    // 上传文件前 --- 校验
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件不能超过10M，请重新上传')
        return false // 阻止文件上传
      }
      return true // 允许上传
    },
    // 上传文件
    handleChangeShopInfos(response, file, fileList) {
      // 文件上传成功后 逻辑处理
      if (response.status === '200') {
        this.$message.success('上传成功')
        this.refreshList()
      } else {
        this.$message.error(response.message)
      }
    },
    // 更新文件
    handleChangeShopInfo(response, file, fileList) {
      // 文件上传成功后 逻辑处理
      if (response.status === '200') {
        this.$message.success('更新成功')
        this.refreshList()
      } else {
        this.$message.error(response.message)
      }
    },
    // 刷新列表
    // refreshList () {
    //   this.$refs.aspSmartTable.asp_sendTableQuery('editAndAuditList')
    // },
    /**
     * 智能表格监听所有组件的交互事件操作：监听、捕捉事件响应
     * @param item 响应组件对象属性集（类型、组件Id，控件内元数属性），columnName每个组件单元的唯一码（组件单元Id）
     * @param type 事件类型（click/blur/onblur等）
     * @param index 当是表格组件时，返回组件的行号
     * @param model 查询区域表单数据模型
     * @param tableModel 表格组件数据模型
     * @param row 表格行数据
     * @param multipleSelection 表格多选数据（当出现列表复选时才有，包括跨页数据，整行数据）
     * @param sortProps 排序属性
     * @returns {Promise<void>}
     */
    async onbind ({ item, type, index, model, tableModel, row, subFormSelectData, sortProps }) {
      if (item.columnName === 'button_update') {
        this.$refs.uploads.$children[0].$refs.input.click()
        this.updateid = row.id
        this.uploadUrls = aspUtil.getRealUrl(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/file/update?id=' + this.updateid, this)
      }
      // 下载
      if (item.columnName === 'button_download') {
        console.log(row, 'row')
        const params = {
          fileId: row.fileId,
          downLoadName: row.uploadFileName
        }
        this.$aspHttps.asp_Post_file(
          this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/export/operateManual/file/download',
          params
        ).then(res => {
          console.log(res, 'resss')
        })
      }
    },
    /**
     * 智能表格页面所有请求前的前置操作
     * 例如：修改请求参数、修改请求方式、修改请求URL、或者请求条件不满足不给发送请求
     * @param tableItem 组件对象属性集
     * @param params 请求参数body，数据格式如下(字段格式不一致的需要自行转换)如下:
     *                                         {
     *                                             page：1， // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                             rows: 10，// 分页属性(页大小)，数字类型 （不是分页接口，没有这个字段）
     *                                             .......   // 业务属性字段
     *                                          }
     * @param httpMethod.url 请求地址URL
     * @param httpMethod.type 请求方式，目前主要六种：'post+json', 'post+form', 'get'，'put+json'，'delete+json'，'patch+json'
     * @param row 当组件为表格并且是表格操作列触发的请求，此参数返回表格行数据，其它返回undefined
     */
    beforeHttp ({ tableItem, params, httpMethod, row }) { },
    /**
     * 智能表格页面所有请求后置操作
     * 例如：请求后的数据包体需要做二次处理
     * @param tableItem 组件对象属性集
     * @param responseBody 响应数据body, 数据包格式(字段格式不一致的需要自行转换)如下：
     *                                              {
     *                                                status: "200", // 业务状态码，字符串类型，成功返回"200"，失败返回其它数据
     *                                                message: "",   // 业务提示语，字符串类型，给业务的提示语属性
     *                                                page：1，      // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                                total: 53，    // 分页属性(总记录大小)，数字类型 （不是分页接口，没有这个字段）
     *                                                data: {}或者[] // 业务数据区，对象或数组类型，用于各业务逻辑处理
     *                                               }
     */
    afterHttp ({ tableItem, responseBody }) {
    },
    /**
     * 智能表格页面上的按钮的前置操作：包括不限于查询区域，表格顶部、表格操作列
     * 例如：对操作按钮进行处理的数据进行预处理，或者对按钮请求进行个性胡逻辑判断等
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param item 组件对象属性集
     * @param rowObj 当组件为表格操作列中的按钮，此参数返回表格行数据，其它返回undefined
     * @param next 回调函数
     */
    beforeButton ({ item, rowObj, next }) {
      next(item, rowObj) // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 智能表格页面路由跳转的前置操作
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param item 响应组件对象属性集
     * @param row 当响应组件为表格操作列中的按钮时，此参数返回表格行数据，其它返回undefined
     * @param routerObj.routerType: 路由类型
     * @param routerObj.routerParamType 路由参数类型
     * @param routerObj.routerUrl 路由地址或名称
     * @param routerObj.routerParamValue 路由参数
     * @param next 回调函数
     */
    beforeRouter ({ item, row, routerObj, next }) {
      next(routerObj) // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 表格内容渲染之前的前置动作，
     * @param tableName 当前表格名称
     * @param tableData 表格当页的数据
     * @param columnItem 表格当前列的信息
     * @param scope 表格行信息包含属性 $index row等
     * @param callback 回调事件，用于改变指定列的显示内容
     * @param callback 参数说明如下
     * 参数一：指定修改的表格名称 tableName
     * 参数二：指定修改的列名 columnName
     * 参数三：指定索引集合，整列生效则传空数组[],指定某几行生效则传索引集合[1,3] indexList
     * 参数四：显示内容{ content: 可以是文本也可以是html代码片段}
     * 示例：callBack('aspSmartTable', 'name', [], { content: `【附加标题】<a @click="detial(${scope.row})">${scope.row.name}</a>` })
     */
    beforeTableRender ({ tableName, tableData, columnItem, scope }, callBack) { },
    /**
     * 智能表格监听所有行绘制的前置回调响应
     * @param item 组件对象属性集(类型、组件columnName，组件内元数属性)，columnName是每个组件的唯一标识码
     * @param tableData 表格数据模型
     * @param row:  表格组件当前绘制的行数据
     * @param rowClassName: 子表单组件当前行绘制class name
     * @param callback: 回调api
     * @param           callback回调api参数: rowClassName: 子表单组件当前行绘制class name
     */
    beforeTableRowRender ({ item, tableData, row, rowClassName }) {
      return rowClassName // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 智能表格单元格点击的前置操作
     * @param item 响应组件对象属性集
     * @param row 此参数返回表格行数据
     * @param tableData: 表格数据模型
     */
    beforeClickTableCell ({ item, row, tableData }) { },
    /**
     * 表格页码大小发生变化时触发的前置事件
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param pageSize 表格页码大小
     * @param next 回调函数
     */
    sizeChange ({ tableItem, pageSize, next }) {
      next(true) // 允许继续运行传true, 否则传false  // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 表格当前页发生变化时触发的前置事件，包括点翻页、上一页、下一页、刷新页、重置页
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param currentPage 当前页码号
     * @param next 回调函数
     */
    currentChange ({ tableItem, currentPage, next }) {
      next(true) // 允许继续运行传true, 否则传false // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-table th.el-table__cell>.cell {
  text-align: center;
}
::v-deep .el-table .cell {
  text-align: center;
}
// ::v-deep .asp-smart-table {
//   height: 120vh;
// }
::v-deep .create-tab-css {
  margin: 4px 4px;
}
.asp-pagination {
  background-color: #fff;
}
::v-deep .asp-smart-table .widget-box-button{
  padding-top: 0 !important;
}
::v-deep .asp-smart-table .solid-with-icon-btn {
  padding: 7px 25px;
}
.btnStyle {
  display: flex;
  justify-content: flex-end;
  .btnitem {
    margin-right: 10px;
    display:flex;
    flex-shrink: 0;
    border:1px solid #DCDFE6;
    border-radius:5px;
    padding:1px 5px;
    font-size: 14px;
    cursor: pointer;
    user-select: none;
    background: linear-gradient(360deg, #4576e4 0%, #6f9ef0 100%);
    color: #ffffff;
    .icondownload{
      margin: 0 7px;
    }
    span{
      color: #fff;
    }
  }
}
</style>
