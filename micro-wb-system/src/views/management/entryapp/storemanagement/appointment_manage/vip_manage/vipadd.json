{"formList": [{"label": "省份：", "type": "select", "isLabelWidth": false, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "请选择省份", "rules": [{"required": true, "message": "请选择省份", "trigger": "blur"}], "columnName": "provinceCode", "targetName": "label_select_1742550950475", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "provinceName", "option-value": "provinceCode", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/shop/common/regionInfo", "apiType": "post+json", "apiParam": "", "separator": ","}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "", "showOptions": [{"label": "Option 1", "value": "1", "disabled": false}, {"label": "Option 2", "value": "2", "disabled": false}], "labelWidth": 160, "isHelpTipText": "", "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": "", "operations": [{"status": ["add"], "attr": "clearable", "formatter": ""}, {"status": ["edit"], "attr": "hidden", "formatter": ""}], "dynamic": {}}, {"label": "VIP类型：", "type": "select", "isLabelWidth": false, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "请选择VIP类型", "rules": [{"required": true, "message": "请选择VIP类型", "trigger": "blur"}], "columnName": "vipType", "targetName": "label_select_1742550961857", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "vipTypeName", "option-value": "vipTypeName", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/vipCustomerConfig/getVipTypes", "apiType": "post+json", "apiParam": "", "separator": ","}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "", "showOptions": [{"label": "Option 1", "value": "1", "disabled": false}, {"label": "Option 2", "value": "2", "disabled": false}], "labelWidth": 160, "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": "", "operations": [{"status": ["edit"], "attr": "disabled", "formatter": ""}]}, {"label": "VIP星级：", "type": "select", "isLabelWidth": false, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "请选择VIP星级", "rules": [{"required": true, "message": "请选择VIP星级", "trigger": "blur"}], "columnName": "vipLevel", "targetName": "label_select_1743499563141", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "vipGradesName", "option-value": "vipGradesName", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/vipCustomerConfig/getVipGrades", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "showOptions": [{"label": "Option 1", "value": "1", "disabled": false}, {"label": "Option 2", "value": "2", "disabled": false}], "labelWidth": 160, "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": "", "operations": [{"status": ["edit"], "attr": "disabled", "formatter": ""}]}, {"label": "是否VIP：", "type": "select", "isLabelWidth": false, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "请选择是否为VIP", "rules": [{"required": true, "message": "请选择是否为VIP", "trigger": "blur"}], "columnName": "vipStatus", "targetName": "label_select_1743499593838", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "VIP", "value": "0"}, {"label": "非VIP", "value": "1"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "showOptions": [{"label": "VIP", "value": "0", "disabled": false}, {"label": "非VIP", "value": "1", "disabled": false}], "labelWidth": 160, "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": ""}, {"label": "VIP等级：", "type": "select", "isLabelWidth": false, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "请选择VIP等级", "rules": [{"required": true, "message": "请选择VIP等级", "trigger": "blur"}], "columnName": "vipGrade", "targetName": "label_select_1743499629648", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "vipLevelName", "option-value": "vipLevelName", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/vipCustomerConfig/getVipLevels", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "showOptions": [{"label": "Option 1", "value": "1", "disabled": false}, {"label": "Option 2", "value": "2", "disabled": false}], "labelWidth": 160, "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": ""}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1742555981396", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "cacel", "type": "primary", "icon": "", "label": "取消", "interactive": "", "validateProp": "", "apiName": "", "class": "hollow-with-icon-btn", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_close_dialog", "add": "show", "edit": "show"}, {"columnName": "submit", "type": "primary", "icon": "", "label": "保存", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/vipCustomerConfig/insertConfig", "class": "solid-with-icon-btn", "default": "show", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": true, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": "", "add": "show", "edit": "hidden"}, {"is-table-column": false, "columnName": "edit", "label": "修改", "type": "primary", "class": "solid-with-icon-btn", "icon": "", "bpmFlowImageCloumnName": "", "default": "show", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiMethod": "post+json", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/vipCustomerConfig/updateConfig", "apiCloseDialogWithResposne": true, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "add": "hidden", "edit": "show", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "activeType": "button_group_form_interface", "apiParam": {}, "apiIsRefresh": "", "apiIsReturn": ""}], "name": "layout", "labelWidth": 160, "copyOldVal": ""}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "vipadd", "defaultClass": "webbas", "size": "small", "statusList": ["add", "edit"], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "isOpenExportPDFSwitch": false, "exportPDFProps": {"eleId": "", "eleClassName": "", "markProps": {"pdfFileName": "", "markTitle": "", "markImgUrl": "", "imgFileName": "", "opacity": 100, "imgUp": false, "pStyle": {"font-size": "44px", "margin": "40px 0px", "display": "flex", "justify-content": "center"}}}, "titleName": "预约取号-VIP客户配置新增"}, "dataConfig": {}, "virtual_model": {}, "model": {"provinceCode": "", "vipType": "", "vipLevel": "", "vipGrade": "", "vipStatus": ""}}