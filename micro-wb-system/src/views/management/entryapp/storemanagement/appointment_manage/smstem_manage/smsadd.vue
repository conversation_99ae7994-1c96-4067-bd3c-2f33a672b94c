<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="50%"
  >
  <el-form ref="dataFrom" :model="dataFrom" :rules="rules">
    <el-form-item label="所属省份：" prop="provinceCode">
      <el-select v-model="dataFrom.provinceCode" placeholder="请选择所属省份" :disabled="type === 'audit' || type === 'edit'">
        <el-option v-for="item in provinceList" :key="item.provinceCode" :label="item.provinceName" :value="item.provinceCode"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="模板类型：" style="margin-top: 20px;" prop="typeId">
      <el-select v-model="dataFrom.typeId" placeholder="请选模板类型" @change="checkTemType" :disabled="type === 'audit' || type === 'edit'">
        <el-option v-for="item in templateTypeList" :key="item.id" :label="item.smsTypeName" :value="item.id"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="模板名称：" style="margin-top: 20px;" prop="templateName">
      <el-input v-model="dataFrom.templateName" :disabled="type === 'audit'"></el-input>
    </el-form-item>
    <el-form-item label="模板内容：" style="margin-top: 20px;" prop="templateContent">
      <el-input type="textarea" :disabled="type === 'audit'" maxlength="255" show-word-limit v-model="dataFrom.templateContent"></el-input>
    </el-form-item>
    <el-form-item label="是否通过：" style="margin-top: 20px;" prop="auditStatus" v-if="type === 'audit'">
      <el-select v-model="dataFrom.auditStatus" placeholder="请选择">
        <el-option label="审核通过" value="1"></el-option>
        <el-option label="审核不通过" value="2"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="审核信息：" style="margin-top: 20px;" prop="remarks" v-if="type === 'audit'">
      <el-input type="textarea" v-model="dataFrom.remarks"></el-input>
    </el-form-item>
    <p class="tips">可选参数 ：{电子票号}{取号时间}{营业厅}</p>
  </el-form>
  <template slot="footer-center">
    <div class="webbas footerCon">
      <asp-btn-hollow
      @click="closeDialog"
        name="取消"
      >
      </asp-btn-hollow>
      <asp-btn-solid
        v-if="type === 'add'"
        name="保存"
        @click="save"
      >
      </asp-btn-solid>
      <asp-btn-solid
        v-if="type === 'edit'"
        name="保存"
        @click="editSave"
      >
      </asp-btn-solid>
      <asp-btn-solid
        v-if="type === 'audit'"
        name="保存"
        @click="auditSave"
      >
      </asp-btn-solid>
    </div>
  </template>
  </asp-dialog>
</template>
<script>
const defaultDataForm = {
  provinceCode: '', // 省份
  typeId: '', // 模板类型
  templateName: '', // 模板名称
  templateContent: '', // 模板内容
  auditStatus: '', // 审核是否通过
  remarks: '' // 审核信息
}
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        // 当前点击按钮类型
        this.type = val.type
        // 点击新增 清空数据
        if (this.type === 'add') {
          this.dataFrom = { ...defaultDataForm }
        } else if (this.type === 'audit') {
          this.dataFrom = Object.assign({}, val.data)
          this.dataFrom.auditStatus = ''
        } else {
          this.dataFrom = Object.assign({}, val.data)
        }
        // 省份数据
        this.$aspHttps.asp_Post(
          this.$apiConfig.level1cloudstorePathPreFix + '/shop/common/regionInfo', {}).then(res => {
          if (res.status === '200') {
            // 集团用户删除全国选项
            if (JSON.parse(sessionStorage.getItem('buttonInfo')).group_flag) {
              res.data.splice(0, 1)
              this.provinceList = res.data
            } else {
              this.provinceList = res.data
            }
          } else {
            this.$message.error(res.message)
          }
        })
        // 模板类型数据
        this.$aspHttps.asp_Post(
          this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsTypeManagement/all', {}).then(res => {
          if (res.status === '200') {
            this.templateTypeList = res.data
          } else {
            this.$message.error(res.message)
          }
        })
      }
    }
  },
  data () {
    return {
      provinceList: [],
      templateTypeList: [],
      type: '',
      dataFrom: { ...defaultDataForm },
      rules: {
        provinceCode: [
          { required: true, message: '请选择省份', trigger: 'change' }
        ],
        typeId: [
          { required: true, message: '请选择模板类型', trigger: 'change' }
        ],
        templateName: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ],
        templateContent: [
          { required: true, message: '请填写模板内容', trigger: 'blur' }
        ],
        auditStatus: [
          { required: true, message: '请选择审核结果', trigger: 'change' }
        ],
        remarks: [
          { required: true, message: '请填写审核信息', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 选择模板类型
    checkTemType(e) {
      console.log(e)
      const item = this.templateTypeList.find(item => item.id === e)
      console.log(item)
      this.dataFrom.templateName = item.smsTypeName
      this.dataFrom.templateContent = item.smsContent
    },
    // 新增
    save() {
      this.$refs.dataFrom.validate((valid) => {
        if (valid) {
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsTemplateConfig/create', this.dataFrom).then(res => {
            if (res.status === '200') {
              this.dataFrom = {}
              this.$message.success(res.message)
              this.closeDialog()
              this.$parent.refreshList()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 编辑
    editSave() {
      this.$refs.dataFrom.validate((valid) => {
        if (valid) {
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsTemplateConfig/update', this.dataFrom).then(res => {
            if (res.status === '200') {
              this.dataFrom = {}
              this.$message.success(res.message)
              this.closeDialog()
              this.$parent.refreshList()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 审核
    auditSave() {
      this.$refs.dataFrom.validate((valid) => {
        if (valid) {
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsTemplateConfig/audit', this.dataFrom).then(res => {
            if (res.status === '200') {
              this.dataFrom = {}
              this.$message.success(res.message)
              this.closeDialog()
              this.$parent.refreshList()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    closeDialog() {
      this.dialogParam.modelVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.tips{
  font-size: 13px;
  margin-left: 22%;
  span{
    color: red;
  }
}
</style>
