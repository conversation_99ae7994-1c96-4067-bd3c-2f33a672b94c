<template>
  <div class="webbas" style="height: 80vh;overflow: scroll;position: relative;">
    <el-row>
        <el-col :span="5">
          <div style="display: flex; align-items: center;">
            <p style="width: 70px;font-weight: bold;">省份：</p>
            <el-select v-model="form.provinceCode" placeholder="请选省份">
              <el-option v-for="item in provinceList" :key="item.provinceCode" :label="item.provinceName" :value="item.provinceCode"></el-option>
            </el-select>
          </div>
        </el-col>
        <el-col style="margin-left: 10px;" :span="18">
          <asp-btn-solid class="solid-with-icon-btn" name="查询" style="margin-top: 1%;" @click="select"></asp-btn-solid>
        </el-col>
      </el-row>
      <el-table :data="tableData" border style="width: 100%;margin-top: 20px;padding-bottom: 50px;">
        <el-table-column
          label="序号"
          align="center"
          type="index"
          width="50">
        </el-table-column>
        <el-table-column
          prop="queuePriority"
          align="center"
          label="队列优先级"
          width="100">
          <template slot-scope="scope">
            <el-input v-model="scope.row.queuePriority" v-if="scope.row.type === 'edit'"></el-input>
            <span v-else>{{ scope.row.queuePriority }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="provinceName"
          align="center"
          label="省份"
          width="80">
        </el-table-column>
        <el-table-column
          prop="queueCode"
          align="center"
          label="队列编码"
          width="100">
        </el-table-column>
        <el-table-column
          prop="queueAliasCode"
          align="center"
          label="队列代替编码"
          width="120">
          <template slot-scope="scope">
            <el-select v-model="scope.row.queueAliasCode" placeholder="模板类型" v-if="scope.row.type === 'edit'">
              <el-option v-for="item in queueCodeList" :key="item.queueCode" :label="item.queueName" :value="item.queueCode" :disabled="idJsonHas.includes(item.queueCode)"></el-option>
            </el-select>
            <p v-else>{{ scope.row.queueAliasCode }}</p>
          </template>
        </el-table-column>
        <el-table-column
          prop="queueName"
          align="center"
          label="队列名称"
          width="200">
        </el-table-column>
        <el-table-column
          prop="queueAliasName"
          align="center"
          label="队列替换名称"
          width="200">
          <template slot-scope="scope">
            <el-input v-model="scope.row.queueAliasName" maxlength="20" show-word-limit v-if="scope.row.type === 'edit'"></el-input>
            <p v-else>{{ scope.row.queueAliasName }}</p>
          </template>
        </el-table-column>
        <el-table-column
          prop="isMandatory"
          align="center"
          label="是否必选"
          width="100">
          <template slot-scope="scope">
            <el-select v-model="scope.row.isMandatory" placeholder="模板类型" v-if="scope.row.type === 'edit'">
              <el-option label="否" :value="0"></el-option>
              <el-option label="是" :value="1"></el-option>
            </el-select>
            <p v-else>{{ showIsMandatory(scope.row.isMandatory) }}</p>
          </template>
        </el-table-column>
        <el-table-column
          prop="titles"
          align="center"
          label="业务类型"
          width="200">
          <template slot-scope="scope">
            <el-select multiple v-model="scope.row.scopeIds" placeholder="请选择业务类型" v-if="scope.row.type === 'edit'">
              <el-option v-for="item in businessTypeList" :key="item.scopeId" :label="item.title" :value="item.scopeId"></el-option>
            </el-select>
            <p v-else>{{ scope.row.titles }}</p>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          >
          <template slot-scope="scope">
            <div style="cursor:pointer" v-if="queue_p_edit_btn">
              <el-button  class="solid-with-icon-btn" @click="edit(scope.row)" v-if="scope.row.type === 'over'">编辑</el-button>
              <el-button class="solid-with-icon-btn" @click="save(scope.$index,scope.row)" v-if="scope.row.type === 'edit'">保存</el-button>
              <el-button class="hollow-with-icon-btn" @click="cancel(scope.row)" v-if="scope.row.type === 'edit'">取消</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
  </div>
</template>
<script>
export default {
  data () {
    return {
      queue_p_edit_btn: JSON.parse(sessionStorage.getItem('buttonInfo')).queue_p_edit_btn, // 编辑按钮
      provinceList: [],
      businessTypeList: [],
      form: {
        provinceCode: ''
      },
      tableData: [],
      queueCodeList: [
        { queueName: 'A', queueCode: 'A' },
        { queueName: 'B', queueCode: 'B' },
        { queueName: 'C', queueCode: 'C' },
        { queueName: 'D', queueCode: 'D' },
        { queueName: 'E', queueCode: 'E' },
        { queueName: 'F', queueCode: 'F' },
        { queueName: 'G', queueCode: 'G' },
        { queueName: 'H', queueCode: 'H' },
        { queueName: 'I', queueCode: 'I' },
        { queueName: 'J', queueCode: 'J' },
        { queueName: 'K', queueCode: 'K' },
        { queueName: 'L', queueCode: 'L' },
        { queueName: 'M', queueCode: 'M' },
        { queueName: 'N', queueCode: 'N' },
        { queueName: 'O', queueCode: 'O' },
        { queueName: 'P', queueCode: 'P' },
        { queueName: 'Q', queueCode: 'Q' },
        { queueName: 'R', queueCode: 'R' },
        { queueName: 'S', queueCode: 'S' },
        { queueName: 'T', queueCode: 'T' },
        { queueName: 'U', queueCode: 'U' },
        { queueName: 'V', queueCode: 'V' },
        { queueName: 'W', queueCode: 'W' },
        { queueName: 'X', queueCode: 'X' },
        { queueName: 'Y', queueCode: 'Y' },
        { queueName: '1', queueCode: '1' },
        { queueName: '2', queueCode: '2' },
        { queueName: '3', queueCode: '3' },
        { queueName: '4', queueCode: '4' },
        { queueName: '5', queueCode: '5' },
        { queueName: '6', queueCode: '6' },
        { queueName: '7', queueCode: '7' },
        { queueName: '8', queueCode: '8' },
        { queueName: '9', queueCode: '9' }
      ]
    }
  },
  computed: {
    idJsonHas: function() {
      const queueAliasArr = []
      const queueArr = []
      this.tableData.forEach((item) => {
        console.log(item, 'itemmm')
        queueAliasArr.push(item.queueAliasCode)
      })
      this.tableData.forEach((item) => {
        console.log(item, 'itemmm')
        queueArr.push(item.queueCode)
      })
      const allArr = [...queueAliasArr, ...queueArr]
      return allArr
    }
  },
  mounted() {
    // 省份接口
    this.$aspHttps.asp_Post(
      this.$apiConfig.level1cloudstorePathPreFix + '/shop/common/regionInfo', {}).then(res => {
      if (res.status === '200') {
        this.provinceList = res.data
        this.form.provinceCode = this.provinceList[0].provinceCode
        this.select()
      } else {
        this.$message.error(res.message)
      }
    })
    // 处理数据
    this.tableData.forEach((item) => {
      this.$set(item, 'type', 'over')
    })
    console.log(this.tableData, 'tableData')
  },
  methods: {
    showIsMandatory(data) {
      if (data !== undefined) {
        console.log(data, 'datadata')
        if (data === 0) {
          return '否'
        } else {
          return '是'
        }
      } else {
        console.log(data, 'data')
        return ''
      }
    },
    // 查询
    select() {
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/province/queryList', this.form).then(res => {
        if (res.status === '200') {
          this.tableData = res.data
          this.total = res.total
          // 处理数据
          this.tableData.forEach((item) => {
            this.$set(item, 'type', 'over')
          })
        }
      })
    },
    // 编辑
    edit(row) {
      // 变更当前状态
      row.type = 'edit'
      console.log(row, 'rowww')
      if (row.scopeIds) {
        row.scopeIds = row.scopeIds.split(',')
      }
      // 查询业务类型数据
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/businessScope/page', {
          provinceCode: this.form.provinceCode,
          status: '2'
        }).then(res => {
        if (res.status === '200') {
          this.businessTypeList = res.data
        }
        console.log(res, 'resss')
      })
    },
    // 保存
    save(index, row) {
      const data = Object.assign({}, this.tableData)
      // 查询业务类型数据
      const params = {
        id: data[index].id,
        queuePriority: data[index].queuePriority,
        queueAliasCode: data[index].queueAliasCode,
        queueAliasName: data[index].queueAliasName,
        isMandatory: data[index].isMandatory ? data[index].isMandatory : '',
        scopeIds: data[index].scopeIds.toString()
      }
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/province/update', params).then(res => {
        if (res.status === '200') {
          this.$message.success(res.message)
          row.type = 'over'
          row.isCheckLevel = false
          this.select()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    cancel(row) {
      row.type = 'over'
      row.isCheckLevel = false
      this.select()
    },
    checkLevel(index, row) {
      row.isCheckLevel = true
    }
  }
}
</script>
<style lang="scss" scoped>
</style>