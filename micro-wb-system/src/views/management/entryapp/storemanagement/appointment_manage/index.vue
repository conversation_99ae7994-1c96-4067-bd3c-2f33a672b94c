<template>
    <div style="height: 100%; ">
      <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
        <template v-for="item in componentsCfg">
          <el-tab-pane v-if="item.isShow" :key="item.label" :label="item.label" :name="item.name">
          <component
            :is="item.component"
            ></component>
          </el-tab-pane>
        </template>
      </el-tabs>
    </div>
  </template>
<script>
import smstype_manage from './smstype_manage/smstype_manage.vue'
import smstem_manage from './smstem_manage/smstem_manage.vue'
import qrcode_manage from './qrcode_manage/qrcode_manage.vue'
import vip_manage from './vip_manage/vip_manage.vue'
import queue_manage from './queue_manage/queue_manage.vue'
import queue_province_manage from './queue_province_manage/queue_province_manage.vue'
import sms_rule from './sms_rule/sms_rule.vue'
export default {
  components: {
    smstype_manage,
    smstem_manage,
    qrcode_manage,
    vip_manage,
    queue_manage,
    queue_province_manage,
    sms_rule
  },
  data () {
    return {
      activeName: '',
      componentsCfg: [
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).sms_manage_menu_btn, // 短信类型管理按钮权限
          component: smstype_manage,
          label: '短信类型管理',
          name: 'first'
        },
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).smstem_manage_menu, // 短信模板配置管理按钮权限
          component: smstem_manage,
          label: '短信模板配置管理',
          name: 'second'
        },
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).sms_rule_menu, // 短信规则配置管理按钮权限
          component: sms_rule,
          label: '短信规则配置管理',
          name: 'third'
        },
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).qrcode_manage, // 二维码配置管理按钮权限
          component: qrcode_manage,
          label: '二维码配置管理',
          name: 'fourth'
        },
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).vip_config_menu, // VIP客户配置按钮权限
          component: vip_manage,
          label: 'VIP客户配置',
          name: 'fifth'
        },
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).queue_menu, // 队列配置管理按钮权限
          component: queue_manage,
          label: '队列配置管理',
          name: 'sixth'
        },
        {
          isShow: JSON.parse(sessionStorage.getItem('buttonInfo')).queue_province_menu, // 队列配置管理-省按钮权限
          component: queue_province_manage,
          label: '队列配置管理-省',
          name: 'seventh'
        }
      ]
    }
  },
  created() {
    const visibleComponents = this.componentsCfg.filter(item => item.isShow)
    this.activeName = visibleComponents[0].name
  },

  methods: {
    handleClick(tab, event) {
    }
  }
}
</script>
<style lang="scss" scoped>
</style>