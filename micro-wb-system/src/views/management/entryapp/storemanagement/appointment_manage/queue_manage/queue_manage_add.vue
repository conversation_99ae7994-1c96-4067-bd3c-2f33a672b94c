<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="50%"
  >
  <el-form ref="dataFrom" :model="dataFrom" :rules="rules">
    <el-form-item label="适用范围：">
      <region v-if="dataFrom.provinceCodes" v-model="dataFrom.provinceCodes"></region>
    </el-form-item>
    <el-form-item label="队列名称：" style="margin-top: 20px;" prop="queueName">
      <el-input v-model="dataFrom.queueName" maxlength="20" show-word-limit></el-input>
    </el-form-item>
    <el-form-item label="队列编号：" style="margin-top: 20px;" prop="queueCode">
      <el-select v-model="dataFrom.queueCode" placeholder="请选择" :disabled="type === 'edit'">
        <el-option
          v-for="item in queueCodeList"
          :key="item.queueCode"
          :label="item.queueName"
          :value="item.queueCode"
          :disabled="arr.includes(item.queueCode)">
        </el-option>
      </el-select>
    </el-form-item>
  </el-form>
  <template slot="footer-center">
    <div class="webbas footerCon">
      <asp-btn-hollow
      @click="closeDialog"
        name="取消"
      >
      </asp-btn-hollow>
      <asp-btn-solid
      v-if="type === 'add'"
        name="保存"
        @click="save"
      >
      </asp-btn-solid>
      <asp-btn-solid
      v-else
        name="保存"
        @click="edit"
      >
      </asp-btn-solid>
    </div>
  </template>
  </asp-dialog>
</template>
<script>
import Region from '@/components/region.vue'
export default {
  components: {
    Region
  },
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        this.type = val.type
        if (val.type === 'add') {
          const arr = []
          val.tableData.forEach((item) => {
            console.log(item, 'itemmm')
            arr.push(item.queueCode)
          })
          this.arr = arr
          this.clearData()
        }
        if (val.type === 'edit') {
          const arr = []
          val.tableData.forEach((item) => {
            console.log(item, 'itemmm')
            arr.push(item.queueCode)
          })
          this.arr = arr
          const data = Object.assign({}, val.data)
          data.provinceCodes = data.provinceCodes.split(',')
          this.dataFrom = data
        }
      },
      deep: true,
      immediate: true
    }
  },
  data () {
    return {
      tableData: [],
      arr: [],
      queueCodeList: [
        { queueName: 'A', queueCode: 'A' },
        { queueName: 'B', queueCode: 'B' },
        { queueName: 'C', queueCode: 'C' },
        { queueName: 'D', queueCode: 'D' },
        { queueName: 'E', queueCode: 'E' },
        { queueName: 'F', queueCode: 'F' },
        { queueName: 'G', queueCode: 'G' },
        { queueName: 'H', queueCode: 'H' },
        { queueName: 'I', queueCode: 'I' },
        { queueName: 'J', queueCode: 'J' },
        { queueName: 'K', queueCode: 'K' },
        { queueName: 'L', queueCode: 'L' },
        { queueName: 'M', queueCode: 'M' },
        { queueName: 'N', queueCode: 'N' },
        { queueName: 'O', queueCode: 'O' },
        { queueName: 'P', queueCode: 'P' },
        { queueName: 'Q', queueCode: 'Q' },
        { queueName: 'R', queueCode: 'R' },
        { queueName: 'S', queueCode: 'S' },
        { queueName: 'T', queueCode: 'T' },
        { queueName: 'U', queueCode: 'U' },
        { queueName: 'V', queueCode: 'V' },
        { queueName: 'W', queueCode: 'W' },
        { queueName: 'X', queueCode: 'X' },
        { queueName: 'Y', queueCode: 'Y' },
        { queueName: 'Z', queueCode: 'Z' }
      ],
      rules: {
        queueName: [
          { required: true, message: '请输入队列名称', trigger: 'blur' }
        ],
        queueCode: [
          { required: true, message: '请选择队列编号', trigger: 'change' }
        ]
      },
      dataFrom: {
        provinceCodes: ['-1'],
        queueName: '',
        region: '',
        queueCode: ''
      }
    }
  },
  methods: {
    clearData () {
      this.dataFrom = {
        provinceCodes: ['-1'],
        queueName: '',
        region: '',
        queueCode: ''
      }
    },
    // 保存
    save() {
      this.$refs.dataFrom.validate((valid) => {
        if (valid) {
          this.dataFrom.provinceCodes = this.dataFrom.provinceCodes.toString()
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/global/insert', this.dataFrom).then(res => {
            if (res.status === '200') {
              this.$message.success(res.message)
              this.closeDialog()
              this.$parent.refreshList()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 编辑
    edit() {
      this.$refs.dataFrom.validate((valid) => {
        if (valid) {
          this.dataFrom.provinceCodes = this.dataFrom.provinceCodes.toString()
          console.log(this.dataFrom.provinceCodes, 'ss')
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/global/update', this.dataFrom).then(res => {
            if (res.status === '200') {
              this.$message.success(res.message)
              this.closeDialog()
              this.$parent.refreshList()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    closeDialog() {
      this.dialogParam.modelVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.tips{
  font-size: 13px;
  margin-left: 22%;
  span{
    color: red;
  }
}
</style>
