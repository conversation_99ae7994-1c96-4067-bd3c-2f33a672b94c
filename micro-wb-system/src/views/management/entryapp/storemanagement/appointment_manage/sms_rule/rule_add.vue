<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="70%"
  >
  <el-form ref="dataForm" :model="dataForm" :rules="rules" style="height: 500px; overflow: scroll;">
    <div class="dataContent">
      <div class="citytree" v-if="type === 'add'">
        <el-tree :data="treeData" show-checkbox :props="defaultProps" ref="tree"></el-tree>
      </div>
      <div class="datinfo">
        <el-form-item label="所属省份：" prop="provinceCode">
          <el-select v-model="dataForm.provinceCode" placeholder="请选择省份" @change="changeProvince">
            <el-option v-for="item in provinceList" :key="item.provinceCode" :label="item.provinceName" :value="item.provinceCode"></el-option>
          </el-select>
        </el-form-item>
        <div style="display: flex;justify-content: space-around;margin-top: 20px;">
          <el-form-item label="规则名称：">
            <el-input disabled v-model="dataForm.ruleName"></el-input>
          </el-form-item>
          <el-form-item label="短信类型：" prop="smsTypeId">
            <el-select v-model="dataForm.smsTypeId" @change="checkTemType" placeholder="请选择">
              <el-option v-for="item in smsTypeList" :key="item.id" :label="item.smsTypeName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <el-form-item style="margin-top: 20px;" label="规则应用对象：">
          <el-select disabled style="width: 200px;" v-model="dataForm.ruleApplier" placeholder="请选择">
            <el-option label="客户" :value="0"></el-option>
            <el-option label="营业厅" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信模板：" style="margin-top: 20px;" prop="templateId">
          <el-select v-model="dataForm.templateId" placeholder="请选择" @change="changeTemplate">
            <el-option v-for="item in smsTemplateList" :key="item.id" :label="item.templateName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <div style="display: flex;justify-content: space-around;margin-top: 20px;">
          <el-form-item label="触发指标：" prop="triggerType">
            <el-select v-model="dataForm.triggerType" placeholder="请选择">
              <el-option v-for="item in triggerTypeList" :key="item.value" :label="item.key" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="触发条件：">
            <el-select disabled v-model="dataForm.triggerCondition" placeholder="请选择">
              <el-option label="大于" :value="0"></el-option>
              <el-option label="等于" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="触发阈值：" style="margin-top: 20px;">
          <div style="display: flex;align-items: center;margin-top: -2%;">
            <div style="display: flex;align-items: center;width: 150px;margin-right: 20px;">
              <el-select v-model="dataForm.threshold1" placeholder="请选择">
                <el-option v-for="item in thresholdList" :key="item" :label="item" :value="item"></el-option>
              </el-select>
              <p v-if="dataForm.triggerType === '1' || dataForm.triggerType === '2'">人</p>
              <p v-else style="width: 50px;">分钟</p>
            </div>
            <div style="display: flex;align-items: center;width: 150px;margin-right: 20px;">
              <el-select v-model="dataForm.threshold2" placeholder="请选择">
                <el-option v-for="item in thresholdList" :key="item" :label="item" :value="item"></el-option>
              </el-select>
              <p v-if="dataForm.triggerType === '1' || dataForm.triggerType === '2'">人</p>
              <p v-else style="width: 50px;">分钟</p>
            </div>
            <div style="display: flex;align-items: center;width: 150px;margin-right: 20px;">
              <el-select v-model="dataForm.threshold3" placeholder="请选择">
                <el-option v-for="item in thresholdList" :key="item" :label="item" :value="item"></el-option>
              </el-select>
              <p v-if="dataForm.triggerType === '1' || dataForm.triggerType === '2'">人</p>
              <p v-else style="width: 50px;">分钟</p>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="手机号码：" style="margin-top: 20px;">
          <el-input :disabled="isShowPhone === '-1'" v-model="dataForm.alarmPhone"></el-input>
          <p style="color: red;font-size: 13px;">* 多个号码以英文逗号（,）分开</p>
        </el-form-item>
        <el-form-item label="发送频率：">
          <div style="display: flex;">
            <el-select style="width: 150px;" v-model="dataForm.hour" placeholder="请选择">
              <el-option v-for="item in hourList" :key="item" :label="item" :value="item"></el-option>
            </el-select>
            &nbsp;小时
            <el-select style="width: 150px;margin-left: 20px;" v-model="dataForm.minute" placeholder="请选择">
              <el-option v-for="item in minList" :key="item" :label="item" :value="item"></el-option>
            </el-select>
            &nbsp;分钟
          </div>
        </el-form-item>
        <el-form-item label="短信模板内容：" style="margin-top: 20px;" prop="templateContent">
          <el-input disabled v-model="dataForm.templateContent" type="textarea" maxlength="255" show-word-limit></el-input>
        </el-form-item>
      </div>
    </div>
  </el-form>
  <template slot="footer-center">
    <div class="webbas footerCon">
      <asp-btn-hollow
      @click="closeDialog"
        name="取消"
      >
      </asp-btn-hollow>
      <asp-btn-solid
        v-if="type === 'add'"
        name="保存"
        @click="save"
      >
      </asp-btn-solid>
      <asp-btn-solid
        v-if="type === 'edit'"
        name="保存"
        @click="editSave"
      >
      </asp-btn-solid>
    </div>
  </template>
  </asp-dialog>
</template>
<script>
import aspUtils from '@/components/aspire/asp-api/asp-Utils.js'
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  data () {
    return {
      type: '',
      dataFrom: {},
      treeData: [],
      zxsidList: ['100', '220', '210', '230'],
      provinceMap: Object.freeze(aspUtils.asp_CfgData().asp_Provice_List),
      dataForm: {
        shopIds: [],
        provinceCode: '',
        ruleName: '规则配置',
        smsTypeId: '',
        ruleApplier: '',
        templateId: '',
        triggerType: '',
        triggerCondition: '',
        threshold1: '',
        threshold2: '',
        threshold3: '',
        alarmPhone: '',
        hour: '',
        minute: '',
        sendRate: '',
        templateContent: ''
      },
      rules: {
        provinceCode: [
          { required: true, message: '请选择省份信息', trigger: 'change' }
        ],
        smsTypeId: [
          { required: true, message: '请选择短信类型', trigger: 'change' }
        ],
        templateId: [
          { required: true, message: '请选择短信模板', trigger: 'change' }
        ],
        triggerType: [
          { required: true, message: '请选择触发指标', trigger: 'change' }
        ],
        templateContent: [
          { required: true, message: '请填写"短信模板内容', trigger: 'blur' }
        ]
      },
      isShowPhone: '',
      provinceList: [],
      smsTypeList: [],
      smsTemplateList: [],
      triggerTypeList: [
        {
          key: '平均等待时长',
          value: '0'
        },
        {
          key: '等待人数',
          value: '1'
        },
        {
          key: '临号提醒人数',
          value: '2'
        },
        {
          key: '预约短信下发时间',
          value: '3'
        }
      ],
      thresholdList: ['5', '10', '15', '20', '25', '30', '35', '40', '45', '50', '55', '60'],
      hourList: ['0', '1', '2', '3', '4', '5', '6', '7', '8'],
      minList: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59'],
      defaultProps: {
        children: 'children',
        label: 'text'
      }
    }
  },

  watch: {
    dialogParam: {
      handler(val) {
        this.type = val.type
        console.log(val, 'valll')
        // 新增按钮
        if (val.type === 'add') {
          this.clearData()
          this.treeData = val.data
          // 省份数据
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/shop/common/regionInfo', {}).then(res => {
            if (res.status === '200') {
              // 集团用户删除全国选项
              if (JSON.parse(sessionStorage.getItem('buttonInfo')).group_flag) {
                res.data.splice(0, 1)
                this.provinceList = res.data
                this.dataForm.provinceCode = res.data[0].provinceCode
                this.changeProvince()
              } else {
                this.provinceList = res.data
                this.dataForm.provinceCode = res.data[0].provinceCode
              }
            } else {
              this.$message.error(res.message)
            }
          })
          // 短信类型数据
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsTypeManagement/all', {}).then(res => {
            if (res.status === '200') {
              this.smsTypeList = res.data
            } else {
              this.$message.error(res.message)
            }
          })
        }
        // 编辑
        if (val.type === 'edit') {
          // 省份数据
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/shop/common/regionInfo', {}).then(res => {
            if (res.status === '200') {
              // 集团用户删除全国选项
              if (JSON.parse(sessionStorage.getItem('buttonInfo')).group_flag) {
                res.data.splice(0, 1)
                this.provinceList = res.data
              } else {
                this.provinceList = res.data
              }
            } else {
              this.$message.error(res.message)
            }
          })
          // 短信类型数据
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsTypeManagement/all', {}).then(res => {
            if (res.status === '200') {
              this.smsTypeList = res.data
              this.$aspHttps.asp_Post(
                this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsTemplateConfig/querySmsTemplateList', {
                  typeId: val.data.smsTypeId,
                  page: 1,
                  rows: 10
                }).then(res => {
                if (res.status === '200') {
                  this.smsTemplateList = res.data
                }
              })
            } else {
              this.$message.error(res.message)
            }
          })
          val.data.smsTypeId = parseInt(val.data.smsTypeId)
          this.dataForm = Object.assign({}, val.data)
          // console.log(this.dataForm, 'this.dataForm')
        }
        // 当前点击按钮类型
      }
    }
  },
  methods: {
    // 清空数据
    clearData() {
      this.dataForm = {
        shopIds: [],
        provinceCode: '',
        ruleName: '规则配置',
        smsTypeId: '',
        ruleApplier: '',
        templateId: '',
        triggerType: '',
        triggerCondition: '',
        threshold1: '',
        threshold2: '',
        threshold3: '',
        alarmPhone: '',
        hour: '',
        minute: '',
        sendRate: '',
        templateContent: ''
      }
      this.smsTemplateList = []
    },
    // 选择省份
    changeProvince() {
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/queryProvinceShopList', {
          provinceCode: this.dataForm.provinceCode
        }).then(res => {
        if (res.status === '200') {
          // 判断是否在直辖市列表中
          if (this.zxsidList.includes(this.dataForm.provinceCode)) {
            this.treeData = res.data
          } else {
            const provinceName = this.provinceMap[this.dataForm.provinceCode]
            this.treeData = [{
              text: provinceName,
              id: this.dataForm.provinceCode,
              children: res.data
            }]
          }
        }
      })
    },
    // 选择短信类型
    checkTemType(e) {
      // 查询短信模板数据
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsTemplateConfig/querySmsTemplateList', {
          typeId: e,
          provinceCode: this.dataForm.provinceCode,
          page: 1,
          rows: 10
        }).then(res => {
        if (res.status === '200') {
          console.log(res.data, 'res.data')
          this.smsTemplateList = res.data
          if (res.data.length !== 0) {
            this.dataForm.templateId = res.data[0].id
            const item = this.smsTypeList.find(item => item.id === e)
            this.dataForm.templateContent = res.data[0].templateContent
            this.dataForm.ruleApplier = item.ruleApplier
            this.dataForm.triggerCondition = item.triggerCondition
            this.isShowPhone = item.alarmPhone
          } else {
            this.dataForm.templateId = ''
            this.dataForm.ruleApplier = ''
            this.dataForm.templateContent = ''
            this.dataForm.triggerCondition = ''
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 选择短信模板
    changeTemplate(e) {
      const item = this.smsTemplateList.find(item => item.id === e)
      this.dataForm.templateContent = item.templateContent
    },
    // 新增 --- 保存
    save() {
      const nodeList = this.$refs.tree.getCheckedNodes()
      this.dataForm.shopIds = nodeList.filter(data => {
        return !!data.shopId && data.shopId !== ''
      }).map(data => data.shopId)
      if (this.dataForm.shopIds.length === 0) {
        this.$message.error('请至少选择一家营业厅')
        return
      } else if (this.dataForm.threshold1 === '' && this.dataForm.threshold2 === '' && this.dataForm.threshold3 === '') {
        this.$message.error('请选择触发阈值')
        return
      } else if (this.dataForm.hour === '') {
        this.$message.error('请选择发送频率（小时）')
        return
      } else if (this.dataForm.minute === '') {
        this.$message.error('请选择发送频率（分钟）')
        return
      }
      this.$refs.dataForm.validate((valid) => {
        this.dataForm.sendRate = parseInt(this.dataForm.hour) * 60 + parseInt(this.dataForm.minute)
        if (valid) {
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsRuleConfig/batchAdd', this.dataForm).then(res => {
            if (res.status === '200') {
              this.$message.success(res.message)
              this.closeDialog()
              this.clearData()
              this.$parent.refreshList()
            } else {
              this.$message.error(res.message)
            }
            console.log(res, 'resss')
          })
        }
      })
    },
    // 编辑 --- 保存
    editSave() {
      if (this.dataForm.threshold1 === '' && this.dataForm.threshold2 === '' && this.dataForm.threshold3 === '') {
        this.$message.error('请选择触发阈值')
        return
      } else if (this.dataForm.hour === '') {
        this.$message.error('请选择发送频率（小时）')
        return
      } else if (this.dataForm.minute === '') {
        this.$message.error('请选择发送频率（分钟）')
        return
      }
      this.$refs.dataForm.validate((valid) => {
        this.dataForm.sendRate = parseInt(this.dataForm.hour) * 60 + parseInt(this.dataForm.minute)
        if (valid) {
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsRuleConfig/update', this.dataForm).then(res => {
            if (res.status === '200') {
              this.$message.success(res.message)
              this.closeDialog()
              this.clearData()
              this.$parent.refreshList()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 取消
    closeDialog() {
      this.dialogParam.modelVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.dataContent{
  display: flex;
  .citytree {
    width: 35%;
  }
  .datainfo {
    width: 50%;
  }
}
.tips{
  font-size: 13px;
  margin-left: 22%;
  span{
    color: red;
  }
}
</style>
