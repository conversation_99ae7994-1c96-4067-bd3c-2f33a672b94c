{"formList": [{"label": "所属省份：", "classify": "basic", "type": "input", "columnName": "provinceName", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": true, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": ""}, {"label": "厅店名称：", "classify": "basic", "type": "input", "columnName": "shopName", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": true, "readonly": false, "wordLimitStyle": "transparent", "controls": true}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "labelWidth": 160, "copyNewVal": ""}, {"label": "渠道编码：", "classify": "basic", "type": "input", "columnName": "unifiedChannelId", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": true, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": ""}, {"label": "类型：", "type": "select", "isLabelWidth": false, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": false, "requiredCutomizeTips": "", "rules": [], "columnName": "linkType", "targetName": "label_select_1742463005983", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "二维码", "value": "0"}, {"label": "小程序", "value": "1"}, {"label": "app安装二维码", "value": "2"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "", "showOptions": [{"label": "二维码", "value": "0", "disabled": false}, {"label": "小程序", "value": "1", "disabled": false}, {"label": "app安装二维码", "value": "2", "disabled": false}], "labelWidth": 160, "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": "", "isHelpTipText": "", "dynamic": {"single_mainform_list": [{"key": 1743492688095, "source": {"label": "类型：:linkType", "columnName": "linkType", "targetName": "label_select_1742463005983", "props": {"placeholder": "", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "name": "basic", "type": "select"}, "target": [{"label": "二维码地址： : qrUrl", "columnName": "qrUrl", "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "name": "basic", "type": "input"}], "condition": [{"columnName": "linkType", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "0", "type": ["hidden"], "result": false, "value": "", "status": [], "numberSign": ">=", "numberValue": 1}, {"columnName": "linkType", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "1||2", "type": ["hidden"], "result": true, "value": "", "status": [], "numberSign": ">=", "numberValue": 1}]}, {"key": 1743492738327, "source": {"label": "类型：:linkType", "columnName": "linkType", "targetName": "label_select_1742463005983", "props": {"placeholder": "", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "name": "basic", "type": "select"}, "target": [{"label": "小程序渠道： : miniProgramChannel", "columnName": "miniProgramChannel", "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "name": "basic", "type": "input"}, {"label": "小程序大小： : miniProgramSize", "columnName": "miniProgramSize", "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true}, "name": "basic", "type": "input"}], "condition": [{"columnName": "linkType", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "1", "type": ["hidden"], "result": false, "value": "", "status": [], "numberSign": ">=", "numberValue": 1}, {"columnName": "linkType", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "0||2", "type": ["hidden"], "result": true, "value": "", "status": [], "numberSign": ">=", "numberValue": 1}]}]}, "copyOldVal": "1", "statusList": [], "isMutexSwitch": false}, {"label": "二维码地址：", "classify": "basic", "type": "input", "columnName": "qrUrl", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "ruleType": "", "isHelpTipText": ""}, {"label": "小程序渠道：", "type": "select", "isLabelWidth": false, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": false, "requiredCutomizeTips": "", "rules": [], "columnName": "miniProgramChannel", "targetName": "label_select_1743497966940", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "channelName", "option-value": "channelCode", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "multipleLimit": 0, "showTitle": false, "wordLimitStyle": "transparent", "controls": true}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/qRCodeConfig/getConfigChannels", "apiType": "post+json", "apiParam": "", "separator": ","}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "showOptions": [{"label": "Option 1", "value": "1", "disabled": false}, {"label": "Option 2", "value": "2", "disabled": false}], "labelWidth": 160, "filterOptionStatus": [], "disabledOptionStatus": [], "ruleType": "", "copyNewVal": ""}, {"label": "小程序大小：", "classify": "basic", "type": "input", "columnName": "miniProgramSize", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "请输入小程序大小", "rules": [{"required": true, "message": "请输入小程序大小", "trigger": "blur"}, {"message": "请输入正确的小程序大小", "pattern": "^[+-]?(\\d+([.]\\d*)?([eE][+-]?\\d+)?|[.]\\d+([eE][+-]?\\d+)?)$", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "小程序大小为：280~1280", "maxlength": "4", "show-word-limit": true, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "controls": true, "minlength": "", "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "ruleType": "^[+-]?(\\d+([.]\\d*)?([eE][+-]?\\d+)?|[.]\\d+([eE][+-]?\\d+)?)$", "numberTypeRequired": true, "isHelpTipText": ""}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1742463289231", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "cacel", "type": "", "icon": "", "label": "取消", "interactive": "", "validateProp": "", "apiName": "", "class": "hollow-with-icon-btn", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_close_dialog"}, {"columnName": "submit", "type": "", "icon": "", "label": "保存", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/qRCodeConfig/update", "class": "solid-with-icon-btn", "default": "show", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": true, "confirmStatus": [], "apiCloseDialog": false, "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": ""}], "name": "layout", "labelWidth": 160, "copyOldVal": ""}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "qrcodeedit", "defaultClass": "webbas", "size": "small", "statusList": [], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "isOpenExportPDFSwitch": false, "exportPDFProps": {"eleId": "", "eleClassName": "", "markProps": {"pdfFileName": "", "markTitle": "", "markImgUrl": "", "imgFileName": "", "opacity": 100, "imgUp": false, "pStyle": {"font-size": "44px", "margin": "40px 0px", "display": "flex", "justify-content": "center"}}}, "titleName": "二维码配置管理-编辑"}, "dataConfig": {}, "virtual_model": {}, "model": {"provinceName": "", "shopName": "", "unifiedChannelId": "", "linkType": "", "label_select_1742463005983": "", "qrUrl": "", "miniProgramChannel": "", "label_select_1743497966940": "", "miniProgramSize": ""}, "dynamic": {}}