<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="50%"
  >
  <el-form ref="editFrom" :model="editFrom" :rules="rules">
    <el-form-item label="模板类型：">
      <el-input v-model="editFrom.smsTypeName" disabled></el-input>
    </el-form-item>
    <el-form-item label="模板内容：" style="margin-top: 20px;" prop="smsContent">
      <el-input type="textarea" maxlength="255" show-word-limit v-model="editFrom.smsContent"></el-input>
    </el-form-item>
    <p class="tips">可选参数 ：{预约时间}{营业厅}{营业厅地址：营业厅地址。}</p>
  </el-form>
  <template slot="footer-center">
    <div class="webbas footerCon">
      <asp-btn-hollow
      @click="closeDialog"
        name="取消"
      >
      </asp-btn-hollow>
      <asp-btn-solid
      @click="save"
        name="保存"
      >
      </asp-btn-solid>
    </div>
  </template>
  </asp-dialog>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        this.editFrom = val.data
      }
    }
  },
  data () {
    return {
      editFrom: {},
      rules: {
        smsContent: [
          { required: true, message: '请填写活动形式', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 保存
    save() {
      this.$refs.editFrom.validate((valid) => {
        if (valid) {
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/smsTypeManagement/update', this.editFrom).then(res => {
            if (res.status === '200') {
              this.$message.success(res.message)
              this.$parent.refreshList()
              this.closeDialog()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    closeDialog() {
      this.dialogParam.modelVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.tips{
  font-size: 13px;
  margin-left: 22%;
  span{
    color: red;
  }
}
</style>
