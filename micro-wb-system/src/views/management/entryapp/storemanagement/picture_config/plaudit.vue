<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="50%"
  >
  <el-form ref="form" :model="form" :rules="rules">
    <el-form-item style="margin-left: 23%;">
      <el-radio-group v-model="form.status">
        <el-radio :label="1">通过</el-radio>
        <el-radio :label="2">未通过</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="form.status === 2" style="margin-top: 20px;" label="原因：" prop="rejectReason">
      <el-input type="textarea" maxlength="60" show-word-limit placeholder="请输入审核未通过原因，字数限制在60字以内" v-model="form.rejectReason"></el-input>
    </el-form-item>
  </el-form>
  <template slot="footer-center">
    <div class="webbas footerCon">
      <asp-btn-solid
        @click="checkAudit"
        name="审核"
      >
      </asp-btn-solid>
    </div>
  </template>
  </asp-dialog>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        this.form.idList = val.data
      }
    }
  },
  data () {
    return {
      form: {
        status: 1,
        idList: [],
        rejectReason: ''
      },
      rules: {
        rejectReason: [
          { required: true, message: '请填写未通过原因', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    clearAudit() {
      this.form = {
        status: 1,
        idList: [],
        rejectReason: ''
      }
    },
    // 审核按钮
    checkAudit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/picture/batchAudit', this.form).then(res => {
            if (res.status === '200') {
              this.$message.success('审核成功')
              this.dialogParam.modelVisible = false
              this.$parent.refreshList()
              this.$parent.clearPlAudit()
              this.clearAudit()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
