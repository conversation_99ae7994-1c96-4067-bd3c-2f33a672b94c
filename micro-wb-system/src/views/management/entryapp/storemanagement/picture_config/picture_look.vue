<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="50%"
  >
  <img style="width: 350px; height: 350px; margin-left: 25%;" :src="this.$apiConfig.level1cloudstorePathPreFix + '/proxyFs/show?file_id=' + this.picture" alt="">
  <!-- <template slot="footer-center">
    <div class="webbas footerCon">
      <asp-btn-solid
        @click="checkAudit"
        name="审核"
      >
      </asp-btn-solid>
    </div>
  </template> -->
  </asp-dialog>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        this.picture = val.data.fileId
      }
    }
  },
  data () {
    return {
      picture: ''
    }
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
</style>
