<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="50%"
  >
  <el-form ref="form" :model="form" :rules="rules">
    <el-form-item label="全网统一渠道编码：">
      <span>{{ form.unifiedChannelId }}</span>
    </el-form-item>
    <el-form-item label="归属省、市：" class="mt">
      <span>{{ form.provinceName }} {{ form.cityName ? form.cityName + '市' : '' }}</span>
    </el-form-item>
    <el-form-item label="门店简称：" class="mt">
      <span>{{ form.shopShortName }}</span>
    </el-form-item>
    <el-form-item label="上传图片：" v-if="type === 'edit'" class="mt">
      <uploader
        v-model="form.pictureShow"
        :uploadUrl = uploadUrl
        verify-picture="200x200"
        tips="说明：为保证前端展示美观，上传图片尺寸为200px*200px，且图片格式为jpg;jpeg;png格式，图片大小不超过100K"
        @input="input"
      ></uploader>
    </el-form-item>
    <el-form-item label="图片：" class="mt"  v-else>
      <img :src="this.$apiConfig.level1cloudstorePathPreFix + '/proxyFs/show?file_id=' + this.form.fileId" alt="">
    </el-form-item>
    <div v-if="type === 'audit'">
      <el-form-item style="margin-left: 23%;">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">通过</el-radio>
          <el-radio :label="2">未通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.status === 2" style="margin-top: 20px;" label="原因：" prop="rejectReason">
        <el-input type="textarea" maxlength="60" show-word-limit placeholder="请输入审核未通过原因，字数限制在60字以内" v-model="form.rejectReason"></el-input>
      </el-form-item>
    </div>
  </el-form>
  <template slot="footer-center">
    <div class="webbas footerCon">
      <asp-btn-solid
        v-if="type === 'edit'"
        @click="checkEdit"
        name="保存并提交审核"
      >
      </asp-btn-solid>
      <asp-btn-solid
        v-if="type === 'audit'"
        @click="checkAudit"
        name="审核"
      >
      </asp-btn-solid>
    </div>
  </template>
  </asp-dialog>
</template>
<script>
import Uploader from '@/components/uploaders.vue'
import aspUtil from '@/components/aspire/asp-api/asp-Utils.js'
export default {
  components: {
    Uploader
  },
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        if (val.type === 'edit') {
          this.form = val.data
        } else if (val.type === 'audit') {
          this.form = Object.assign({}, val.data, this.auditForm)
        }
        this.type = val.type
        this.form.pictureShow = this.$apiConfig.level1cloudstorePathPreFix + '/proxyFs/show?file_id=' + this.form.fileId
      }
    }
  },
  data () {
    return {
      type: '',
      uploadUrl: aspUtil.getRealUrl(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/file/shopPicture/upload', this),
      form: {
      },
      auditForm: {
        status: 1,
        rejectReason: ''
      },
      rules: {
        rejectReason: [
          { required: true, message: '请填写未通过原因', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 处理图片传参
    input(q) {
      this.form.picture = q.fileId
      this.form.pictureShow = this.$apiConfig.level1cloudstorePathPreFix + '/proxyFs/show?file_id=' + q.fileId
    },
    // 编辑 --- 保存
    checkEdit() {
      console.log(this.form, 'p')
      if (this.form.picture) {
        this.form.fileId = this.form.picture
      }
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/picture/saveAndAuditPicture', this.form).then(res => {
        if (res.status === '200') {
          this.dialogParam.modelVisible = false
          this.$message.success('提交成功')
          this.$parent.refreshList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 审核
    checkAudit () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.idList = [this.form.id]
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/picture/batchAudit', this.form).then(res => {
            if (res.status === '200') {
              this.$message.success('审核成功')
              this.dialogParam.modelVisible = false
              this.$parent.refreshList()
              this.clearAudit()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.mt {
  margin-top: 15px;
}
::v-deep .tips {
  color: #919291;
}
</style>
