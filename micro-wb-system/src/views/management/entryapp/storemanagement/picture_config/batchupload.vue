<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="60%"
  >
  <el-upload
    class="upload-demo"
    ref="upload"
    :action="uploadUrl"
    accept=".zip"
    :on-preview="handlePreview"
    :on-remove="handleRemove"
    :file-list="fileList"
    :on-change="handleChange"
    :on-success="handleSuccess"
    :on-error="handleError"
    :auto-upload="false">
    <el-button class="solid-with-icon-btn" slot="trigger" size="small" type="primary"><i class="el-icon-folder-opened"></i> 选取文件</el-button>
    <el-button class="solid-with-icon-btn" style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
</el-upload>
<div class="picturetip">
  <p class="exportTips">
    <span>批量门店照片导入说明：</span>
    <br/>1、每个门店对应一张门店图片，尺寸为200px*200px;
    <br/>2、门店图片命名方式：全网统一渠道编码.jpg，例如：8907768910029876554.jpg;
    <br/>3、每张图片的大小不得超过100k，图片格式为jpg;jpeg;png;
    <br/>4、图片按照上述2格式命名，并压缩在指定名称的文件夹中上传，仅支持ZIP格式;
    <br/>5、批量上传的文件大小不超过50M;
  </p>
  <div>
    <img src="../../../assets/img/1737616593524.png" alt="">
    <p class="slt">压缩包示例图</p>
  </div>
</div>
  <!-- <template slot="footer-center">
      <div class="webbas footerCon" v-if="type === 'edit'">
        <asp-btn-solid
          name="确定"
          @click="updateBusiness"
        >
        </asp-btn-solid>
      </div>
      <div class="webbas footerCon" v-else>
        <asp-btn-solid
          name="确定"
          @click="saveBusiness"
        >
        </asp-btn-solid>
        <asp-btn-hollow
          name="取消"
          @click="closeMark"
        >
        </asp-btn-hollow>
      </div>
    </template> -->
  </asp-dialog>
</template>
<script>
import aspUtil from '@/components/aspire/asp-api/asp-Utils.js'
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
      }
    }
  },
  data () {
    return {
      fileList: [],
      uploadUrl: aspUtil.getRealUrl(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/zipFile/upload', this)
    }
  },
  methods: {
    handleChange(file, fileList) {
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]
      }
    },
    submitUpload() {
      this.$refs.upload.submit()
    },
    handleError (err, file, fileList) {
      console.log(err, 'errerrerr')
      if (err.status === 413) {
        this.$message.error('文件不能超过50M，请重新上传')
      }
    },
    handleSuccess(response, file, fileList) {
      // 文件上传成功后 逻辑处理
      if (response.status === '200') {
        this.$message.success('已生成导入任务，请到“文件导入结果”页面中查看图片导入情况')
        // 清空数据
        this.clearData()
        this.$parent.refreshList()
        this.dialogParam.modelVisible = false
      } else {
        this.$message.error(response.message)
      }
    },
    clearData() {
      this.fileList = []
      // this.$refs.upload.clearFiles()
    },
    handleRemove(file, fileList) {
      console.log(file, fileList, '1')
    },
    handlePreview(file) {
      console.log(file, '2')
    }
  }
}
</script>
<style lang="scss" scoped>
  .picturetip {
    display: flex;
    justify-content: space-between;
    img {
      width: 350px;
      height: 300px;
    }
    .slt {
      text-align: center;
    }
  }
  .exportTips {
    font-size: 14px;
    color: #A81A1A;
    span {
      font-size: 15px;
    }
  }
  ::v-deep .el-list-enter-active,
  ::v-deep .el-list-leave-active {
    transition: none;
  }

  ::v-deep .el-list-enter,
  ::v-deep .el-list-leave-active {
    opacity: 0;
  }
</style>
