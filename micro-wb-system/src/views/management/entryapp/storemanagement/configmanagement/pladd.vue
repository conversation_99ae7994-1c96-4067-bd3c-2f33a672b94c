<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="50%"
  >
  <p class="tipss">您的文件已成功导入，正在执行中，可能需要一些时间，您可以到“<span style="color: #469CD4;">文件导入结果</span>”中查看详细的导入结果。</p>
  <template slot="footer-center">
      <div class="webbas footerCon">
        <asp-btn-solid
          @click="closeEditMark"
          name="知道了，立即查看导入结果"
        >
        </asp-btn-solid>
      </div>
    </template>
  </asp-dialog>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
      }
    }
  },
  data () {
    return {
    }
  },
  methods: {
    // 取消
    closeEditMark() {
      this.$router.push({
        name: 'import_result'
      })
      // this.dialogParam.modelVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
  .tipss {
    margin-bottom: 70px;
  }
</style>
