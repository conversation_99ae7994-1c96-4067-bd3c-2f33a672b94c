<template>
  <asp-dialog
  class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="60%"
  >
  <el-form ref="configForm" :model="configForm">
    <el-form-item>
      <span style="font-weight: 600;">是否入驻一级云店 <i class="el-icon-question" title="省公司入驻一级云店后，直接使用云店开通线上店。可在“移云店”微信小程序查看线上店信息。"></i>：</span>
      <el-switch
        v-model="configForm.isSettled"
        inactive-text="否"
        active-text="是"
        :active-value="1"
        :inactive-value="2">
    </el-switch>
    </el-form-item>
    <!-- <el-form-item>
      <span style="font-weight: 600;">是否统一配置营业范围 <i class="el-icon-question" title="如选择“是”，省侧所有营业厅展示对应渠道类型的默认营业范围，且每个营业厅不可单独配置；如选择“否”，可单独为营业厅选择营业范围。"></i>：</span>
      <el-switch
        v-model="configForm.ispz"
        inactive-text="否"
        active-text="是">
      </el-switch>
    </el-form-item> -->
    <el-form-item>
      <span style="font-weight: 600;vertical-align: top;">APP前端营业厅电话 <i class="el-icon-question" title="展示在中国移动APP“附近厅”页面的厅店联系电话"></i>：</span>
      <el-radio-group v-model="configForm.isShowContact">
        <el-radio style="margin-top: 10px;" :label="1">不展示</el-radio><br/>
        <el-radio style="margin-top: 10px;" :label="2">优先展示门店客服电话</el-radio><br/>
        <el-radio style="margin-top: 10px;" :label="3">优先展示店长手机号</el-radio><br/>
        <el-radio style="margin-top: 10px;" :label="4">仅展示门店客服电话</el-radio><br/>
        <el-radio style="margin-top: 10px;" :label="5">仅展示店长手机号</el-radio>
      </el-radio-group>
    </el-form-item>
  </el-form>
  <template slot="footer-center">
      <div class="webbas footerCon">
        <asp-btn-solid
          name="保存"
          @click="saveConfig"
        >
        </asp-btn-solid>
        <asp-btn-hollow
          @click="closeEditMark"
          name="取消"
        >
        </asp-btn-hollow>
      </div>
    </template>
  </asp-dialog>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        this.configForm = val.data
      }
    }
  },
  data () {
    return {
      configForm: {
        isSettled: null,
        isShowContact: null
      }
    }
  },
  methods: {
    // 保存
    saveConfig() {
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/merchant/updateOsmConfig', this.configForm).then(res => {
        if (res.status === '200') {
          this.$message.success('保存成功')
          this.closeEditMark()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 取消
    closeEditMark() {
      this.dialogParam.modelVisible = false
    }
  }
}
</script>
<style scoped lang="scss">
.el-form-item {
  margin-top: 10px;
}
.el-form-item__content {
  margin-left: 10px;
}
</style>
