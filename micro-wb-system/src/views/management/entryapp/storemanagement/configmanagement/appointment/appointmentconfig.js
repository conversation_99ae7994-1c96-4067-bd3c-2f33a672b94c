/** 票号模式 */
export const ticketModeJson = ['长号', '短号']
/** 对接方式 */
export const integrationModeJson = ['直接使用', '部分对接']
/** 厅店取号状态 */
export const hallStatusJson = ['正常', '测试', '暂停']
/** 消息类型 */
export const messageTypeJson = ['短信消息', '5G消息']
/** 二维码类型 */
export const qrTypeJson = ['普通二维码', '小程序二维码', 'app安装二维码']
/** 窗口优先级 */
export const priorityJson = ['队列优先', '业务优先']
/** 窗口当前状态 */
export const statusJson = ['占用', '未占用', '暂停']
export default {
  ticketModeJson,
  integrationModeJson,
  hallStatusJson,
  messageTypeJson,
  qrTypeJson,
  priorityJson,
  statusJson
}
