import {
  ticketModeJson,
  integration<PERSON>ode<PERSON><PERSON>,
  hallStatus<PERSON>son,
  messageTypeJson,
  qrType<PERSON>son,
  priorityJson,
  statusJson
} from './appointmentconfig.js'
export default {
  data () {
    return {
      ticketModeJson: ticketModeJson,
      integrationModeJson: integrationMode<PERSON>son,
      hallStatusJson: hallStatusJson,
      messageTypeJson: messageTypeJson,
      qrTypeJson: qrTypeJson,
      priorityJson: priorityJson,
      statusJson: statusJson,
      idJson: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      editForm: {
        shopShortName: '',
        shopName: '',
        vipEnabled: '支持', // 默认选项为支持
        ticketMode: '',
        integrationMode: '',
        whitelist: '',
        hallStatus: '',
        messageType: '',
        qrType: 0,
        qrChannel: 0,
        qrCodeAddress: '',
        queueConfig: '',
        callWindowConfigList: [],
        businessHours: '',
        qrCode: ''
      },
      rules: {
        vipEnabled: [
          { required: true, message: '请选择支持VIP取号信息', trigger: 'change' }
        ],
        ticketMode: [
          { required: true, message: '请选择票号模式信息', trigger: 'change' }
        ]
      },
      myId: '', // 选择窗口号
      // 预约时间配置
      weekdayValue: {
        1: {
          label: '周一',
          isAppoint: true,
          appointInterval: null, // 选择预约间隔
          appointNum: null, // 选择预约人数
          tableData: [],
          key: 1
        },
        2: {
          label: '周二',
          isAppoint: true,
          appointInterval: null, // 选择预约间隔
          appointNum: null, // 选择预约人数
          tableData: [],
          key: 2
        },
        3: {
          label: '周三',
          isAppoint: true,
          appointInterval: null, // 选择预约间隔
          appointNum: null, // 选择预约人数
          tableData: [],
          key: 3
        },
        4: {
          label: '周四',
          isAppoint: true,
          appointInterval: null, // 选择预约间隔
          appointNum: null, // 选择预约人数
          tableData: [],
          key: 4
        },
        5: {
          label: '周五',
          isAppoint: true,
          appointInterval: null, // 选择预约间隔
          appointNum: null, // 选择预约人数
          tableData: [],
          key: 5
        },
        6: {
          label: '周六',
          isAppoint: true,
          appointInterval: null, // 选择预约间隔
          appointNum: null, // 选择预约人数
          tableData: [],
          key: 6
        },
        7: {
          label: '周日',
          isAppoint: true,
          appointInterval: null, // 选择预约间隔
          appointNum: null, // 选择预约人数
          tableData: [],
          key: 7
        }
      },
      activeName: '1', // 选择工作日
      appointInterval: [1, 2, 3, 4, 5], // 预约间隔
      appointNum: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 20, 50], // 预约人数
      tableData: [], // 窗口配置列表
      hallAppointmentConfigList: [], // 营业厅预约配置列表
      createObj: {
        userIdleCalling: true,
        businessIdleCalling: true,
        queueConfig: [],
        businessConfig: [],
        priority: 0,
        status: 1,
        updateTime: '',
        rowStatus: 'edit'
      },
      createAppointObj: {
        startTime: '',
        endTime: '',
        capacity: null,
        rowStatus: 'edit',
        dateRange: [] // 新增的dateRange字段
      },
      queueConfigJson: {},
      businessConfigJson: {},
      miniProgramCode: '', // 生成的小程序二维码地址
      myWeekDate: [] // 当前周日期集合
    }
  },
  watch: {
    dialogParam: {
      handler(val) {
        this.init(val)
        this.getQueueList()
        this.getBusinessList()
      }
    },
    'editForm.qrType': {
      handler(val) {
        if (val === 1) {
          this.getMiniProgramCode()
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 预约配置初始化和回显 */
    init(val) {
      this.configForm = JSON.parse(JSON.stringify(val.data))
      this.editForm = {
        ...this.editForm,
        ...this.configForm
      }
      /** 处理窗口配置回显 */
      this.tableData = []
      this.configForm.callWindowConfigList.forEach(item => {
        item.rowStatus = 'text'
        this.tableData.push({ ...item })
      })
      /** 编辑状态下，清空预约配置数据；新增数据默认支持预约 */
      if (this.configForm.id) {
        Object.keys(this.weekdayValue).forEach(key => {
          this.weekdayValue[key].tableData = []
          this.weekdayValue[key].isAppoint = false
        })
      }
      /** 处理预约配置回显 */
      if (this.configForm.hallAppointmentConfigList.length > 0) {
        this.configForm.hallAppointmentConfigList.forEach(item => {
          const clone = { ...this.createAppointObj, rowStatus: 'text', ...item }
          this.weekdayValue[item.week].tableData.push(clone)
          this.weekdayValue[item.week].isAppoint = true
        })
      }
      this.getBusinessHours()
    },
    /** 根据店铺营业时间判断，仅展示工作日的配置 */
    getBusinessHours() {
      const hoursRange = this.editForm.businessHours.split(',')
      this.myWeekDate = []
      hoursRange.forEach((item, index) => {
        if (item) {
          const hours = item.split('-')
          this.weekdayValue[index + 1].businessHours = hours
          this.myWeekDate.push(this.weekdayValue[index + 1])
        }
      })
    },
    /** 根据返回标识展示配置项的文本值  */
    getText(configObj, value) {
      let text = ''
      value.forEach((item) => {
        if (configObj[item]) {
          text += ' ' + configObj[item]
        }
      })
      return text
    },
    /** 获取队列配置范围 */
    getQueueList() {
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/queryWindowQueueConfig', { })
        .then(res => {
          if (res.status === '200') {
            this.queueConfigJson = res.data.queueConfig
          } else {
            this.$message.error(res.message)
          }
        })
    },
    /** 获取业务类型配置范围 */
    getBusinessList() {
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/queryBusinessConfig', { })
        .then(res => {
          if (res.status === '200') {
            this.businessConfigJson = res.data.businessConfig
          } else {
            this.$message.error(res.message)
          }
        })
    },
    /** 获取小程序码 */
    getMiniProgramCode() {
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/generateMiniProgramCode',
        { shopId: this.editForm.shopId, miniProgramSize: 280 })
        .then(res => {
          if (res.status === '200') {
            this.editForm.qrCode = this.$apiConfig.level1cloudstorePathPreFix + '/proxyFs/show?file_id=' + res.data.fileID
            // this.editForm.qrCode = `https://img1.staff.ydsc.liuliangjia.cn/material/${res.data.fileID}.png`
          } else {
            this.$message.error(res.message)
          }
        })
    },
    /** 新增一个窗口配置 */
    handleCreate(windowNumber) {
      if (!windowNumber) {
        this.$message.error('请选择窗口号')
        return false
      }
      this.tableData.push({
        windowNumber,
        ...this.createObj
      })
      this.myId = ''
    },
    /** 点击编辑窗口按钮 */
    handleEdit(row) {
      row.rowStatus = 'edit'
    },
    /** 保存窗口设置 */
    handleSort(index, row) {
      if (row.queueConfig.length === 0) {
        this.$message.warning('请选择队列')
        return false
      }
      row.rowStatus = 'text'
    },
    /** 窗口删除 */
    handleDelete(index, row) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableData.splice(index, 1)
        this.$message({
          type: 'success',
          message: `已删除【${row.windowNumber}】号窗口`
        })
      })
    },
    /** 预约时间编辑 */
    handleAppointTimeEdit(row) {
      row.rowStatus = 'edit'
      row.dateRange = [row.startTime, row.endTime]
    },
    /** 预约时间保存 */
    handleAppointTimeSort(index, row) {
      if (!row.capacity || row.capacity <= 0) {
        this.$message.warning('预约人数不能为空或小于0')
        return false
      }
      row.rowStatus = 'text'
      row.startTime = row.dateRange[0]
      row.endTime = row.dateRange[1]
      row.capacity = parseInt(row.capacity)
    },
    /** 预约时间删除 */
    handleAppointTimeDelete(index, row, tableData) {
      this.$confirm('此操作将删除该时间段, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        tableData.splice(index, 1)
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      })
    },
    /** 一键添加预约时间段 */
    handleCreateAppointAll({ businessHours, appointInterval, appointNum, key }) {
      if (!appointInterval) {
        this.$message.warning('请选择预约间隔')
        return false
      }
      if (!appointNum) {
        this.$message.warning('请输入预约人数')
        return false
      }
      this.$confirm('此操作将删除所有得时间段, 重新生成, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handleCreateAppointAllCallback(businessHours, appointInterval, appointNum, key)
      })
    },
    /** 一键添加确认回调 */
    handleCreateAppointAllCallback(businessHours, appointInterval, appointNum, key) {
      const [startTime, endTime] = businessHours
      const [startHourStr, startMinute] = startTime.split(':')
      const [endHourStr] = endTime.split(':')
      const startHour = parseInt(startHourStr)
      const endHour = parseInt(endHourStr)
      const hourDiff = endHour - startHour
      const appointLength = Math.floor(hourDiff / appointInterval)
      const tableData = []
      // 如果开始时间到结束时间的时间间隔小于选中的预约间隔，则不生成时间段
      if (appointLength === 0) {
        return
      }
      // 如果开始时间到结束时间的时间间隔小于选中的预约间隔的两倍，则只生成一个时间段，且这个时间段的开始时间为开始时间，结束时间为营业结束时间
      if (appointLength === 1) {
        const clone = { ...this.createAppointObj, rowStatus: 'text', startTime, endTime, capacity: appointNum }
        tableData.push(clone)
        this.weekdayValue[key].tableData = [clone]
        return
      }
      // 如果开始时间到结束时间的时间间隔/选中的预约间隔，大于等于2，结束时间是开始时间+间隔时间，最后一个结束时间为营业结束时间
      let currentHour = startHour
      for (let i = 0; i < appointLength; i++) {
        const currentHourStr = currentHour.toString().padStart(2, '0')
        const nextHour = currentHour + appointInterval
        const nextHourStr = nextHour.toString().padStart(2, '0')
        const clone = { ...this.createAppointObj, rowStatus: 'text', capacity: appointNum }
        clone.startTime = `${currentHourStr}:${startMinute}`
        clone.endTime = `${nextHourStr}:${startMinute}`
        tableData.push(clone)
        currentHour = nextHour
      }
      tableData[appointLength - 1].endTime = endTime
      this.weekdayValue[key].tableData = [].concat(tableData)
    },
    /** 添加单个预约时间段 */
    handleCreateAppoint({ businessHours, tableData }) {
      const clone = { ...this.createAppointObj, dateRange: [businessHours[0], businessHours[1]] }
      tableData.push(clone)
    },
    /** 取消保存表单 */
    closeEditMark() {
      this.dialogParam.modelVisible = false
    },
    /** 保存表单 */
    saveConfig(formName) {
      const validCallwindowValue = this.validateCallwindowValue()
      if (!validCallwindowValue) {
        return false
      }
      const validWeekdayValue = this.validateWeekdayValue()
      if (!validWeekdayValue) {
        return false
      }
      this.editForm.hallAppointmentConfigList = this.hallAppointmentConfigList
      this.editForm.callWindowConfigList = this.tableData

      console.log(this.myWeekDate, this.tableData, this.editForm, '保存成功')
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/update', this.editForm)
        .then(res => {
          if (res.status === '200') {
            this.closeEditMark()
          } else {
            this.$message.error(res.message)
          }
        })
    },
    /** 校验窗口号配置 */
    validateCallwindowValue() {
      try {
        if (this.tableData.length === 0) {
          this.$message.warning('请至少启用一个窗口')
          throw new Error('End loop')
        }
        this.tableData.forEach(item => {
          if (item.rowStatus === 'edit') {
            this.$message.warning('请先保存窗口更改信息')
            throw new Error('End loop')
          }
        })
        return true
      } catch (error) {
        return false
      }
    },
    /** 校验预约时间段配置 */
    validateWeekdayValue() {
      this.hallAppointmentConfigList = []
      try {
        this.myWeekDate.forEach(item => {
          if (item.isAppoint) {
            if (item.tableData.length === 0) {
              this.$message.warning(`${item.label}支持预约, 必填配置预约时间段`)
              throw new Error('End loop')
            }
            item.tableData.forEach(tableItem => {
              if (tableItem.rowStatus === 'edit') {
                this.$message.warning(`${item.label}预约时间段未保存`)
                throw new Error('End loop')
              }
              this.hallAppointmentConfigList.push({
                ...tableItem,
                week: item.key
              })
            })
          }
        })
        return true
      } catch (error) {
        return false
      }
    }
  }
}