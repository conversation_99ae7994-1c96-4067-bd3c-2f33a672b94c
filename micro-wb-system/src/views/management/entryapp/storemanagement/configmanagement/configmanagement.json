{"list": [{"type": "form", "label": "表单容器", "columnName": "form_1723444998412", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1723445009083", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 4, "align": "left", "list": [{"type": "select", "label": "", "columnName": "provinceCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "provinceName", "option-value": "provinceCode", "option-alias": "", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/shop/common/regionInfo", "apiType": "post+json", "apiParam": "{}", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "Option 1", "value": "1", "disabled": false}, {"label": "Option 2", "value": "2", "disabled": false}], "hasParentForm": true, "labelWidth": 0, "targetName": "label_select_1723445061571", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1723445061571", "network$$$tag": false, "dynamic": {"single_option_select": [{"source": {"label": "", "columnName": "provinceCode"}, "target": [{"columnName": "cityCode", "label": " cityCode", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "type": "select"}], "optionType": "2", "condition": [{"columnName": "provinceCode", "condition": "", "valueType": "change", "compareValueType": "", "columnValue": "", "apiType": "post+json", "apiName": "/yundian/osm/api/shop/common/regionCitiesInfo", "apiParam": "{\"provinceCode\":\"$provinceCode$\"}", "sessionKey": "", "dicKey": "", "status": []}]}]}, "isClearOtherValue": true, "clearOtherValueList": ["cityCode"]}]}, {"span": 4, "align": "left", "list": [{"type": "select", "label": "", "columnName": "cityCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "cityName", "option-value": "cityCode", "option-alias": "", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "{}", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "Option 1", "value": "1", "disabled": false}, {"label": "Option 2", "value": "2", "disabled": false}], "hasParentForm": true, "labelWidth": 0, "targetName": "label_select_1723445062692", "oldColumnName": "cityCodes", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "network$$$tag": false}]}, {"span": 6, "align": "left", "list": [{"type": "input", "label": "", "columnName": "unifiedChannelId", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "实体渠道编码", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": "", "oldColumnName": "input_1723445077514"}]}, {"span": 5, "align": "left", "list": [{"type": "input", "label": "", "columnName": "shopNameOrShortName", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "营业厅名称/简称", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 0, "oldColumnName": "input_1723445078995"}]}, {"span": 5, "align": "left", "list": [{"type": "input", "label": "", "columnName": "shopAdminNameOrPhone", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "店长姓名/手机号", "maxlength": "", "show-word-limit": false, "clearable": false, "disabled": false, "readonly": false}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 0, "oldColumnName": "input_1723445093749"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}, {"type": "grid", "label": "栅格布局", "columnName": "grid_1723445176966", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 4, "align": "left", "list": [{"type": "select", "label": "渠道类型：", "columnName": "channelCategorys", "defaultValue": [""], "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": true, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "旗舰店", "value": "1001"}, {"label": "标准店", "vlaue": "new_option1724210064847", "value": "1002"}, {"label": "社区店", "vlaue": "new_option1724210166327", "value": "1003"}, {"label": "自建手机卖场", "vlaue": "new_option1724210220064", "value": "1004"}, {"label": "校园店", "vlaue": "new_option1724210235139", "value": "1005"}, {"label": "委托加盟", "vlaue": "new_option1724210887902", "value": "2002"}, {"label": "带店加盟", "vlaue": "new_option1724210896230", "value": "2003"}, {"label": "手机卖场", "vlaue": "new_option1724210918022", "value": "2101"}, {"label": "手机专卖店", "vlaue": "new_option1724210955810", "value": "2102"}, {"label": "授权代理店", "vlaue": "new_option1724210971042", "value": "2103"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "旗舰店", "value": "1001", "disabled": false}, {"label": "标准店", "vlaue": "new_option1724210064847", "value": "1002", "disabled": false}, {"label": "社区店", "vlaue": "new_option1724210166327", "value": "1003", "disabled": false}, {"label": "自建手机卖场", "vlaue": "new_option1724210220064", "value": "1004", "disabled": false}, {"label": "校园店", "vlaue": "new_option1724210235139", "value": "1005", "disabled": false}, {"label": "委托加盟", "vlaue": "new_option1724210887902", "value": "2002", "disabled": false}, {"label": "带店加盟", "vlaue": "new_option1724210896230", "value": "2003", "disabled": false}, {"label": "手机卖场", "vlaue": "new_option1724210918022", "value": "2101", "disabled": false}, {"label": "手机专卖店", "vlaue": "new_option1724210955810", "value": "2102", "disabled": false}, {"label": "授权代理店", "vlaue": "new_option1724210971042", "value": "2103", "disabled": false}], "hasParentForm": true, "labelWidth": 85, "targetName": "label_select_1723445222544", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "channelCategory"}]}, {"span": 4, "align": "left", "list": [{"type": "select", "label": "营业厅状态：", "columnName": "businessHallStatus", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": false, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "正常", "value": "1"}, {"label": "冻结", "vlaue": "new_option1724209212496", "value": "2"}, {"label": "终止", "vlaue": "new_option1724209218561", "value": "9"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "正常", "value": "1", "disabled": false}, {"label": "冻结", "vlaue": "new_option1724209212496", "value": "2", "disabled": false}, {"label": "终止", "vlaue": "new_option1724209218561", "value": "9", "disabled": false}], "hasParentForm": true, "labelWidth": 100, "targetName": "label_select_1723445253636", "oldColumnName": "select_1723445253636", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": ""}]}, {"span": 5, "align": "left", "list": [{"type": "select", "label": "信息审核情况：", "columnName": "applyStatus", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "通过", "value": "3"}, {"label": "下线整改", "vlaue": "new_option1724296950061", "value": "4"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "通过", "value": "3", "disabled": false}, {"label": "下线整改", "vlaue": "new_option1724296950061", "value": "4", "disabled": false}], "hasParentForm": true, "labelWidth": 110, "targetName": "label_select_1723445280134", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"span": 3, "align": "left", "list": [{"type": "select", "label": "精选厅：", "columnName": "vipStatus", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "是", "value": "1"}, {"label": "否", "vlaue": "new_option1724209076999", "value": "0"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "是", "value": "1", "disabled": false}, {"label": "否", "vlaue": "new_option1724209076999", "value": "0", "disabled": false}], "hasParentForm": true, "labelWidth": 70, "targetName": "label_select_1723445333695", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1723445333695"}]}, {"span": 4, "align": "left", "list": [{"type": "select", "label": "默认厅：", "columnName": "defaultStatus", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "是", "value": "1"}, {"label": "否", "vlaue": "new_option1724209170241", "value": "0"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "是", "value": "1", "disabled": false}, {"label": "否", "vlaue": "new_option1724209170241", "value": "0", "disabled": false}], "hasParentForm": true, "labelWidth": 70, "targetName": "label_select_1724140204527", "oldColumnName": "select_1724140204527", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": ""}]}, {"span": 4, "align": "left", "list": [{"type": "select", "label": "支持在线取号：", "columnName": "queueStatus", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "是", "value": "1"}, {"label": "否", "vlaue": "new_option1724209242249", "value": "0"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "是", "value": "1", "disabled": false}, {"label": "否", "vlaue": "new_option1724209242249", "value": "0", "disabled": false}], "hasParentForm": true, "labelWidth": 110, "targetName": "label_select_1724140191947", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1724140191947"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}, {"type": "grid", "label": "栅格布局", "columnName": "grid_1745404649221", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 6, "align": "left", "list": [{"type": "select", "label": "移动优选厅：", "columnName": "chinaMobileFirst", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": false, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "是", "value": "1"}, {"label": "否", "vlaue": "new_option1745463293172", "value": "2"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "是", "value": "1", "disabled": false}, {"label": "否", "vlaue": "new_option1745463293172", "value": "2", "disabled": false}], "hasParentForm": true, "labelWidth": 100, "targetName": "label_select_1745404675007", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "isChinaMobileFirst"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}, {"type": "LRGrid", "label": "左右布局", "columnName": "LRGrid_1723445500384", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 9, "align": "right", "list": [{"type": "button", "label": "查询", "columnName": "button_1723445513963", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"type": "primary"}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "tableId": "table_1723445846685", "formId": "form_1723444998412", "event": "submit", "authSwitch": true, "authId": "o2o_shop_select_but"}]}, {"span": 3, "align": "center", "list": [{"type": "button", "label": "省侧统一配置", "columnName": "provinceConfig", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "button_1728527641902", "authSwitch": true, "authId": "o2o_but_province"}]}, {"span": 12, "align": "left", "list": [{"type": "button", "label": "附近厅推荐方案配置", "columnName": "button_1744354998430", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "event": "router", "routerType": "name", "routerKey": "nearby", "authSwitch": true, "authId": "nearby_btn"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false}, {"type": "form", "label": "表单容器", "columnName": "form_1724140679316", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"label": "", "type": "customArea", "columnName": "fontTip", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont iconquyu", "customLabelWidth": true, "classify": "form", "class": "", "slotName": "fontTip", "props": {"placeholder": ""}, "hidden": false, "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": "", "oldColumnName": "customArea_1724140681646"}], "isBorder": true, "isOverspread": false, "classify": "layout", "hidden": false}, {"type": "empty", "label": "空容器", "columnName": "empty_1723445648450", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "form", "label": "表单容器", "columnName": "form_1730788677311", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1730788680706", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 13, "align": "left", "list": [{"type": "button", "label": "批量下线整改", "columnName": "button_batchOffline", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": false, "labelWidth": 100, "bpmFlowImageCloumnName": "", "oldColumnName": "button_", "authSwitch": true, "authId": "o2o_but_offline"}]}, {"span": 11, "align": "right", "list": [{"label": "", "type": "customArea", "columnName": "customArea_1730788684366", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont iconquyu", "customLabelWidth": true, "classify": "form", "class": "", "slotName": "exportbutton", "props": {"placeholder": ""}, "hidden": false, "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": "", "authSwitch": false, "authId": ""}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false, "hasParentForm": false, "labelWidth": 100}, {"type": "table", "label": "表格", "columnName": "table_1723445846685", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": true, "multipleSelectionFixed": true, "cacheMultipleSelection": false, "multipleSelectionColumnName": ["unifiedChannelId"], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": true, "show-pagination": true, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [{"label": "button_1723446184262", "columnName": "button_1723446184262", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "applyStatus", "compare": "!=", "compareType": "staticData", "targetValue": "下线整改", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "vipStatus", "compare": "=", "compareType": "staticData", "targetValue": "0", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "businessHallStatus", "compare": "!=", "compareType": "staticData", "targetValue": "终止", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "businessHallStatus", "compare": "!=", "compareType": "staticData", "targetValue": "冻结", "authSwitch": false, "authId": "", "if": "&&"}]}, {"label": "button_1723446203372", "columnName": "button_1723446203372", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "vipStatus", "compare": "=", "compareType": "staticData", "targetValue": "1", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "applyStatus", "compare": "!=", "compareType": "staticData", "targetValue": "下线整改", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "businessHallStatus", "compare": "!=", "compareType": "staticData", "targetValue": "终止", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "businessHallStatus", "compare": "!=", "compareType": "staticData", "targetValue": "冻结", "authSwitch": false, "authId": "", "if": "&&"}]}, {"label": "button_1723446174804", "columnName": "button_1723446174804", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "applyStatus", "compare": "!=", "compareType": "staticData", "targetValue": "下线整改", "authSwitch": false, "authId": "", "if": "||"}]}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "defaultStatus", "compare": "=", "compareType": "staticData", "targetValue": "0", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "applyStatus", "compare": "!=", "compareType": "staticData", "targetValue": "下线整改", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "businessHallStatus", "compare": "!=", "compareType": "staticData", "targetValue": "终止", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "businessHallStatus", "compare": "!=", "compareType": "staticData", "targetValue": "冻结", "authSwitch": false, "authId": "", "if": "&&"}]}, {"label": "cancelDefault", "columnName": "cancelDefault", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "defaultStatus", "compare": "=", "compareType": "staticData", "targetValue": "1", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "applyStatus", "compare": "!=", "compareType": "staticData", "targetValue": "下线整改", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "businessHallStatus", "compare": "!=", "compareType": "staticData", "targetValue": "终止", "authSwitch": false, "authId": "", "if": "&&"}, {"columnName": "businessHallStatus", "compare": "!=", "compareType": "staticData", "targetValue": "冻结", "authSwitch": false, "authId": "", "if": "&&"}]}, {"label": "delete", "columnName": "delete", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "applyStatus", "compare": "=", "compareType": "staticData", "targetValue": "下线整改", "authSwitch": false, "authId": "", "if": "&&"}]}, {"label": "recommendedLevel", "columnName": "recommendedLevel", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "channelCategoryGroup", "compare": "=", "compareType": "staticData", "targetValue": "1", "authSwitch": false, "authId": "", "if": "&&"}]}], "list": [{"timeStamp": 1723445874290, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "归属省", "columnName": "province", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "归属市", "columnName": "city", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "实体营业厅名称", "columnName": "shopName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "实体营业厅简称", "columnName": "shopShortName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "110", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "实体渠道编码", "columnName": "unifiedChannelId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "地址", "columnName": "address", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "经度", "columnName": "longitude", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "纬度", "columnName": "latitude", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "营业时间", "columnName": "businessHours", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "营业范围", "columnName": "businessScope", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "店长", "columnName": "shopAdminName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "店长手机号", "columnName": "shopAdminPhone", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "渠道类型", "columnName": "channelCategory", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "营业厅状态", "columnName": "businessHallStatus", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "门店客服电话", "columnName": "kefuPhone", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "图片", "columnName": "picture", "type": "customArea", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "activeType": "table_columnArea", "slotName": "picture", "defaultValue": "", "icon": "", "required": false, "rules": [], "asideIcon": "iconfont iconquyu", "customLabelWidth": false, "class": "", "props": {"placeholder": ""}, "hidden": false, "nullHidde": true}, {"label": "支持在线取号任务", "columnName": "queueStatus", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "-", "value": "0"}, {"label": "√", "value": "1"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "精选厅", "columnName": "vipStatus", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "√", "value": "1"}, {"label": "-", "value": "0"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "默认厅", "columnName": "defaultStatus", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "-", "value": "0"}, {"label": "√", "value": "1"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "移动优选厅", "columnName": "chinaMobileFirst", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "是", "value": "1"}, {"label": "否", "value": "2"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "推荐等级", "columnName": "recommendLevel", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "高", "value": "1"}, {"label": "中", "value": "2"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "营业厅入驻一级APP时间", "columnName": "createdTime", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "信息审核情况", "columnName": "applyStatus", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "社会渠道", "columnName": "channelCategoryGroup", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [{"type": "text", "timestamp": 1723446163224, "columnName": "button_details", "label": "详情", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "authSwitch": true, "authId": "o2o_but_detail", "_hidden_state": true}, {"type": "text", "timestamp": 1723446167916, "columnName": "manageEdit", "label": "编辑", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "authSwitch": true, "authId": "o2o_but_edit", "_hidden_state": true}, {"type": "text", "timestamp": 1723446174804, "columnName": "button_1723446174804", "label": "下线整改", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/offlineEdit", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"shopId\":\"$shopId$\",\"status\":1}", "confirmationSwitch": "", "confirmationMessage": "", "authSwitch": true, "authId": "o2o_but_offline", "isShowBadge": "", "_hidden_state": true}, {"type": "text", "timestamp": 1723446184262, "columnName": "button_1723446184262", "label": "设为精选厅", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "hidden", "state": "show", "dataLinkage": [], "confirmationStatus": [], "confirmationSwitch": true, "confirmationMessage": "精选厅定义：全业务受理、营销服务能力强的自控物业厅。是否设置成精选厅", "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/setVipStatus", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": {"shopId": "$shopId$", "status": 1}, "authSwitch": true, "authId": "o2o_but_checkshop", "_hidden_state": true}, {"type": "text", "timestamp": 1723446203372, "columnName": "button_1723446203372", "label": "取消精选厅", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "confirmationSwitch": true, "confirmationMessage": "是否取消精选厅", "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/setVipStatus", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"shopId\":\"$shopId$\",\"status\":0}", "authSwitch": true, "authId": "o2o_but_checkshop", "_hidden_state": true}, {"type": "text", "timestamp": 1724656904289, "columnName": "<PERSON><PERSON><PERSON><PERSON>", "label": "设为默认厅", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "authSwitch": true, "authId": "o2o_but_checkshop", "_hidden_state": true}, {"type": "text", "timestamp": 1724656950879, "columnName": "cancelDefault", "label": "取消默认厅", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "authSwitch": true, "authId": "o2o_but_checkshop", "_hidden_state": true}, {"type": "text", "timestamp": 1723446213865, "columnName": "button_1723446213865", "label": "变更记录", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "dialog-table", "dialogConfig": {"bindName": "manageOperate", "title": "变更记录", "subTitle": "", "titleClass": "dialog-title-default", "width": "870px", "height": "auto", "toolList": []}, "authSwitch": true, "authId": "o2o_but_bgjl", "_hidden_state": true}, {"type": "text", "timestamp": 1730358894875, "columnName": "delete", "label": "删除", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "confirmationSwitch": true, "confirmationMessage": "确认删除？删除后列表将不可见。", "authSwitch": true, "authId": "o2o_but_delete", "activeType": "table_row_api", "refreshTableData": true, "apiMethod": "post+json", "apiName": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/deleteShopOnline", "apiSuccessTip": true, "apiFailTip": true, "isVerifyTableRow": false, "apiUpdateCurrentCacheFlag": false, "apiParam": "{\"shopId\":\"$shopId$\"}", "_hidden_state": true}, {"type": "text", "timestamp": 1741924257798, "columnName": "appointment", "label": "设置预约取号信息", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "authSwitch": true, "authId": "o2o_but_app_config", "_hidden_state": true}, {"type": "text", "timestamp": 1747033083896, "columnName": "recommendedLevel", "label": "设置推荐等级", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "dialog-form", "dialogConfig": {"bindName": "levelDialog", "title": "设置附近厅推荐等级", "subTitle": "", "titleClass": "dialog-title-default", "width": "800px", "height": "auto", "toolList": []}, "_hidden_state": true}], "operation-width": "170", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30, 50, 100], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/proxyData/api/proxy/o2o/shoponline/osm/list", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading", "apiFailTip": false, "paramDataName": false}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1723445874290, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "归属省", "columnName": "province", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "归属市", "columnName": "city", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "实体营业厅名称", "columnName": "shopName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "实体营业厅简称", "columnName": "shopShortName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "110", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "实体渠道编码", "columnName": "unifiedChannelId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "地址", "columnName": "address", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "经度", "columnName": "longitude", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "纬度", "columnName": "latitude", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "营业时间", "columnName": "businessHours", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "营业范围", "columnName": "businessScope", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "店长", "columnName": "shopAdminName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "店长手机号", "columnName": "shopAdminPhone", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "渠道类型", "columnName": "channelCategory", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "营业厅状态", "columnName": "businessHallStatus", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "门店客服电话", "columnName": "kefuPhone", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "图片", "columnName": "picture", "type": "customArea", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "activeType": "table_columnArea", "slotName": "picture", "defaultValue": "", "icon": "", "required": false, "rules": [], "asideIcon": "iconfont iconquyu", "customLabelWidth": false, "class": "", "props": {"placeholder": ""}, "hidden": false, "nullHidde": true}, {"label": "支持在线取号任务", "columnName": "queueStatus", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "120", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "-", "value": "0"}, {"label": "√", "value": "1"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "精选厅", "columnName": "vipStatus", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "√", "value": "1"}, {"label": "-", "value": "0"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "默认厅", "columnName": "defaultStatus", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "-", "value": "0"}, {"label": "√", "value": "1"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "align": "center"}, {"label": "移动优选厅", "columnName": "chinaMobileFirst", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "是", "value": "1"}, {"label": "否", "value": "2"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "推荐等级", "columnName": "recommendLevel", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "高", "value": "1"}, {"label": "中", "value": "2"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "营业厅入驻一级APP时间", "columnName": "createdTime", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "信息审核情况", "columnName": "applyStatus", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "hasParentForm": false, "labelWidth": 100, "paginationStatus": [], "singleSelectionStatus": [], "multipleSelectionStatus": [], "router": {}, "show-operation-status": [], "formId": "form_1723444998412", "multipleSelectionSwitch": false}], "classify": "empty", "hidden": false}], "model": {"provinceCode": "", "cityCode": "", "unifiedChannelId": "", "shopNameOrShortName": "", "shopAdminNameOrPhone": "", "channelCategorys": [""], "businessHallStatus": "", "applyStatus": "", "vipStatus": "", "defaultStatus": "", "queueStatus": "", "chinaMobileFirst": "", "fontTip": "", "customArea_1730788684366": ""}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "configmanagement", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "mini", "class": "", "layoutType": "flex", "statusList": [], "localProxy": "", "nigxProxy": "", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "data@shop", "requestDataKey": "", "totalKey": "total", "pageNumKey": "page", "pageSizeKey": "rows"}, "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "厅店配置管理"}}