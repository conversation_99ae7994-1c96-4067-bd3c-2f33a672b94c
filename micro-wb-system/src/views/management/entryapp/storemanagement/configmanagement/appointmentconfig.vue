<template>
  <asp-dialog
    class="webbas"
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    width="80%"
  >
    <el-form ref="configForm" :rules="rules" :model="editForm" >
      <div class="form-con">
        <el-row>
          <el-col :span="8">
            <el-form-item label="支持VIP取号：" label-width="120px" prop="vipEnabled">
              <el-select v-model="editForm.vipEnabled" placeholder="请选择">
                <el-option label="支持" :value="true"></el-option>
                <el-option label="不支持" :value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="票号模式：" label-width="120px" prop="ticketMode">
              <el-select v-model="editForm.ticketMode" placeholder="请选择">
                <el-option v-for="(item,index) in ticketModeJson" :key="item+index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="对接方式：" label-width="120px" prop="integrationMode">
              <el-select v-model="editForm.integrationMode" placeholder="请选择">
                <el-option v-for="(item,index) in integrationModeJson" :key="item+index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="取号白名单：" label-width="120px" prop="whitelist">
              <el-input v-model="editForm.whitelist" placeholder="请输入号码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <div style="line-height: 30px;margin-top:10px;">不限制取号数量，最多配置50个，多个号码使用“,”分割</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="厅店取号状态：" label-width="120px" prop="hallStatus">
              <el-select v-model="editForm.hallStatus" placeholder="请选择">
                <el-option v-for="(item,index) in hallStatusJson" :key="item+index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="消息类型：" label-width="120px" prop="messageType">
              <el-select v-model="editForm.messageType" placeholder="请选择">
                <el-option v-for="(item,index) in messageTypeJson" :key="item+index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="二维码类型：" label-width="120px" prop="qrType">
              <el-select v-model="editForm.qrType" placeholder="请选择">
                <el-option v-for="(item,index) in qrTypeJson" :key="item+index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="二维码地址：" label-width="120px" prop="qrCodeAddress">
              <el-input v-model="editForm.qrCodeAddress" placeholder="请输入地址"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="editForm.qrType === 1">
          <el-col :span="8">
            <el-form-item label="小程序渠道：" label-width="120px" prop="qrChannel">
              <el-select v-model="editForm.qrChannel" placeholder="请选择">
                <el-option label="云店小程序" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="小程序码：" label-width="120px" prop="qrCode">
              <img v-if="editForm.qrCode" :src="editForm.qrCode" class="avatar">
              <el-alert
                v-else
                title="小程序码生成中，请稍等刷新或重新保存"
                type="info"
                show-icon
                :closable="false">
              </el-alert>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="editForm.qrType === 2">
          <el-col :span="8">
            <el-form-item label="app安装二维码：" label-width="120px" prop="qrCode">
              <img src="../../../assets/img/khd.png" class="avatar">
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="tab-con">
        <el-row class="window-select">
          <el-col :span="8">
            <el-form-item label="" prop="queueConfig">
              <el-select v-model="myId" placeholder="请选择窗口号">
                <el-option
                  v-for="idItem in idJson"
                    :key="idItem"
                    :label="idItem"
                    :disabled="idJsonHas.includes(idItem)"
                    :value="idItem">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="" prop="queueConfig">
              <el-button
                size="mini"
                type=""
                @click="handleCreate(myId)">新增</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-table
          :data="tableData"
          class="table"
          style="width: 100%"
          border
        >
          <el-table-column
            prop="windowNumber"
            label="窗口号"
            width="50">
          </el-table-column>
          <el-table-column
            label="用户闲时串叫"
            width="100">
            <template slot-scope="scope">
              <el-form-item label="" prop="userIdleCalling">
                <el-select v-if="scope.row.rowStatus=='edit'" v-model="scope.row.userIdleCalling" placeholder="请选择">
                  <el-option label="支持" :value="true"></el-option>
                  <el-option label="不支持" :value="false"></el-option>
                </el-select>
                <span v-else>{{ scope.row.userIdleCalling ? '支持串叫' : '不支持串叫' }}</span>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            label="业务闲时串叫"
            width="100">
            <template slot-scope="scope">
              <el-form-item label="" prop="businessIdleCalling">
                <el-select v-if="scope.row.rowStatus=='edit'" v-model="scope.row.businessIdleCalling" placeholder="请选择">
                  <el-option label="支持" :value="true"></el-option>
                  <el-option label="不支持" :value="false"></el-option>
                </el-select>
                <span v-else>{{ scope.row.businessIdleCalling ? '支持串叫' : '不支持串叫' }}</span>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            label="队列"
            width="200">
            <template slot-scope="scope">
              <el-form-item label="" prop="queueConfig">
                <el-select v-if="scope.row.rowStatus=='edit'" v-model="scope.row.queueConfig" multiple placeholder="请选择">
                  <el-option
                    v-for="(queueConfigItem, index) in queueConfigJson"
                    :key="queueConfigItem+index"
                    :label="queueConfigItem"
                    :value="index">
                  </el-option>
                </el-select>
                <span v-else>{{ getText(queueConfigJson,scope.row.queueConfig) }}</span>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            label="业务"
            width="200">
            <template slot-scope="scope">
              <el-form-item label="" prop="businessConfig">
                <el-select v-if="scope.row.rowStatus=='edit'" v-model="scope.row.businessConfig" multiple placeholder="请选择">
                  <el-option
                    v-for="(businessConfigItem, index) in businessConfigJson"
                    :key="businessConfigItem+index"
                    :label="businessConfigItem"
                    :value="index">
                  </el-option>
                </el-select>
                <span v-else>{{ getText(businessConfigJson,scope.row.businessConfig) }}</span>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            label="优先级"
            width="100">
            <template slot-scope="scope">
              <el-form-item label="" prop="priority">
                <el-select  v-if="scope.row.rowStatus=='edit'" v-model="scope.row.priority" placeholder="请选择">
                  <el-option
                    v-for="(priorityItem,index) in priorityJson"
                    :key="priorityItem"
                    :label="priorityItem"
                    :value="index">
                  </el-option>
                </el-select>
                <span v-else>{{ priorityJson[scope.row.priority]}}</span>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            label="当前状态"
            width="100">
            <template slot-scope="scope">
              <el-form-item label="" prop="status">
                <el-select v-if="scope.row.rowStatus=='edit'" v-model="scope.row.status" placeholder="请选择">
                  <el-option
                    v-for="(statusItem,index) in statusJson"
                    :key="statusItem"
                    :label="statusItem"
                    :value="index">
                  </el-option>
                </el-select>
                <span v-else>{{ statusJson[scope.row.status]}}</span>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            label="更新时间"
            width="120">
            <template slot-scope="scope">
              {{ parseTime(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.rowStatus=='edit'"
                size="mini"
                @click="handleSort(scope.$index, scope.row)">保存</el-button>
              <el-button
                v-else
                size="mini"
                @click="handleEdit(scope.row)">编辑</el-button>
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane v-for="item in myWeekDate" :key="item.key" :label="item.label+'预约时间'" :name="item.key.toString()">
          <div class="tab-con">
            <div class="form-con">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="营业时间：" label-width="120px">
                    <el-time-picker
                      is-range
                      v-model="item.businessHours"
                      disabled
                      format="HH:mm"
                      value-format = "HH:mm"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围">
                    </el-time-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="是否支持预约：" label-width="120px" prop="qrType">
                    <el-select v-model="item.isAppoint" placeholder="请选择">
                      <el-option label="支持" :value="true"></el-option>
                      <el-option label="不支持" :value="false"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="预约间隔：" label-width="120px">
                    <el-select v-model="item.appointInterval" placeholder="请选择预约时间段间隔">
                      <el-option
                        v-for="appointNumtem in appointInterval"
                        :key="appointNumtem"
                        :label="appointNumtem"
                        :value="appointNumtem">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="预约人数：" label-width="90px">
                    <el-select v-model="item.appointNum" placeholder="请选择预约人数">
                      <el-option
                        v-for="appointNumtem in appointNum"
                        :key="appointNumtem"
                        :label="appointNumtem"
                        :value="appointNumtem">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item label="" label-width="90px">
                    <el-button
                      size="mini"
                      type=""
                      @click="handleCreateAppointAll(item)">一键添加</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="3">
                  <el-button
                    size="mini"
                    type=""
                    @click="handleCreateAppoint(item)">添加</el-button>
                </el-col>
              </el-row>
            </div>
            <el-table
              :data="item.tableData"
              class="table"
              style="width: 100%"
              border
            >
              <el-table-column
                label="预约时间段"
                width="300">
                <template slot-scope="scope">
                  <el-form-item label="" prop="priority">
                    <el-time-picker
                      v-if="scope.row.rowStatus=='edit'"
                      is-range
                      v-model="scope.row.dateRange"
                      format="HH:mm"
                      value-format = "HH:mm"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围">
                    </el-time-picker>
                    <span v-else>{{ scope.row.startTime }} - {{ scope.row.endTime }}</span>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column
                label="预约人数"
                width="200">
                <template slot-scope="scope">
                  <el-form-item label="" prop="capacity">
                    <el-input v-if="scope.row.rowStatus=='edit'" type="number" v-model="scope.row.capacity"></el-input>
                    <span v-else>{{ scope.row.capacity }}</span>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.rowStatus=='edit'"
                    size="mini"
                    @click="handleAppointTimeSort(scope.$index, scope.row, item.tableData)">保存</el-button>
                  <el-button
                    v-else
                    size="mini"
                    @click="handleAppointTimeEdit(scope.row)">编辑</el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleAppointTimeDelete(scope.$index, scope.row, item.tableData)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template slot="footer-center">
      <div class="webbas footerCon">
        <asp-btn-solid
          name="保存"
          @click="saveConfig('configForm')"
        >
        </asp-btn-solid>
        <asp-btn-hollow
          @click="closeEditMark"
          name="取消"
        >
        </asp-btn-hollow>
      </div>
    </template>
  </asp-dialog>
</template>
<script>
import mixins from './appointment/appointmentmixin'
import { parseTime } from '@/utils/index'
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  mixins: [mixins],
  data() {
    return {
      configForm: {}
    }
  },
  computed: {
    idJsonHas: function() {
      const arr = []
      this.tableData.forEach((item) => {
        arr.push(parseInt(item.windowNumber))
      })
      return arr
    }
  },
  methods: {
    parseTime(time, cFormat) {
      return parseTime(time, cFormat)
    }
  }
}
</script>
<style scoped lang="scss">
.el-form-item {
  margin-top: 10px;
}
.tab-con{
  border:1px solid #ccc;
  padding:10px;
  margin:20px 0;
}
.table .el-form-item{
  margin-top:0px;
}
.form-con ::v-deep .el-form-item__content {
  margin-left: 0px!important;
  width:calc(100% - 120px) !important;
}
.tab-con ::v-deep .el-form-item__content {
  text-align: center!important;
}
.form-con ::v-deep .el-alert__title{
  font-size: 10px!important;
}
.window-select ::v-deep .el-form-item__content{
  width: 100%;
}
.avatar{
  width:80px;
  height: 80px;
}
</style>
