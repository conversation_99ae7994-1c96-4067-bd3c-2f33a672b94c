<!-- 用户支付渠道拦截报表 -->
/**
* 业务实体
*/
<template>
  <div style="height: 100%; ">
    <asp-smart-table ref="aspSmartTable"
                     v-model="model"
                     :table-json="tableJson"
                     :size-change="sizeChange"
                     :current-change="currentChange"
                     :before-http="beforeHttp"
                     :after-http="afterHttp"
                     :before-button="beforeButton"
                     :before-router="beforeRouter"
                     :before-table-row-render="beforeTableRowRender"
                     :before-table-render="beforeTableRender"
                     :before-click-table-cell="beforeClickTableCell"
                     :dialog-config="dialogConfig"
                     :render-table="renderTable"
                     @on="onbind">
      <template slot="fontTip">
        <span class="fontTip">精选厅:全业务受理、营销服务能力强的自控物业厅，可设置多家</span><br>
        <span class="fontTip">默认厅:中国移动APP向未登录、未授权用户展示的兜底营业厅，每个地市仅可设置一家</span>
      </template>
      <template slot="picture" slot-scope="scope">
        <img style="width: 50px; height: 50px;" :src="$apiConfig.level1cloudstorePathPreFix + '/proxyFs/show?file_id=' + scope.data.picture" alt="">
      </template>
      <template slot="exportbutton">
        <div class="btnStyle">
          <div class="btnitem" v-if="o2o_but_pladd">
            <el-upload
              ref="upload"
              action="#"
              accept=".xls,.xlsx"
              :show-file-list="false"
              :http-request="handleAddMessage"
              :auto-upload="true"
              >
              <span style="border-right:1px solid #DCDFE6;padding-right:10px" title="导入">批量添加厅店信息</span>
            </el-upload>
            <span @click="loadAddTemplate" class="icondownload">
              <el-icon title="下载模板" name="download"></el-icon>
            </span>
          </div>

          <div style="padding: 0 20px;" class="btnitem" @click="batchexport" v-if="o2o_shop_export_but">
            导出
          </div>
          <div class="btnitem" v-if="o2o_but_plxg">
            <el-upload
              ref="upload"
              action="#"
              accept=".xls,.xlsx"
              :show-file-list="false"
              :http-request="handleChangeShopInfos"
              :auto-upload="true"
              >
              <span style="border-right:1px solid #DCDFE6;padding-right:10px" title="导入">批量修改营业厅信息</span>
            </el-upload>
            <span @click="modifyTemplate(1)" class="icondownload">
              <el-icon title="下载模板" name="download"></el-icon>
            </span>
          </div>
          <div class="btnitem" v-if="o2o_but_plshop">
            <el-upload
              ref="upload"
              action="#"
              accept=".xls,.xlsx"
              :show-file-list="false"
              :http-request="handleModifyFileUpload"
              :auto-upload="true"
              >
              <span style="border-right:1px solid #DCDFE6;padding-right:10px" title="批量设置默认厅/精选厅/推荐等级">批量设置默认厅/精选厅...</span>
            </el-upload>
            <span @click="modifyTemplate(2)" class="icondownload">
              <el-icon title="下载模板" name="download"></el-icon>
            </span>
          </div>
        </div>
      </template>
    </asp-smart-table>
    <!-- 编辑 -->
    <editreview :dialog-param="editReviewParam"></editreview>
    <!-- 省侧统一配置 -->
    <provinceconfig :dialog-param="provinceconfigParam"></provinceconfig>
    <!-- 批量添加厅店信息弹层 -->
    <pladd :dialog-param="pladdParams"></pladd>
    <!-- 设置预约取号信息弹层 -->
    <appointmentconfig :dialog-param="appointconfigParam"></appointmentconfig>
  </div>
</template>

<script>
import editreview from '../reviewandedit/editreview.vue'
import provinceconfig from './provinceconfig'
import pladd from './pladd'
import appointmentconfig from './appointmentconfig.vue'
export default {
  name: 'configmanagement',
  components: {
    editreview,
    provinceconfig,
    pladd,
    appointmentconfig
  },
  data () {
    return {
      o2o_shop_export_but: JSON.parse(sessionStorage.getItem('buttonInfo')).o2o_shop_export_but, // 导出按钮
      o2o_but_plxg: JSON.parse(sessionStorage.getItem('buttonInfo')).o2o_but_plxg, // 批量修改营业厅信息（下载模板）按钮
      o2o_but_plshop: JSON.parse(sessionStorage.getItem('buttonInfo')).o2o_but_plshop, // 批量设置默认厅、精选厅（下载模板）按钮
      o2o_but_pladd: JSON.parse(sessionStorage.getItem('buttonInfo')).o2o_but_pladd, // 导出按钮
      tempSelect: [],
      editReviewParam: {},
      pladdParams: {},
      provinceconfigParam: {},
      appointconfigParam: {},
      tableJson: null,
      model: {},
      testData: [],
      renderTable: {
        xxdy: (h, item, scope, tableData) => { // xxdy是列名columnName, 请根据实际情况修改
          return (<span style="color: red; background: #f2dede;">{scope.row[item.columnName]}</span>)
        }
      },
      rowId: '',
      dialogStatus: '',
      dialogConfig: {
        // 设置附近厅推荐等级
        levelDialog: {
          _this: this, // 获取当前弹窗所在的表格页面的this对象；使用方式this._this
          name: 'levelDialog',
          mounted: async ({ $_dialogData }) => {
            const modelHtml = JSON.parse(
              JSON.stringify(require('./levelDialog.json'))
            ) // 修改模板名称
            let modelData1 = modelHtml.model
            if ($_dialogData.PreTableRow) {
              // 如果带表单几个数据过来
              // 表单所在行所有数据 $_dialogData.PreTableRow
              modelData1 = {
                ...modelHtml.data,
                ...$_dialogData.PreTableRow
              }
            }
            modelData1.status = modelData1.status + ''
            const modelData = modelData1
            if (modelData.recommendLevel) {
              modelData.recommendLevel = modelData.recommendLevel.toString()
            }
            console.log(modelData)
            const oldModel = $_dialogData.PreTableRow || {}
            return {
              modelHtml,
              modelData,
              oldModel,
              status: ''
            }
          },
          // 数据安装后执行函数
          setup (data) {
          },
          onbind (data) {
          },
          beforeHttpPro ({ item, parent, index, model }, httpObject, callback) {
            delete httpObject.httpBody.details
            callback(httpObject)
          },
          afterHttpPro (data, responseBody, callback) {
            // 活跃度指标编辑后 刷新列表
            if (data.item.columnName === 'submit') {
              const successFlag = '' + responseBody.status === '200'
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'createTab' && '' + responseBody.status === '200') {
              data.$_this.$refs.smartForm.asp_updateModel({
                createdTable: '1',
                createdTableName: '是'
              })
              data.$_this.$refs.smartForm.asp_setHidden('buttonGroup_create', true)
            }
            callback(responseBody)
          },
          beforeLoadingHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          beforeBpmHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterBpmHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          compDataChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          compDataActiveChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            let isContinue = true
            if (data.index === 0) {
              this._this.$message.error('主键不允许删除')
              isContinue = false
            }
            callback(isContinue, data.rowData)
          },
          afterButtonPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, rowData)
          },
          beforeAuthPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, isContinue)
          },
          beforeRouterPro ({ item, row, routerObj, next }) { next(routerObj) },
          beforeColumnValidatePro ({ tableItem, model, value, type }, callback) {
            const isContinue = true
            const hasContinue = false
            callback(isContinue, hasContinue, undefined)
          },
          beforeDrawTableRowPro ({ item, model, row, rowClassName }, callback) { callback(rowClassName) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        },
        // 审核记录
        manageOperate: {
          name: 'manageOperate',
          mounted: async ({ $_dialogData }) => {
            console.log($_dialogData)
            const modelHtml = JSON.parse(
              JSON.stringify(require('./manageOperate.json'))
            ) // 修改模板名称
            let modelData1 = modelHtml.model
            if ($_dialogData.PreTableRow) {
              // 如果带表单几个数据过来
              // 表单所在行所有数据 $_dialogData.PreTableRow
              modelData1 = {
                ...modelHtml.data,
                shopId: $_dialogData.PreTableRow.shopId
              }
            }
            const modelData = modelData1
            const oldModel = $_dialogData.PreTableRow || {}
            return {
              modelHtml,
              model: modelData,
              oldModel,
              status: 'info',
              tableJson: modelHtml
            }
          },
          // 数据安装后执行函数
          setup (data) {
          },
          onbind (data) {
          },
          beforeHttpPro ({ item, parent, index, model }, httpObject, callback) {
            /* if (item.columnName === 'submit') {
              httpObject.httpBody.businessColumn[0].primary = '1'
            }
            callback(httpObject) */
          },
          afterHttpPro (data, responseBody, callback) {
            /* if (data.item.columnName === 'submit') {
            const successFlag = '' + responseBody.status === this.successCode
              successFlag
                ? this._this.$message.success('保存成功')
                : this._this.$message.error(responseBody.message || '保存失败')
              successFlag && data.$_close()
              successFlag && this._this.$refs.aspSmartTable.asp_refreshTableList()
            } else if (data.item.columnName === 'createTab' && '' + responseBody.status === this.successCode) {
              data.$_this.$refs.smartForm.asp_updateModel({
                createdTable: '1',
                createdTableName: '是'
              })
              data.$_this.$refs.smartForm.asp_setHidden('buttonGroup_create', true)
            }
            callback(responseBody) */
          },
          beforeLoadingHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterLoadingHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          beforeBpmHttpPro ({ item, parent, index, model }, httpObject, callback) { callback(httpObject) },
          afterBpmHttpPro ({ item, parent, index, model }, responseBody, callback) { callback(responseBody) },
          compDataChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          compDataActiveChangePro ({ item, parent, index, isTable, model, oldValue, newValue, changeDirtect, changeValue }, callback) {
            const isContinue = true
            callback(isContinue)
          },
          beforeButtonPro (data, callback) {
            let isContinue = true
            if (data.index === 0) {
              this._this.$message.error('主键不允许删除')
              isContinue = false
            }
            callback(isContinue, data.rowData)
          },
          afterButtonPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, rowData)
          },
          beforeAuthPro ({ item, parent, index, isTable, model, rowData }, callback) {
            const isContinue = true
            callback(isContinue, isContinue)
          },
          beforeRouterPro ({ item, row, routerObj, next }) { next(routerObj) },
          beforeColumnValidatePro ({ tableItem, model, value, type }, callback) {
            const isContinue = true
            const hasContinue = false
            callback(isContinue, hasContinue, undefined)
          },
          beforeDrawTableRowPro ({ item, model, row, rowClassName }, callback) { callback(rowClassName) },
          beforeDialogClose (data) { data.next() },
          afterDialogClose (data) { }
        }
      },
      successCode: '200'
    }
  },
  async mounted () {
    this.tableJson = JSON.parse(JSON.stringify(require('./configmanagement.json')))
    this.model = this.tableJson.model
    // 省市下拉框默认展示
    this.$aspHttps
      .asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/getCurrent')
      .then((response) => {
        if (this.$reponseStatus(response)) {
          this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/shop/common/regionInfo', {}).then(res => {
            if (res.status === this.successCode) {
              if (res.data[0].cities.length === 1) {
                this.model.cityCode = res.data[0].cities[0].cityCode
              } else {
                this.model.cityCode = '000'
              }
            }
            console.log(res, 'resssss')
          })
          this.model.provinceCode = response.data.divisions[0].substring(0, 3)
        }
      })
  },
  methods: {
    // 批量导出营业厅信息
    batchexport() {
      const ids = []
      this.tempSelect.forEach(item => {
        ids.push(item.shopId)
      })
      const params = {
        applyStatus: this.model.applyStatus,
        businessHallStatus: this.model.businessHallStatus,
        channelCategorys: this.model.channelCategorys,
        cityCode: this.model.cityCode,
        defaultStatus: this.model.defaultStatus,
        provinceCode: this.model.provinceCode,
        queueStatus: this.model.queueStatus,
        shopAdminNameOrPhone: this.model.shopAdminNameOrPhone,
        shopNameOrShortName: this.model.shopNameOrShortName,
        unifiedChannelId: this.model.unifiedChannelId,
        vipStatus: this.model.vipStatus,
        shopIds: ids,
        downLoadName: '营业厅信息'
      }
      this.$aspHttps.asp_Post_file(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/exportShopInfo', params).then(res => {
      })
    },
    // 批量导入模板下载 1、批量修改营业厅信息模板 2.设置默认厅、精选厅模板
    modifyTemplate(value) {
      switch (value) {
        case 1:
          this.$aspHttps.asp_Post_file(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/exportShopInfoTemplate', { downLoadName: '营业厅信息模板' }).then(res => {
          })
          break
        case 2:
          this.$aspHttps.asp_Post_file(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/exportTemplate', { downLoadName: '默认厅、精选厅、推荐等级模板' }).then(res => {
          })
          break
      }
    },
    // 批量添加厅店信息
    handleAddMessage(fileObject) {
      // 校验附件
      if (!fileObject.file) {
        this.$message.error('不能上传空文件！')
        return false
      }
      const fd = new FormData()
      fd.append('file', fileObject.file)
      this.$aspHttps.asp_FileUpload(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/upload/api/apply/import/4',
        fd,
        4
      ).then((response) => {
        if (response.status !== this.successCode) {
          this.$message.error(response.message)
        } else {
          this.refreshListTwo()
          // 弹层
          this.pladdParams = {
            title: '批量添加提示',
            modelVisible: true
          }
        }
      })
    },
    // 批量添加厅店信息 --- 下载模板
    loadAddTemplate() {
      this.$aspHttps.asp_Post_file(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/export/api/apply/exportShopInfoAddTemplate', { downLoadName: '批量添加厅店信息模板' }).then(res => {
      })
    },
    // 批量修改营业厅信息
    handleChangeShopInfos(fileObject) {
      // 校验附件
      if (!fileObject.file) {
        this.$message.error('不能上传空文件！')
        return false
      }
      // const fileSuffix = fileObject.file.name.split('.')[1]
      // if (fileSuffix !== 'xls' && fileSuffix !== 'xlsx') {
      //   this.$message.error('文件后缀只能是xls或者xlsx')
      //   return false
      // }
      const fd = new FormData()
      fd.append('file', fileObject.file)
      this.$aspHttps.asp_FileUpload(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/upload/api/apply/import/3',
        fd,
        3
      ).then((response) => {
        if (response.status !== this.successCode) {
          this.$message.error(response.message)
        } else {
          this.refreshListTwo()
          this.$message.success('文件已导入，可在文件导入结果查看')
        }
      })
    },
    // 批量设置默认厅、精选厅
    handleModifyFileUpload(fileObject) {
      // 校验附件
      if (!fileObject.file) {
        this.$message.error('不能上传空文件！')
        return false
      }
      // const fileSuffix = fileObject.file.name.split('.')[1]
      // if (fileSuffix !== 'xls' && fileSuffix !== 'xlsx') {
      //   this.$message.error('文件后缀只能是xls或者xlsx')
      //   return false
      // }
      const fd = new FormData()
      fd.append('file', fileObject.file)
      this.$aspHttps.asp_FileUpload(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/upload/api/apply/import/2',
        fd,
        2
      ).then((response) => {
        if (response.status !== this.successCode) {
          this.$message.error(response.message)
        } else {
          this.refreshListTwo()
          this.$message.success('导入完成')
        }
      })
    },
    // 刷新列表
    refreshListTwo () {
      this.$refs.aspSmartTable.asp_sendTableQuery('table_1723445846685')
    },
    setDefaultShop (params) {
      // 设置默认厅
      this.$aspHttps.asp_Post(
        this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/setDefaultStatus', params).then(res => {
        if (res.status === this.successCode) {
          this.$message.success('成功')
          this.refreshListTwo()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /**
     * 智能表格监听所有组件的交互事件操作：监听、捕捉事件响应
     * @param item 响应组件对象属性集（类型、组件Id，控件内元数属性），columnName每个组件单元的唯一码（组件单元Id）
     * @param type 事件类型（click/blur/onblur等）
     * @param index 当是表格组件时，返回组件的行号
     * @param model 查询区域表单数据模型
     * @param tableModel 表格组件数据模型
     * @param row 表格行数据
     * @param multipleSelection 表格多选数据（当出现列表复选时才有，包括跨页数据，整行数据）
     * @param sortProps 排序属性
     * @returns {Promise<void>}
     */
    async onbind ({ item, type, index, model, tableModel, row, subFormSelectData, sortProps }) {
      if (subFormSelectData) {
        this.tempSelect = subFormSelectData
      }
      // 点击查询 清空导出数据
      if (item.columnName === 'button_1723445513963') {
        this.tempSelect = []
      }
      switch (item.columnName) {
        case 'button_1723446174804':
          this.$confirm('下线整改的厅店将不再展示在一级APP中', {
            distinguishCancelAndClose: true,
            confirmButtonText: '仍要下线',
            cancelButtonText: '取消'
          }).then(() => {
            const params = {
              shopId: row.shopId,
              status: 1
            }
            this.$aspHttps.asp_Post(
              this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/offlineEdit', params).then(res => {
              if (res.status === this.successCode) {
                this.refreshListTwo()
                this.$message.success('成功')
              } else {
                this.$message.error(res.message)
              }
              console.log(res)
            })
          }).catch((e) => {
            if (e === 'cancel') {
              this.$message({
                type: 'info',
                message: '已取消'
              })
            }
          })
          break
        // 批量审核
        case 'button_batchOffline':
          if (this.tempSelect.length === 0) {
            this.$message.error('请选择要下线整改的数据')
          } else {
            this.$confirm('下线整改的厅店将不再展示在一级APP中', {
              distinguishCancelAndClose: true,
              confirmButtonText: '仍要下线',
              cancelButtonText: '取消'
            }).then(() => {
              const ids = []
              this.tempSelect.forEach(item => {
                ids.push(item.shopId)
              })
              const params = {
                shopIds: ids,
                status: 1
              }
              this.$aspHttps.asp_Post(
                this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/offlineEdit', params).then(res => {
                if (res.status === this.successCode) {
                  this.refreshListTwo()
                  this.$message.success('成功')
                } else {
                  this.$message.error(res.message)
                }
                console.log(res)
              })
            }).catch((e) => {
              if (e === 'cancel') {
                this.$message({
                  type: 'info',
                  message: '已取消'
                })
              }
            })
          }
      }
      if (item.columnName === 'setDefault') {
        const params = {
          provinceCode: row.provinceCode,
          cityCode: row.cityCode
        }
        // 查询当前地市是否有默认厅
        this.$aspHttps.asp_Post(
          this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/queryDefaultShop', params).then(res => {
          if (res.status === this.successCode) {
            if (Object.keys(res.data).length === 0) {
              this.$confirm('是否设置为地市默认精选厅', {
                distinguishCancelAndClose: true,
                confirmButtonText: '是',
                cancelButtonText: '否'
              }).then(() => {
                const params = {
                  shopId: row.shopId,
                  status: 1
                }
                // 设置默认厅
                this.setDefaultShop(params)
              }).catch((e) => {
                if (e === 'cancel') {
                  this.$message({
                    type: 'info',
                    message: '已取消'
                  })
                }
              })
            } else {
              this.$confirm(`当前地市已有默认厅“${res.data.shopName}”是否替换该店铺`, {
                distinguishCancelAndClose: true,
                confirmButtonText: '是',
                cancelButtonText: '否'
              }).then(() => {
                console.log(tableModel)
                console.log(row.shopId)
                const params = {
                  shopId: row.shopId,
                  status: 1
                }
                // 设置默认厅
                this.setDefaultShop(params)
              }).catch((e) => {
                if (e === 'cancel') {
                  this.$message({
                    type: 'info',
                    message: '已取消'
                  })
                }
              })
            }
          }
        })
      }
      // 取消默认厅
      if (item.columnName === 'cancelDefault') {
        this.$confirm('是否取消地市默认精选厅', {
          distinguishCancelAndClose: true,
          confirmButtonText: '是',
          cancelButtonText: '否'
        }).then(() => {
          const params = {
            shopId: row.shopId,
            status: 0
          }
          this.setDefaultShop(params)
        }).catch((e) => {
          if (e === 'cancel') {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          }
        })
      }
    },
    /**
     * 智能表格页面所有请求前的前置操作
     * 例如：修改请求参数、修改请求方式、修改请求URL、或者请求条件不满足不给发送请求
     * @param tableItem 组件对象属性集
     * @param params 请求参数body，数据格式如下(字段格式不一致的需要自行转换)如下:
     *                                         {
     *                                             page：1， // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                             rows: 10，// 分页属性(页大小)，数字类型 （不是分页接口，没有这个字段）
     *                                             .......   // 业务属性字段
     *                                          }
     * @param httpMethod.url 请求地址URL
     * @param httpMethod.type 请求方式，目前主要六种：'post+json', 'post+form', 'get'，'put+json'，'delete+json'，'patch+json'
     * @param row 当组件为表格并且是表格操作列触发的请求，此参数返回表格行数据，其它返回undefined
     */
    beforeHttp ({ tableItem, params, httpMethod, row }) {
      if (tableItem.columnName === 'table_1723445846685') {
        if (params.unifiedChannelId) {
          params.unifiedChannelIdList = params.unifiedChannelId.split(',')
        } else {
          params.unifiedChannelIdList = []
        }
      }
    },
    /**
     * 智能表格页面所有请求后置操作
     * 例如：请求后的数据包体需要做二次处理
     * @param tableItem 组件对象属性集
     * @param responseBody 响应数据body, 数据包格式(字段格式不一致的需要自行转换)如下：
     *                                              {
     *                                                status: "200", // 业务状态码，字符串类型，成功返回"200"，失败返回其它数据
     *                                                message: "",   // 业务提示语，字符串类型，给业务的提示语属性
     *                                                page：1，      // 分页属性(当前页号)，数字类型 （不是分页接口，没有这个字段）
     *                                                total: 53，    // 分页属性(总记录大小)，数字类型 （不是分页接口，没有这个字段）
     *                                                data: {}或者[] // 业务数据区，对象或数组类型，用于各业务逻辑处理
     *                                               }
     */
    afterHttp ({ tableItem, responseBody }) {
    },
    /**
     * 智能表格页面上的按钮的前置操作：包括不限于查询区域，表格顶部、表格操作列
     * 例如：对操作按钮进行处理的数据进行预处理，或者对按钮请求进行个性胡逻辑判断等
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param item 组件对象属性集
     * @param rowObj 当组件为表格操作列中的按钮，此参数返回表格行数据，其它返回undefined
     * @param next 回调函数
     */
    beforeButton ({ item, rowObj, next }) {
      // 省侧统一配置
      switch (item.columnName) {
        case 'provinceConfig':
          this.$aspHttps
            .asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/shop/common/regionInfo', {}).then((ress) => {
              if (ress.status === this.successCode) {
                this.$aspHttps.asp_Post(
                  this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/merchant/getOsmConfig', {}).then(res => {
                  if (res.status === this.successCode) {
                    this.provinceconfigParam = {
                      title: '省侧统一配置',
                      modelVisible: true,
                      data: res.data
                    }
                  }
                  console.log(res, 'resssss')
                })
              }
            })
          break
        case 'button_details':
          // 详情接口
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/detail', {
              shopId: rowObj.shopId
            }).then(res => {
            if (res.status === this.successCode) {
              const detailData = res.data
              // 弹层
              this.editReviewParam = {
                title: '详情',
                modelVisible: true,
                data: detailData,
                type: item.columnName
              }
            }
          })
          break
        case 'manageEdit':
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/osm/detail', {
              shopId: rowObj.shopId
            }).then(res => {
            if (res.status === this.successCode) {
              const detailData = res.data
              // 营业范围列表接口
              this.$aspHttps.asp_Post(
                this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/apply/getBusinessScopeList', {}).then((res) => {
                if (res.status === this.successCode) {
                  // 成功后赋值
                  detailData.businessScopeAll = res.data
                  // AI稽核传参
                  detailData.aiCheck = true
                  // 弹层
                  this.editReviewParam = {
                    title: '编辑',
                    modelVisible: true,
                    data: detailData,
                    type: rowObj.applyStatus === '下线整改' ? 'iszg' : item.columnName
                  }
                } else {
                  this.$message.error(res.message)
                }
              })
            } else {
              this.$message.error(res.message)
            }
          })
          break
        case 'appointment':
          // 预约取号配置
          this.$aspHttps.asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/proxy/o2o/shoponline/hallticketconfig/get', {
              shopId: rowObj.shopId
            }).then(res => {
            if (res && !res.code) {
              const detailData = res.data
              this.appointconfigParam = {
                title: '省侧统一配置',
                modelVisible: true,
                data: detailData
              }
            } else {
              this.$message.error(res.message)
            }
          })
          break
      }
      next(item, rowObj) // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 智能表格页面路由跳转的前置操作
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param item 响应组件对象属性集
     * @param row 当响应组件为表格操作列中的按钮时，此参数返回表格行数据，其它返回undefined
     * @param routerObj.routerType: 路由类型
     * @param routerObj.routerParamType 路由参数类型
     * @param routerObj.routerUrl 路由地址或名称
     * @param routerObj.routerParamValue 路由参数
     * @param next 回调函数
     */
    beforeRouter ({ item, row, routerObj, next }) {
      next(routerObj) // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 表格内容渲染之前的前置动作，
     * @param tableName 当前表格名称
     * @param tableData 表格当页的数据
     * @param columnItem 表格当前列的信息
     * @param scope 表格行信息包含属性 $index row等
     * @param callback 回调事件，用于改变指定列的显示内容
     * @param callback 参数说明如下
     * 参数一：指定修改的表格名称 tableName
     * 参数二：指定修改的列名 columnName
     * 参数三：指定索引集合，整列生效则传空数组[],指定某几行生效则传索引集合[1,3] indexList
     * 参数四：显示内容{ content: 可以是文本也可以是html代码片段}
     * 示例：callBack('aspSmartTable', 'name', [], { content: `【附加标题】<a @click="detial(${scope.row})">${scope.row.name}</a>` })
     */
    beforeTableRender ({ tableName, tableData, columnItem, scope }, callBack) { },
    /**
     * 智能表格监听所有行绘制的前置回调响应
     * @param item 组件对象属性集(类型、组件columnName，组件内元数属性)，columnName是每个组件的唯一标识码
     * @param tableData 表格数据模型
     * @param row:  表格组件当前绘制的行数据
     * @param rowClassName: 子表单组件当前行绘制class name
     * @param callback: 回调api
     * @param           callback回调api参数: rowClassName: 子表单组件当前行绘制class name
     */
    beforeTableRowRender ({ item, tableData, row, rowClassName }) {
      return rowClassName // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 智能表格单元格点击的前置操作
     * @param item 响应组件对象属性集
     * @param row 此参数返回表格行数据
     * @param tableData: 表格数据模型
     */
    beforeClickTableCell ({ item, row, tableData }) { },
    /**
     * 表格页码大小发生变化时触发的前置事件
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param pageSize 表格页码大小
     * @param next 回调函数
     */
    sizeChange ({ tableItem, pageSize, next }) {
      next(true) // 允许继续运行传true, 否则传false  // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    },
    /**
     * 表格当前页发生变化时触发的前置事件，包括点翻页、上一页、下一页、刷新页、重置页
     * 注意：本函数有next()回调函数需要执行，调取next()函数才能继续后面的业务逻辑，否则操作将中止
     * @param tableItem 表格对象属性集
     * @param currentPage 当前页码号
     * @param next 回调函数
     */
    currentChange ({ tableItem, currentPage, next }) {
      next(true) // 允许继续运行传true, 否则传false // !!!注意!!!此行不许删除，否则无法继续！！！(该注释也不能删除)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-table th.el-table__cell>.cell {
  text-align: center;
}
::v-deep .el-table .cell {
  text-align: center;
}
::v-deep .asp-smart-table {
  height: 130vh;
}
::v-deep .create-tab-css {
  margin: 4px 4px;
}
.asp-pagination {
  background-color: #fff;
}
::v-deep .asp-smart-table .widget-box-button{
  padding-top: 0 !important;
}
.fontTip {
  font-size: 13px;
}
.btnStyle {
  display: flex;
  justify-content: flex-end;
  .btnitem {
    margin-right: 10px;
    display:flex;
    flex-shrink: 0;
    border:1px solid #DCDFE6;
    border-radius:5px;
    padding:1px 5px;
    font-size: 14px;
    cursor: pointer;
    user-select: none;
    background: linear-gradient(360deg, #4576e4 0%, #6f9ef0 100%);
    color: #ffffff;
    .icondownload{
      margin: 0 7px;
    }
    span{
      color: #fff;
    }
  }
}
</style>
