{"list": [{"type": "form", "label": "表单容器", "columnName": "form_1728798701986", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1728799488619", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 10, "align": "left", "list": [{"label": "", "type": "AspDateRange", "required": false, "rules": [], "classify": "form", "class": "", "columnName": "startTime", "targetName": "endTime", "asideIcon": "iconfont iconriqi", "customLabelWidth": true, "icon": "", "operation": [], "hidden": false, "dateRangeType": 0, "props": {"type": "datetime", "format": "yyyy-MM-dd HH:mm:ss", "value-format": "yyyy-MM-dd HH:mm:ss", "range-separator": "至", "start-placeholder": "开始时间", "end-placeholder": "结束时间", "clearable": false, "disabled": false, "readonly": false, "picker-options": {}}, "width": "100%", "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": "", "oldColumnName": "AspDateRange_1728798714185", "oldTargetName": "label_AspDateRange_1728798714185", "isChangeDefault1": false, "startDateRangeDefType": 1, "isChangeDefault2": false, "endDateRangeDefType": 1}]}, {"span": 5, "align": "left", "list": [{"type": "select", "label": "导入/导出结果：", "columnName": "status", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "导入中", "value": "1"}, {"label": "导入完成", "vlaue": "new_option1728800731108", "value": "2"}, {"label": "导入失败", "vlaue": "new_option1728800735216", "value": "3"}, {"label": "导出中", "vlaue": "new_option1740041994700", "value": "4"}, {"label": "导出成功", "vlaue": "new_option1740041995276", "value": "5"}, {"label": "导出失败", "vlaue": "new_option1740041995876", "value": "6"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "导入中", "value": "1", "disabled": false}, {"label": "导入完成", "vlaue": "new_option1728800731108", "value": "2", "disabled": false}, {"label": "导入失败", "vlaue": "new_option1728800735216", "value": "3", "disabled": false}, {"label": "导出中", "vlaue": "new_option1740041994700", "value": "4", "disabled": false}, {"label": "导出成功", "vlaue": "new_option1740041995276", "value": "5", "disabled": false}, {"label": "导出失败", "vlaue": "new_option1740041995876", "value": "6", "disabled": false}], "hasParentForm": true, "labelWidth": 120, "targetName": "label_select_1728799558312", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1728799558312"}]}, {"span": 5, "align": "left", "list": [{"type": "select", "label": "导入/导出类型：", "columnName": "importType", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "不限", "value": ""}, {"label": "批量导入审核单", "value": "1"}, {"label": "精选厅默认厅导入", "vlaue": "new_option1731567824552", "value": "2"}, {"label": "批量编辑店铺信息", "vlaue": "new_option1731567955436", "value": "3"}, {"label": "门店图片导入", "vlaue": "new_option1739351210475", "value": "8"}, {"label": "营业厅信息导出", "vlaue": "new_option1740041818396", "value": "5"}, {"label": "数据同步信息导出", "vlaue": "new_option1740041819741", "value": "6"}, {"label": "待审核工单导出", "vlaue": "new_option1740041820257", "value": "7"}, {"label": "厅店信息批量添加", "vlaue": "new_option1740042046613", "value": "4"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "不限", "value": "", "disabled": false}, {"label": "批量导入审核单", "value": "1", "disabled": false}, {"label": "精选厅默认厅导入", "vlaue": "new_option1731567824552", "value": "2", "disabled": false}, {"label": "批量编辑店铺信息", "vlaue": "new_option1731567955436", "value": "3", "disabled": false}, {"label": "门店图片导入", "vlaue": "new_option1739351210475", "value": "8", "disabled": false}, {"label": "营业厅信息导出", "vlaue": "new_option1740041818396", "value": "5", "disabled": false}, {"label": "数据同步信息导出", "vlaue": "new_option1740041819741", "value": "6", "disabled": false}, {"label": "待审核工单导出", "vlaue": "new_option1740041820257", "value": "7", "disabled": false}, {"label": "厅店信息批量添加", "vlaue": "new_option1740042046613", "value": "4", "disabled": false}], "hasParentForm": true, "labelWidth": 120, "targetName": "label_select_1731567785711", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "oldColumnName": "select_1731567785711"}]}, {"span": 4, "align": "left", "list": [{"type": "button", "label": "查询", "columnName": "button_1731567772048", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "tableId": "table_1728799657023", "formId": "form_1728798701986", "event": "submit", "bpmFlowImageCloumnName": "", "authSwitch": true, "authId": "import_but_list"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false, "hasParentForm": false}, {"type": "empty", "label": "空容器", "columnName": "empty_1728799646551", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "table", "label": "表格", "columnName": "table_1728799657023", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": true, "operationFixed": true, "show-pagination": true, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [{"label": "button_faildata", "columnName": "button_faildata", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "status", "compare": "=", "compareType": "staticData", "targetValue": "3", "authSwitch": false, "authId": "", "if": "||"}]}, {"label": "button_1728807271811", "columnName": "button_1728807271811", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "status", "compare": "=", "compareType": "staticData", "targetValue": "1||2", "authSwitch": false, "authId": "", "if": "||"}]}, {"label": "button_successdata", "columnName": "button_successdata", "authId": "", "statusList": [], "state": "show", "dataLinkage": [{"columnName": "status", "compare": "=", "compareType": "staticData", "targetValue": "5", "authSwitch": false, "authId": "", "if": "||"}]}], "list": [{"timeStamp": 1728799664321, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "导入/导出流水", "columnName": "recordId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "文件名", "columnName": "importFile", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "导入/导出类型", "columnName": "importType", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "批量导入审核单", "value": "1"}, {"label": "默认厅精选厅或推荐等级导入", "value": "2"}, {"label": "批量编辑店铺信息", "value": "3"}, {"label": "厅店信息批量添加", "value": "4"}, {"label": "门店图片导入", "value": "8"}, {"label": "厅店信息导出", "value": "5"}, {"label": "数据同步信息导出", "value": "6"}, {"label": "待审核工单导出", "value": "7"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "成功数据量", "columnName": "successNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "失败数据量", "columnName": "failNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "导入/导出时间", "columnName": "createTime", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "导入/导出页面", "columnName": "importPage", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "执行人", "columnName": "importBy", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "导入/导出结果", "columnName": "status", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "导入中", "value": "1"}, {"label": "导入完成", "value": "2"}, {"label": "导入失败", "value": "3"}, {"label": "导出成功", "value": "5"}, {"label": "导出中", "value": "4"}, {"label": "导出失败", "value": "6"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "备注", "columnName": "importRemark", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [{"type": "text", "timestamp": 1728799794156, "columnName": "button_faildata", "label": "导出失败数据", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "activeType": "", "exportAsyncConfig": {"syncApiUrl": "", "syncApiMethod": "post+blob", "syncApiParams": {}, "exportFileName": "", "exportFileNamePrefix": "", "exportFileNameSuffix": "", "exportFileMineType": "", "exportAsyncApiUrl": "", "exportAsyncApiMethod": "post+json", "exportAsyncApiParams": {}, "exportAsyncHints": "", "exportAsyncResType": "session", "exportAsyncResKey": "", "exportAsyncResDict": "", "exportAsyncDefaultTimout": 50}, "exportType": "request", "apiMethod": "post+arraybuffer", "exportKey": "", "apiName": "/yundian/osm/api/proxyData/api/apply/export", "apiParam": "{\"recordId\":\"$recordId$\",\"downLoadName\":\"$importFile$\"}", "authSwitch": true, "authId": "import_but_file", "_hidden_state": true}, {"type": "text", "timestamp": 1728807271811, "columnName": "button_1728807271811", "label": "无", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "_hidden_state": true}, {"type": "text", "timestamp": 1740038525432, "columnName": "button_successdata", "label": "下载", "align": "left", "class": "", "icon": "", "justShowIcon": false, "defaultState": "show", "state": "show", "dataLinkage": [], "confirmationStatus": [], "_hidden_state": true}], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/proxyData/api/import/list", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1728799664321, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "导入/导出流水", "columnName": "recordId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "文件名", "columnName": "importFile", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "导入/导出类型", "columnName": "importType", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "批量导入审核单", "value": "1"}, {"label": "精选厅默认厅导入", "value": "2"}, {"label": "批量编辑店铺信息", "value": "3"}, {"label": "厅店信息批量添加", "value": "4"}, {"label": "门店图片导入", "value": "8"}, {"label": "厅店信息导出", "value": "5"}, {"label": "数据同步信息导出", "value": "6"}, {"label": "待审核工单导出", "value": "7"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "成功数据量", "columnName": "successNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "失败数据量", "columnName": "failNum", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "导入/导出时间", "columnName": "createTime", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "导入/导出页面", "columnName": "importPage", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "执行人", "columnName": "importBy", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "导入/导出结果", "columnName": "status", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": true, "convertProps": {"type": "select", "optionType": "0", "options": [{"label": "导入中", "value": "1"}, {"label": "导入完成", "value": "2"}, {"label": "导入失败", "value": "3"}, {"label": "导出成功", "value": "5"}, {"label": "导出中", "value": "4"}, {"label": "导出失败", "value": "6"}], "apiType": "post+json", "localProxy": "", "statusKey": "status", "statusValue": "200", "responseDataKey": "data", "option-value": "value", "option-label": "label", "separator": ","}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "备注", "columnName": "importRemark", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "hasParentForm": false, "labelWidth": 100, "paginationStatus": [], "router": {}, "show-operation-status": [], "formId": "form_1728798701986"}], "classify": "empty", "hidden": false}], "model": {"startTime": "", "status": "", "importType": ""}, "tableModel": {}, "pagination": {}, "publicConfig": {"exportName": "import_reult", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "mini", "class": "", "layoutType": "flex", "statusList": [], "localProxy": "", "nigxProxy": "", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": "", "pageNumKey": "page", "pageSizeKey": "rows", "totalKey": "total"}, "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "文件导入结果", "routerInfo": {}}}