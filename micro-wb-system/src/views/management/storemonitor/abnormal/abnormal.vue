<template>
  <div style="height: 100%; ">
    <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="异常店铺管理" name="first">
        <abnormalitem></abnormalitem>
      </el-tab-pane>
      <el-tab-pane label="通知记录" name="second">
        <abnormalnotice></abnormalnotice>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import abnormalitem from '../abnormalitem/abnormalitem.vue'
import abnormalnotice from '../abnormalnotice/abnormalnotice.vue'
export default {
  components: {
    abnormalitem,
    abnormalnotice
  },
  data () {
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
