{"formList": [{"label": "清退原因", "type": "textarea", "columnName": "reason", "classify": "basic", "defaultValue": "", "isModel": true, "required": true, "requiredCutomizeTips": "请填写清退原因", "rules": [{"required": true, "message": "请填写清退原因", "trigger": "blur"}], "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": "", "shortcut": false, "shortcutOptions": [{"label": "option1", "value": "0"}], "class": "", "operation": [], "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "wordLimitStyle": "transparent", "clearable": false, "disabled": false, "readonly": false, "rows": 2, "text": "", "controls": true}, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "ruleType": ""}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1711012636713", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "", "label": "确定", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/yundian/osm/api/shop/monitor/clearShop", "class": "solid-with-icon-btn", "default": "hidden", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": true, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": "", "handelclear": "show", "handelclearp": "hidden"}, {"is-table-column": false, "columnName": "psubmit", "label": "确定", "type": "primary", "class": "solid-with-icon-btn", "icon": "", "bpmFlowImageCloumnName": "", "default": "hidden", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiMethod": "post+json", "apiName": "/yundian/osm/api/shop/monitor/clearShopBatch", "apiCloseDialogWithResposne": true, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "handelclear": "hidden", "handelclearp": "show", "activeType": "button_group_form_interface", "apiParam": {"shops": "$clearList$", "reason": "$reason$"}, "apiIsRefresh": "", "apiIsReturn": ""}], "name": "layout", "labelWidth": 160}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "handelclear", "defaultClass": "webbas", "size": "small", "statusList": ["handelclear", "hand<PERSON><PERSON><PERSON><PERSON>"], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}}, "dataConfig": {}, "virtual_model": {}, "model": {"reason": ""}}