<template>
  <div style="height: 100%; ">
    <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="低活跃店铺管理" name="first">
        <lowactitem></lowactitem>
      </el-tab-pane>
      <el-tab-pane label="通知记录" name="second">
        <notice></notice>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import lowactitem from '../lowactitem/lowactitem.vue'
import notice from '../notice/notice.vue'
export default {
  components: {
    lowactitem,
    notice
  },
  data () {
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
