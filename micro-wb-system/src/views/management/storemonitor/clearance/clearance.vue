<template>
  <div style="height: 100%; ">
    <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="待清退店铺管理" name="first">
        <clearancemanage></clearancemanage>
      </el-tab-pane>
      <el-tab-pane label="清退记录" name="second">
        <clearoperate ref="clearoperate"></clearoperate>
      </el-tab-pane>
      <el-tab-pane label="通知记录" name="thrid">
        <cleardetail></cleardetail>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import clearancemanage from '../clearancemanage/clearancemanage.vue'
import cleardetail from '../cleardetail/cleardetail.vue'
import clearoperate from '../clearoperate/clearoperate.vue'
export default {
  components: {
    clearancemanage,
    clearoperate,
    cleardetail
  },
  data () {
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      if (this.activeName === 'second') {
        this.$refs.clearoperate.refreshList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
