<template>
  <div class="webbas">
    <asp-dialog
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    @beforeCloseDialog="handleClose"
    width="50%"
  >
    <el-form :model="lookappeals" ref="lookappeals">
      <el-form-item label-width="100px" label="申诉时间：">
        {{ dialogParam.data ? dialogParam.data.modifyTime : '' }}
      </el-form-item>
      <el-form-item label-width="100px" label="诉求：">
        {{ dialogParam.data ? dialogParam.data.content : '' }}
      </el-form-item>
      <el-form-item label-width="100px" label="申诉材料：">
        {{ dialogParam.data ? dialogParam.data.fileName : '' }}
        <el-button style="margin-left: 10px;" class=" hollow-with-icon-btn" v-if="dialogParam.data && dialogParam.data.fileName" @click="downloadFile">下载附件</el-button>
      </el-form-item>
      <div class="brs">
        <el-form-item label-width="100px" label="操作：">
          {{ dialogParam.data ? dialogParam.data.operateTypeShow : '' }}
        </el-form-item>
        <el-form-item label-width="100px" label="操作时间：">
          {{ dialogParam.data ? dialogParam.data.modifyTime : '' }}
        </el-form-item>
        <el-form-item label-width="100px" label="操作用户：">
          {{ dialogParam.data ? dialogParam.data.createUser : '' }}
        </el-form-item>
      </div>
      <div class="brs" v-if="role === '000' && clearStatus !== 3 && clearStatus !== 4">
        <el-form-item label-width="100px" label="操作：">
          <el-radio v-model="operateType" label="1" class="checkitem">申诉通过。变更店铺状态为“开通”</el-radio>
          <el-radio v-model="operateType" label="2" class="checkitem">申诉通过。变更店铺状态为“冻结”</el-radio>
          <el-radio v-model="operateType" label="3" class="checkitem">申诉通过。变更店铺状态为“终止”</el-radio>
          <el-radio v-model="operateType" label="4" class="checkitem">驳回</el-radio>
        </el-form-item>
      </div>
    </el-form>
      <template slot="footer-center" v-if="role === '000' && clearStatus !== 3 && clearStatus !== 4">
        <asp-btn-solid
          name="提交"
          @click="submit()"
        ></asp-btn-solid>
     </template>
    </asp-dialog>
  </div>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  computed: {
  },
  watch: {
    'dialogParam.data': {
      handler(val) {
        this.id = val.id
        this.fileName = val.fileName
      }
    },
    'dialogParam.row': {
      handler(row) {
        this.clearStatus = row.clearStatus
      }
    }
  },
  data() {
    return {
      role: sessionStorage.getItem('currentCode'),
      clearStatus: null, // 清退状态
      fileName: '', // 文件名称
      id: '',
      operateType: '',
      lookappeals: {}
    }
  },
  methods: {
    // 获取详情
    // getDetails() {
    //   this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/proxyData/api/shop/select', {
    //     reqNo: this.reqNo
    //   }).then((res) => {
    //     if (this.$reponseStatus(res)) {
    //       this.detailsList = res.data
    //       // 冻结/终止原因数据处理
    //       if (res.data.exitReason && res.data.exitReason === '1') {
    //         this.reason = '授权到期'
    //       } else if (res.data.exitReason && res.data.exitReason === '2') {
    //         this.reason = '因渠道违规等问题企业与渠道解除合作'
    //       } else if (res.data.exitReason && res.data.exitReason === '3') {
    //         this.reason = res.data.exitReasonDesc
    //       } else {
    //         this.reason = ''
    //       }
    //     }
    //   })
    // },
    // 下载附件
    downloadFile() {
      const params = {
        id: this.id,
        downLoadName: this.fileName,
        fileName: this.fileName
      }
      this.$aspHttps.asp_Post_file(this.$apiConfig.level1cloudstorePathPreFix + '/shop/monitor/clearAppealFile', params).then(res => {
        if (res.status === '200') {
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 提交
    submit() {
      this.$aspHttps.asp_Post(this.$apiConfig.level1cloudstorePathPreFix + '/shop/monitor/clearAppealApproval', {
        id: this.id,
        operateType: this.operateType
      }).then((res) => {
        if (res.status === '200') {
          // 提示语
          this.$message.success(res.message)
          // 关闭弹窗
          this.dialogParam.modelVisible = false
          // 刷新列表
          this.$emit('refreshList')
          // 置空数据
          this.operateType = ''
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleClose() {
      this.operateType = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.brs {
  margin-top: 15px;
}
.checkitem {
  display: block;
  margin-top: 1.5%;
}
::v-deep .el-form-item__content {
  width: calc(100% - 200px);
  margin-left: 10px !important;
}
::v-deep .el-form-item__label {
  width: 200px;
}
</style>
