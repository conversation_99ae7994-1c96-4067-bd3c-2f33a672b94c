<template>
  <div class="webbas">
    <asp-dialog
    v-model="dialogParam.modelVisible"
    :visible.sync="dialogParam.modelVisible"
    :title="dialogParam.title"
    @beforeCloseDialog="handleClose"
    width="50%"
  >
    <el-form :rules="rules" :model="applesFrom" ref="applesFrom">
      <el-form-item prop="content" label-width="100px" label="诉求：">
        <el-input
          type="textarea"
          :rows="2"
          maxlength="100"
          show-word-limit
          v-model="applesFrom.content">
        </el-input>
      </el-form-item>
      <el-form-item label-width="100px" label="申诉材料：" style="margin-top: 15px;">
        <el-upload
          ref="upload"
          class="upload-demo"
          action="#"
          :limit="1"
          accept=".docx,.xlsx"
          :file-list="fileList"
          :on-change="handleChange"
          :before-remove="beforeRemove"
          :on-exceed="handleExceed"
          :auto-upload="false"
          :multiple="false"
        >
          <el-button type="primary" class="solid-with-icon-btn">上传附件</el-button>
          <div slot="tip" class="el-upload__tip">附件格式为.xlsx、docx</div>
        </el-upload>
      </el-form-item>
    </el-form>
      <template slot="footer-center">
        <asp-btn-solid
          name="提交"
          @click="submit('applesFrom')"
        ></asp-btn-solid>
     </template>
    </asp-dialog>
  </div>
</template>
<script>
export default {
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {
        }
      }
    }
  },
  computed: {
  },
  watch: {
    'dialogParam.data': {
      handler(val) {
        // 判断是否为批量申诉
        if (!val.length) {
          this.applesFrom.id = val.id
        } else {
          const ids = []
          val.forEach(item => {
            ids.push(item.id)
          })
          this.ids = [...new Set(ids)].join()
        }
      }
    }
  },
  data() {
    return {
      formdata: new FormData(),
      fileList: [],
      ids: '',
      applesFrom: {
        content: '',
        id: ''
      },
      rules: {
        content: [
          { required: true, message: '请填写诉求', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleChange(file) {
      this.formdata.set('file', file.raw)
    },
    // 删除附件
    beforeRemove(file) {
      this.formdata = new FormData()
    },
    handleExceed() {
      this.$message.warning('当前限制选择 1 个附件')
    },
    clearFrom() {
      this.applesFrom = {}
      this.formdata = new FormData()
      this.ids = ''
    },
    // 提交表单
    submit(applesFrom) {
      this.$refs[applesFrom].validate((valid) => {
        if (valid) {
          if (this.applesFrom.id) {
            // 申诉
            this.formdata.set('content', this.applesFrom.content)
            this.formdata.set('id', this.applesFrom.id)
            this.$aspHttps.asp_FileUpload(
              this.$apiConfig.level1cloudstorePathPreFix + '/shop/monitor/clearAppealInitiate', this.formdata).then(res => {
              if (res.status === '200') {
                this.$message.success(res.message)
                this.dialogParam.modelVisible = false
                this.clearFrom()
                this.$parent.refreshList()
              } else {
                this.$message.error(res.message)
              }
            })
          } else if (this.ids) {
            // 批量申诉
            this.formdata.set('content', this.applesFrom.content)
            this.formdata.set('ids', this.ids)
            this.$aspHttps.asp_FileUpload(
              this.$apiConfig.level1cloudstorePathPreFix + '/shop/monitor/clearAppealInitiateBatch', this.formdata).then(res => {
              if (res.status === '200') {
                this.$message.success(res.message)
                this.dialogParam.modelVisible = false
                this.clearFrom()
                this.$parent.refreshList()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 关闭弹窗
    handleClose() {
      this.clearFrom()
    }
  }
}
</script>
<style lang="scss" scoped>
.brs {
  margin-top: 15px;
}
.checkitem {
  display: block;
  margin-top: 1.5%;
}
::v-deep .el-form-item__content {
  width: calc(100% - 200px);
  margin-left: 10px !important;
}
::v-deep .el-form-item__label {
  width: 200px;
}
</style>
