{"formList": [{"label": "诉求：", "type": "textarea", "columnName": "content", "classify": "basic", "defaultValue": "", "isModel": true, "required": true, "requiredCutomizeTips": "请填写诉求", "rules": [{"required": true, "message": "请填写诉求", "trigger": "blur"}], "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": "", "shortcut": false, "shortcutOptions": [{"label": "option1", "value": "0"}], "class": "", "operation": [], "hidden": false, "props": {"placeholder": "", "maxlength": "100", "show-word-limit": true, "wordLimitStyle": "transparent", "clearable": true, "disabled": false, "readonly": false, "rows": 2, "text": "", "controls": true}, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "isTrim": false}, {"label": "申诉材料：", "type": "upload", "columnName": "file", "classify": "advance", "targetName": "label_upload_1711333385213", "defaultValue": [], "isModel": true, "isLabelWidth": false, "icon-dev": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>", "span": 24, "width": "100%", "operation": [], "isTemplateDownload": false, "exportFileName": "", "apiName": "", "required": false, "requiredCutomizeTips": "", "rules": [], "hidden": false, "props": {"action": "https://jsonplaceholder.typicode.com/photos/", "tip": "", "limit": 1, "uploadType": ".xlsx,.docx", "text": [], "wordLimitStyle": "transparent", "controls": true}, "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "advance", "copyNewVal": [], "labelWidth": 160, "isBottomTipSwitch": true, "isHelpTipText": "", "class": "", "isUpload": false, "authSwitch": false, "isBottomTipValidateCloseSwitch": false, "ruleType": ""}, {"label": "附件格式为xlsx、docx", "type": "text", "columnName": "text_1711334527237", "defaultValue": "", "isModel": true, "icon-dev": "iconfont icontext", "classify": "basic", "isLabelWidth": true, "span": 24, "width": "100%", "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": true, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 300, "closeFormItemBorder": true, "content-font-color-switch": true, "content-font-color": "rgba(255, 0, 0, 0.97)"}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1711333534587", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "", "label": "提交", "interactive": "button_group_submit_all_validate", "validateProp": "", "apiName": "/yundian/osm/api/shop/monitor/clearAppealInitiate", "class": "solid-with-icon-btn", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": true, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": ""}], "name": "layout", "labelWidth": 160, "copyOldVal": ""}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "shopappeal", "defaultClass": "webbas", "size": "small", "statusList": [], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}}, "dataConfig": {}, "virtual_model": {}, "model": {"text_1711334527237": "", "content": "", "file": []}}