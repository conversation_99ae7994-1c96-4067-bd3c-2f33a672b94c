{"list": [{"type": "form", "label": "表单容器", "columnName": "form_1703575140984", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"type": "grid", "label": "栅格布局", "columnName": "grid_1703575148760", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 6, "align": "left", "list": [{"label": "", "type": "AspDateRange", "required": false, "rules": [], "classify": "form", "class": "", "columnName": "startDate", "targetName": "endDate", "asideIcon": "iconfont iconriqi", "customLabelWidth": true, "icon": "", "operation": [], "hidden": false, "dateRangeType": 0, "props": {"type": "date", "format": "yyyy-MM-dd", "value-format": "yyyy-MM-dd", "range-separator": "至", "start-placeholder": "", "end-placeholder": "", "clearable": false, "disabled": false, "readonly": false, "picker-options": {}}, "width": "100%", "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": "", "oldColumnName": "AspDateRange_1703575193651", "oldTargetName": "label_AspDateRange_1703575193651", "isChangeDefault1": true, "startDateRangeDefType": 1, "defaultValueNum1": -1, "isChangeDefault2": true, "endDateRangeDefType": 1, "defaultValueNum2": -1}]}, {"span": 3, "align": "left", "list": [{"type": "select", "label": "", "columnName": "provinceCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "provinceName", "option-value": "provinceCode", "option-alias": "", "option-disabled": "", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "/yundian/osm/api/shop/common/regionInfo", "apiType": "post+json", "apiParam": "{}", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "Option 1", "value": "1", "": false}, {"label": "Option 2", "value": "2", "": false}], "hasParentForm": true, "labelWidth": 0, "targetName": "label_select_1703575226122", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "dynamic": {"single_option_select": [{"source": {"label": "", "columnName": "select_1703575226122"}, "target": [{"columnName": "cityCode", "label": " cityCode", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "type": "select"}], "optionType": "2", "condition": [{"columnName": "provinceCode", "condition": "", "valueType": "change", "compareValueType": "", "columnValue": "", "apiType": "post+json", "apiName": "/yundian/osm/api/shop/common/regionCitiesInfo", "apiParam": "{\"provinceCode\":\"$provinceCode$\"}", "sessionKey": "", "dicKey": "", "status": []}]}]}, "oldColumnName": "select_1703575226122", "network$$$tag": false, "isClearOtherValue": true, "clearOtherValueList": ["cityCode"]}]}, {"span": 3, "align": "left", "list": [{"type": "select", "label": "", "columnName": "cityCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "confont icon<PERSON><PERSON><PERSON>", "classify": "form", "needLabel": true, "defaultSetLabel": false, "customLabelWidth": true, "option-label": "cityName", "option-value": "cityCode", "option-alias": "", "option-disabled": "", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "optionProps": {"optionType": "2", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "{}", "separator": ",", "allLabel": "全选", "allValue": "000", "isUserAllValue": false}, "class": "", "props": {"placeholder": "请选择", "multiple": false, "clearable": false, "disabled": false, "readonly": false, "filterable": false, "remote": false, "collapse-tags": false}, "options": [{"label": "Option 1", "value": "1"}, {"label": "Option 2", "value": "2"}], "hidden": false, "isAddOption": false, "showOptions": [{"label": "Option 1", "value": "1", "": false}, {"label": "Option 2", "value": "2", "": false}], "hasParentForm": true, "labelWidth": 0, "targetName": "label_select_1703575227431", "oldColumnName": "select_1703575227431", "filterOptionStatus": [], "disabledOptionStatus": [], "bpmFlowImageCloumnName": "", "network$$$tag": false}]}, {"span": 4, "align": "left", "list": [{"type": "input", "label": "", "columnName": "channelCode", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "请输入渠道编码", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "clearableStatus": []}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 0, "oldColumnName": "input_1703575251431", "bpmFlowImageCloumnName": "", "isTrim": true}]}, {"span": 4, "align": "left", "list": [{"type": "input", "label": "", "columnName": "shopId", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont icon<PERSON><PERSON><PERSON>", "customLabelWidth": true, "classify": "form", "class": "", "props": {"placeholder": "请输入店铺ID", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "clearableStatus": []}, "operation": [], "button": false, "slot": "", "button-name": "", "hidden": false, "hasParentForm": true, "labelWidth": 0, "oldColumnName": "input_1703575252816", "isTrim": true}]}, {"span": 4, "align": "left", "list": [{"type": "button", "label": "查询", "columnName": "button_1703575278448", "icon": "icon-input", "width": "100%", "required": false, "hidden": false, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"icon": "el-icon-search", "type": "primary"}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": true, "labelWidth": 100, "bpmFlowImageCloumnName": "", "tableId": "table_1703576174537", "formId": "form_1703575140984", "event": "submit"}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": true, "labelWidth": 100}], "isBorder": false, "isOverspread": false, "classify": "layout", "hidden": false, "hasParentForm": false}, {"type": "empty", "label": "空容器", "columnName": "empty_1703575531591", "asideIcon": "iconfont iconkongkuang", "width": "100%", "list": [{"type": "form", "label": "表单容器", "columnName": "form_1703575540840", "asideIcon": "iconfont <PERSON><PERSON><PERSON><PERSON><PERSON>xuweiwanchengpandianren<PERSON>dan", "width": "100%", "list": [{"label": "", "type": "customArea", "columnName": "customArea_1703575542335", "defaultValue": "", "icon": "", "width": "100%", "required": false, "rules": [], "asideIcon": "iconfont iconquyu", "customLabelWidth": true, "classify": "form", "class": "", "slotName": "appointmentnum", "props": {"placeholder": ""}, "hidden": false, "hasParentForm": true, "labelWidth": 0, "bpmFlowImageCloumnName": ""}], "isBorder": true, "isOverspread": false, "classify": "layout", "hidden": false, "hasParentForm": false, "labelWidth": 100}, {"label": "预约占比", "type": "headline", "button": false, "isLabelWidth": false, "columnName": "headline_1703575727159", "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "showSubTitle": false, "subTitle": "", "align": null, "hidden": false, "operation": [], "asideIcon": "iconfont iconzhedie<PERSON>ban", "list": [{"type": "dynamicTable", "label": "表格", "columnName": "table_1703575752612", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": false, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": false, "stripe": false, "tooltip-effect": "light", "highlight-current-row": false, "show-header": false, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [{"timeStamp": 1703575797935, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "label_1706686305211", "columnName": "bookingTypeShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "label_1706686309560", "columnName": "orderNumShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "label_1706686310272", "columnName": "percentage", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/orderManager/bookingOrderDataRate", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1703575797935, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "label_1706686305211", "columnName": "bookingTypeShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "label_1706686309560", "columnName": "orderNumShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "label_1706686310272", "columnName": "percentage", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "hasParentForm": false, "labelWidth": 100, "paginationStatus": [], "httpHeader": {"initHttp": true, "methods": "", "type": "post", "apiParam": {}}, "dynamicColumnCfg": {"columnCode": "code", "columnName": "name", "width": "auto"}, "router": {}, "expandRowKeys": [], "fixed-pagination": true, "tableHeight": "100px", "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": ""}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}}], "titleHiddenStatusList": [], "titleHidden": false, "open": true, "toolList": [], "title-background-color": "rgb(255,255,255,1)", "title-font-size": 15, "title-color": "rgb(48,49,51,1)", "hasParentForm": false, "labelWidth": 100}, {"label": "预约单明细数据报表", "type": "headline", "button": false, "isLabelWidth": false, "columnName": "headline_1703576053385", "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "showSubTitle": false, "subTitle": "", "align": null, "hidden": false, "operation": [], "asideIcon": "iconfont iconzhedie<PERSON>ban", "list": [{"type": "LRGrid", "label": "左右布局", "columnName": "LRGrid_1703576114602", "asideIcon": "iconfont <PERSON>on-editor-grid", "width": "100%", "list": [{"span": 24, "align": "right", "list": [{"type": "button", "label": "导出明细表", "columnName": "button_1703576144426", "icon": "icon-input", "width": "100%", "required": false, "hidden": true, "isEncrypt": false, "isVerifyTableRow": false, "asideIcon": "iconfont <PERSON><PERSON><PERSON>", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "props": {"type": "primary", "plain": false}, "class": "solid-with-icon-btn", "classify": "button", "hasParentForm": false, "labelWidth": 100, "bpmFlowImageCloumnName": "", "tableId": "table_1703576174537", "event": "export", "formId": "form_1703575140984", "treasuryConfig": {"isEnableTreasury": false, "treasuryResType": "session", "treasuryResKey": "", "treasuryResDict": "codeData.GOLD_BANK_APPROVAL_CODE", "treasuryActionApi": "", "treasuryAccessApi": "/web/business/v1/base/goldBank/getGoldBankParam", "treasuryAccessApiMethod": "post+json", "treasuryAccessApiParams": {"goldBankApprovalCode": "操作标识Key，不用修改自动匹配", "businessName": "", "businessType": ""}, "treasuryWriteLogApi": "/web/business/v1/base/goldBank/saveGoldBankLog", "treasuryWriteLogApiMethod": "post+json", "treasuryWriteLogApiParmas": {"goldBankApprovalResult": "金库的审批结果，不用修改自动匹配", "goldBankApprovalCode": "操作标识Key，不用修改自动匹配", "businessName": "", "businessType": ""}, "treasuryView": "asp-treasury-approval", "treasuryTitle": "金库审批", "treasuryDialogWidth": "60%", "treasuryDialogHeight": "auto", "treasuryDialogTitleClass": "dialog-title-default", "treasuryHints": "正在等待金库管理员审批", "treasuryConfirmMsg": "待金库管理员反馈审批结果，确定关闭当前的金库审批弹框吗？关闭后将取消本次操作。"}, "exportAsyncConfig": {"syncApiUrl": "", "syncApiMethod": "post+blob", "syncApiParams": {}, "exportFileName": "", "exportFileNamePrefix": "", "exportFileNameSuffix": "", "exportFileMineType": "", "exportAsyncApiUrl": "", "exportAsyncApiMethod": "post+json", "exportAsyncApiParams": {}, "exportAsyncHints": "", "exportAsyncResType": "session", "exportAsyncResKey": "", "exportAsyncResDict": "", "exportAsyncDefaultTimout": 50}, "exportType": "request", "apiMethod": "post+arraybuffer", "apiName": "/yundian/osm/api/shop/orderManager/export/bookingOrderDataList", "apiParam": {"startDate": "$startDate$", "endDate": "$endDate$", "provinceCode": "$provinceCode$", "cityCode": "$cityCode$", "channelCode": "$channelCode$", "shopId": "$shopId$", "downLoadName": "预约单明细数据报表"}, "hiddenStatus": []}]}], "isTableGrid": false, "classify": "layout", "hidden": false, "hasParentForm": false, "labelWidth": 100}, {"type": "table", "label": "表格", "columnName": "table_1703576174537", "asideIcon": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "width": "100%", "classify": "layout", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": true, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": "", "ascending": "asc", "descending": "desc"}, "showExpand": false, "slotName": "expand", "expandList": [], "childConfig": {"type": "table", "label": "表格", "resorece": "api", "resoreceColName": "children", "expandType": "table", "columnName": "table<PERSON><PERSON>d", "show-index": true, "indexFixed": false, "show-single-selection": false, "singleSelectionFixed": false, "show-multiple-selection": false, "multipleSelectionFixed": false, "cacheMultipleSelection": false, "multipleSelectionColumnName": [], "highlight-current-row": false, "highlight-current-row-color": "", "show-operation": false, "operationFixed": false, "show-pagination": false, "defaultSelectAll": false, "defaultSort": {"prop": "", "order": ""}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, sizes, prev, pager, next, jumper", "pageSizes": [10, 20, 30], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "", "methods": "", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false}, "fuzzyQuery": false, "fuzzyQueryColumnName": "<PERSON><PERSON><PERSON><PERSON>", "fuzzyQueryPlaceholder": "请输入", "fuzzyQueryPostion": "relative", "fuzzyQueryAlign": "right", "props": {"border": true, "stripe": true, "tooltip-effect": "dark", "highlight-current-row": false, "show-header": true, "default-sort": "", "empty-text": "暂无数据"}, "btnGroupDynamic": [], "list": [{"timeStamp": 1703576186359, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "预约单创建日期", "columnName": "statTime", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "省份", "columnName": "provinceName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "地市", "columnName": "cityName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "店铺ID", "columnName": "shopId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "原19位渠道编码", "columnName": "offlineChannelCode", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "180", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "线上线下结合渠道编码", "columnName": "onlineChannelCode", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "180", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "预约单编号", "columnName": "orderId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "预约单状态", "columnName": "statusShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "预约类型", "columnName": "bookingTypeShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "operation": [], "toolList": [], "operation-width": "100", "pagination": {"background": false, "layout": "total, prev, pager, next, sizes, jumper", "pageSizes": [10, 20, 30, 40, 50], "currentPage": 1, "pageSize": 10, "total": 0, "disabled": false, "hide-on-single-page": false}, "http": {"initHttp": true, "type": "post", "methods": "/yundian/osm/api/shop/orderManager/bookingOrderDataList", "tableLoading": false, "tableLoadingStatus": false, "tableLoadingText": "拼命加载中", "tableLoadingIcon": "el-icon-loading"}, "hidden": false, "tableTipDesc": "", "tableTipClass": "", "showTableTip": false, "defaultTableList": [{"timeStamp": 1703576186359, "label": "表格可编辑参数", "columnName": "$asp_isEdit", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "200", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": false, "tooltip": true, "$_isBuiltInParams": true, "filterColumn": false, "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "订单创建日期", "columnName": "statTime", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "省份", "columnName": "provinceName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "地市", "columnName": "cityName", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "店铺ID", "columnName": "shopId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "原19位渠道编码", "columnName": "offlineChannelCode", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "180", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "线上线下结合渠道编码", "columnName": "onlineChannelCode", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "180", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "预约单编号", "columnName": "orderId", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "预约单状态", "columnName": "statusShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}, {"label": "预约类型", "columnName": "bookingTypeShow", "type": "text", "sort": false, "fixed": false, "classify": "table", "width": "", "min-width": "100", "canEdit": false, "mergeRows": false, "editProps": {"type": ""}, "needConvertData": false, "convertProps": {}, "list": [], "show": true, "tooltip": true, "filterColumn": true, "align": "center", "customColor": "", "defaultValue": "", "asideIcon": "iconfont icontext", "isLabelWidth": false, "span": 24, "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false}], "hasParentForm": false, "labelWidth": 100, "paginationStatus": [], "router": {}, "fixed-pagination": true, "tableHeight": "400px", "formId": "form_1703575140984"}], "titleHiddenStatusList": [], "titleHidden": false, "open": true, "toolList": [], "title-background-color": "rgb(255,255,255,1)", "title-font-size": 15, "title-color": "rgb(48,49,51,1)", "hasParentForm": false, "labelWidth": 100}], "classify": "empty", "hidden": false}], "model": {"startDate": "2024-03-21", "endDate": "2024-03-21", "provinceCode": "", "cityCode": "", "channelCode": "", "shopId": "", "customArea_1703575542335": ""}, "tableModel": {"childList": []}, "pagination": {}, "publicConfig": {"exportName": "appointmentdetails", "exportrRemarks": "", "authorName": "", "project": "webbas", "labelWidth": 100, "labelPosition": "right", "size": "mini", "class": "", "layoutType": "flex", "statusList": [], "localProxy": "", "nigxProxy": "", "serverProps": {"statusKey": "", "statusValue": "", "dataKey": "", "requestDataKey": "", "pageNumKey": "page", "pageSizeKey": "rows", "totalKey": "total"}, "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "预约单明细数据报表"}}