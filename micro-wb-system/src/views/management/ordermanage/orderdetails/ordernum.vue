<template>
  <div>
    <span class="date">统计时间：{{ indexList.startDate }} - {{ indexList.endDate }}</span>
    <div class="shopNumContent">
      <div>
        <p><i class="el-icon-question" title="店铺完成的订单总数"></i> 订单成交量：<span>{{ indexList.orderNumShow }}</span> 笔</p>
      </div>
      <div>
        <p><i class="el-icon-question" title="店铺完成的订单总金额"></i> 订单销售额：<span>{{ indexList.orderMoneyShow }}</span> 元</p>
      </div>
      <div>
        <p><i class="el-icon-question" title="成交的订单数与店铺访问量（PV）之比"></i> 订单转化率：<span>{{ indexList.orderConversionRateShow }}</span></p>
      </div>
  </div>
  </div>
</template>
<script>
export default {
  props: ['indexList'],
  data () {
    return {}
  },
  computed: {
    // 防止数据没加载完成报错
    indexLists() {
      return this.indexList || {}
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-icon-question:before{
  color: #999999;
}
.date{
  font-size: 13px;
  color: #C0C2C6;
}
.shopNumContent{
  height: 80px;
  margin-top: 10px;
  border: 1px solid #E7E9EE;
  display: flex;
  align-items: center;
  justify-content: center;
  div{
    flex: 1;
    text-align: center;
    font-size: 17px;
    font-weight: 600;
    border-right: 1px solid #D9DADD;
    color: #333333;
    span{
      color: red;
    }
  }
  div:last-child{
    border: none;
  }
}
</style>
