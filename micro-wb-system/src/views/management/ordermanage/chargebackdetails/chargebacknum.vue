<template>
  <div>
    <span class="date">统计时间：{{ indexList.startDate }} - {{ indexList.endDate }}</span>
    <div class="shopNumContent">
      <div>
        <p><i class="el-icon-question" title="已完成的退货退款单总数"></i> 退单总量：<span>{{ indexList.orderNumShow ? indexList.orderNumShow : '0' }}</span> 笔</p>
      </div>
  </div>
  </div>
</template>
<script>
export default {
  props: ['indexList'],
  data () {
    return {}
  },
  computed: {
    // 防止数据没加载完成报错
    indexLists() {
      return this.indexList || {}
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-icon-question:before{
  color: #999999;
}
.date{
  font-size: 13px;
  color: #C0C2C6;
}
.shopNumContent{
  margin-top: 10px;
  display: flex;
  align-items: center;
  div{
    flex: 1;
    font-size: 17px;
    font-weight: 600;
    border-right: 1px solid #D9DADD;
    color: #333333;
    span{
      color: red;
    }
  }
  div:last-child{
    border: none;
  }
}
</style>
