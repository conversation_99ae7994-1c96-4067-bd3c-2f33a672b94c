.commpnStyleWrapper {
    border: 1px solid #eff2f7;
    background: #fff;
}

.commonStyleContainer {
    background: #f2f6fc;
    transition: 0.3s ease all;
}

.commonStylePongetitle {
    padding: 18px 20px;
    border-bottom: 1px solid #eff2f7;
    overflow: hidden;
    font-size: 14px;
    color: #303133;
    display: flex;

    .title {
        display: flex;
        align-items: center;
    }

    .navbarWrap {
        margin-left: auto;
        position: relative;
        display: flex;
        align-items: center;

        .el-menu {
            border-right: 0;
            display: flex;
            justify-content: right;
        }

        .navbar {
            position: static;
            width: 30px;
            height: 16px;

            .hamburger-container {
                line-height: inherit;
                height: auto;
                padding: 0;
            }
        }
    }
}

.commonStyleContainer {
    height: 100%;

    .commpnStyleWrapper {
        height: 100%;
        display: flex;
        flex-direction: column;

        .content {
            flex: 1;
            min-height: 0;
        }
    }
}