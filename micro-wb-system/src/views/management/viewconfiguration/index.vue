<!--
 * @User: JOJO
 * @FilePath: \netshoposmweb\micro-wb-system\src\views\management\viewconfiguration\index.vue
-->
<template>
  <div class="view-cnfiguration commonStyleContainer webbas">
    <div class="commpnStyleWrapper">
      <div class="commonStylePongetitle">
        <div class="title">一级线上店集中运营管理系统 - 数据看板配置</div>
      </div>
      <div class="content">
        <el-scrollbar class="elscrollbarHeight100">
          <el-form ref="form" :model="form" :rules="rules">
            <ul>
              <li>
                <div class="title center">数据总览</div>
                <div class="con">
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="overviewData.pv"
                  >
                    <el-radio border :label="1">线上店PV</el-radio>
                  </el-radio-group>
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="overviewData.uv"
                  >
                    <el-radio border :label="2">线上店UV</el-radio>
                  </el-radio-group>
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="overviewData.sales"
                  >
                    <el-radio border :label="3">总销售额</el-radio>
                  </el-radio-group>
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="overviewData.order"
                  >
                    <el-radio border :label="4">总订单量</el-radio>
                  </el-radio-group>
                </div>
              </li>
              <li class="mapLegend">
                <div class="title">地图图例</div>
                <div class="con">
                  <el-tabs class="eltabs" type="border-card">
                    <el-tab-pane label="日视图-对应订单量">
                      <dt v-for="(item, index) in form.mapLegendD" :key="index">
                        <el-color-picker
                          :value="form.mapLegendColour[index]"
                          class="color"
                          :disabled="colorPicker"
                          @change="handleActiveChange($event, index)"
                          size="medium"
                          :predefine="predefineColors"
                        >
                        </el-color-picker>
                        <!-- <template v-if="index !== form.mapLegendD.length - 1"> -->
                        <el-input
                          size="mini"
                          :readonly="index !== 0"
                          :disabled="index === 0"
                          class="ipt"
                          placeholder=""
                          v-model.number="item.begin"
                          clearable
                        >
                        </el-input>
                        <!-- </template> -->
                        <!-- <template v-else>
                          <el-form-item
                            class="fromItemBegin ipt"
                            :prop="`mapLegendD[${index}].begin`"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: (rule, value, callback) => {
                                  let endval = form.mapLegendD[index - 1].end
                                  if (parseInt(value) <= endval) {
                                    callback(callback(`当前值应大于${endval}`))
                                  } else {
                                    callback()
                                  }
                                }
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.begin"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template> -->

                        <span>-</span>

                        <template v-if="index !== form.mapLegendD.length - 1">
                          <el-form-item
                            class="ipt"
                            :prop="`mapLegendD[${index}].end`"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: validateCom1Fun
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.end"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                        <template v-else>
                          <el-form-item class="ipt">
                            <el-input
                              size="mini"
                              class="ipt"
                              :disabled="true"
                              style="font-size: 20px"
                              placeholder="∞"
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                      </dt>
                    </el-tab-pane>
                    <el-tab-pane label="周视图-对应订单量">
                      <dt :key="index" v-for="(item, index) in form.mapLegendW">
                        <el-color-picker
                          @change="handleActiveChange($event, index)"
                          v-model="form.mapLegendColour[index]"
                          class="color"
                          :disabled="colorPicker"
                          size="medium"
                          :predefine="predefineColors"
                        >
                        </el-color-picker>
                        <!-- <template v-if="index !== form.mapLegendW.length - 1"> -->
                        <el-input
                          size="mini"
                          :readonly="index !== 0"
                          :disabled="index === 0"
                          class="ipt"
                          placeholder=""
                          v-model.number="item.begin"
                        >
                        </el-input>
                        <!-- </template> -->
                        <!-- <template v-else>
                          <el-form-item
                            :prop="`mapLegendW[${index}].begin`"
                            class="fromItemBegin ipt"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: (rule, value, callback) => {
                                  let endval = form.mapLegendW[index - 1].end
                                  if (parseInt(value) <= endval) {
                                    callback(callback(`当前值应大于${endval}`))
                                  } else {
                                    callback()
                                  }
                                }
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.begin"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template> -->

                        <span>-</span>

                        <template v-if="index !== form.mapLegendW.length - 1">
                          <el-form-item
                            :prop="`mapLegendW[${index}].end`"
                            v-if="index !== form.mapLegendW.length - 1"
                            class="fromItemBegin ipt"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: validateCom2Fun
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.end"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                        <template v-else>
                          <el-form-item class="ipt">
                            <el-input
                              size="mini"
                              class="ipt"
                              :disabled="true"
                              style="font-size: 20px"
                              placeholder="∞"
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                      </dt>
                    </el-tab-pane>
                    <el-tab-pane label="月视图-对应订单量">
                      <dt :key="index" v-for="(item, index) in form.mapLegendM">
                        <el-color-picker
                          v-model="form.mapLegendColour[index]"
                          class="color"
                          @change="handleActiveChange($event, index)"
                          :disabled="colorPicker"
                          size="medium"
                          @input="handleInput"
                          :predefine="predefineColors"
                        >
                        </el-color-picker>
                        <!-- <template v-if="index !== form.mapLegendM.length - 1"> -->
                        <el-input
                          size="mini"
                          :readonly="index !== 0"
                          :disabled="index === 0"
                          class="ipt"
                          placeholder=""
                          v-model.number="item.begin"
                          clearable
                        >
                        </el-input>
                        <!-- </template> -->
                        <!-- <template v-else>
                          <el-form-item
                            :prop="`mapLegendM[${index}].begin`"
                            class="fromItemBegin ipt"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: (rule, value, callback) => {
                                  let endval = form.mapLegendM[index - 1].end
                                  if (parseInt(value) <= endval) {
                                    callback(callback(`当前值应大于${endval}`))
                                  } else {
                                    callback()
                                  }
                                }
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.begin"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template> -->

                        <span>-</span>

                        <template v-if="index !== form.mapLegendM.length - 1">
                          <el-form-item
                            class="ipt"
                            :prop="`mapLegendM[${index}].end`"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: validateCom3Fun
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.end"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                        <template v-else>
                          <el-form-item class="ipt">
                            <el-input
                              size="mini"
                              class="ipt"
                              :disabled="true"
                              style="font-size: 20px"
                              placeholder="∞"
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                      </dt>
                    </el-tab-pane>
                    <el-tab-pane label="季度视图-对应订单量">
                      <dt :key="index" v-for="(item, index) in form.mapLegendQ">
                        <el-color-picker
                          v-model="form.mapLegendColour[index]"
                          @change="handleActiveChange($event, index)"
                          class="color"
                          :disabled="colorPicker"
                          size="medium"
                          :predefine="predefineColors"
                        >
                        </el-color-picker>
                        <!-- <template v-if="index !== form.mapLegendQ.length - 1"> -->
                        <el-input
                          size="mini"
                          :readonly="index !== 0"
                          :disabled="index === 0"
                          class="ipt"
                          placeholder=""
                          v-model.number="item.begin"
                          clearable
                        >
                        </el-input>
                        <!-- </template> -->
                        <!-- <template v-else>
                          <el-form-item
                            :prop="`mapLegendQ[${index}].begin`"
                            class="fromItemBegin ipt"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: (rule, value, callback) => {
                                  let endval = form.mapLegendQ[index - 1].end
                                  if (parseInt(value) <= endval) {
                                    callback(callback(`当前值应大于${endval}`))
                                  } else {
                                    callback()
                                  }
                                }
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.begin"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template> -->
                        <span>-</span>
                        <template v-if="index !== form.mapLegendD.length - 1">
                          <el-form-item
                            class="ipt"
                            :prop="`mapLegendQ[${index}].end`"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: validateCom4Fun
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.end"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                        <template v-else>
                          <el-form-item class="ipt">
                            <el-input
                              size="mini"
                              class="ipt"
                              :disabled="true"
                              style="font-size: 20px"
                              placeholder="∞"
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                      </dt>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </li>

              <li>
                <div class="title center">云店数据</div>
                <div class="con">
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="cloudData.cumulative"
                  >
                    <el-radio border :label="'1'">累计开店</el-radio>
                  </el-radio-group>
                </div>
              </li>

              <li>
                <div class="title center">会员数据</div>
                <div class="con">
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="members.cumulative"
                  >
                    <el-radio border :label="'1'">累计会员</el-radio>
                  </el-radio-group>
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="members.trend"
                  >
                    <el-radio border :label="'2'">会员趋势</el-radio>
                  </el-radio-group>
                </div>
              </li>

              <li class="flexColumn topStyle">
                <div class="title">自有业务订单量/销售额情况一览</div>
                <div class="con">
                  <dl>
                    <div class="l">
                      <el-radio-group
                        size="mini"
                        class="group"
                        v-model="situation.radio"
                      >
                        <el-radio :label="'1'">关注业务</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="r">
                      <el-checkbox-group
                        v-model="situation.checkboxModel"
                        size="mini"
                        :min="1"
                        :max="4"
                      >
                        <el-checkbox
                          v-for="(item, index) in situation.checkboxData"
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          class="checkbox1"
                          border
                        ></el-checkbox>
                      </el-checkbox-group>
                      <div class="tip">* 至少选择1项, 最多可同时勾选4项</div>
                    </div>
                  </dl>
                  <dl v-if="!departmentId">
                    <div class="tops">
                      <el-radio-group
                        size="mini"
                        class="group"
                        v-model="situation.topRadioValue"
                      >
                        <el-radio :label="'1'">Top省份/地市成绩展示</el-radio>
                      </el-radio-group>
                    </div>
                    <el-select
                      size="mini"
                      v-model="situation.topValue"
                      class="elSelectTopValue"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in situation.topOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </dl>
                </div>
              </li>

              <li class="flexColumn">
                <div class="title">异业/自有商品订单量对比</div>
                <div class="con">
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="goodsOreder.total"
                  >
                    <el-radio border :label="'1'"
                      >异业/自有商品订单量占比</el-radio
                    >
                  </el-radio-group>
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="goodsOreder.different"
                  >
                    <el-radio border :label="'2'">异业商品订单量</el-radio>
                  </el-radio-group>
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="goodsOreder.own"
                  >
                    <el-radio border :label="'3'">自有商品订单量</el-radio>
                  </el-radio-group>
                </div>
              </li>

              <li class="flexColumn">
                <div class="title">异业/自有商品销售额对比</div>
                <div class="con">
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="goodsSales.total"
                  >
                    <el-radio border :label="'1'"
                      >异业/自有商品销售额占比</el-radio
                    >
                  </el-radio-group>
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="goodsSales.different"
                  >
                    <el-radio border :label="'2'">异业商品销售额</el-radio>
                  </el-radio-group>
                  <el-radio-group
                    size="mini"
                    class="group"
                    v-model="goodsSales.own"
                  >
                    <el-radio border :label="'3'">自有商品销售额</el-radio>
                  </el-radio-group>
                </div>
              </li>

              <li v-if="!departmentId">
                <div class="title center">异业商品Top省份/地市成绩展示</div>
                <div class="con">
                  <el-select
                    size="mini"
                    v-model="results.value"
                    placeholder="请选择"
                    class="elSelectTopValue"
                  >
                    <el-option
                      v-for="item in results.options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </div>
              </li>

              <li class="submitLi">
                <div class="title">
                  <el-button
                    size="mini"
                    class="submitBtn"
                    type="primary"
                    :loading="remindLoading"
                    @click="handleSubmit"
                    >提交</el-button
                  >
                </div>
              </li>
            </ul>
          </el-form>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'

export default {
  computed: {},
  mounted() {
    // 获取省商户还是集团
    this.isGroup()

    this.getData()
  },
  methods: {
    commonValidate({ rule, value, callback }, name) {
      const index = rule.field.match(/\[(\d+)\]/i)[1]

      const currentVal = this.$data.form[name][parseInt(index)]
      let currentEndVal = currentVal.end
      const currentBeginVal = currentVal.begin

      // 结束值不能小于等于开始值
      if (currentEndVal <= currentBeginVal) {
        callback(new Error('结束值不能小于等于开始值'))
        return
      }

      // 最多为4个, 所以到第4个暂停赋值
      if (parseInt(index) + 1 > 3) {
        callback()
        return
      }

      // 结束值为空或为0就为空
      if (currentEndVal === '' || currentEndVal === 0) {
        currentEndVal = ''
      } else {
        currentEndVal += 1
      }

      // if (
      //   this.$data.form[name][parseInt(index) + 1].begin > currentEndVal &&
      //   parseInt(index) + 1 === 3
      // ) {
      // } else {
      // }
      this.$data.form[name][parseInt(index) + 1].begin = currentEndVal

      callback()
    },

    /* 是否是集团账户 */
    isGroup() {
      return new Promise((resolve) => {
        this.$aspHttps
          .asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/getCurrent')
          .then((response) => {
            if (this.$reponseStatus(response)) {
              const zxs = ['北京', '天津', '上海', '重庆']

              this.departmentId = zxs.includes(
                response.data.listDivisionName[0]
              )
              resolve()
            }
          })
          .catch(() => {
            this.loading = false
            resolve()
          })
      })
    },

    /* 日视图校验 */
    validateCom1Fun(rule, value, callback) {
      this.commonValidate(
        {
          rule,
          value,
          callback
        },
        'mapLegendD'
      )
    },
    /* 周视图校验 */
    validateCom2Fun(rule, value, callback) {
      this.commonValidate(
        {
          rule,
          value,
          callback
        },
        'mapLegendW'
      )
    },
    /* 月视图校验 */
    validateCom3Fun(rule, value, callback) {
      this.commonValidate(
        {
          rule,
          value,
          callback
        },
        'mapLegendM'
      )
    },
    /* 季度视图校验 */
    validateCom4Fun(rule, value, callback) {
      this.commonValidate(
        {
          rule,
          value,
          callback
        },
        'mapLegendQ'
      )
    },

    /* 颜色选择后触发 */
    handleActiveChange(color, index) {
      this.$nextTick(() => {
        this.$set(this.form.mapLegendColour, index, color)
        this.$forceUpdate()
      })
    },

    handleInput() {
      this.$forceUpdate()
    },

    /* 请求数据 */
    getData() {
      this.loading = true

      this.$aspHttps
        .asp_Post(
          this.$apiConfig.level1cloudstorePathPreFix +
            '/databoard/conifg/getInfo',
          {}
        )
        .then((res) => {
          if (res.status === '400' && res.message === '获取用户信息失败') {
            this.$router.push('/login')
            return
          }

          const data = _.get(res, 'data', {})

          // 处理-自有业务订单量/销售额情况一览-关注业务
          const ownBusinessProportion = data.ownBusinessProportion.split(',')
          const checkboxmodel = []
          this.situation.checkboxData.forEach((item) => {
            ownBusinessProportion.forEach((item2) => {
              if (item.value === Number(item2)) {
                checkboxmodel.push(item.label)
              }
            })
          })
          this.situation.checkboxModel = checkboxmodel

          // 处理-地图图例
          const mapLegend = data.mapColour
          mapLegend.length = 5
          const mapLegendfilterArr = Array.from(mapLegend).filter(
            (item) => item
          )
          // 日
          const mapLegendD = mapLegendfilterArr
            .map((item, index) => {
              const newObj = {}
              if (item.d) {
                for (const key in item.d) {
                  if (
                    index === mapLegendfilterArr.length - 1 &&
                    key === 'begin'
                  ) {
                    newObj[key] = 0
                  } else {
                    newObj[key] = item.d[key] ? parseInt(item.d[key]) : ''
                  }
                }
              } else {
                newObj.begin = 0
                newObj.end = 0
              }
              return newObj
            })
            .reverse()
          mapLegendD[3].end = ''
          // 周
          const mapLegendW = mapLegendfilterArr
            .map((item, index) => {
              const newObj = {}
              if (item.w) {
                for (const key in item.w) {
                  if (
                    index === mapLegendfilterArr.length - 1 &&
                    key === 'begin'
                  ) {
                    newObj[key] = 0
                  } else {
                    newObj[key] = item.w[key] ? parseInt(item.w[key]) : ''
                  }
                }
              } else {
                newObj.begin = 0
                newObj.end = 0
              }
              return newObj
            })
            .reverse()
          mapLegendW[3].end = ''
          // 月
          const mapLegendM = mapLegendfilterArr
            .map((item, index) => {
              const newObj = {}
              if (item.m) {
                for (const key in item.m) {
                  if (
                    index === mapLegendfilterArr.length - 1 &&
                    key === 'begin'
                  ) {
                    newObj[key] = 0
                  } else {
                    newObj[key] = item.m[key] ? parseInt(item.m[key]) : ''
                  }
                }
              } else {
                newObj.begin = 0
                newObj.end = 0
              }
              return newObj
            })
            .reverse()
          mapLegendM[3].end = ''
          // 季度
          const mapLegendQ = mapLegendfilterArr
            .map((item, index) => {
              const newObj = {}
              if (item.q) {
                for (const key in item.q) {
                  if (
                    index === mapLegendfilterArr.length - 1 &&
                    key === 'begin'
                  ) {
                    newObj[key] = 0
                  } else {
                    newObj[key] = item.q[key] ? parseInt(item.q[key]) : ''
                  }
                }
              } else {
                newObj.begin = 0
                newObj.end = 0
              }
              return newObj
            })
            .reverse()
          mapLegendQ[3].end = ''
          // 颜色
          // const mapLegendColour = mapLegendfilterArr.map((item) => {
          //   return item.colour
          // })
          const mapLegendColour = ['#0050AB', '#0485BD', '#13B2F1', '#0BE8E2']

          this.form.mapLegendD = mapLegendD
          this.form.mapLegendW = mapLegendW
          this.form.mapLegendM = mapLegendM
          this.form.mapLegendQ = mapLegendQ
          this.form.mapLegendColour = mapLegendColour

          // 处理-异业/自有商品订单量对比
          const salesVolumeComparison = data.salesVolumeComparison.split(',')
          this.goodsOreder.total = salesVolumeComparison[0]
          this.goodsOreder.different = salesVolumeComparison[1]
          this.goodsOreder.own = salesVolumeComparison[2]

          // 处理-自有业务订单量/销售额情况一览
          this.situation.topValue = data.ownBusinessTop

          // 处理-异业商品Top省份/地市成绩展示
          this.results.value = data.yyTop

          // 处理-会员数据
          const numberData = data.numberData.split(',')
          this.members.cumulative = numberData[0]
          this.members.trend = numberData[1]

          // 处理当前id
          this.id = data.id

          this.$nextTick(() => {
            this.$refs.form.validate()
          })

          setTimeout(() => {
            this.loading = false
          }, 300)
        })
        .catch(() => {
          console.log('失败')
          this.loading = false
        })
    },

    /* 提交按钮 */
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.remindLoading = true

          const dataOverview = [
            this.overviewData.pv,
            this.overviewData.uv,
            this.overviewData.sales,
            this.overviewData.order
          ].join(',')
          const numberData = [this.members.cumulative, this.members.trend].join(
            ','
          )
          const orderComparison = [
            this.goodsOreder.total,
            this.goodsOreder.different,
            this.goodsOreder.own
          ].join(',')

          let ownBusinessProportion = []
          this.situation.checkboxModel.map((item) => {
            this.situation.checkboxData.forEach((item2) => {
              if (item === item2.label) {
                ownBusinessProportion.push(item2.value)
              }
            })
          })
          ownBusinessProportion = ownBusinessProportion.join(',')

          const ownBusinessTop = this.situation.topValue
          const yyTop = this.results.value
          const id = this.id

          // 处理地图图例数据
          let mapColourGetters = {}
          const mapColour = {}

          for (let index = 0; index < 4; index++) {
            const d = JSON.parse(
              JSON.stringify(this.form.mapLegendD)
            ).reverse()[index]
            const w = JSON.parse(
              JSON.stringify(this.form.mapLegendW)
            ).reverse()[index]
            const m = JSON.parse(
              JSON.stringify(this.form.mapLegendM)
            ).reverse()[index]
            const q = JSON.parse(
              JSON.stringify(this.form.mapLegendQ)
            ).reverse()[index]

            for (const key in d) {
              if (!d[key]) {
                if (key === 'begin') {
                  d[key] = '0'
                } else {
                  d[key] = ''
                }
              }
            }
            for (const key in w) {
              if (!d[key]) {
                if (key === 'begin') {
                  d[key] = '0'
                } else {
                  d[key] = ''
                }
              }
            }
            for (const key in m) {
              if (!d[key]) {
                if (key === 'begin') {
                  d[key] = '0'
                } else {
                  d[key] = ''
                }
              }
            }
            for (const key in q) {
              if (!d[key]) {
                if (key === 'begin') {
                  d[key] = '0'
                } else {
                  d[key] = ''
                }
              }
            }

            mapColourGetters[index] = {
              colour: JSON.parse(JSON.stringify(this.form.mapLegendColour))[
                index
              ],
              d,
              m,
              q
              // w
            }
          }

          mapColourGetters = JSON.parse(
            JSON.stringify(Object.values(mapColourGetters))
          )
          for (let i = 1; i < 5; i++) {
            mapColour[i] = JSON.parse(JSON.stringify(mapColourGetters[i - 1]))
          }

          const salesVolumeComparison = [
            this.goodsSales.total,
            this.goodsSales.different,
            this.goodsSales.own
          ].join(',')
          const ydData = this.cloudData.cumulative

          // console.log('数据啊', mapColour)

          this.$aspHttps
            .asp_Post(
              this.$apiConfig.level1cloudstorePathPreFix +
                '/databoard/conifg/save',
              {
                dataOverview,
                id,
                mapColour,
                numberData,
                orderComparison,
                ownBusinessProportion,
                ownBusinessTop,
                yyTop,
                salesVolumeComparison,
                ydData,
                mapColourFlag: true
              }
            )
            .then((res) => {
              this.remindLoading = false

              if (res.status !== '200') {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
                return
              }

              this.getData()
              this.$message({
                message: res.message,
                type: 'success'
              })
            })
        } else {
          this.$message({
            message: '提交不成功, 请检查是否填写有误',
            type: 'error'
          })
        }
      })
    }
  },
  data() {
    return {
      colorPicker: true,
      remindLoading: false,
      id: '',

      loading: true,

      departmentId: '',

      /* 预定义颜色 */
      predefineColors: [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],

      /* 数据总览 */
      overviewData: {
        // 线上店PV
        pv: 1,
        // 线上店UV
        uv: 2,
        // 总销售额
        sales: 3,
        // 总订单量
        order: 4
      },

      numberValidateForm: {
        age: 8
      },
      form: {
        mapLegendD: [],
        mapLegendW: [],
        mapLegendM: [],
        mapLegendQ: [],
        mapLegendColour: []
      },
      rules: {
        begin: [
          // { required: true, message: '必填项，请维护', trigger: 'blur' }
          { validator: this.validateCom, trigger: 'blur' }
          // { validator: this.validateMin, trigger: 'blur' }
        ],
        end: [
          { required: true, message: '必填项，请维护', trigger: 'blur' }
          // { validator: this.validateCom, trigger: 'blur' },
          // { validator: this.validateMax, trigger: 'blur' }
        ]
      },

      /* 云店数据 */
      cloudData: {
        // 累计开店
        cumulative: '1'
      },

      /* 会员数据 */
      members: {
        // 累计会员
        cumulative: '1',
        // 会员趋势
        trend: '2'
      },

      /* 异业/自有商品订单量对比 */
      goodsOreder: {
        // 异业/自有商品订单量占比
        total: '1',
        // 异业商品订单量
        different: '2',
        // 自有商品订单量
        own: '3'
      },

      /* 异业/自有商品销售额对比 */
      goodsSales: {
        // 异业/自有商品销售额占比
        total: '1',
        // 异业商品销售额
        different: '2',
        // 自有商品销售额
        own: '3'
      },

      /* 异业商品Top省份/地市成绩展示 */
      results: {
        value: '5',
        options: [
          { label: 'Top3', value: '3' },
          { label: 'Top5', value: '5' }
        ]
      },

      /* 自有业务订单量/销售额情况一览 */
      situation: {
        // 关注业务radio
        radio: '1',
        // 多选框model
        checkboxModel: [],
        // 多选框arr数据
        checkboxData: [
          { label: '话费充值', value: 1 },
          { label: '流量充值', value: 2 },
          { label: '终端', value: 3 },
          { label: '配件', value: 4 },
          { label: '套卡', value: 5 },
          { label: '套餐', value: 6 },
          { label: '增值业务', value: 7 },
          { label: '信用购机', value: 8 },
          { label: '优惠购机', value: 9 },
          { label: '宽带', value: 10 }
        ],
        // Top省份/地市成绩展示数据
        topOptions: [
          { label: 'Top3', value: '3' },
          { label: 'Top5', value: '5' }
        ],
        // Top省份/地市成绩model
        topValue: '5',
        // Top省份/地市成绩单选框value
        topRadioValue: '1'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../scss/common.scss';

.view-cnfiguration {
  .elSelectTopValue {
    width: 100px;
  }

  .mapLegend {
    .con {
      dt {
        .fromItemBegin {
          .ipt {
            // width: 76px;
          }
        }
        display: flex;
        align-items: center;
        margin-bottom: 18px;
        ::v-deep .el-color-picker--medium .el-color-picker__mask {
          display: none;
        }
        ::v-deep .el-form-item__error {
          position: absolute;
          width: 300px;
          line-height: 1;
          padding-top: 4px;
        }
        .color {
          display: flex;
          align-items: center;
          margin-right: 10px;
        }
        ::v-deep .el-color-picker__trigger {
          width: 30px;
          height: 30px;
        }
        .ipt {
          width: 100px;
        }
        span {
          margin: 0 10px;
        }
      }
      .eltabs {
        width: 620px;
        ::v-deep .el-tabs__item {
          font-size: 13px;
        }
      }
    }
  }

  ::v-deep .el-checkbox__input {
    display: inline-flex;
    align-items: center;
  }

  ::v-deep .el-color-picker__color {
    border: 0 !important;
  }

  ::v-deep .el-checkbox {
    display: inline-flex;
    align-items: center;
  }
  .submitBtn {
    width: 100px;
  }
  .submitLi {
    display: flex;
    justify-content: center;
  }

  .el-checkbox {
    margin-right: 10px;
    margin-bottom: 10px;
    margin-left: 0;
  }
  .el-checkbox.is-bordered + .el-checkbox.is-bordered {
    margin-left: 0;
  }
  .commpnStyleWrapper {
    .content {
      overflow: hidden;
      overflow-y: scroll;
      ul {
        padding: 0px 20px;
        padding-top: 20px;
        padding-bottom: 40px;
        margin: 0;
        li {
          list-style: none;
          display: flex;
          margin-bottom: 30px;
          &:last-child {
            margin-bottom: 0;
          }
          &.flexColumn {
            flex-direction: column;
            .con {
              margin-top: 10px;
            }
          }

          &.topStyle {
            dl {
              display: flex;
              .tops {
                display: flex;
                align-items: center;
              }
              .l {
              }
              .r {
                flex: 1;
                min-width: 0;
                & >>> {
                  .el-checkbox {
                    margin-right: 10px;
                    margin-bottom: 10px;
                    margin-left: 0;
                  }
                }
                .tip {
                  text-align: left;
                  font-size: 13px;
                  color: #888;
                }
              }
            }
          }

          .title {
            font-size: 14px;
            color: #606266;
            margin-right: 30px;
            &.center {
              display: flex;
              align-items: center;
            }
          }
          .con {
            flex: 1;
            min-width: 0;

            .group {
              margin-right: 20px;
            }
          }

          &.mapLegend {
            .con {
              dl {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                margin-top: 0;

                .color {
                  // background: #3c067a;
                  margin-right: 20px;
                  // box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                  &.c1 {
                    background: #7915f5;
                  }
                  &.c2 {
                    background: #367baf;
                  }
                  &.c3 {
                    background: #95d1f4;
                  }
                }
                .tit {
                  font-size: 12px;
                  color: #333;
                }
                .ipt {
                  width: 80px;
                  margin: 0 5px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
