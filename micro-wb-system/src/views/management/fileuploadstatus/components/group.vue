<!--
 * @User: JOJO
 * @FilePath: \netshoposmweb\micro-wb-system\src\views\management\fileuploadstatus\components\group.vue
-->
<template>
  <!-- <div class="group2">
    <table-pane
      v-tableheightauto="{}"
      :data-source="dataSource"
      class="tablePane"
    />
  </div> -->
  <!-- 表格部分 -->
  <asp-table
    ref="table"
    :url="table.url"
    :param="table.searchForm"
    :indexSwitch="false"
    :prefix="table.prefix"
    @tableSearchSuccess="tableSearchSuccess"
    :isMountedTableData="false"
    :tableRowClassName="tableRowClassName"
    :tip1="`* 使用一级云店的省份无需上传数据文件`"
    type=""
  >
    <asp-table-column
      prop="proName"
      min-width="100"
      show-overflow-tooltip
      label="省份"
    >
    </asp-table-column>
    <asp-table-column
      prop="fileDates"
      min-width="100"
      label="数据所属日期"
      show-overflow-tooltip
    >
    </asp-table-column>
    <asp-table-column
      v-if="fileStatus === 1 || fileStatus === ''"
      prop="uploadNum"
      min-width="100"
      show-overflow-tooltip
      label="已上传"
    >
      <template slot-scope="{ scope }">
        <span
          v-if="!scope.row.province15"
          :style="{
            color: scope.row.uploadNum > 0 ? 'green' : ''
          }"
          >{{ scope.row.uploadNum }}</span
        >
        <span v-else>--</span>
      </template>
    </asp-table-column>
    <asp-table-column
      prop="notUploadNum"
      v-if="fileStatus === 0 || fileStatus === ''"
      min-width="100"
      label="未上传"
      show-overflow-tooltip
    >
      <template slot-scope="{ scope }">
        <span
          v-if="!scope.row.province15"
          :style="{ color: scope.row.notUploadNum > 0 ? 'red' : '' }"
          >{{ scope.row.notUploadNum }}</span
        >
        <span v-else>--</span>
      </template>
    </asp-table-column>

    <asp-table-column
      :width="this.$aspFontSize.asp_ColButtonSize([2, 2, 2, 2, 2])"
      label="查看"
      fixed="right"
    >
      <template slot-scope="{ scope }">
        <asp-btn-text
          v-if="!scope.row.province15"
          name="查看"
          @click="handleDetailStaff(scope.row)"
        >
        </asp-btn-text>
      </template>
    </asp-table-column>
  </asp-table>
</template>

<script>
import tableheightauto from '../directive/tableheightauto'
export default {
  directives: { tableheightauto },
  components: {},
  props: ['pickerValue', 'fileStatus'],
  watch: {
    pickerValue: {
      handler(pickerValue) {
        if (pickerValue) {
          this.table.searchForm.params.startDate = pickerValue[0]
          this.table.searchForm.params.endDate = pickerValue[1]
        } else {
          this.table.searchForm.params.startDate = ''
          this.table.searchForm.params.endDate = ''
        }
      },
      deep: true
    }
  },
  data() {
    return {
      /* 表格配置 */
      table: {
        prefix: this.$apiConfig.level1cloudstorePathPreFix,
        url: '/databoard/upload/summaryDataList',
        searchForm: {
          params: {
            startDate: '',
            endDate: ''
          }
        }
      }
    }
  },
  methods: {
    tableRowClassName() {
      return ''
      // if (row.province15) {
      //   return ''
      // } else {
      //   return ''
      // }
    },

    /* 点击查看全部 */
    handleDetailStaff(row) {
      this.$emit('handleRow', row)
    },

    /* 搜索 */
    asp_search() {
      this.$refs.table.asp_search()
    },

    /* 搜索成功回调 */
    tableSearchSuccess(data) {
      const unArr = [
        '湖南',
        '湖南省',
        // '内蒙古',
        '宁夏',
        '宁夏省',
        '上海',
        '上海市',
        '甘肃',
        '甘肃省',
        // '福建',
        // '福建省',
        '新疆',
        '吉林',
        '吉林省',
        '青海',
        '青海省',
        '西藏',
        '西藏省',
        '海南',
        '海南省',
        '江西',
        '江西省',
        // '山西',
        // '山西省',
        '河北',
        '河北省',
        '天津',
        '黑龙江'
      ]
      const filterData = data.map((item) => {
        if (unArr.includes(item.proName)) {
          return {
            ...item,
            province15: true
          }
        } else {
          return item
        }
      })
      const arr1 = filterData.filter((item) => {
        return item.province15
      })
      const arr2 = filterData.filter((item) => {
        return !item.province15
      })
      const zdata = [...arr2, ...arr1]
      this.$refs.table.listData = zdata

      this.$emit('tableSearchSuccess', zdata)
    }
  }
}
</script>

<style lang="scss" scoped>
.group2 {
  height: 100%;
  display: flex;
  flex-direction: column;
  >>> .el-scrollbar__bar {
    z-index: 3;
  }
}
</style>
