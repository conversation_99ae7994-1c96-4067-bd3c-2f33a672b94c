<!--
 * @User: JOJO
 * @FilePath: \netshoposmweb\micro-wb-system\src\views\management\fileuploadstatus\components\savecompany.vue
-->
<template>
  <asp-table
    class="savecompany"
    ref="table"
    :url="table.url"
    :param="table.searchForm"
    :indexSwitch="false"
    :prefix="table.prefix"
    @tableSearchSuccess="tableSearchSuccess"
    :isMountedTableData="false"
    type=""
  >
    <template slot="header">
      <el-tag
        class="child"
        :style="{ backgroundColor: commonColor }"
        effect="dark"
        type="success"
        >已上传 : {{ uploadNumber }}</el-tag
      >
      <el-tag
        class="child"
        :style="{ backgroundColor: commonColor }"
        effect="dark"
        type="danger"
        >未上传 : {{ nouploadNumber }}</el-tag
      >
    </template>
    <asp-table-column
      prop="proName"
      min-width="100"
      show-overflow-tooltip
      label="省份"
    >
      <template slot-scope="{ scope }">
        <span :style="{ color: scope.row.status === 0 ? 'red' : '' }">{{
          scope.row.proName
        }}</span>
      </template>
    </asp-table-column>
    <asp-table-column
      prop="fileDate"
      min-width="100"
      label="日期"
      show-overflow-tooltip
    >
      <template slot-scope="{ scope }">
        <span :style="{ color: scope.row.status === 0 ? 'red' : '' }">
          {{ formatTimer(scope.row.fileDate) }}
        </span>
      </template>
    </asp-table-column>
    <asp-table-column
      prop="status"
      min-width="100"
      show-overflow-tooltip
      label="文件状态"
    >
      <template slot-scope="{ scope }">
        <span :style="{ color: scope.row.status === 0 ? 'red' : 'green' }">
          {{ scope.row.status === 0 ? '未上传' : '已上传' }}
        </span>
      </template>
    </asp-table-column>
    <asp-table-column
      prop="uploadTime"
      min-width="100"
      label="上传日期"
      show-overflow-tooltip
    >
      <template slot-scope="{ scope }">
        <span :style="{ color: scope.row.status === 0 ? 'red' : '' }">
          {{
            scope.row.status === 0 ? '--' : formatTimer(scope.row.uploadTime)
          }}
        </span>
      </template>
    </asp-table-column>
  </asp-table>
</template>

<script>
import tableheightauto from '../directive/tableheightauto'

export default {
  directives: { tableheightauto },
  components: {},
  props: ['pickerValue', 'fileStatus', 'uploadNumber', 'nouploadNumber'],
  watch: {
    pickerValue: {
      handler(pickerValue) {
        if (pickerValue) {
          this.table.searchForm.params.startDate = pickerValue[0]
          this.table.searchForm.params.endDate = pickerValue[1]
        } else {
          this.table.searchForm.params.startDate = ''
          this.table.searchForm.params.endDate = ''
        }
      },
      deep: true
    },
    fileStatus(fileStatus) {
      this.table.searchForm.params.status = fileStatus
    }
  },
  data() {
    return {
      /* 公共颜色 */
      commonColor: '',

      /* 表格配置 */
      table: {
        prefix: this.$apiConfig.level1cloudstorePathPreFix,
        url: '/databoard/upload/dataList',
        searchForm: {
          params: {
            startDate: '',
            endDate: '',
            status: ''
          }
        }
      }
    }
  },

  methods: {
    /* 格式化时间 */
    formatTimer: function (value) {
      value = String(value)
      const date = new Date(value)
      const y = date.getFullYear()
      let MM = date.getMonth() + 1
      MM = MM < 10 ? '0' + MM : MM
      let d = date.getDate()
      d = d < 10 ? '0' + d : d
      return y + '-' + MM + '-' + d
    },

    /* 搜索成功回调 */
    tableSearchSuccess(data) {
      this.$emit('tableSearchSuccess', data)
    },

    /* 搜索 */
    asp_search() {
      this.$refs.table.asp_search()
    },

    /* 获取颜色值 */
    getColor(e) {
      if (e.key === 'theme_color') {
        this.commonColor = e.newValue
      }
    }
  },

  mounted() {
    // 获取主色调
    this.commonColor = sessionStorage.getItem('theme_color')

    // 监听sessionStorage的setitem事件
    window.addEventListener('setItemEvent', this.getColor)
  },
  destroyed() {
    window.removeEventListener('setItemEvent', this.getColor)
  }
}
</script>

<style lang="scss" scoped>
.savecompany {
  .child {
    height: 30px;
    margin-right: 10px;
    margin-bottom: 8px;
    border: 0;
    margin-top: 8px;
  }
}
</style>
