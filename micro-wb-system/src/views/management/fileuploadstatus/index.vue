<!--
 * @User: JOJO
 * @FilePath: \netshoposmweb\micro-wb-system\src\views\management\fileuploadstatus\index.vue
-->
<template>
  <div class="file-upload-status commonStyleContainer">
    <div class="webbas">
      <div class="list-page-content-css">
        <!--搜索部分 -->
        <div class="query-area-content-css">
          <div class="searchBlock" style="padding: 10px">
            <el-form>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="查询日期：">
                    <el-date-picker
                      v-model="pickerValue"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="daterange"
                      size="mini"
                      align="right"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="文件状态：">
                    <el-select
                      ref="multiSelect"
                      v-model="fileStatus"
                      size="mini"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in fileStatusOptions"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <div style="display: flex">
                    <div style="width: 90px">
                      <el-form-item label="">
                        <asp-btn-solid
                          name="查询"
                          :disabled="aspbtndisabled"
                          icon="el-icon-search"
                          @click="handleSearchDatasetList"
                        ></asp-btn-solid>
                      </el-form-item>
                    </div>
                    <div style="width: 120px" v-show="tableData.length">
                      <el-form-item label="">
                        <asp-btn-hollow
                          name="导出明细"
                          icon="el-icon-download"
                          @click="exportSubsidiary"
                        ></asp-btn-hollow>
                      </el-form-item>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>

        <!-- 集团表格 -->
        <Group
          ref="table"
          :pickerValue="pickerValue"
          :fileStatus="exportParams.fileStatus"
          v-if="!departmentId"
          @handleRow="handleRow"
          @tableSearchSuccess="tableSearchSuccess"
        ></Group>

        <!-- 省公司表格 -->
        <Savecompany
          ref="table"
          :pickerValue="pickerValue"
          :fileStatus="fileStatus"
          @tableSearchSuccess="tableSearchSuccess"
          :uploadNumber="uploadNumber"
          :nouploadNumber="nouploadNumber"
          v-else
        ></Savecompany>
      </div>
    </div>
  </div>
</template>

<script>
import Group from './components/group'
import _ from 'lodash'
import Savecompany from './components/savecompany'

export default {
  data() {
    return {
      /* 查询日期时间 */
      pickerValue: [],

      /* 文件状态 */
      fileStatus: '',
      /* 文件状态下拉框 */
      fileStatusOptions: [
        { label: '全部文件状态', value: '' },
        { label: '已上传', value: 1 },
        { label: '未上传', value: 0 }
      ],

      /* 已上传 */
      uploadNumber: 0,

      /* 未上传 */
      nouploadNumber: 0,

      /* 表格数据 */
      tableData: [],

      /* 表格loading */
      loading: true,

      /* 集团变量 */
      departmentId: '',

      /* 需要传到导出接口的具体参数 */
      exportParams: {
        fileStatus: '',
        pickerValue: ''
      },

      /* 已上传按钮颜色 */
      commonColor: ''
    }
  },
  components: {
    Group,
    Savecompany
  },
  computed: {},
  mounted() {
    // 获取省商户还是集团
    this.isGroup().then((res) => {
      if (sessionStorage.getItem('fileuploadstatusIndexData')) {
        this.handleSearchDatasetList()
      }
    })

    // 获取主色调
    this.commonColor = sessionStorage.getItem('theme_color')

    // 监听sessionStorage的setitem事件
    window.addEventListener('setItemEvent', this.getColor)
  },
  created() {
    let fileuploadstatusIndexData = sessionStorage.getItem(
      'fileuploadstatusIndexData'
    )

    if (fileuploadstatusIndexData) {
      fileuploadstatusIndexData = JSON.parse(fileuploadstatusIndexData)
      this.fileStatus = fileuploadstatusIndexData.fileStatus
      this.pickerValue = fileuploadstatusIndexData.pickerValue
      this.exportParams = fileuploadstatusIndexData.exportParams
    }
  },
  destroyed() {
    window.removeEventListener('setItemEvent', this.getColor)

    const dataobj = {}
    dataobj.fileStatus = this.fileStatus
    dataobj.pickerValue = this.pickerValue
    dataobj.exportParams = this.exportParams

    sessionStorage.setItem('fileuploadstatusIndexData', JSON.stringify(dataobj))
  },
  watch: {
    pickerValue: {
      handler(pickerValue) {
        if (pickerValue && pickerValue.length) {
          this.aspbtndisabled = false
        } else {
          this.aspbtndisabled = true
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /* 获取颜色值 */
    getColor(e) {
      if (e.key === 'theme_color') {
        this.commonColor = e.newValue
      }
    },

    /* 查询成功回调 */
    tableSearchSuccess(data) {
      this.uploadNumber = data.filter((item) => item.status === 1).length
      this.nouploadNumber = data.filter((item) => item.status === 0).length

      this.exportParams.fileStatus = this.fileStatus
      this.exportParams.pickerValue = this.pickerValue

      this.tableData = data
    },

    /* 是否是集团账户 */
    isGroup() {
      return new Promise((resolve) => {
        this.$aspHttps
          .asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/getCurrent')
          .then((response) => {
            if (this.$reponseStatus(response)) {
              this.departmentId = response.data.departmentId
              resolve()
            }
          })
          .catch(() => {
            this.loading = false
            resolve()
          })
      })
    },

    /* 获取数据 */
    getData() {
      this.loading = true

      if (this.departmentId) {
        // 省公司
        this.$aspHttps
          .asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix +
              '/databoard/upload/dataList',
            {
              params: {
                startDate: this.pickerValue ? this.pickerValue[0] : '',
                endDate: this.pickerValue ? this.pickerValue[1] : '',
                status: this.fileStatus
              }
            }
          )
          .then((res) => {
            const data = _.get(res, 'data', [])

            // 赋值
            this.tableData = data

            setTimeout(() => {
              this.loading = false
            }, 100)
          })
          .catch(() => {
            console.log('失败')
          })
      } else {
        // 集团
        this.$aspHttps
          .asp_Post(
            this.$apiConfig.level1cloudstorePathPreFix +
              '/databoard/upload/summaryDataList',
            {
              params: {
                startDate: this.pickerValue ? this.pickerValue[0] : '',
                endDate: this.pickerValue ? this.pickerValue[1] : ''
              }
            }
          )
          .then((res) => {
            const data = _.get(res, 'data', [])

            this.exportParams.fileStatus = this.fileStatus
            this.exportParams.pickerValue = this.pickerValue

            this.tableData = data

            setTimeout(() => {
              this.loading = false
            }, 300)
          })
          .catch(() => {
            console.log('失败')
            this.loading = false
          })
      }
    },

    /* 点击导出 */
    exportSubsidiary() {
      this.$aspHttps.asp_FileDownload(
        this.$apiConfig.level1cloudstorePathPreFix +
          '/databoard/upload/exportDataList',
        {
          params: {
            startDate: this.exportParams.pickerValue[0],
            endDate: this.exportParams.pickerValue[1],
            status: this.exportParams.fileStatus
          }
        },
        '文件上传情况明细'
      )
    },

    // 日期转换
    changeDate(dateData) {
      if (!dateData) return ''
      const months =
        dateData.getMonth() + 1 < 10
          ? '0' + (dateData.getMonth() + 1)
          : dateData.getMonth() + 1
      const days =
        dateData.getDate() < 10 ? '0' + dateData.getDate() : dateData.getDate()
      return dateData.getFullYear() + '-' + months + '-' + days
    },

    /* 点击查询 */
    handleSearchDatasetList() {
      this.$refs.table.asp_search()
    },

    /* 点击查看全部 */
    handleRow(item) {
      let url = `/management/fileuploadstatus/lookall?name=${item.proName}&proId=${item.proId}`
      if (this.exportParams.pickerValue[0]) {
        url += `&startDate=${this.exportParams.pickerValue[0]}`
      }
      if (this.exportParams.pickerValue[1]) {
        url += `&endDate=${this.exportParams.pickerValue[1]}`
      }
      if (this.exportParams.fileStatus || this.exportParams.fileStatus === 0) {
        url += `&status=${String(this.exportParams.fileStatus)}`
      }
      this.$router.push({
        path: url
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../scss/common.scss';

.file-upload-status {
  .searchBlock {
    padding: 10px;
  }

  .content {
    display: flex;
    flex-direction: column;

    .rap {
      flex: 1;
      min-height: 0;
      padding: 0 20px;
      padding-bottom: 20px;
    }

    .searchBlock {
      padding: 10px;
      ul {
        padding: 0 20px;
        padding-top: 20px;
        display: flex;
        flex-flow: wrap;
        margin: 0;
        li {
          list-style: none;
          margin-right: 20px;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          & > span {
            display: block;
            font-size: 13px;
            // margin-bottom: 10px;
            margin-right: 10px;
          }
        }
      }
    }

    .tablePane {
      padding: 0 20px;
    }
  }
}
</style>
