/*
 * @User: JOJO
 * @FilePath: \shopaaa\micro-wb-system\src\views\management\directive\tableheightauto\tableheightauto.js
 */
import {
  addResizeListener,
  removeResizeListener
} from 'element-ui/src/utils/resize-event'

// 设置表格高度
const doResize = async (el, binding, vnode) => {
  setTimeout(() => {
    const height = el.parentNode.getBoundingClientRect().height + 'px'
    vnode.context.dataSource.height = height
  }, 20)
}

export default {
  // 初始化设置
  async bind(el, binding, vnode) {
    // 设置resize监听方法
    el.resizeListener = async () => {
      await doResize(el, binding, vnode)
    }

    // 绑定监听方法到addResizeListener
    addResizeListener(window.document.body, el.resizeListener)
  },
  // 绑定默认高度
  async inserted(el, binding, vnode) {
    await doResize(el, binding, vnode)
  },

  // 销毁时设置
  unbind(el) {
    // 移除resize监听
    removeResizeListener(el, el.resizeListener)
  }
}
