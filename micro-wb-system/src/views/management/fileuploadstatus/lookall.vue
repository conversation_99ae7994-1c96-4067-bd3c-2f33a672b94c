<!--
 * @User: JOJO
 * @FilePath: \netshoposmweb\micro-wb-system\src\views\management\fileuploadstatus\lookall.vue
-->
<template>
  <div class="lookall commonStyleContainer">
    <div class="webbas">
      <div class="list-page-content-css">
        <!-- 搜索部分 -->
        <div class="query-area-content-css">
          <el-page-header
            @back="$router.back()"
            :content="pName"
            class="pageheadera"
          ></el-page-header>
          <div class="searchBlock" style="padding: 10px">
            <el-form>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="查询日期：">
                    <el-date-picker
                      v-model="pickerValue"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="daterange"
                      size="mini"
                      align="right"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="文件状态：">
                    <el-select
                      ref="multiSelect"
                      v-model="fileStatus"
                      size="mini"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in fileStatusOptions"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <div style="display: flex">
                    <div style="width: 90px">
                      <el-form-item label="">
                        <asp-btn-solid
                          name="查询"
                          icon="el-icon-search"
                          @click="handleSearchDatasetList"
                        ></asp-btn-solid>
                      </el-form-item>
                    </div>
                    <div style="width: 120px" v-show="tableData.length">
                      <el-form-item label="">
                        <asp-btn-hollow
                          name="导出明细"
                          icon="el-icon-download"
                          @click="exportSubsidiary"
                        ></asp-btn-hollow>
                      </el-form-item>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>

        <!-- 表格部分 -->
        <asp-table
          class="savecompany"
          ref="table"
          :url="table.url"
          :param="table.searchForm"
          :indexSwitch="false"
          :prefix="table.prefix"
          @tableSearchSuccess="tableSearchSuccess"
          type=""
        >
          <template slot="header">
            <el-tag
              :style="{ backgroundColor: commonColor }"
              class="child"
              effect="dark"
              type="success"
              >已上传 : {{ uploadNumber }}</el-tag
            >
            <el-tag
              :style="{ backgroundColor: commonColor }"
              class="child"
              effect="dark"
              type="danger"
              >未上传 : {{ nouploadNumber }}</el-tag
            >
          </template>
          <asp-table-column
            prop="proName"
            min-width="100"
            show-overflow-tooltip
            label="省份"
          >
            <template slot-scope="{ scope }">
              <span :style="{ color: scope.row.status === 0 ? 'red' : '' }">{{
                scope.row.proName
              }}</span>
            </template>
          </asp-table-column>
          <asp-table-column
            prop="fileDate"
            min-width="100"
            label="日期"
            show-overflow-tooltip
          >
            <template slot-scope="{ scope }">
              <span :style="{ color: scope.row.status === 0 ? 'red' : '' }">
                {{ formatTimer(scope.row.fileDate) }}
              </span>
            </template>
          </asp-table-column>
          <asp-table-column
            prop="status"
            min-width="100"
            show-overflow-tooltip
            label="文件状态"
          >
            <template slot-scope="{ scope }">
              <span
                :style="{ color: scope.row.status === 0 ? 'red' : 'green' }"
              >
                {{ scope.row.status === 0 ? '未上传' : '已上传' }}
              </span>
            </template>
          </asp-table-column>
          <asp-table-column
            prop="uploadTime"
            min-width="100"
            label="上传日期"
            show-overflow-tooltip
          >
            <template slot-scope="{ scope }">
              <span :style="{ color: scope.row.status === 0 ? 'red' : '' }">
                {{
                  scope.row.status === 0
                    ? '--'
                    : formatTimer(scope.row.uploadTime)
                }}
              </span>
            </template>
          </asp-table-column>
        </asp-table>
      </div>
    </div>
  </div>
</template>

<script>
import tableheightauto from './directive/tableheightauto'

export default {
  directives: { tableheightauto },
  data() {
    return {
      commonColor: '',

      /* 表格配置 */
      table: {
        prefix: this.$apiConfig.level1cloudstorePathPreFix,
        url: '/databoard/upload/dataList',
        searchForm: {
          params: {
            proId: '',
            startDate: '',
            endDate: '',
            status: ''
          }
        }
      },

      /* 省份名称 */
      pName: '',

      /* 已上传 */
      uploadNumber: 0,

      /* 未上传 */
      nouploadNumber: 0,

      /* 查询日期时间 */
      pickerValue: [],

      /* 文件状态 */
      fileStatus: '',
      /* 文件状态下拉框 */
      fileStatusOptions: [
        { label: '全部文件状态', value: '' },
        { label: '已上传', value: 1 },
        { label: '未上传', value: 0 }
      ],

      /* 需要传到导出接口的具体参数 */
      exportParams: {
        fileStatus: '',
        pickerValue: ''
      },

      tableData: []
    }
  },
  computed: {},
  components: {},
  watch: {
    pickerValue: {
      handler(pickerValue) {
        if (pickerValue) {
          this.table.searchForm.params.startDate = pickerValue[0]
          this.table.searchForm.params.endDate = pickerValue[1]
        } else {
          this.table.searchForm.params.startDate = ''
          this.table.searchForm.params.endDate = ''
        }
      },
      deep: true
    },
    fileStatus(fileStatus) {
      this.table.searchForm.params.status = fileStatus
    }
  },
  methods: {
    /* 表格组件请求数据成功执行 */
    tableSearchSuccess(data) {
      this.uploadNumber = data.filter((item) => item.status === 1).length
      this.nouploadNumber = data.filter((item) => item.status === 0).length

      this.exportParams.fileStatus = this.fileStatus
      this.exportParams.pickerValue = this.pickerValue

      this.tableData = data
    },

    /* 文件状态改变, 失去焦点 */
    chooseCustom() {
      this.$nextTick(this.$refs.multiSelect.blur)
      // this.dataSource.cols = cols
    },

    /* 点击查询 */
    handleSearchDatasetList() {
      this.$refs.table.asp_search()
    },

    /* 格式化时间 */
    formatTimer: function (value) {
      value = String(value)
      const date = new Date(value)
      const y = date.getFullYear()
      let MM = date.getMonth() + 1
      MM = MM < 10 ? '0' + MM : MM
      let d = date.getDate()
      d = d < 10 ? '0' + d : d
      return y + '-' + MM + '-' + d
    },

    /* 点击导出 */
    exportSubsidiary() {
      this.$aspHttps.asp_FileDownload(
        this.$apiConfig.level1cloudstorePathPreFix +
          '/databoard/upload/exportDataList',
        {
          params: {
            proId: this.$route.query.proId,
            startDate: this.exportParams.pickerValue[0],
            endDate: this.exportParams.pickerValue[1],
            status: this.exportParams.fileStatus
          }
        },
        '文件上传情况明细'
      )
    },

    // 日期转换
    changeDate(dateData) {
      if (!dateData) return ''
      const months =
        dateData.getMonth() + 1 < 10
          ? '0' + (dateData.getMonth() + 1)
          : dateData.getMonth() + 1
      const days =
        dateData.getDate() < 10 ? '0' + dateData.getDate() : dateData.getDate()
      return dateData.getFullYear() + '-' + months + '-' + days
    },

    /* 获取颜色值 */
    getColor(e) {
      if (e.key === 'theme_color') {
        this.commonColor = e.newValue
      }
    }
  },
  mounted() {
    const startDate = this.$route.query.startDate
    const endDate = this.$route.query.endDate
    const status = this.$route.query.status
    const pName = this.$route.query.name
    const proId = this.$route.query.proId

    // 获取唯一id
    if (proId) {
      this.table.searchForm.params.proId = proId
    }

    // 获取状态
    if (status) {
      this.fileStatus = parseInt(status)
    }

    // 获取时间
    if (endDate && startDate) {
      this.pickerValue = [startDate, endDate]
    }

    // 获取省份名字
    this.pName = pName

    // 获取主色调
    this.commonColor = sessionStorage.getItem('theme_color')

    // 监听sessionStorage的setitem事件
    window.addEventListener('setItemEvent', this.getColor)
  },

  destroyed() {
    window.removeEventListener('setItemEvent', this.getColor)
  }
}
</script>

<style lang="scss">
.lookall {
  height: 100%;
  .child {
    height: 30px;
    margin-right: 10px;
    margin-bottom: 8px;
    border: 0;
    margin-top: 8px;
  }

  .pageheadera {
    padding: 10px;
    .el-page-header__content {
      font-size: 14px;
    }
    >>> .el-page-header__content {
      font-size: 14px;
    }
  }
}
</style>
