<!--
 * @User: JOJO
 * @FilePath: \netshoposmweb\micro-wb-system\src\views\home\index.vue
-->
/** * 首页模块 * 初始化默认入口 */
<template>
  <!-- :style="homeHeight"  -->
  <div class="webbas" style="position: relative">
    <div
      class="home"
      style="position: absolute; left: 0; right: 0; top: 0; bottom: 0"
    >
      <iframe
        v-if="smartDataPage"
        ref="iframeSmartDataPage"
        style="width: 100%; height: 100%"
        :src="smartDataPage"
        frameborder="0"
      ></iframe>
    </div>

    <!-- 修改店铺订单量弹窗 -->
    <modificationOrderQuantity
      :dialogVisible="dialogVisible"
      @configSuccess="configSuccess"
      @changeShow="(val) => dialogVisible = val"
    />
  </div>
</template>

<script>
import modificationOrderQuantity from '../../components/modificationOrderQuantity/index.vue'
export default {
  name: 'Home',
  components: {
    modificationOrderQuantity
  },
  data: function () {
    return {
      homeHeight: { height: '500px' },
      smartDataPage: null,
      dialogVisible: false
    }
  },
  beforeRouteEnter(to, from, next) {
    // 更新首页导航条
    next((vm) => {
      // vm.$main_tools.store.commit('breadcrumbApp/setCurrentPath', [{ title: vm.$pageConfig.homePage.titleName }])
      // vm.$main_tools.store.commit('app/setCurrentPageName', to.path)
      // vm.$main_tools.store.commit('app/setShowRouterUrl', { gotoUrl: to.path })
    })
  },
  beforeRouteUpdate(to, from, next) {
    // console.log('beforeRouteUpdate', to, from, this)
    next()
  },
  beforeRouteLeave(to, from, next) {
    // console.log('beforeRouteLeave', to, from, this)
    next()
  },
  created: function () {
    const height = `${document.documentElement.clientHeight - 90 - 48 - 25}px`
    this.homeHeight = {
      height: height
    }
    this.initSmartDataPage()
  },
  watch: {
    $route(to, from) {
      // console.log('micro-wb-system breadcrumb route to', to)
      // console.log('micro-wb-system breadcrumb route from', from)
    }
  },
  mounted() {
    window.addEventListener('message', this.getMsgFromIframe, false)
  },
  methods: {
    /* 向iframe发送消息 */
    configSuccess() {
      const iframeSmartDataPage = this.$refs.iframeSmartDataPage
      iframeSmartDataPage.contentWindow.postMessage({ updateStore: true }, window.location.origin)
    },

    /* 接收iframe传来的消息 */
    getMsgFromIframe(e) {
      if (!e.data) {
        return
      }

      // 通过Iframe控制框架进行菜单跳转的，可传递menuId进行控制对应菜单跳转
      if (JSON.stringify(e.data).indexOf('{') === -1) {
        this.$main_tools.menuTreeUtil.openMenuPageByIdOrUrl(e.data)
        return
      }

      const data = JSON.parse(JSON.stringify(e.data))

      // 接收Iframe子应用项目---接收修改订单量操作
      if (data && data.pageType && data.pageType === 'changeStore') {
        this.dialogVisible = true
      }

      // 接收Iframe子应用项目---接收导出表格操作
      if (data && data.pageType && data.pageType === 'derivedForm') {
        const options = data
        this.$aspHttps.asp_FileDownload(
          this.$apiConfig.level1cloudstorePathPreFix +
          '/databoard/export/exportOrderData',
          {
            params: {
              businessType: options.businessType,
              dateType: options.dateType,
              endDate: options.endDate,
              indexType: options.indexType,
              proId: options.proId,
              startDate: options.startDate
            }
          },
          '导出表格'
        )
      }
    },

    initSmartDataPage() {
      // 省公司
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.level1cloudstorePathPreFix +
            '/databoard/page/getPageId',
          { type: 'index' }
        )
        .then((res) => {
          if (res.status === '200') {
            const { pageId } = res.data
            this.smartDataPage = this.$projectConfig.smartDataPage.replace(
              '{code}',
              pageId
            )
          } else {
            this.$message(res.message)
          }
        })
    }
  }
}
</script>
<style lang="scss"></style>
