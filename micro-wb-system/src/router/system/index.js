/**
 * *内置路由
 * Created by TurboC on 2021/03/12
 */
export const system = [
  {
    path: '/system/role',
    title: '系统角色管理',
    name: 'Role',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/role/role'),
    meta: {
      titleLevel: 1,
      title: '系统角色管理',
      keepAlive: true
    }
  },
  {
    path: '/system/roleForDept',
    title: '机构角色管理',
    name: 'RoleForDept',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/role/roleForDept'),
    meta: {
      titleLevel: 1,
      title: '机构角色管理',
      keepAlive: true
    }
  },
  {
    path: '/system/newAuthResource',
    title: '系统角色分配权限',
    name: 'NewAuthResource',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/role/newAuthResourceTab'),
    // component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/role/newAuthResource'),
    meta: {
      titleLevel: 1,
      title: '分配权限',
      fromCache: true
    }
  },
  {
    path: '/system/newAuthResourceForDept',
    title: '机构角色分配权限',
    name: 'NewAuthResourceForDept',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/role/newAuthResourceTab'),
    // component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/role/newAuthResource'),
    meta: {
      titleLevel: 1,
      title: '分配权限',
      fromCache: true
    }
  },
  {
    path: '/system/dept',
    title: '机构管理',
    name: 'Dept',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/dept/dept'),
    meta: {
      titleLevel: 1,
      title: '机构管理',
      keepAlive: true
    }
  },
  {
    path: '/system/deptForDept',
    title: '部门管理',
    name: 'DeptConfigure',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/dept/deptConfigure'),
    meta: {
      titleLevel: 1,
      title: '机构详情',
      fromCache: true
    }
  },
  {
    path: '/system/deptDetailConfigure',
    title: '机构详情',
    name: 'DeptDetailConfigure',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/dept/deptDetailConfigure'),
    meta: {
      titleLevel: 1,
      title: '机构详情',
      fromCache: true
    }
  },
  {
    path: '/system/log',
    title: '日志管理',
    name: 'log',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/log/list'),
    meta: {}
  },
  {
    path: '/system/staff/log',
    title: '账号登录日志',
    name: 'staffLog',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/staff/staffLog'),
    meta: {}
  },
  {
    path: '/system/staff',
    title: '系统成员管理',
    name: 'Staff',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/staff/staff'),
    meta: {
      titleLevel: 1,
      title: '系统成员管理',
      keepAlive: true
    }
  },
  {
    path: '/system/staffForDept',
    title: '部门成员管理',
    name: 'StaffForDept',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/staff/staffForDept'),
    meta: {
      titleLevel: 1,
      title: '部门成员管理',
      keepAlive: true
    }
  },
  {
    path: '/system/staffForDept2',
    title: '用户列表',
    name: 'StaffForDept2',
    component: () => import(/* webpackChunkName: "userlist" */ '@/views/system/staff/staffForDept2'),
    meta: {
      titleLevel: 1,
      title: '用户列表',
      keepAlive: true
    }
  },
  {
    path: '/system/agreementManage',
    title: '协议管理',
    name: 'agreementManage',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/agreementManage'),
    meta: {}
  },
  {
    path: '/system/resources',
    title: '资源列表',
    name: 'resources',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/resources/resources'),
    meta: {}
  },
  {
    path: '/system/resourcesForHis',
    title: '资源历史操作查询',
    name: 'resourcesForHis',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/resources/resourcesForHis'),
    meta: {}
  },
  // system/paramsConfig
  {
    path: '/system/paramsConfig',
    title: '参数配置',
    name: 'paramsConfig',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/paramsConfig/list'),
    meta: {}
  },
  {
    path: '/notice',
    title: '公告管理列表',
    name: 'notice',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/notice/index'),
    meta: {},
    children: [
      {
        path: '/system/annoucement/manager/audit',
        title: '我审核的',
        name: 'AuditNotice',
        component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/notice/audit'),
        meta: {}
      },
      {
        path: '/system/annoucement/manager/create',
        title: '我创建的',
        name: 'CreateNotice',
        component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/notice/create'),
        meta: {}
      },
      {
        path: '/notice/add',
        title: '公告创建',
        name: 'NoticeAdd',
        component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/notice/add'),
        meta: {}
      },
      {
        path: '/notice/detail',
        title: '公告详情',
        name: 'NoticeDetail',
        component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/notice/detail'),
        meta: {}
      },
      {
        path: '/notice/simpleDetail',
        title: '普通权限公告详情',
        name: 'simpleDetail',
        component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/notice/simpleDetail'),
        meta: {}
      },
      {
        path: '/system/annoucement/read',
        title: '公告查询',
        name: 'QueryList',
        component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/notice/query-list'),
        meta: {}
      }
    ]
  },
  {
    path: '/system/calendar',
    title: '节假日设置',
    name: 'calendar',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/calendar/calendar.vue'),
    meta: {}
  },
  {
    path: '/system/dict',
    title: '字典表维护',
    name: 'Dictionary',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/dict/list'),
    meta: {}
  },
  {
    path: '/system/virtualGroup',
    title: '虚拟组管理',
    name: 'VirtualGroup',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/virtualGroup/virtualGroup'),
    meta: {}
  },
  {
    path: '/system/virtualGroupEdit',
    title: '虚拟组新增修改',
    name: 'VirtualGroupEdit',
    component: () => import(/* webpackChunkName: "systemConfig" */ '@/views/system/virtualGroup/editVirtualGroup'),
    meta: {}
  },
  {
    path: '/system/theme',
    title: '主题设置',
    name: 'Theme',
    component: () => import('@/views/system/theme/list'),
    meta: {}
  },
  {
    path: '/system/businsess-obj',
    title: '业务对象',
    name: 'businsess-obj',
    component: () => import(/* webpackChunkName: "business-obj" */ '@/views/system/business-obj/index'),
    meta: {},
    children: [
      {
        path: '/system/ida',
        title: '业务实体',
        name: 'BusinessIda',
        component: () => import('@/views/system/business-obj/ida/listModelClass'),
        meta: {}
      },
      {
        path: '/system/business-object',
        title: '业务对象',
        name: 'BussinessObject',
        component: () => import('@/views/system/business-obj/business-object/businessObjectTab'),
        meta: {}
      }
    ]
  },
  {
    path: '/system/menuHelpInfoQuery',
    title: '菜单帮助信息查询',
    name: 'menuHelpInfoQuery',
    component: () => import('@/views/system/helpInfoConfig/menuHelpInfo/index'),
    meta: {}
  },
  {
    path: '/business-object/edit-objet',
    title: '业务对象编辑',
    name: 'EditObject',
    component: () => import('@/views/system/business-obj/business-object/form/edit-object'),
    meta: {}
  },
  {
    path: '/system/cacheManage/redisCacheManage',
    title: 'Redis缓存管理',
    name: 'redis-cache',
    component: () => import('@/views/system/cacheManage/redisCacheManage'),
    meta: {}
  },
  {
    path: '/system/pageHelpInfoQuery',
    title: '页面帮助信息查询',
    name: 'PageHelpInfoQuery',
    component: () => import('@/views/system/helpInfoConfig/pageHelpInfo/pageHelpInfoList.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/system/fieldHelpInfoList',
    title: '字段帮助信息查询',
    name: 'FieldHelpInfoList',
    component: () => import('@/views/system/helpInfoConfig/pageHelpInfo/comm/fieldHelpInfoList.vue'),
    meta: {}
  }
]

export default system
