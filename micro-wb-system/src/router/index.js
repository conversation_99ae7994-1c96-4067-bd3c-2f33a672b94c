/** Created by TurboC on 2021/03/12 **/
/** Change by <PERSON><PERSON> on 2024/11/06 **/

import Vue from 'vue'
import VueRouter from 'vue-router'
// import Home from './home'
import System from './system'
import Iframe from './iframe'
import Home from './home'
import Tenant from './tenant'
import Management from './management'
import Pagedata from './pagedata'
Vue.use(VueRouter)
const routes = [Iframe, ...System, ...Home, ...Tenant, ...Management, ...Pagedata]
// 创建路由
const router = new VueRouter({
  // base: window.__POWERED_BY_QIANKUN__ ? '/system/' : '/subapp/system/',
  // mode: 'history',
  routes: routes
})

// export default routes
export default router

