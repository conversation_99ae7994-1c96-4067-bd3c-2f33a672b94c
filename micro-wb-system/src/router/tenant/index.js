/**
 * *多租户路由
 * Created by TurboC on 2021/03/12
 */
export const tenantRouter = [
  {
    path: '/tenant/manage',
    title: '租户管理',
    name: 'tenantManage',
    component: () => import(/* webpackChunkName: "tenantRouter" */ '@/views/tenant/manage/index'),
    meta: {
      titleLevel: 1,
      title: '租户管理'
    }
  },
  {
    path: '/tenant/administrator',
    title: '租户管理员管理',
    name: 'administrator',
    component: () => import(/* webpackChunkName: "tenantRouter" */ '@/views/tenant/administrator/index'),
    meta: {
      titleLevel: 1,
      title: '租户管理员管理'
    }
  },
  {
    path: '/tenant/newAuthResource',
    title: '租户角色分配权限',
    name: 'NewTenantAuthResource',
    component: () => import(/* webpackChunkName: "tenantRouter" */ '@/views/tenant/manage/newAuthResourceTab'),
    meta: {
      titleLevel: 1,
      title: '分配权限',
      fromCache: true
    }
  }
]

export default tenantRouter
