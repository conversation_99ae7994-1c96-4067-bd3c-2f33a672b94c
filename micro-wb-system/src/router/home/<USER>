/**
 * *内置路由
 * Created by TurboC on 2021/03/12
 */

// 首页
export const homeRouter = [
  // {
  //   path: '/',
  //   redirect: '/home'
  // },
  // {
  //   path: '/',
  //   redirect: to => {
  //     // console.log('redirect==》》to', to)
  //     // console.log('redirect==》》store', store, store.state.update)
  //     // store.commit('setUpdate', '/home')
  //     // 方法接收 目标路由 作为参数
  //     // return 重定向的 字符串路径/路径对象
  //     return '/home'
  //   }
  // },
  {
    path: '/home',
    title: '首页',
    name: 'home',
    component: () => import(/* webpackChunkName: "homeRouter" */ '@/views/home/<USER>'),
    meta: {
      titleLevel: 1,
      title: '首页'
    }
  }
]

export default homeRouter
