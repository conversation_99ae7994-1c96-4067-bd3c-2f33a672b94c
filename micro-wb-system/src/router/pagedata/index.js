import pagedataindicators from '@/views/pagedata/indicators/index'
import pagedatastore from '@/views/pagedata/store/index'
import pagedataorder from '@/views/pagedata/order/index'

export const pagedata = [
  {
    path: '/',
    redirect: to => {
      // console.log('redirect==》》to', to)
      // console.log('redirect==》》store', store, store.state.update)
      // store.commit('setUpdate', '/home')
      // 方法接收 目标路由 作为参数
      // return 重定向的 字符串路径/路径对象
      return '/pagedata/indicators'
    }
  },
  {
    path: '/pagedata/indicators',
    title: '关键数据指标',
    name: 'pagedataindicators',
    iframeComponent: pagedataindicators,
    // component: () => import('@/views/pagedata/indicators/index'),
    meta: {
      // titleLevel: 1,
      title: '关键数据指标'
      // keepAlive: true
    }
  },
  {
    path: '/pagedata/store',
    meta: {
      title: '店铺数据'
    },
    iframeComponent: pagedatastore,
    name: 'pagedatastore'
    // component: () => import('@/views/pagedata/store/index')
  },

  {
    path: '/pagedata/order',
    name: 'pagedataorder',
    iframeComponent: pagedataorder,
    // component: () => import('@/views/pagedata/order/index'),
    meta: {
      title: '订单数据'
    }
  }
]

export default pagedata
