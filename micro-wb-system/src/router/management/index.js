/*
 * @User: J<PERSON><PERSON><PERSON>
 * @FilePath: \netshoposmweb\micro-wb-system\src\router\management\index.js
 */
/**
 * *内置路由
 * Created by TurboC on 2021/03/12
 */
export const management = [
  // {
  //   path: '/',
  //   redirect: to => {
  //     // console.log('redirect==》》to', to)
  //     // console.log('redirect==》》store', store, store.state.update)
  //     // store.commit('setUpdate', '/home')
  //     // 方法接收 目标路由 作为参数
  //     // return 重定向的 字符串路径/路径对象
  //     return '/management/fileuploadstatus/index'
  //   }
  // },
  {
    path: '/management/staffForDept2',
    title: '用户列表',
    name: 'staffForDept2',
    component: () => import('@/views/management/staff/staffForDept2'),
    meta: {
      // titleLevel: 1,
      title: '用户列表'
      // keepAlive: true
    }
  },
  {
    path: '/management/myaccount/index',
    title: '我的账号',
    name: 'myaccount',
    component: () => import('@/views/management/myaccount/index'),
    meta: {
      // titleLevel: 1,
      title: '我的账号'
      // keepAlive: true
    }
  },

  {
    path: '/management/viewconfiguration/index',
    meta: {
      title: '视图配置'
    },
    name: 'viewconfiguration',
    component: () => import('@/views/management/viewconfiguration/index')
  },

  {
    path: '/management/fileuploadstatus/index',
    name: 'fileuploadstatusIndex',
    component: () => import('@/views/management/fileuploadstatus/index'),
    meta: {
      title: '文件上传情况'
    }
  },

  {
    path: '/management/fileuploadstatus/lookall',
    name: 'fileuploadstatusLookall',
    component: () => import('@/views/management/fileuploadstatus/lookall'),
    meta: {
      title: '文件上传情况-查看全部'
    }
  },

  {
    path: '/management/shopmanage',
    name: 'shopmanage',
    component: () => import('@/views/management/shopmanage'),
    meta: {
      title: '店铺管理'
    },
    children: [
      {
        path: '/management/shopmanage/onlineshop',
        name: 'onlineshop',
        component: () => import('@/views/management/shopmanage/onlineshop/index'),
        meta: {
          title: '线上店管理'
        }
      },
      {
        path: '/management/shopmanage/shopoverview',
        name: 'shopoverview',
        component: () => import('@/views/management/shopmanage/shopoverview/shopoverview.vue'),
        meta: {
          title: '店铺概况'
        }
      },
      {
        path: '/management/shopmanage/shopactivereport',
        name: 'shopactivereport',
        component: () => import('@/views/management/shopmanage/shopactivereport/shopactivereport.vue'),
        meta: {
          title: '店铺活跃报表'
        }
      },
      {
        path: '/management/shopmanage/shopcommodity',
        name: 'shopcommodity',
        component: () => import('@/views/management/shopmanage/shopcommodity/shopcommodity.vue'),
        meta: {
          title: '店铺商品报表'
        }
      },
      {
        path: '/management/shopmanage/shopfensforms',
        name: 'shopfensforms',
        component: () => import('@/views/management/shopmanage/shopfensforms/shopfensforms.vue'),
        meta: {
          title: '粉丝留存报表'
        }
      },
      {
        path: '/management/shopmanage/shopstatus',
        name: 'shopstatus',
        component: () => import('@/views/management/shopmanage/shopstatus/shopstatus.vue'),
        meta: {
          title: '线上店状态管理'
        }
      },
      {
        path: '/management/shopmanage/shopchannelinfo',
        name: 'shopchannelinfo',
        component: () => import('@/views/management/shopmanage/shopchannelinfo/shopchannelinfo.vue'),
        meta: {
          title: '线上店渠道信息管理'
        }
      },
      {
        path: '/management/shopmanage/shopmanager',
        name: 'shopmanager',
        component: () => import('@/views/management/shopmanage/shopmanager/shopmanager.vue'),
        meta: {
          title: '店长管理'
        }
      }
    ]
  },

  {
    path: '/management/entryapp',
    name: 'entryapp',
    component: () => import('@/views/management/entryapp'),
    meta: {
      title: '营业厅入驻APP管理'
    },
    children: [
      {
        path: '/management/entryapp/storemanagement',
        name: 'storemanagement',
        component: () => import('@/views/management/entryapp/storemanagement/reviewandedit/reviewandedit.vue'),
        meta: {
          title: '厅店审核单管理'
        }
      },
      {
        path: '/management/entryapp/configmanagement',
        name: 'configmanagement',
        component: () => import('@/views/management/entryapp/storemanagement/configmanagement/configmanagement.vue'),
        meta: {
          title: '厅店配置管理'
        }
      },
      {
        path: '/management/entryapp/configmanagement/nearby',
        name: 'nearby',
        component: () => import('@/views/management/entryapp/storemanagement/configmanagement/nearby.vue'),
        meta: {
          title: '附近厅推荐方案配置'
        }
      },
      {
        path: '/management/entryapp/message_notice',
        name: 'message_notice',
        component: () => import('@/views/management/entryapp/storemanagement/message_notice/message_notice.vue'),
        meta: {
          title: '短信通知配置'
        }
      },
      {
        path: '/management/entryapp/notice_record',
        name: 'notice_record',
        component: () => import('@/views/management/entryapp/storemanagement/message_notice/notice_record.vue'),
        meta: {
          title: '短信下发记录'
        }
      },
      {
        path: '/management/entryapp/business_scope_config',
        name: 'business_scope_config',
        component: () => import('@/views/management/entryapp/storemanagement/business_scope_config/business_scope_config.vue'),
        meta: {
          title: '营业范围配置'
        }
      },
      {
        path: '/management/entryapp/import_result',
        name: 'import_result',
        component: () => import('@/views/management/entryapp/storemanagement/import_result/import_result.vue'),
        meta: {
          title: '文件导入结果'
        }
      },
      {
        path: '/management/entryapp/operatie_manual',
        name: 'operatie_manual',
        component: () => import('@/views/management/entryapp/storemanagement/operatie_manual/operatie_manual.vue'),
        meta: {
          title: '操作手册下载管理'
        }
      },
      {
        path: '/management/entryapp/picture_config',
        name: 'picture_config',
        component: () => import('@/views/management/entryapp/storemanagement/picture_config/picture_config.vue'),
        meta: {
          title: '自定义门店图片管理'
        }
      },
      {
        path: '/management/entryapp/appoint_batch_config',
        name: 'appoint_batch_config',
        component: () => import('@/views/management/entryapp/storemanagement/appoint_batch_config/index.vue'),
        meta: {
          title: '预约取号批量配置'
        }
      },
      {
        path: '/management/entryapp/appointment_manage',
        name: 'appointment_manage',
        component: () => import('@/views/management/entryapp/storemanagement/appointment_manage'),
        meta: {
          title: '预约取号配置管理'
        }
      }
      // {
      //   path: '/management/entryapp/appointment_manage',
      //   name: 'appointment_manage',
      //   component: () => import('@/views/management/entryapp/storemanagement/appointment_manage'),
      //   meta: {
      //     title: '预约取号配置管理'
      //   }
      // }
    ]
  },

  {
    path: '/management/ordermanage',
    name: 'ordermanage',
    component: () => import('@/views/management/ordermanage'),
    meta: {
      title: '订单管理报表'
    },
    children: [
      {
        path: '/management/ordermanage/orderdetails',
        name: 'orderdetails',
        component: () => import('@/views/management/ordermanage/orderdetails/orderdetails.vue'),
        meta: {
          title: '订单明细数据报表'
        }
      },
      {
        path: '/management/ordermanage/appointmentdetails',
        name: 'appointmentdetails',
        component: () => import('@/views/management/ordermanage/appointmentdetails/appointmentdetails.vue'),
        meta: {
          title: '预约单明细数据报表'
        }
      },
      {
        path: '/management/ordermanage/chargebackdetails',
        name: 'chargebackdetails',
        component: () => import('@/views/management/ordermanage/chargebackdetails/chargebackdetails.vue'),
        meta: {
          title: '退单明细数据报表'
        }
      }
    ]
  },
  {
    path: '/management/systemmanage',
    name: 'systemmanage',
    component: () => import('@/views/management/systemmanage'),
    meta: {
      title: '系统管理'
    },
    children: [
      {
        path: '/management/systemmanage/indicatorconfig',
        name: 'indicatorconfig',
        component: () => import('@/views/management/systemmanage/indicatorconfig/indicatorconfig.vue'),
        meta: {
          title: '指标配置'
        }
      },
      {
        path: '/management/systemmanage/exception',
        name: 'exception',
        component: () => import('@/views/management/systemmanage/indicatorconfig/exception.vue'),
        meta: {
          title: '异常类型管理'
        }
      },
      {
        path: '/management/systemmanage/contacts',
        name: 'contacts',
        component: () => import('@/views/management/systemmanage/contacts/contacts.vue'),
        meta: {
          title: '联系人管理'
        }
      },
      {
        path: '/management/systemmanage/insertcode',
        name: 'insertcode',
        component: () => import('@/views/management/systemmanage/insertcode/insertcode.vue'),
        meta: {
          title: '插码管理'
        }
      },
      {
        path: '/management/systemmanage/analysismodel',
        name: 'analysismodel',
        component: () => import('@/views/management/systemmanage/analysismodel/analysismodel.vue'),
        meta: {
          title: '分析模型管理'
        }
      },
      {
        path: '/management/systemmanage/modeldetail',
        name: 'modeldetail',
        component: () => import('@/views/management/systemmanage/analysismodel/modeldetail.vue'),
        meta: {
          title: '分析模型管理---详情'
        }
      }
    ]
  },
  {
    path: '/management/storemonitor',
    name: 'storemonitor',
    component: () => import('@/views/management/storemonitor'),
    meta: {
      title: '店铺监控'
    },
    children: [
      {
        path: '/management/storemonitor/lowact',
        name: 'lowact',
        component: () => import('@/views/management/storemonitor/lowact/lowact.vue'),
        meta: {
          title: '低活跃店铺管理'
        }
      },
      {
        path: '/management/storemonitor/abnormal',
        name: 'abnormal',
        component: () => import('@/views/management/storemonitor/abnormal/abnormal.vue'),
        meta: {
          title: '异常店铺管理'
        }
      },
      {
        path: '/management/storemonitor/clearance',
        name: 'clearance',
        component: () => import('@/views/management/storemonitor/clearance/clearance.vue'),
        meta: {
          title: '清退管理'
        }
      }
    ]
  }
  // {
  //   path: '/notice',
  //   title: '公告管理列表',
  //   name: 'notice',
  //   component: () =>
  //     import(/* webpackChunkName: "systemConfig" */ '@/views/notice/index'),
  //   meta: {},
  //   children: [
  //     {
  //       path: '/system/annoucement/manager/audit',
  //       title: '我审核的',
  //       name: 'AuditNotice',
  //       component: () =>
  //         import(/* webpackChunkName: "systemConfig" */ '@/views/notice/audit'),
  //       meta: {}
  //     },
  //   ]
  // }
]

export default management
