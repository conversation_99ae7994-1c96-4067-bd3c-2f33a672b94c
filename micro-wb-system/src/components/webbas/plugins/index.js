import Vue from "vue";
import waveButton from "./waveButton";
import attachmentDownload from "./attachmentDownload";
import loadingBar from "./loadingBar";
let Plugin = {};

let components = {
  WaveButton: waveButton,
  AttachmentDownload: attachmentDownload
};

Vue.prototype.$Loading = loadingBar;

Plugin.install = (Vue, options) => {
  // console.log(options)
  Object.keys(components).forEach(key => {
    Vue.component(key, components[key]);
  });
};

export default Plugin;
