/** * Created by wang<PERSON><PERSON> on 2018/11/15. * 附件下载 * */

<template>
  <div v-if="attachmentFile">
    <a :href="downloadUrl">{{ attachmentFile.fileName }}</a>
  </div>
</template>
<script>
export default {
  // props: ['attachmentFile'],
  props: {
    attachmentFile: {
      type: Object,
      default: null
    }
  },
  computed: {
    downloadUrl: function() {
      // if (this.attachmentFile) { return '' }
      if (this.attachmentFile.attachFileId === null) {
        return "";
      }
      return (
        this.$apiConfig.managerPathPrefix +
        "/attachment/downloadFile?attachFileId=" +
        this.attachmentFile.attachFileId
      );
    }
  }
};
</script>
