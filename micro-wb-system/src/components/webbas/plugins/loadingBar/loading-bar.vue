<template>
  <transition name="fade">
    <div v-show="show" :class="classes" :style="outerStyles">
      <div :class="innerClasses" :style="styles"></div>
    </div>
  </transition>
</template>

<style lang="scss">
.ivu-loading-bar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2000;
  .ivu-loading-bar-inner {
    transition: width 0.2s linear;
  }
  .ivu-loading-bar-inner-color-primary {
    /*background-color: #c3e11f;*/
    background-image: linear-gradient(to right, #56d90f, #fff94f);
  }
}
</style>
<script>
//    import { oneOf } from '../../utils/assist';

const prefixCls = "ivu-loading-bar";

export default {
  props: {
    //            percent: {
    //                type: Number,
    //                default: 0
    //            },
    color: {
      type: String,
      default: "primary"
    },
    failedColor: {
      type: String,
      default: "error"
    },
    height: {
      type: Number,
      default: 2
    }
    //            status: {
    //                type: String,
    //                validator (value) {
    //                    return oneOf(value, ['success', 'error']);
    //                },
    //                default: 'success'
    //            },
    //            show: {
    //                type: Boolean,
    //                default: false
    //            }
  },
  data() {
    return {
      percent: 0,
      //                color: 'primary',
      //                failedColor: 'error',
      // height: 3,
      status: "success",
      show: false
    };
  },
  computed: {
    classes() {
      return `${prefixCls}`;
    },
    innerClasses() {
      return [
        `${prefixCls}-inner`,
        {
          [`${prefixCls}-inner-color-primary`]:
            this.color === "primary" && this.status === "success",
          [`${prefixCls}-inner-failed-color-error`]:
            this.failedColor === "error" && this.status === "error"
        }
      ];
    },
    outerStyles() {
      return {
        height: `${this.height}px`
      };
    },
    styles() {
      let style = {
        width: `${this.percent}%`,
        height: `2px`
      };

      if (this.color !== "primary" && this.status === "success") {
        style.backgroundColor = this.color;
      }

      if (this.failedColor !== "error" && this.status === "error") {
        style.backgroundColor = this.failedColor;
      }

      return style;
    }
  }
};
</script>
