/**
* Created by TurboC on 2019/08/01.
* 重写消息框样式
*/
.webbas {
    // 提示框样式修改
    .el-message-box {
        border-radius: 10px;
        padding-bottom: 26px;
        box-shadow: 8px 8px 8px #444444;
        height: 234px;
        width: 436px;
        box-sizing: border-box;
        position: relative;
    }
    .el-message-box__title {
        font-size: 16px;
        color: #666666;
        margin-top: 5px;
        font-weight: 600;
    }
    .el-message-box__message p {
        font-size: 16px;
        color: #304d67;
        line-height: 26px;
    }
    .el-message-box__header {
        box-sizing: border-box;
        border-bottom: 1px solid #cccc;
        height: 66px;
    }
    .el-message-box__content {
        padding: 26px 34px 26px 34px;
    }
    .el-message-box__btns {
        padding: 5px 24px 0;
        position: absolute;
        right: 0;
        bottom: 26px;
    }
    .el-message-box__status {
        top: auto;
        -webkit-transform: translateY(-50%);
        transform: translateY(0%);
    }
    .el-message-box__btns button:nth-child(2) {
        margin-left: 28px;
    }
    .el-message-box__headerbtn .el-message-box__close {
        height: 18px;
        width: 18px;
    }
    .el-message-box__header .el-message-box__headerbtn {
        right: 24px;
        top: 20px;
    }
}
.el-message-box__message p {
    word-break: break-all;
}


