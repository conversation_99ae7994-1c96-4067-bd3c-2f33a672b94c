/**
* Created by TurboC on 2019/08/01.
* 框架顶部样式
*/
@import "../mixins/mixins";

.asp-header-area {
    color: #fff;
    background: $wb-theme-color;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
    padding-left: 0;
    padding-right: 0;
    box-shadow: none;
    cursor: pointer;
    .el-link {
        color: #ffffff;
        font-size: 16px;
        margin-right: 10px;
        margin-bottom: 3px;
        text-decoration: underline;
    }
    .el-link:hover {
        color: #ffffff;
    }
}

.asp-header-area-left {
    img {
        height: auto;
        max-width: 100%;
        vertical-align: middle;
        margin-left: 15px;
        border: 0;
    }
    strong {
        margin-left: 25px;
        font-size: 18px;
    }
    i {
        font-size: 22px;
    }
}

.asp-header-area-middle {
    li {
        float: left;
        font-size: 14px;
        padding: 12px 8px 0px;
        margin-right: 5px;
        line-height: 24px;
    }
    li.active {
        border-bottom: 1px solid #fff;
    }
}

.asp-header-area-right {
    text-align: right;
    padding-right: 25px;
    .el-dropdown {
        color: #fff;
    }
}

.asp-header-department-css { // z-index: 0(个人中心修改个人信息，默认不可修改)
    position: absolute;
    z-index: 0;
    right: 0
}
