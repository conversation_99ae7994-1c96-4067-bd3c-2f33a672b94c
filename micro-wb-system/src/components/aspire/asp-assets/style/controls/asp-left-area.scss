/**
* Created by TurboC on 2019/08/01.
* 一级菜单高度、一级菜单背景
*/
@import "../mixins/mixins";

.asp-left-area {
    @include main-layout;
    background: #ffffff;
    background-color: #ffffff;
    overflow: hidden;
    overflow-y: auto;
    border: 1px solid #dee8f8;
    z-index: 99;
    .el-menu {
        background: #ffffff;
    }
    .el-submenu .el-menu {
        background: #f3f7fc;
    }
    .el-menu-item, .el-submenu__title {
        height: 40px;
        line-height: 40px;
        font-size: 13px;
        color: #787878;
    }
    .el-submenu__title {
        font-weight: 600;
    }
    .el-menu-item.is-active {
        background-color: #e8eff2;
        color: $wb-theme-color;
        font-weight: 600;
        border-right: 4px solid #3878d2;
    }
    .el-submenu__title:hover, .el-menu-item:hover {
        background-color: #e8eff2;
    }
    .el-menu-item:focus, .el-submenu__title:focus {
        background-color: #e8f0fc;
    }
    ul li {
        border-bottom: 1px solid #ebebeb;
    }
    .el-menu--collapse .hiddeName {
        display: none;
    }
    .el-menu > div > .el-menu-item {
        font-weight: 600;
    }
    .el-submenu__title .el-menu-item {
        /* font-size: 16px; */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        /* white-space: normal; */
        word-break: break-all;
    }
}

// webbas项目菜单
.wb_01, .wb_02, .wb-menu-withicon {
    width: 21px;
    height: 21px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    overflow: hidden;
    vertical-align: middle;
    font-size: 1.4rem;
    display: inline-block;
    line-height: 15px;
    text-align: center;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    margin-right: 3px;
    i:before {
        width: 21px;
        height: 21px;
    }
}
// 項目個性化二級菜單展示按项目个性化控制
.icon-show {
    display: none;
}
.el-icon-arrow-right {
    font-size: 12px;
}
.el-menu-title-span {
    /* font-size: 16px; */
    //white-space: nowrap;
    //overflow: hidden;
    //text-overflow: ellipsis;
    ///* white-space: normal; */
    //word-break: break-all;
}


