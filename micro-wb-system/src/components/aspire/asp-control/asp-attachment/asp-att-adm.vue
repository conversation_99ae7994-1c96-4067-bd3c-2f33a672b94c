/** * Modify by TurboC on 2012/02/17. * 附件上传 * 可传入参数： * 1.attachType
附件提示语相关类型 * 2.groupId 附件组ID * 3.isRequest 是否自动发送请求 *
4.fileList 由父组件直接传入的附件信息，需要设置isRequest属性为false才能使用 */
<template>
  <div>
    <el-upload
      :auto-upload="false"
      :file-list="dataList"
      :on-preview="handlePreview"
      :before-remove="handleBeforeRemove"
      :on-exceed="handleExceed"
      :on-change="handleChange"
      :multiple="multiple"
      :show-file-list="showFlag"
      :limit="1"
      action=""
    >
      <div>
        <el-button class="solid-with-icon-btn attachBtn" icon="el-icon-upload2"
          >选择文件</el-button
        >
        <slot name="right"></slot>
      </div>
      <div slot="tip" class="el-upload__tip">{{ attachTip }}</div>
    </el-upload>
  </div>
</template>

<script>
import aspUtils from '../../asp-api/asp-Utils'
import aspMsgbox from '../../asp-api/asp-Msgbox'
export default {
  name: 'AspAttAdm',
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    attachType: {
      type: String,
      default: ''
    },
    groupId: {
      type: String,
      default: ''
    },
    isRequest: {
      type: Boolean,
      default: true
    },
    fileList: {
      type: Array,
      default () {
        return []
      }
    }
  },
  data() {
    return {
      fileData: new FormData(),
      dataList: [], // 附件信息
      keys: [], // 新增附件的附件ID
      attachTip: '', // 附件提示语
      fileType: '', // 可上传附件类型
      sizeLimit: 0, // 单个附件限制大小
      countLimit: 1, // 可上传附件数量
      hasDelete: false, // 附件是否做过删除操作的判断
      requestFlag: true, // 辨别能否发送请求的标志
      showFlag: true
    }
  },
  computed: {},
  watch: {
    groupId (val) {
      if (this.requestFlag) {
        this.initFile()
      }
    },
    fileList (val) {
      this.dataList = val
    },
    dataList (val) {
      for (let i = 0; i < val.length; i++) {
        // const item = val[i]
        val[i].name = val[i].fileName
        // item.attachFileId = item.attachFileId
      }
    }
  },
  created () {
    this.init()
    this.getFileTypeConfig()
  },
  methods: {
    init () {
      if (this.isRequest === false) {
        this.requestFlag = false
      } else {
        this.initFile()
      }
    },
    // 查询附件类型
    getFileTypeConfig () {
      const param = {
        attachTypeId: this.attachType
      }
      this.$aspHttps
        .asp_Post(aspUtils.asp_ProductAttUrl('/attachment/viewType'), param)
        .then(response => {
          if (this.$reponseStatus(response)) {
            const fileType = response.data
            this.fileType = fileType.fileSuffixLimit
              ? fileType.fileSuffixLimit.replace(/,/g, '、')
              : '无限制'
            this.sizeLimit = fileType.singleSizeLimit
            this.countLimit = fileType.attachCountLimit
            this.attachTip = `您可上传的文件类型：${
              this.fileType
            },单个附件大小限${this.sizeLimit / 1024 / 1024}M,限上传${
              this.countLimit
            }个文件`
          }
        })
    },
    // 下载单个附件
    handlePreview(file) {
      const url = aspUtils.asp_ProductAttUrl('/attachment/downloadFile?attachFileId=') + file.attachFileId
      // 新开窗口下载
      this.$aspHttps.asp_ExportGetOpen(url)
    },
    // 根据ID删除单个附件
    handleBeforeRemove (file, fileList) {
      aspMsgbox.confirm(this, `是否删除附件：${file.name}？`, function () {
        const attachFileId = file.attachFileId
        const url = aspUtils.asp_ProductAttUrl(
          '/attachment/deleteByAttachFileId'
        )
        this.$aspHttps
          .asp_Post(url, { attachFileId: attachFileId })
          .then(response => {
            if (this.$reponseStatus(response)) {
              for (let i = 0; i < this.dataList.length; i++) {
                if (this.dataList[i].attachFileId === attachFileId) {
                  this.dataList.splice(i, 1)
                  break
                }
              }
              for (let i = 0; i < this.keys.length; i++) {
                if (this.keys[i] === attachFileId) {
                  this.keys.splice(i, 1)
                  break
                }
              }
              this.hasDelete = true
              this.$emit('update:fileId', this.keys.join(','))
              this.$message.success('删除附件成功')
            }
          })
      })
      return false
    },
    handleChange (file, fileList) {
      this.showFlag = false
      this.handleExceed([file.raw], [])
    },
    handleExceed (files, fileList) {
      if (!files || !files.length) {
        return
      }
      if (fileList.length + files.length > this.countLimit) {
        this.$message.error(`最多可以上传${this.countLimit}个附件！`)
        return
      }
      const fileDatas = new FormData()
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        if (file.size === 0) {
          this.$message.error('上传附件内容不能为空！')
          return false
        }
        if (file.size > this.sizeLimit) {
          this.$message.error(
            `上传文件的大小不可超过${this.sizeLimit / 1024 / 1024}M！`
          )
          return false
        }
        const type = file.name.substring(file.name.lastIndexOf('.') + 1)
        const fileTypeFlag = this.fileType.split('、').some(item => {
          return item.toLowerCase() === type.toLowerCase()
        })
        if (!fileTypeFlag) {
          this.$message.error(`不可上传类型为${type}的附件！`)
          return false
        }
        fileDatas.append('file', files[i])
      }

      fileDatas.append('attachTypeId', this.attachType)
      fileDatas.append('attachGroupId', this.groupId)
      this.$fileUpload(
        aspUtils.asp_ProductAttUrl('/attachment/add'),
        fileDatas
      ).then(response => {
        if (this.$reponseStatus(response)) {
          this.requestFlag = false
          const data = response.data
          if (data) {
            const files = []
            files.push(data)
            for (let i = 0; i < files.length; i++) {
              this.keys.push(files[i].attachFileId)
              this.dataList.push(files[i])
            }
            this.$message.success('上传附件成功')
            this.showFlag = true
            this.$emit('update:fileId', this.keys.join(','))
            this.$emit('update:groupId', files[0].attachGroupId)
            const attachGroupIdParam = {
              attachGroupId: files[0].attachGroupId
            }
            // 附件生效接口
            this.$aspHttps
              .asp_Post(
                aspUtils.asp_ProductAttUrl('/attachment/formalGroup'),
                attachGroupIdParam
              )
              .then(response => {
                if (!this.$reponseStatus(response)) {
                  this.$message.error(response.message)
                }
              })
          }
        }
      })
    },
    // 查询正式文件列表
    initFile () {
      if (!this.groupId) {
        this.dataList = []
        return
      }
      const param = {
        attachGroupId: this.groupId
      }
      this.$aspHttps
        .asp_Post(aspUtils.asp_ProductAttUrl('/attachment/list'), param)
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.requestFlag = false
            this.dataList = response.data
          }
        })
      // [{
      //     "attachFileId":"111ef65d9c244e30a1b7d9730bd46556",
      //     "attachFileStatus":"formal",
      //     "attachGroupId":"ea0c21dd99cc493ab9c216f852083368",
      //     "createDate":"2020-03-27T00:00:00",
      //     "fileName":"工作簿1的副本.xlsx",
      //     "fileSaveName":"group1/M00/00/01/CgHLM158EXmAXyn_AAAiETmyoRk69.xlsx",
      //     "fileSize":8721,
      //     "fileType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      // }]
    },
    // 附件是否为空的判断
    asp_Att_IsEmpty () {
      return this.dataList.length === 0
    }
  }
}
</script>
