/** * Modify by TurboC on 2012/02/17. * 附件回显 * 可传入的参数 * 1.groupId
附件组ID,会自动通过组ID发现请求 * 2.fileList 由父组件直接传入的附件信息 */
<template>
  <div>
    <el-link
      v-for="item in dataList"
      :key="item.attachFileId"
      type="primary"
      @click="handleUpload(item.attachFileId)"
    >
      {{ item.fileName }}
    </el-link>
  </div>
</template>

<script>
import aspUtils from '../../asp-api/asp-Utils'
export default {
  name: 'AspAttDetail',
  props: {
    fileList: {
      type: Array,
      default () {
        return []
      }
    },
    groupId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dataList: []
    }
  },
  computed: {},
  watch: {
    fileList (val) {
      this.dataList = val
    },
    groupId (val) {
      this.initFile()
    }
  },
  created () {
    this.init()
  },
  mounted () { },
  methods: {
    init () {
      if (this.groupId) {
        this.initFile()
      }
    },
    // 查询正式文件列表
    initFile () {
      if (!this.groupId) {
        this.dataList = []
        return
      }
      const param = {
        attachGroupId: this.groupId
      }
      this.$aspHttps
        .asp_Post(aspUtils.asp_ProductAttUrl('/attachment/list'), param)
        .then(response => {
          if (this.$reponseStatus(response)) {
            this.dataList = response.data
          }
        })
    },
    handleUpload (fileId) {
      const url = aspUtils.asp_ProductAttUrl('/attachment/downloadFile?attachFileId=') + fileId
      // 新开窗口下载
      this.$aspHttps.asp_ExportGetOpen(url)
    }
  }
}
</script>
