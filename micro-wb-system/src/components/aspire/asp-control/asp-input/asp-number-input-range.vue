/** * Modify by t<PERSON><PERSON><PERSON> on 2020/07/22. * 价格区间组件 */
<template>
  <div>
    <el-input-number
      v-model="start"
      :min="0"
      :precision="2"
      :controls="false"
      :placeholder="startPlaceholder"
      type="number"
      style="width: 45%"
    >
    </el-input-number>
    <div
      style="display: inline-table;text-align: center;width: calc(10% - 8px);margin-top: 5px;"
    >
      {{ rangeSeparator }}
    </div>
    <el-input-number
      v-model="end"
      :min="0"
      :precision="2"
      :controls="false"
      :placeholder="endPlaceholder"
      type="number"
      style="width: 45%"
    >
    </el-input-number>
  </div>
</template>

<script>
export default {
  name: 'AspNumberInputRange',
  props: {
    startPrice: {
      type: [Number, String],
      default: undefined
    },
    endPrice: {
      type: [Number, String],
      default: undefined
    },
    startPlaceholder: {
      type: String,
      default: ''
    },
    endPlaceholder: {
      type: String,
      default: ''
    },
    rangeSeparator: {
      type: String,
      default: '~'
    }
  },
  data() {
    return {
      // 初始化为undefined，避免输入框默认为0
      start: undefined,
      end: undefined
    }
  },
  computed: {},
  watch: {
    startPrice(val) {
      this.start = val
    },
    start(val) {
      this.$emit('update:startPrice', val)
    },
    endPrice(val) {
      this.end = val
    },
    end(val) {
      this.$emit('update:endPrice', val)
    }
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>
