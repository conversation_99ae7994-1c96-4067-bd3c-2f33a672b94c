/** * Modify by TurboC on 2012/02/17. * 范围时间组件 */
<template>
  <div ref="datePickerDiv"
       style="display: flex;">
    <el-date-picker v-model="start"
                    :placeholder="startPlaceholder"
                    :clearable="clearable"
                    :picker-options="startPickerOptions"
                    type="date"
                    value-format="yyyy-MM-dd"
                    :style="dateWidth" />
    <div style="display: inline-table;text-align: center;"
         :style="labelWidth"> {{ rangeSeparator }} </div>
    <el-date-picker v-model="end"
                    :placeholder="endPlaceholder"
                    :clearable="clearable"
                    :picker-options="endPickerOptions"
                    type="date"
                    value-format="yyyy-MM-dd"
                    :style="dateWidth" />
  </div>
</template>

<script>
export default {
  name: 'AspDateRange',
  props: {
    clearable: {
      type: Boolean,
      default: true
    },
    startDate: {
      type: [Number, String],
      default: undefined
    },
    endDate: {
      type: [Number, String],
      default: undefined
    },
    startPlaceholder: {
      type: String,
      default: '开始时间'
    },
    endPlaceholder: {
      type: String,
      default: '结束时间'
    },
    rangeSeparator: {
      type: String,
      default: '至'
    },
    labelWith: {
      type: Number,
      default: 8
    }
  },
  data () {
    return {
      start: '',
      end: '',
      // 限制开始时间的选择区域
      startPickerOptions: {
        disabledDate: time => {
          if (this.end) {
            return time.getTime() > new Date(this.end).getTime()
          }
        }
      },
      // 限制结束时间的选择区域
      endPickerOptions: {
        disabledDate: time => {
          if (this.start) {
            return (
              time.getTime() <
              new Date(this.start).getTime() - 24 * 60 * 60 * 1000
            )
          }
        }
      }
    }
  },
  computed: {
    dateWidth () {
      if (this.labelWith) {
        const dateWidth = this.labelWith / 2
        return `width: calc(50% - ${dateWidth}px)`
      }
      return 'width: 45%'
    },
    labelWidth () {
      if (this.labelWith) {
        return `width: ${this.labelWith}px`
      }
      return 'width: calc(10% - 8px)'
    }
  },
  watch: {
    startDate (val) {
      this.start = val
    },
    start (val) {
      this.$emit('update:startDate', val)
    },
    endDate (val) {
      this.end = val
    },
    end (val) {
      this.$emit('update:endDate', val)
    }
  },
  created () { },
  mounted () { },
  methods: {}
}
</script>
