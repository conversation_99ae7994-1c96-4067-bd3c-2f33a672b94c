/** * Created by TurboC on 2019/08/12 * 分页查询组件 */
<template>
  <div class="list-area-css">
    <div class="list-top-btn">
      <slot name="header"></slot>
    </div>
    <div class="list-table-css">
      <el-table v-loading="tableLoading"
                ref="multipleTable"
                :data="listData"
                :border="true"
                :default-sort="defaultSort"
                stripe
                @sort-change="sortTable"
                @select="(selection, row) => $emit('select', selection, row)"
                @select-all="selection => $emit('select-all', selection)">
        <el-table-column v-if="indexSwitch"
                         :width="indexWidth()"
                         :index="indexMethod"
                         label="序号"
                         type="index"
                         align="center">
        </el-table-column>
        <slot></slot>
      </el-table>
    </div>
    <div>
      <slot name="footer"></slot>
    </div>
    <el-pagination v-if="paginationStatus && showPage"
                   :current-page.sync="page"
                   :page-sizes="paginationPageSizes()"
                   :page-size="pageSize"
                   :total="total"
                   :layout="paginationLayout()"
                   background
                   @size-change="handleSizeChange"></el-pagination>
  </div>
</template>

<script>
import aspFontSize from '../../asp-api/asp-Fontsize'
import aspUtils from '../../asp-api/asp-Utils'
export default {
  name: 'AspTable',
  components: {},
  props: {
    url: {
      // 发送的请求
      type: String,
      default: ''
    },
    param: {
      // 请求携带的参数
      type: Object,
      default () {
        return {}
      }
    },
    type: {
      // 如果是3.2规范的需要填'new' 其他可不填
      type: String,
      default: 'old'
    },
    prefix: {
      // URL前缀
      type: String,
      default: 'default'
    },
    indexSwitch: {
      type: Boolean,
      default: true
    },
    data: {
      // 支持组件外传入数据，不调url接口查询 add by tuwenwen 20200615
      type: Array,
      default: null
    },
    showPage: {
      // 组件外传入数据时，是否展示分页 add by tuwenwen 20200615
      type: Boolean,
      default: true
    },
    preLoading: {
      // 组件外传入，是否是否预加载，默认为是
      type: Boolean,
      default: true
    },
    afterHttp: {
      // 组件外传入
      type: Function,
      default: undefined
    }
  },
  data () {
    return {
      listData: [], // 列表数据
      tableLoading: false, // 表格加载中状态
      pageSize: aspUtils.asp_CfgData().asp_PaginationDefaultSize, // 每页展示行数
      page: 1, // 当前页码
      total: 0, // 总条数
      sortName: this.param.sortName ? this.param.sortName : '', // 排序字段
      order: 'desc', // 降序升序
      paginationStatus: false, // 分页状态
      searchParam: {},
      defaultSort: {
        prop: this.param.defaultSortName ? this.param.defaultSortName : '', // 默认排序字段
        order: this.param.defaultSortOrder
          ? this.param.defaultSortOrder === 'descending'
            ? 'descending'
            : 'ascending'
          : '' // 排序规则
      },
      multipleSelection: []
    }
  },
  computed: {},
  watch: {
    page () {
      this.searchPage()
    },
    data (val) {
      this.listData = val
      this.total = val.length
      this.paginationStatus = this.showPage
    }
  },
  created () { },
  mounted () {
    this.storeParam()
    this.preLoading && this.searchPage()
  },
  methods: {
    indexWidth () {
      return aspFontSize.asp_ColNumSize()
    },
    paginationLayout () {
      return 'total, prev, pager, next, sizes, jumper'
    },
    paginationPageSizes () {
      return [10, 20, 30, 40, 50]
    },
    storeParam () {
      for (const key in this.param) {
        this.searchParam[key] = this.param[key]
      }
    },
    handleSizeChange (val) {
      this.pageSize = val
      this.asp_search()
    },
    searchPage () {
      const param = {
        page: this.page,
        rows: this.pageSize,
        sortName: this.sortName,
        order: this.order
      }

      const newParam = Object.assign({}, this.param)
      for (const key in newParam) {
        if (newParam[key]) {
          // newParam[key] = newParam[key]
        }
      }

      if (this.type === 'old') {
        param.params = newParam
      } else {
        Object.assign(param, newParam)
      }
      // 外部没有传入数据时，调用url发送请求
      if (!this.data) {
        this.tableLoading = true
        this.$aspHttps.asp_Post(this.prefix + this.url, param).then(response => {
          if (aspUtils.asp_ReponseStatus(response)) {
            this.total = parseInt(response.total)
            this.listData =
              (Object.prototype.hasOwnProperty.call(response, 'rows')
                ? response.rows
                : response.data) || []
            // 暂时解决问题，后续优化
            this.url === '/dict/listType' &&
              this.$emit('renderListData', this.listData)
            this.paginationStatus = this.listData.length > 0
          }
          this.tableLoading = false
          // 必须给最新的行数据才能渲染成功 add bu tuwenwen
          if (this.multipleSelection.length > 0) {
            this.$nextTick(() => {
              this.$emit('getData', this.listData)
            })
          }
          // 表格搜索成功回调
          this.$emit('tableSearchSuccess', this.listData)
          this.clearSelection()
        })
      }
    },
    // 排序操作
    sortTable (column) {
      if (
        column.column &&
        column.column.labelClassName &&
        column.column.order
      ) {
        this.order = column.column.order
          ? column.column.order.replace('ending', '')
          : ''
        this.param.order = this.order
        this.sortName = column.column.labelClassName
        this.param.sortName = this.sortName
      } else {
        this.order = ''
        this.sortName = ''
      }
      this.asp_search()
    },
    // 重建序号
    indexMethod (index) {
      return index + this.pageSize * (this.page - 1) + 1
    },
    // 对外提供查询列表操作
    asp_search () {
      if (this.page === 1) {
        this.searchPage()
      } else {
        this.page = 1
      }
    },
    // 对外提供清空操作(由组件实现查询内容区内容恢复初始状态)
    asp_reset () {
      for (const key in this.searchParam) {
        this.param[key] = this.searchParam[key]
      }
      this.asp_search()
    },
    // 对外提供的取消勾选/勾选指定行 add by tuwenwen
    toggleRowSelection (row, flag) {
      flag && this.multipleSelection.push(row)
      this.$refs.multipleTable.toggleRowSelection(row, flag)
    },
    // 清除勾选数据
    clearSelection () {
      this.$nextTick(() => {
        this.$emit('clear-selection')
      })
    }
  }
}
</script>
