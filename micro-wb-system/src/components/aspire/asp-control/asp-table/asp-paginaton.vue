/** * Created by TurboC on 2019/08/12 * 分页查询组件 */

<template>
  <div v-if="paginationStatus" class="list-pagination-css">
    <el-pagination
      :current-page.sync="page"
      :page-sizes="selectPageSizes"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next, sizes, jumper"
      background
      @size-change="handleSizeChange"
    ></el-pagination>
  </div>
</template>

<script>
export default {
  name: 'AspPaginaton',
  props: {
    url: {
      type: String,
      default: ''
    },
    method: {
      type: Function,
      default() {
        return {}
      }
    },
    param: {
      type: Object,
      default() {
        return {}
      }
    },
    tableLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageSize: 10, // 每页展示行数
      page: 1, // 当前页码
      total: 0, // 总条数
      sortName: '', // 排序字段
      order: 'desc', // 降序升序
      paginationStatus: false, // 分页状态
      selectPageSizes: [10, 20, 30, 40, 50]
    }
  },
  computed: {},
  watch: {
    page() {
      this.search()
    }
  },
  created() {},
  mounted() {
    this.search()
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val
      this.resetTable()
    },
    search() {
      const pageParam = {
        page: this.page,
        rows: this.pageSize,
        sortName: this.sortName,
        order: this.order
        // params: this.param
      }
      const reqParam = Object.assign(pageParam, this.param)
      this.$emit('update:tableLoading', true)
      this.method(this.url, reqParam).then(response => {
        if (this.$reponseStatus(response)) {
          if (Object.prototype.hasOwnProperty.call(response, 'records')) {
            this.total = response.records
          }
          if (Object.prototype.hasOwnProperty.call(response, 'total')) {
            this.total = response.total
          }
          if (this.total > 0) {
            this.paginationStatus = true
          } else {
            this.paginationStatus = false
          }
          let dataList = []
          if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
            dataList = response.rows
          }
          if (Object.prototype.hasOwnProperty.call(response, 'data')) {
            dataList = response.data
          }
          this.$emit('input', dataList) // 调用父组件方法
        }
        this.$emit('update:tableLoading', false)
      })
    },
    // 对外提供重置列表操作
    resetTable() {
      if (this.page === 1) {
        this.search()
      } else {
        this.page = 1
      }
    },
    // 对外提供排序操作
    sortTable(column) {
      if (
        column.column &&
        column.column.labelClassName &&
        column.column.order
      ) {
        this.order = column.column.order
          ? column.column.order.replace('ending', '')
          : ''
        this.sortName = column.column.labelClassName
      } else {
        this.order = ''
        this.sortName = ''
      }
      this.resetTable()
    },
    // 重建序号
    indexMethod(index) {
      return index + this.pageSize * (this.page - 1) + 1
    }
  }
}
</script>
