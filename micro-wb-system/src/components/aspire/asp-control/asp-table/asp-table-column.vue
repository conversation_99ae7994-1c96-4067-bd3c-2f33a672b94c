/** * Created by TurboC on 2019/08/12 * 分页查询组件 */
<template>
  <el-table-column
    v-if="$scopedSlots.default"
    :width="colWidth"
    :min-width="minWidth"
    :label="label"
    :prop="prop"
    :sortable="sortableNew"
    :label-class-name="sortKey"
    :type="type"
    :align="align"
    :selectable="selectable"
    :show-overflow-tooltip="showToolTip"
  >
    <template slot-scope="scope">
      <slot :scope="scope"></slot>
    </template>
  </el-table-column>

  <el-table-column
    v-else
    :formatter="initFormatter"
    :width="colWidth"
    :min-width="minWidth"
    :label="label"
    :prop="prop"
    :sortable="sortableNew"
    :label-class-name="sortKey"
    :type="type"
    :align="align"
    :selectable="selectable"
    :show-overflow-tooltip="showToolTip"
  ></el-table-column>
</template>

<script>
import aspDateTime from '../../asp-api/asp-Datetime'
import aspFontSize from '../../asp-api/asp-Fontsize'
import aspUtils from '../../asp-api/asp-Utils'
export default {
  name: 'AspTableColumn',
  props: {
    type: {
      type: String,
      default: ''
    },
    align: {
      type: String,
      default: 'left'
    },
    label: {
      type: String,
      default: ''
    },
    prop: {
      type: String,
      default: ''
    },
    sortKey: {
      type: String,
      default: ''
    },
    width: {
      type: [Number, String],
      default: ''
    },
    minWidth: {
      type: [Number, String],
      default: ''
    },
    formatter: {
      type: Function,
      default: function() {
        return false
      }
    },
    selectable: {
      type: Function,
      default: function() {
        return true
      }
    },
    dateFormat: {
      type: Boolean,
      default: false
    },
    timeFormat: {
      type: Boolean,
      default: false
    },
    provinceFormat: {
      type: Boolean,
      default: false
    },
    sortable: {
      type: Boolean,
      default: false
    },
    showToolTip: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      colWidth: '',
      sortableNew: '' // sortable中间变量
    }
  },
  computed: {},
  watch: {},
  created() {
    this.initSort()
    this.initWidth()
  },
  mounted() {},
  methods: {
    initSort() {
      // 默认不排序，当外部传入sortKey时，按后台返回的数据顺序排序，否则自排序
      if (this.sortKey) {
        this.sortableNew = 'custom' // 此设置是为了让表格本身的排序失效，直接使用后台返回的数据顺序
      } else {
        this.sortableNew = false
      }
    },
    initWidth() {
      let width = ''
      // if (this.width === '') {
      if (this.width === '' && this.minWidth === '') {
        const label = this.label.length
        const sort = this.sortKey !== '' ? 1 : 0
        width = aspFontSize.asp_ColFontSize(label, sort)
      } else {
        width = this.width
      }
      if (this.dateFormat) {
        const dateWidth = aspFontSize.asp_ColFontSize(5)
        width = dateWidth > width ? dateWidth : width
      }
      if (this.timeFormat) {
        const dateWidth = aspFontSize.asp_ColFontSize(10)
        width = dateWidth > width ? dateWidth : width
      }
      this.colWidth = width
    },
    initFormatter(row, column, cellValue, index) {
      if (this.dateFormat) {
        return aspDateTime.asp_FormatTimeDd(cellValue, index)
      }
      if (this.timeFormat) {
        return aspDateTime.asp_FormatTimeSs(cellValue, index)
      }
      if (this.provinceFormat) {
        return aspUtils.asp_FormatProvince(cellValue)
      }
      const result = this.formatter(row, column, cellValue, index)
      return result === false ? cellValue : result
    }
  }
}
</script>
