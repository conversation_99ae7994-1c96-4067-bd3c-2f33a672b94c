/** * Modify by TurboC on 2012/02/17. * 二次封装对话框 */
<template>
  <el-dialog
    v-if="visible"
    ref="aspDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
    :title="title"
    :width="width"
    :before-close="handleClose"
    :destroy-on-close="true"
    :append-to-body="appendToBody"
  >
    <slot></slot>
    <div slot="footer">
      <slot name="footer"></slot>
    </div>
    <div slot="footer">
      <center>
        <slot name="footer-center"></slot>
      </center>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AspDialog',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '50%'
    },
    appendToBody: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.aspDialog.$el.scrollTop = 0
        })
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleClose() {
      this.$emit('beforeCloseDialog', {})
      this.$emit('update:visible', false)
    }
  }
}
</script>
