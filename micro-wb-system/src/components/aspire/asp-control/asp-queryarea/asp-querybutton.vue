<!--asp-btns: [
    {isAuth:false, text:'查看', method:'search', icon:'el-icon-search'},
    {isAuth:true, hasAuth:'wb_110104', text:'修改', method:'reset', icon:'el-icon-search}
] -->

<template>
  <el-col :span="12">
    <el-form-item class="list-query-area-btn-css">
      <el-button
        v-hasAuth="doAuthArr({ btnCodeArr: item.authList })"
        v-for="(item, i) in aspBtns"
        :key="item.text + i"
        :icon="item.icon"
        :class="item.class"
        size="small"
        @click="btnClick(item.method)"
        >{{ item.text }}
      </el-button>
    </el-form-item>
  </el-col>
</template>

<script>
export default {
  name: 'AspQueryButton',
  props: {
    spanNum: {
      type: String,
      default: ''
    },
    aspBtns: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      propName1: ''
    }
  },
  computed: {},
  watch: {
    propName1() {
      this.$emit('input', this.propName1)
    }
  },
  mounted() {
    this.propName1 = this.propName
  },
  methods: {
    search() {},
    // 鉴权
    doAuthArr(opt) {
      const routeMeta = this.$route.meta
      return { menuId: routeMeta.id, btnCodeArr: opt.btnCodeArr }
    },
    btnClick(method) {
      this.$emit('aspQueryClick', { method })
    }
  }
}
</script>
