<template>
  <el-col :span="6">
    <el-form-item :label="showName">
      <el-input
        v-model.trim="subPropName"
        placeholder
        @keyup.enter.native="search(true)"
      ></el-input>
    </el-form-item>
  </el-col>
</template>

<script>
export default {
  name: 'AspQueryInput',
  props: {
    spanNum: {
      type: String,
      default: ''
    },
    showName: {
      type: String,
      default: ''
    },
    propName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      subPropName: ''
    }
  },
  watch: {
    subPropName() {
      this.$emit('input', this.subPropName)
    },
    propName() {
      this.subPropName = this.propName
    }
  },
  // mounted () {
  //     this.subPropName = this.propName
  // },
  methods: {
    search() {}
  }
}
</script>
