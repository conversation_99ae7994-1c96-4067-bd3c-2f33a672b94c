<template>
  <section class="list-query-area-css">
    <el-form ref="searchModel" :inline="true" :model="searchModel">
      <el-row v-for="(row, i) in newQueryList" :key="i">
        <el-col v-for="(col, j) in row" :key="j" :span="6">
          <el-form-item :label="col.label">
            <el-input
              v-model.trim="col.prop"
              placeholder
              @keyup.enter.native="search(true)"
            ></el-input>
            {{ col }}
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
                    <el-form-item prop="description" label="参数描述:">
                        <el-input
                            v-model.trim="table.searchForm.description"
                            placeholder
                            name="orgName"
                            @keyup.enter.native="search(true)"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item class="list-query-area-btn-css">
                        <el-button
                            v-hasAuth="doAuth({btnCode:'wb_110102'})"
                            type="primary"
                            size="small"
                            icon="el-icon-search"
                            class="solid-with-icon-btn"
                            @click="search(true)"
                        >查询
                        </el-button>
                        <el-button
                            size="small"
                            icon="el-icon-refresh"
                            class="hollow-with-icon-btn"
                            @click="reset('searchForm')"
                        >重置
                        </el-button>
                    </el-form-item>
                </el-col> -->
      </el-row>
    </el-form>
  </section>
</template>

<script>
const threeCol = 3
const fourCol = 4
export default {
  name: 'AspQuery',
  props: {
    aspQuerys: {
      type: Array,
      default: () => []
    },
    searchModel: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      newQueryList: []
    }
  },
  mounted() {
    // this.productNewQuerys()
  },
  created() {
    this.productNewQuerys()
  },
  methods: {
    spanValue() {
      for (const query in this.aspQuerys) {
        if (query.type === 'date') {
          return threeCol
        }
      }
      return fourCol
    },
    productNewQuerys() {
      this.newQueryList = []
      const total = this.aspQuerys.length
      const colNum = this.spanValue()
      const rowNum =
        total % colNum ? parseInt(total / colNum) + 1 : total / colNum
      for (let i = 0; i < rowNum; i++) {
        const rowData = []
        for (let j = 0; j < colNum && j < total; j++) {
          const index = i * colNum + j
          rowData.push(this.aspQuerys[index])
        }
        this.newQueryList.push(rowData)
      }
    },
    bindPropName(propName) {
      this.searchModel.hasOwnProperty(propName)
    }
  }
}
</script>
