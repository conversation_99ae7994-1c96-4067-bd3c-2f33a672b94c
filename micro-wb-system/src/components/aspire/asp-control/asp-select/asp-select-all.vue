/** * Modify by TurboC on 2012/02/17. * 单个下拉框组件 自带【全部】选项
列表查询可用 */
<template>
  <el-select v-model='code'
             :disabled='disabled'
             :placeholder='placeholder'
             :filterable="filterable"
             clearable
             @change='changeMethod'>
    <el-option v-if="isShowAll"
               :label='defAllSelName'
               :value='defAllSelValue'></el-option>
    <el-option v-for='item in codeData'
               :key='item.code'
               :label='item.name'
               :value='item.code'>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'AspSelectAll',
  model: {
    prop: 'defSelValue'
  },
  props: {
    // 字典表的请求类型(与codeList互斥)
    codeType: {
      type: String,
      default: ''
    },
    // 下拉选项列表(与codeType互斥)
    codeList: {
      type: Array,
      default () {
        return []
      }
    },
    // '全部'选项的key值
    defAllSelValue: {
      type: String,
      default: ''
    },
    // 默认选中的选项key值
    defSelValue: {
      type: String,
      default: '' // this.defAllSelValue  修改TurboC
    },
    // '全部'选项的name值
    defAllSelName: {
      type: String,
      default: '全部'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    isShowAll: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: ''
    },
    filterable: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      code: this.defSelValue,
      codeData: []
    }
  },
  computed: {},
  watch: {
    code (val) {
      this.$emit('input', val)
    },
    defSelValue (val) {
      this.code = val
    },
    codeList (val) {
      this.codeData = val
    }
  },
  created () { },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      if (this.codeList.length > 0) {
        this.codeData = this.codeList
      }
      if (this.codeType.length > 0) {
        this.$aspHttps
          .asp_Post(this.$apiConfig.supportPathPrefix + '/dict/getType', {
            code: this.codeType
          })
          .then(response => {
            if (this.$reponseStatus(response)) {
              this.codeData = response.data.dicts
            }
          })
      }
    },
    changeMethod (val) {
      this.$emit('change', val)
    }
  }
}
</script>
