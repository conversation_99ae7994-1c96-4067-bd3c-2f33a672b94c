/** * Modify by TurboC on 2012/02/17. * 单个下拉框组件 自带【请选择】选项
通常在新增界面使用 */
<template>
  <el-select v-model='code'
             :disabled='disabled'
             clearable
             @change='changeMethod'>
    <el-option v-if='needDefValue'
               :label='defOptSelName'
               :value='defOptSelValue'></el-option>
    <el-option v-for='item in codeData'
               :key='codeKey(item)'
               :label='showName(item)'
               :value='codeKey(item)'>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'AspSelectOpt',
  model: {
    prop: 'defSelValue'
  },
  props: {
    // 下拉选项列表(与codeType互斥)
    codeList: {
      type: Array,
      default () {
        return []
      }
    },
    // 字典表的请求类型(与codeList互斥)
    codeType: {
      type: String,
      default: ''
    },
    // 默认选中的选项key值
    defSelValue: {
      type: String,
      default: '' // this.defOptSelValue 修改TurboC
    },
    // '--请选择--'选项的key值
    defOptSelValue: {
      type: String,
      default: ''
    },
    // '--请选择--'选项的name值
    defOptSelName: {
      type: String,
      default: '--请选择--'
    },
    // 不需要添加附件信息
    needDefValue: {
      type: Boolean,
      default: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      code: this.defSelValue,
      codeData: []
    }
  },
  computed: {},
  watch: {
    code (val) {
      this.$emit('input', val)
    },
    defSelValue (val) {
      this.code = val
    },
    codeList (val) {
      this.codeData = val
    }
  },
  created () { },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      if (this.codeList.length > 0) {
        this.codeData = this.codeList
      }
      if (this.codeType.length > 0) {
        this.codeData = this.$aspUtils.getCodeValueByType(this, this.codeType)
      }
    },
    changeMethod (val) {
      this.$emit('change', val)
    },
    showName: function (item) {
      if (Object.prototype.hasOwnProperty.call(item, 'codeValue')) {
        return item.codeValue
      } else if (Object.prototype.hasOwnProperty.call(item, 'name')) {
        return item.name
      }
      return ''
    },
    codeKey: function (item) {
      if (Object.prototype.hasOwnProperty.call(item, 'codeKey')) {
        return item.codeKey
      } else if (Object.prototype.hasOwnProperty.call(item, 'code')) {
        return item.code
      }
      return ''
    }
  }
}
</script>
