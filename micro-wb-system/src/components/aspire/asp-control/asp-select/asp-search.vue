/** * * 单个下拉框远程搜索组件 从服务器搜索数据，输入关键字进行查找 */
<template>
  <el-select
    :remote-method="remoteMethod"
    :loading="loading"
    :style="selectStyle"
    v-model.trim="code"
    filterable
    remote
    placeholder="支持模糊搜索匹配"
    @change="changeMethod"
  >
    <el-option
      v-for="item in option"
      :key="item.code"
      :label="item.name"
      :value="item.code"
    >
      {{ getRealLabel(item) }}
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'AspSearch',
  model: {
    prop: 'defSelValue'
  },
  props: {
    // 默认展示的name值
    initName: {
      type: String,
      default: ''
    },
    // 默认展示的选项key值
    defSelValue: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    },
    selectStyle: {
      type: Object,
      default() {
        return { width: '70%' }
      }
    }
  },
  data() {
    return {
      code: this.defSelValue,
      option: [],
      loading: false
    }
  },
  computed: {},
  watch: {
    initName(val) {
      this.remoteMethod(val)
    },
    code(val) {
      this.$emit('input', val)
    },
    defSelValue(val) {
      this.code = val
    }
  },
  created() {
    this.remoteMethod(this.initName)
  },
  mounted() {},
  methods: {
    remoteMethod(val) {
      if (val) {
        this.loading = true
        this.$aspHttps.asp_Post(this.url, { condition: val }).then(response => {
          if (this.$reponseStatus(response)) {
            this.option = response.data
          }
          this.loading = false
        })
      } else {
        this.option = []
      }
    },
    changeMethod(val) {
      this.$emit('change', { val, option: this.option })
    },
    getRealLabel(item) {
      let label = item.name
      label = item.alias ? (label + '/' + item.alias) : label
      return label
    }
  }
}
</script>
