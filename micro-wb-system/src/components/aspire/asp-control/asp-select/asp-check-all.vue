/** * Modify by TurboC on 2012/02/17. * 带全选功能的复选框 * 可传入参数： *
1.v-model 双向绑定，对应复选框label的字符串，如 '100,351,577' * 2.codeList
复选框选项数组 格式 * { * name:name, 复选框展示的值 * code:code, 复选框label的值
* disabled:true 复选框是否要禁选 * } * 3.disabledCode
被禁选且要被选中的值，要禁选的复选框label的字符串，与数组内的disabled可同用(注意：通过此字段禁选的复选框会被选中)
* 4.disabledNoCheck 禁用是否需要勾选 * */
<template>
  <div class="check-20-css">
    <el-checkbox
      :indeterminate="isIndeterminate"
      v-model="checkAll"
      :disabled="disabledAll ? true : false"
      @change="handleCheckAll"
      >全选
    </el-checkbox>
    <el-checkbox-group v-model="codeKey" @change="handleCheckOne">
      <el-checkbox
        v-for="item in allList"
        :label="item.code"
        :key="item.code"
        :disabled="disabledAll ? true : item.disabled ? true : false"
        :title="item.title"
      >
        {{ item.name }}
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script>
export default {
  name: 'AspCheckAll',
  model: {
    prop: 'value'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    // 复选框的选项LIST
    codeList: {
      type: Array,
      default() {
        return []
      }
    },
    // 被禁选且要被选中的值
    disabledCode: {
      type: String,
      default: ''
    },
    // 是否全部禁选
    disabledAll: {
      type: Boolean,
      default: false
    },
    // 禁用是否需要勾选
    disabledNoCheck: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      codeKey: [], // 被选中的LIST
      allList: [], // 全部选项的LIST
      allCheckList: [], // 全部选项的LIST(去除被禁选的)
      checkAll: false, // 是否全选
      isIndeterminate: false // 全选的图标
    }
  },
  computed: {
    // 被禁选且要被选中的LIST
    disabledList() {
      return this.disabledCode ? this.disabledCode.split(',') : []
    }
  },
  watch: {
    value(val) {
      // console.log(val)
      this.initCheck()
    },
    codeList(val) {
      // console.log(val)
      this.init()
    },
    disabledCode(val) {
      // console.log(val)
      this.init()
    },
    disabledNoCheck(val) {
      // console.log(val)
      this.init()
    }
  },
  created() {
    this.init()
  },
  mounted() {},
  methods: {
    init() {
      const codeList = [].concat(this.codeList)
      const allCheckList = []
      for (let i = 0; i < codeList.length; i++) {
        if (this.disabledList.indexOf(codeList[i].code) !== -1) {
          codeList[i].disabled = true
        }
        if (!codeList[i].disabled) {
          allCheckList.push(codeList[i].code)
        }
      }
      this.allList = codeList
      this.allCheckList = allCheckList
      this.initCheck()
    },
    initCheck() {
      // 业务中实际被选中的值
      const checkList = this.value ? this.value.split(',') : []
      // disabledNoCheck判断禁选是否需要选中,true:不选中禁选选项；false:带上要求被选中的禁选
      this.codeKey = this.disabledNoCheck
        ? checkList
        : checkList.concat(this.disabledList)
      // 是否全选
      this.checkAll = checkList.length === this.allCheckList.length
      // 全选的图标
      this.isIndeterminate =
        checkList.length > 0 && checkList.length < this.allCheckList.length
    },
    handleCheckAll(val) {
      if (val) {
        // 全选(去除带选中状态的禁选)
        this.codeKey = this.allCheckList.concat(this.disabledList)
        this.$emit('input', this.allCheckList.join(','))
      } else {
        // 全不选(去除带选中状态的禁选)
        this.codeKey = this.disabledList
        this.$emit('input', '')
      }
      this.isIndeterminate = false
    },
    handleCheckOne(value) {
      const set1 = new Set(value)
      const set2 = new Set(this.disabledList)
      // 被选中的(除掉被禁选的)
      const checkList = [...new Set([...set1].filter(x => !set2.has(x)))]
      // 被选中的数量
      const checkedCount = checkList.length
      // 是否全选
      this.checkAll = checkedCount === this.allCheckList.length
      // 全选的图标
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.allCheckList.length
      // 重置v-model的值
      this.$emit('input', checkList.join(','))
    }
  }
}
</script>
