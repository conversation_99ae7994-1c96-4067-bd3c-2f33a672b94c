/** * Created by aspire on 2019/08/12. * 按钮 */
<template>
  <el-button
    :icon="icon"
    :disabled="disabled"
    class="solid-with-icon-btn"
    size="small"
    @click="click()"
    >{{ name }}
  </el-button>
</template>

<script>
export default {
  name: 'AspBtnSolid',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  mounted() {},
  created() {},
  methods: {
    click() {
      this.$emit('click', {})
    }
  }
}
</script>
