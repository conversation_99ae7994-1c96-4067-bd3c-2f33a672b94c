/** * Created by aspire on 2019/08/12. * 按钮 */
<template>
  <el-button
    :icon="icon"
    :title="title"
    type="text"
    class="add-table-btn-css"
    @click="click()"
  >
  </el-button>
</template>

<script>
export default {
  name: 'AddTableBtn',
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  mounted() {},
  created() {},
  methods: {
    click() {
      this.$emit('click')
    }
  }
}
</script>
