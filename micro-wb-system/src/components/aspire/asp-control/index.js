// import { version, Form } from "element-ui";
// import { version } from "element-ui";
import aspQueryInput from './asp-queryarea/asp-queryinput'
import aspQueryButton from './asp-queryarea/asp-querybutton'
import aspBtnSolid from './asp-button/asp-btn-solid'
import aspBtnHollow from './asp-button/asp-btn-hollow'
import aspBtnText from './asp-button/asp-btn-text'
import addTableBtn from './asp-button/add-table-btn'

import aspTable from './asp-table/asp-table'
import aspTableColumn from './asp-table/asp-table-column'
import aspPaginaton from './asp-table/asp-paginaton'
import aspAttAdm from './asp-attachment/asp-att-adm'
import aspAttDetail from './asp-attachment/asp-att-detail'
import aspDialog from './asp-dialog/asp-dialog'
import aspDateRange from './asp-date/asp-date-range'
import aspCheckAll from './asp-select/asp-check-all'
import aspSelectAll from './asp-select/asp-select-all'
import aspSelectOpt from './asp-select/asp-select-opt'
import aspSearch from './asp-select/asp-search'
import aspNumberInputRange from './asp-input/asp-number-input-range'

const components = [
  aspPaginaton,
  aspTable,
  aspTableColumn,
  aspAttAdm,
  aspAttDetail,
  aspDialog,
  aspDateRange,
  aspCheckAll,
  aspSelectAll,
  aspSelectOpt,
  aspQueryInput,
  aspQueryButton,
  aspBtnSolid,
  aspBtnHollow,
  aspBtnText,
  addTableBtn,
  aspSearch,
  aspNumberInputRange
]

const install = Vue => {
  window.$version = { vue: Vue.version, ele: '1.0' }
  components
    .filter(v => typeof v !== 'function')
    .forEach(v => Vue.component(v.name, v))
}

export default install
