/**
 * Created by TurboC on 2020/04/03
 */

const aspMsgBox = {}
/**
 * 2次确认框封装
 * @param arg       必传 this
 * @param message   确认框提示语
 * @param callback  确认后执行函数
 */
aspMsgBox.confirm = (arg, message, callback) => {
  arg
    .$confirm(message, '确认', {
      closeOnClickModal: false,
      // closeOnPressEscape: false,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      beforeClose: (action, instance, done) => {
        done()
        if (action === 'confirm') {
          callback.call(arg, instance)
        }
      }
    })
    .then(action => {})
    .catch(() => {})
}

/**
 * 消息提示封装
 * @param arg       必传 this
 * @param message   确认框提示语
 * @param callback  确认后执行函数
 */
aspMsgBox.alert = (arg, message, callback) => {
  arg.$alert(message, '提示', {
    closeOnClickModal: false,
    closeOnPressEscape: true,
    confirmButtonText: '关闭',
    callback: action => {
      callback.call(arg)
    }
  })
}

export default aspMsgBox
