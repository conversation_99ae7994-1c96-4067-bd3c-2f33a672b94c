/**
 * Created by TurboC on 2019/08/12
 */
import { Message } from 'element-ui'
import router from '@/router'

const aspUtil = {}

// 配置数据
const gCfgData = require('../asp-config/asp-cfg-datas.json')
aspUtil.asp_CfgData = () => {
  return gCfgData
}

// 生成附件相关地址url
aspUtil.asp_ProductAttUrl = apiName => {
  if (process.env.NODE_ENV !== 'development') {
    return (
      gCfgData.asp_Http_CurPathPrefix +
      gCfgData.asp_Http_Attachment_PathPrefix +
      apiName
    )
  } else {
    return (
      gCfgData.asp_Http_Proxy +
      gCfgData.asp_Http_Attachment_PathPrefix +
      apiName
    )
  }
}

// 替换掉代理属性
aspUtil.asp_ProductHttpUrl = urlStr => {
  if (process.env.NODE_ENV === 'production') {
    urlStr = urlStr.replace(gCfgData.asp_Http_Proxy, '')
  }
  return urlStr
}

/**
 * 计算文件大小
 * @param size
 * @returns fileSize
 */
aspUtil.asp_ParseFileSize = size => {
  let fileSize
  if (size != null) {
    const gb = 1024 * 1024 * 1024
    const mb = 1024 * 1024
    const kb = 1024
    if (size / gb >= 1) {
      // 如果当前Byte的值大于等于1GB
      fileSize = parseFloat(size / gb).toFixed(2) + 'GB'
    } else if (size / mb >= 1) {
      // 如果当前Byte的值大于等于1MB
      fileSize = parseFloat(size / mb).toFixed(2) + 'MB'
    } else if (size / kb >= 1) {
      // 如果当前Byte的值大于等于1KB
      fileSize = parseFloat(size / kb).toFixed(2) + 'KB'
    } else {
      fileSize = size + 'B'
    }
  }
  return fileSize
}

// proviceId格式化转换省名称
aspUtil.asp_FormatProvince = (proviceId) => {
  if (!proviceId) {
    return null
  }
  return gCfgData.asp_Provice_List[proviceId]
}

/**
 * 去掉样式中不必要的px
 * @param val 只能是数字或字符串
 * @returns {number}
 */
aspUtil.asp_DeletePx = val => {
  let num = 0
  if (typeof val === 'number') {
    num = val
  } else if (typeof val === 'string') {
    if (val.substring(val.length - 2, val.length) === 'px') {
      num = parseInt(val.substring(0, val.length - 1))
    }
    if (!isNaN(val)) {
      num = parseInt(val)
    }
  }
  return num
}

// 将对象转为数组
aspUtil.asp_ObjToArr = obj => {
  const arr = []
  for (let i = 0; i < obj.dicts.length; i++) {
    const item = obj.dicts[i]
    arr.push({
      codeKey: item.code,
      codeValue: item.name
    })
  }
  return arr
}

// 封装响应判断,closeMessage关闭错误信息提示，默认开启:true
aspUtil.asp_ReponseStatus = (response, closeMessage = true) => {
  let retValue = false
  if (gCfgData && gCfgData.asp_Http_RetCodeObj) {
    // 有多种返回码名称的情况
    for (let i = 0; i < gCfgData.asp_Http_RetCodeObj.length; i++) {
      const retObj = gCfgData.asp_Http_RetCodeObj[i]
      if (response && Object.prototype.hasOwnProperty.call(response, retObj.name)) {
        retValue = response[retObj.name].toString() === retObj.value
        // 排除不提标的返回码
        if (
          gCfgData.asp_Http_RetCodeTip.indexOf(retObj.value) <= -1 &&
          !retValue &&
          closeMessage
        ) {
          if (
            Object.prototype.hasOwnProperty.call(response, 'message') ||
            Object.prototype.hasOwnProperty.call(response, 'msg')
          ) {
            Message.error(response.message || response.msg)
          } else {
            Message.error('后端未提供message数据!')
          }
        }
        return retValue
      }
    }
  }
  return retValue
}

// js对象转url参数
aspUtil.objToparam = function (param, key, encode) {
  if (param === null) return ''
  var paramStr = ''
  var t = typeof param
  if (t === 'string' || t === 'number' || t === 'boolean') {
    paramStr +=
      '&' +
      key +
      '=' +
      (encode === null || encode ? encodeURIComponent(param) : param)
  } else {
    for (var i in param) {
      var k =
        key === null
          ? i
          : key + (param instanceof Array ? '[' + i + ']' : '.' + i)
      paramStr += aspUtil.objToparam(param[i], k, encode)
    }
  }
  return paramStr
}

aspUtil.asp_httPrefix = () => {
  const httpPrefix = window.location.href.split('://')[0]
  return httpPrefix + '://'
}


/**
 * 通过codeType从字典数据中获取需要的数据
 * @param vim
 * @param codeType
 * @returns {[]|*[]}
 */
aspUtil.getCodeValueByType = (vim, codeType) => {
  const codeData = aspUtil.getCodeData(vim)
  if (!codeData) {
    return []
  }
  const localCodeData = codeType ? codeData[codeType] : codeData
  return localCodeData
}

/**
* 从缓存中获取字典数据
* @param vim
* @param o
* @returns {{}|''|any}
*/
aspUtil.getCodeData = (vim, o = true) => {
  const codeData = vim.$aspUtil.getCodeDataObject(vim)
  if (codeData) {
    return codeData
  } else {
    vim.$message.warning('获取字典数据失败，请重新尝试该操作！')
    return {}
  }
}

// 字典表转义
aspUtil.formatDict = (code, dictList) => {
  if (!code) {
    return ''
  }
  let dictStr = code
  if (dictList && dictList.length > 0) {
    dictList.forEach((item) => {
      if (item.code === code) {
        dictStr = item.name
      }
    })
  }
  return dictStr
}

/**
 * 初始化性别数据
 * @param param
 * @returns {string}
 */
aspUtil.initSex = param => {
  if (!param) {
    return ''
  }
  switch (param.toString()) {
    case '0':
      return ''
    case '1':
      return '1'
    case '2':
      return '2'
    default:
      return ''
  }
}

// 过滤代理url（通过location.href形式请求）
aspUtil.getRealUrl = (url, _t) => {
  if (process.env.NODE_ENV !== 'development') {
    _t.$apiConfig && _t.$apiConfig.proxyConfig && _t.$apiConfig.proxyConfig.forEach(item => {
      url = url.replace(item.localProxy, item.nginxProxy)
    })
  }
  return url
}

// 时间戳 {params} string 1514780542000
aspUtil.timestamp = date => {
  const str = date.replace(/年|月|日| |-|:/g, '-')
  const arr = str.split('-')
  const datum = new Date(
    Date.UTC(arr[0], arr[1] - 1, arr[2], arr[3] - 8, arr[4], arr[5])
  )
  return datum.getTime() / 1000 // 单位秒
}

// 字符串去掉空格
aspUtil.trim = str => {
  return str.replace(/(^\s*)|(\s*$)/g, '')
}

// 构造table 参数
aspUtil.constrTableParam = (vm, option, pagination) => {
  const params = []
  const defaultConfig = {
    params: [],
    rows: pagination.pageSize,
    page: pagination.page,
    sortName: 'asc'
  }

  for (const key in option) {
    if (Object.prototype.hasOwnProperty.call(option, key)) {
      const obj = {}
      obj[key] = option[key]
      params.push(obj)
    } else {
      vm.$message({
        message: '参数不全',
        type: 'warning'
      })
    }
  }
  return Object.assign(defaultConfig, { params: params })
}

// 判断字符串是否为空
aspUtil.checkMaxLength = (rule, value, callback, source, options) => {
  const maxLength = rule.maxLength
  if (value === undefined || value === null) {
    callback()
  } else {
    const sum = value.length
    if (sum > maxLength) {
      callback(new Error('不能超过' + maxLength + '个字符'))
    }
    callback()
  }
}

// 判断本地缓存是否有地域属性
aspUtil.getDivisions = (_t) => {
  let res = localStorage.division
  if (!res) {
    const result = _t.$aspHttps.asp_AsyncPost(
      _t.$apiConfig.supportPathPrefix + '/division/listDivisionTree'
    )
    res = JSON.parse(result || '').data
    localStorage.division = JSON.stringify(res)
  } else {
    res = JSON.parse(res)
  }
  return res
}

/**
 * 格式化性别数据
 * @param param
 * @returns {string}
 */
aspUtil.formatSex = param => {
  if (!param) {
    return ''
  }
  switch (param.toString()) {
    case '0':
      return ''
    case '1':
      return '男'
    case '2':
      return '女'
    default:
      return ''
  }
}

/**
 * 初始化性别数据
 * @param param
 * @returns {string}
 */
aspUtil.initSex = param => {
  if (!param) {
    return ''
  }
  switch (param.toString()) {
    case '0':
      return ''
    case '1':
      return '1'
    case '2':
      return '2'
    default:
      return ''
  }
}

// 获取当前路径中指定的参数
aspUtil.getParam = name => {
  const arrParams = window.location.search.substr(1).split('&')
  let rtn = ''
  for (let i = 0; i < arrParams.length; i++) {
    const pair = arrParams[i]
    const arrEnt = pair.split('=')
    const key = arrEnt[0]
    const val = arrEnt[1]
    if (key !== name) continue
    if (rtn === '') {
      rtn = val
    } else {
      rtn += ',' + val
    }
  }
  return rtn
}

/*  1.用正则表达式实现html转码 */
aspUtil.htmlEncodeByRegExp = str => {
  var s = ''
  if (str.length === 0) return ''
  s = str.replace(/&/g, '&amp')
  s = s.replace(/</g, '&lt')
  s = s.replace(/>/g, '&gt')
  s = s.replace(/ /g, '&nbsp')
  // s = s.replace(/\\'/g, '&#39')
  // s = s.replace(/\\'/g, '&quot')
  return s
}

/* 2.用正则表达式实现html解码 */
aspUtil.htmlDecodeByRegExp = str => {
  if (!str) {
    return ''
  }
  var s = ''
  if (str.length === 0) return ''
  s = str.replace(/&amp/g, '&')
  s = s.replace(/&lt/g, '<')
  s = s.replace(/&gt/g, '>')
  s = s.replace(/&nbsp/g, ' ')
  // s = s.replace(/&#39/g, '\'')
  // s = s.replace(/&quot/g, '\\'')
  return s
}

aspUtil.toDefaultPage = (routers, name, route, next) => {
  const len = routers.length
  let notHandle = true
  for (let i = 0; i < len; i++) {
    if (routers[i].name === name && routers[i].redirect === undefined) {
      route.replace({
        name: routers[i].children[0].name
      })
      notHandle = false
      next()
      break
    }
    i++
  }
  if (notHandle) {
    next()
  }
}

aspUtil.getRamNumber = () => {
  var result = ''
  for (var i = 0; i < 16; i++) {
    result += Math.floor(Math.random() * 16).toString(16) // 获取0-15并通过toString转16进制
  }
  // 默认字母小写，手动转大写
  return result.toUpperCase()
}

aspUtil.getUrlQueryString = name => {
  const query = window.location.hash.split('?')
  if (query && query.length === 2) {
    const vars = query[1].split('&')
    for (let i = 0; i < vars.length; i++) {
      var pair = vars[i].split('=')
      if (pair[0] === name) {
        return pair[1]
      }
    }
  }
  return false
}

// 登录前置信息
aspUtil.getListAll = (_t) => {
  const url = _t.$apiConfig.supportPathPrefix + '/domainConfig/listAll'
  _t.$aspHttps.asp_Post(url, { domain: _t.$projectConfig.domain }).then(response => {
    if (aspUtil.reponseStatus(response)) {
      aspUtil.saveSStorage(_t, 'LISTALL', response.data)
    }
  })
}

/**
 * 判断数组是否为空
 * @param arr
 * @returns {boolean} true：为空，false：不为空
 */
aspUtil.isEmptyArray = arr => {
  let result = true
  if (Object.prototype.toString.call(arr) === '[object Array]') {
    if (arr && arr.length > 0) {
      result = false
    }
  } else {
    //
  }
  return result
}

/**
 * 判断对象是否为空
 * @param obj
 * @returns {boolean} true：为空，false：不为空
 */
aspUtil.isEmptyObject = obj => {
  return Object.keys(obj).length === 0
}

/**
 * 在数组A-arrayA中过滤数组B-arrayB的元素
 * @param arrayA
 * @param arrayB
 */
aspUtil.filterArrays = (arrayA, arrayB) => {
  return arrayA.filter(item => {
    return arrayB.indexOf(item) < 0
  })
}

/**
 * get the repeat part Arrays between ArrayA with ArrayB And return new Array
 * support ie9+ google others
 * @param arrayA
 * @param arrayB
 */
aspUtil.repeatArrays = (arrayA, arrayB) => {
  if (aspUtil.isEmptyArray(arrayA) || aspUtil.isEmptyArray(arrayB)) return []
  let arr = []
  if (arrayA.length >= arrayB.length) {
    arr = arrayB.filter(item => {
      return arrayA.indexOf(item) >= 0
    })
  }
  if (arrayB.length >= arrayA.length) {
    arr = arrayA.filter(item => {
      return arrayB.indexOf(item) >= 0
    })
  }
  return arr
}

/**
 * remove the repeat element in Array And return new Array
 * @param arr
 */
aspUtil.removeRepeatArray = arr => { }

/**
 * 数组中是否含有元素a
 */
aspUtil.isArrayGetA = (a, arr) => {
  if (aspUtil.isEmptyArray(arr)) return false
  return arr.some(a)
}

/**
 * 将数组中某一字段以‘，’拼接成字符串
 * @param arr
 * @param val
 * @returns {string}
 */
aspUtil.arrayToString = (arr, val) => {
  let resultStr = ''
  if (Object.prototype.toString.call(arr) === '[object Array]') {
    arr.forEach(item => {
      resultStr = resultStr ? resultStr + ',' + item[val] : item[val]
    })
  } else {
    //
  }
  return resultStr
}

/**
 * turn the array with Object to the array with String
 * @param arr  the array with Object
 * @param key  the ObjKey of Object
 * @returns {Array} the array with String
 */
aspUtil.arrObjTorrString = (arr, key) => {
  if (aspUtil.isEmptyArray(arr)) return []
  return arr.map(item => {
    return key ? item[key] : null
  })
}

/**
 * turn the array with String to array with Object
 * @param arr array with String
 * @param key array with Object
 * @returns {*}
 */
aspUtil.arrStringToArrObj = (arr, key) => {
  if (aspUtil.isEmptyArray(arr)) return []
  if (Object.prototype.toString.call(arr[0]) === '[object Object]') return arr
  const itemString = Object.prototype.toString.call(arr[0]) === '[object String]'
  return arr.map(returnObj)
  function returnObj (item) {
    if (key) return { [key]: itemString ? item : '' }
    return null
  }
}

/**
 * 合并数组A和数字B，并保证数组元素不重复【仅支持字符串或者对象两种元素数组合并】
 * 当数组元数为字符串时直接对比合并，当数组元素为对象时合并两数组对象
 * concat合并实际上仅仅只是做了数组追加拼接不会去重合并
 * @param arrayA 必填
 * @param arrayB 必填
 * @param key 当数组元素为对象时必填
 * @returns {Array}
 */
aspUtil.concatArrays = (arrayA, arrayB, key = '') => {
  if (!(Object.prototype.toString.call(arrayA) === '[object Array]') ||
    aspUtil.isEmptyArray(arrayA)) {
    return arrayB
  }
  if (!(Object.prototype.toString.call(arrayB) === '[object Array]') ||
    aspUtil.isEmptyArray(arrayB)) {
    return arrayA
  }
  // 当arrayA和arrayB的元素都是字符串String时
  if (
    arrayA[0] &&
    arrayB[0] &&
    Object.prototype.toString.call(arrayA[0]) === '[object String]'
  ) {
    if (Object.prototype.toString.call(arrayB[0]) === '[object String]') {
      return concatStringArray(arrayA, arrayB)
    } else if (
      Object.prototype.toString.call(arrayB[0]) === '[object Object]'
    ) {
      return arrayB
    } else {
      return arrayA
    }
  }
  // 当arrayA和arrayB的元素都是对象Object时
  if (
    arrayA[0] &&
    arrayB[0] &&
    Object.prototype.toString.call(arrayA[0]) === '[object Object]'
  ) {
    if (
      Object.prototype.toString.call(arrayB[0]) === '[object Object]' &&
      !key
    ) {
      return concatStringObject(arrayA, arrayB)
    } else {
      return arrayA
    }
  }
  /* *合并字符串数组 */
  function concatStringArray (arrA, arrB) {
    let newArray = []
    newArray = [...arrayA]
    // let arrayOnlyB = util.filterArrays(arrayB, arrA)
    // newArray = [...arrayOnlyB]
    arrB.forEach(itemB => {
      let isNotSame = true
      arrA.forEach(itemA => {
        if (itemA === itemB) isNotSame = false
      })
      if (isNotSame) newArray.push(itemB)
    })
    return newArray
  }
  /* *合并对象数组 */
  function concatStringObject (arrA, arrB) {
    const newArray = []
    arrA.forEach(itemA => {
      let isNotSame = true
      arrB.forEach(itemB => {
        if (itemA[key] === itemB[key]) {
          newArray.push(Object.assign(itemA, itemB))
          isNotSame = false
        }
      })
      if (isNotSame) newArray.push(itemA)
    })
    arrayB.forEach(itemB => {
      let isNotSame = true
      arrayA.forEach(itemA => {
        if (itemA[key] === itemB[key]) isNotSame = false
      })
      if (isNotSame) newArray.push(itemB)
    })
    return newArray
  }
  return []
}

aspUtil.reLogin = () => {
  // console.log('reLogin==>>>login')
  sessionStorage.clear()
  router.push({ path: '/login' })
}
/**
 * 判断对象是否相同
 * @param obj
 * @returns {boolean} true：为空，false：不为空
 */
aspUtil.isEqualObj = (a, b) => {
  const gettype = Object.prototype.toString
  const typeObj = '[object Object]'
  const typeArr = '[object Array]'
  let resObj
  // a为Object
  if (typeObj === gettype.call(a)) {
    // b为Object
    if (typeObj === gettype.call(b)) {
      for (const i in a) {
        if (typeObj === gettype.call(a[i]) || typeArr === gettype.call(a[i])) {
          resObj = aspUtil.isEqualObj(a[i], b[i])
          if (!resObj) {
            return false
          }
        } else {
          if (a[i] !== b[i]) {
            return false
          }
        }
      }
    } else {
      return false
    }
    // a为数组
  } else if (typeArr === gettype.call(a)) {
    // b为数组
    if (typeArr === gettype.call(b)) {
      if (a.length !== b.length) {
        return false
      }
      for (let i = 0; i < a.length; i++) {
        if (typeObj === gettype.call(a[i]) || typeArr === gettype.call(a[i])) {
          resObj = aspUtil.isEqualObj(a[i], b[i])
          if (!resObj) {
            return false
          }
        } else {
          if (a[i] !== b[i]) {
            return false
          }
        }
      }
    } else {
      return false
    }
  } else {
    if (typeObj === gettype.call(b) || typeArr === gettype.call(b)) {
      return false
    } else {
      if (a !== b) {
        return false
      }
    }
  }
  return true
}

/**
 * 通过codeType从字典数据中获取需要的数据
 * @param vim
 * @param codeType
 * @returns {[]|*[]}
 */
aspUtil.getCodeValueByType = (vim, codeType) => {
  const codeData = aspUtil.getCodeData(vim)
  if (!codeData) {
    return []
  }
  const localCodeData = codeType ? codeData[codeType] : codeData
  return localCodeData
}

/**
 * 从缓存中获取字典数据
 * @param vim
 * @param o
 * @returns {{}|''|any}
 */
aspUtil.getCodeData = (vim, o = true) => {
  const codeData = aspUtil.getCodeDataObject(vim)
  if (codeData) {
    return codeData
  } else {
    vim.$message.warning('获取字典数据失败，请重新尝试该操作！')
    return {}
  }
}

// 字典表转义
aspUtil.formatDict = (code, dictList) => {
  if (!code) {
    return ''
  }
  let dictStr = code
  if (dictList && dictList.length > 0) {
    dictList.forEach(item => {
      if (item.code === code) {
        dictStr = item.name
      }
    })
  }
  return dictStr
}

/**
 * 通过codeType从字典数据中获取需要的数据
 * @param vim
 * @param codeType
 * @returns {[]|*[]}
 */
aspUtil.getFinalDictByType = (vim, codeType) => {
  const codeData = aspUtil.getCodeData(vim)
  if (!aspUtil.isEmptyObject(codeData) && codeType) {
    const dictType = aspUtil.getListAllObject(vim).platformConfig.dictDisableShowType
    let localCodeData = codeData[codeType] || []
    if (dictType === 'invisible') {
      // 'invisible'  不展示status为0的字典数据
      localCodeData = localCodeData.filter(val => val.status !== '0')
    }
    if (dictType === 'notOptional') {
      // notOptional 展示但是禁用status为0的字典数据
      localCodeData = localCodeData.map(val => {
        val.disabled = val.status === '0'
        return val
      })
    }
    return localCodeData
  }
  return []
}

/**
 * 计算文字样式宽度
 * @param num     字体个数
 * @param flag    有无排序
 * @returns {*}   返回具体长度
 */
aspUtil.fontSize = (num, flag) => {
  let size = num * 14 + 6 + 1
  if (flag === 1) {
    size += 14
  }
  return size + 5
}

/**
 * 计算按钮样式宽度
 * @param array      按钮个数组成的数组，单元是具体的按钮内字数
 * @returns {number} 返回具体长度
 */
aspUtil.buttonSize = array => {
  let size = 0
  for (let i = 0; i < array.length; i++) {
    if (i !== 0) {
      size += 4
    }
    size += array[i] * 14 + 2
  }
  if (array.length === 1) {
    size += 20
  } else {
    size += 6
  }

  return size + 6
}

/**
 * 序号宽度
 * @returns {number} 返回具体长度
 */
aspUtil.numSize = () => {
  return 35
}

aspUtil.filerLoginName = (row, name) => {
  const nameKey = name.property || ''
  if (row && nameKey && row[nameKey]) {
    let result = row[nameKey]
    result = result.slice(0, 1) + '***'
    return result
  }
  return ''
}
aspUtil.filerPhone = (row, name) => {
  const nameKey = name.property || ''
  if (row && nameKey && row[nameKey]) {
    const result = row[nameKey]
    const preStr = result.substr(0, 3)
    const suffixStr = result.substr(7, 11)
    return preStr + '****' + suffixStr
  }
  return ''
}

aspUtil.filerEmail = (row, name) => {
  const nameKey = name.property || ''
  if (row && nameKey && row[nameKey]) {
    const result = row[nameKey]
    const suffixStr = '@' + result.split('@')[1]
    const preStr = result.slice(0, 1)
    return preStr + '****' + suffixStr
  }
  return ''
}

aspUtil.httPrefix = () => {
  const httpPrefix = window.location.href.split('://')[0]
  return httpPrefix + '://'
}

aspUtil.getDomainObject = (vim) => {
  const tmpData = vim.$main_tools.sessionStorage[vim.$projectConfig.operator]
  return tmpData ? JSON.parse(tmpData) : {}
}

aspUtil.getListAllObject = (vim) => {
  const tmpData = vim.$main_tools.sessionStorage.getItem('LISTALL')
  return tmpData ? JSON.parse(tmpData) : {}
}

aspUtil.getCodeDataObject = (vim) => {
  const tmpData = vim.$main_tools.sessionStorage.getItem('codeData')
  return tmpData ? JSON.parse(tmpData) : {}
}

aspUtil.getSStorage = (vim, key) => {
  const tmpData = vim.$main_tools.sessionStorage.getItem(key)
  return tmpData ? JSON.parse(tmpData) : {}
}

aspUtil.saveSStorage = (vim, key, data) => {
  vim.$main_tools.sessionStorage[key] = JSON.stringify(data)
}

export default aspUtil
