/**
 * Created by TurboC on 2020/04/03
 */

const aspFontSize = {}
/**
 * 计算文字样式宽度
 * @param num     字体个数
 * @param flag    有无排序
 * @returns {*}   返回具体长度
 */
aspFontSize.asp_ColFontSize = (num, flag) => {
  let size = num * 14 + 6 + 1
  if (flag === 1) {
    size += 14
  }
  return size + 5
}

/**
 * 计算按钮样式宽度
 * @param array      按钮个数组成的数组，单元是具体的按钮内字数
 * @returns {number} 返回具体长度
 */
aspFontSize.asp_ColButtonSize = array => {
  let size = 0
  for (let i = 0; i < array.length; i++) {
    if (i !== 0) {
      size += 4
    }
    size += array[i] * 14 + 2
  }
  if (array.length === 1) {
    size += 20
  } else {
    size += 6
  }
  return size + 6
}

/**
 * 序号宽度
 * @returns {number} 返回具体长度
 */
aspFontSize.asp_ColNumSize = () => {
  return 50
}

export default aspFontSize
