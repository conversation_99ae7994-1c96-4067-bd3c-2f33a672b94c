/**
 * Created by TurboC on 2020/04/03
 */

const aspDatatime = {}


// 时间格式化，
// @param time 要格式的时间
// @returns 返回格式 yyyy-MM-dd
aspDatatime.asp_FormatTimeDd = time => {
  if (!time) {
    return ''
  }
  const format = '{y}-{m}-{d}'
  let date
  if (typeof time === 'object') {
    date = time
  } else if (typeof time === 'string') {
    if (/^[0-9]+$/.test(time)) {
      date = new Date(parseInt(time))
    } else if (time.indexOf('T') !== -1) {
      date = new Date(time.split('T')[0].replace(/-/g, '/'))
    } else {
      date = new Date(time.replace(/-/g, '/'))
    }
  } else {
    date = new Date(time)
  }

  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

// 时间格式化，
// @param time
// @returns 返回格式 yyyy-MM-dd hh:mm:ss
aspDatatime.asp_FormatTimeSs = time => {
  if (!time) {
    return null
  }
  if (time.split('T').length > 0) {
    return (
      time.split('T')[0] +
      ' ' +
      (time.split('T')[1] !== undefined ? time.split('T')[1] : '')
    )
  }

  const format = '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else if (typeof time === 'string') {
    if (/^[0-9]+$/.test(time)) {
      date = new Date(parseInt(time))
    } else if (time.indexOf('T') !== -1) {
      date = new Date(time.split('T')[0].replace(/-/g, '/'))
    } else {
      date = new Date(time.replace(/-/g, '/'))
    }
  } else {
    date = new Date(time)
  }

  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

// 时间加法
// @param time    时间参数 Date类型/时间戳/时间字符串(2019-01-01)
// @param number  增加的天数
// @returns       回yyyy-MM-dd的时间串
aspDatatime.asp_DatetimePlus = (time, number) => {
  let date
  if (typeof time === 'object') {
    date = time
  } else if (typeof time === 'string') {
    if (Number(time)) {
      time = new Date(Number(time))
    } else {
      time = new Date(time.replace(/-/g, '/'))
    }
  } else {
    date = new Date(time)
  }

  date && date.setDate(date.getDate() + number)
  return aspDatatime.asp_FormatTimeDd(date)
}

// 时间跨度
// @param data1     时间参数1 Date类型 时间戳 时间字符串(2019-01-01)
// @param operator  跨度天数
// @param data2     时间参数1 Date类型 时间戳 时间字符串(2019-01-01)
// @returns {boolean}
aspDatatime.asp_SpanTimes = (data1, operator, data2) => {
  if (!data1 || !data2) {
    return false
  }
  if (typeof data1 === 'string') {
    if (Number(data1)) {
      data1 = Number(data1)
    } else {
      data1 = new Date(data1.replace(/-/g, '/')).getTime()
    }
  } else if (data1 instanceof Date) {
    data1 = data1.getTime()
  }
  if (typeof data2 === 'string') {
    if (Number(data2)) {
      data2 = Number(data2)
    } else {
      data2 = new Date(data2.replace(/-/g, '/')).getTime()
    }
  } else if (data2 instanceof Date) {
    data2 = data2.getTime()
  }
  const compare = data2 - data1
  if (compare < 0 || compare > 3600 * 1000 * 24 * operator) {
    return false
  }
  if (compare >= 0 && compare <= 3600 * 1000 * 24 * operator) {
    return true
  }
}

// 时间对比
// @param data1     时间参数1 Date类型 时间戳 时间字符串(2019-01-01)
// @param operator  对比符号('===','>','<','>=','<=')
// @param data2     时间参数1 Date类型 时间戳 时间字符串(2019-01-01)
// @returns {boolean}
aspDatatime.asp_CompareTime = (data1, operator, data2) => {
  if (!data1 || !data2) {
    return false
  }
  if (typeof data1 === 'string') {
    if (Number(data1)) {
      data1 = Number(data1)
    } else {
      data1 = new Date(data1.replace(/-/g, '/')).getTime()
    }
  } else if (data1 instanceof Date) {
    data1 = data1.getTime()
  }
  if (typeof data2 === 'string') {
    if (Number(data2)) {
      data2 = Number(data2)
    } else {
      data2 = new Date(data2.replace(/-/g, '/')).getTime()
    }
  } else if (data2 instanceof Date) {
    data2 = data2.getTime()
  }
  if (operator === '===') {
    return data1 === data2
  }
  if (operator === '>') {
    return data1 > data2
  }
  if (operator === '<') {
    return data1 < data2
  }
  if (operator === '>=') {
    return data1 >= data2
  }
  if (operator === '<=') {
    return data1 <= data2
  }
  return false
}

/**
 * 时间格式化，返回格式 yyyy-MM-dd hh:mm:ss
 * @param time        2019-12-25T22:37:19
 * @returns {string}
 */
aspDatatime.asp_FormatOfTTimeSs = time => {
  if (time) {
    const dateTime = new Date(time)

    const y = dateTime.getFullYear()
    let m = dateTime.getMonth() + 1
    m = m < 10 ? '0' + m : m
    let d = dateTime.getDate()
    d = d < 10 ? '0' + d : d

    let hh = dateTime.getHours()
    hh = hh < 10 ? '0' + hh : hh
    let mm = dateTime.getMinutes()
    mm = mm < 10 ? '0' + mm : mm
    let ss = dateTime.getSeconds()
    ss = ss < 10 ? '0' + ss : ss

    return y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss
  } else {
    return ''
  }
}

export default aspDatatime
