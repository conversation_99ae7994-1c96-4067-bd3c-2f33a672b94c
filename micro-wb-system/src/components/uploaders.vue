<template>
  <div>
    <el-upload
      class="avatar-uploader"
      :action="uploadUrl"
      :show-file-list="false"
      :data="param"
      :name="name"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload">
      <img v-if="value" :src="value" class="avatar">
      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
    </el-upload>
    <div v-if="tips" class="tips">{{ tips }}</div>
  </div>
</template>
<style scope>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .avatar {
    min-width: 80px;
    max-width: 200px;
    max-height: 80px;
    display: block;
  }
  .tips {
    color:red;
  }
</style>

<script>
import { checkImage } from '@/utils/index.js'
export default {
  name: 'uploader',
  props: {
    value: {
      type: String,
      default: () => ''
    },
    uploadUrl: {
      type: String,
      default: () => ''
    },
    thumbSizes: {
      type: String,
      default: () => ''
    },
    name: {
      type: String,
      default: () => 'file'
    },
    size: {
      type: Number,
      default: () => -1
    },
    type: {
      type: String,
      default: () => 'jpg;jpeg;png'
    },
    verifyPicture: {
      type: String,
      default: () => ''
    },
    tips: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      param: {
        thumbSizes: this.thumbSizes
      }
    }
  },
  methods: {
    handleSuccess(res, file) {
      if (this.$reponseStatus(res)) {
        this.value = this.$apiConfig.level1cloudstorePathPreFix + '/proxyFs/show?file_id=' + res.data.fileId
        console.log(res.data, 'res.datares.data')
        this.$emit('input', res.data)
      } else {
        this.$message({
          message: '上传失败，请重试',
          type: 'warning'
        })
      }
    },
    async beforeUpload(file) {
      const isJPG = this.type.split(';').indexOf(file.type.split('/').pop()) > -1
      const isLtSize = this.size === -1 || file.size / 1024 / 1024 < this.size
      return new Promise((resolve, reject) => {
        if (!isJPG) {
          this.$message.error(`上传图片只能是以下格式：${this.type}`)
          reject(new Error())
        } else if (!isLtSize) {
          this.$message.error(`上传图片大小不能超过 ${this.size}KB!`)
          reject(new Error())
        } else {
          checkImage(file, this.verifyPicture).then(flag => {
            if (flag) {
              resolve(true)
            } else {
              this.$message.error(`图片尺寸不符，请选择${this.verifyPicture}的图片`)
              reject(new Error())
            }
          })
        }
      })
    },
    handleError(err) {
      this.$message.error(err.status === 413 ? '文件过大，请重新选择' : '上传失败，请重试')
    }
  }
}
</script>
