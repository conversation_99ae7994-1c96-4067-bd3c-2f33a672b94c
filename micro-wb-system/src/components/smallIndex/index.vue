<template>
  <div>
    <span class="date">统计时间：{{ newStartDate }}</span>
    <div class="shopNumContent">
      <div>
        <p><i class="el-icon-question" title="开通状态的线上店总数"></i> 开通线上店总数：<span>{{ indexLists.openShopNumShow ? indexLists.openShopNumShow : 0 }}</span> 家</p>
      </div>
      <div>
        <p>
          <el-tooltip class="item" effect="light" placement="bottom-start">
            <div slot="content">日：</div>
            <template v-for="(item, index) in otherDatas">
              <div slot="content" :key="index" v-if="item.metricCode.includes('orderDay') || item.metricCode.includes('moneyDay') || item.metricCode.includes('pvDay') || item.metricCode.includes('uvDay')">
                线上店当日产生的{{ item.metricName }} >= {{ item.value }}
              </div>
            </template>
            <div slot="content">月：</div>
            <template v-for="(item, index) in otherDatas">
              <div slot="content" :key="index" v-if="item.metricCode.includes('orderMonth') || item.metricCode.includes('moneyMonth') || item.metricCode.includes('pvMonth') || item.metricCode.includes('uvMonth')">
                线上店当月产生的{{ item.metricName }} >= {{ item.value }}
              </div>
            </template>
            <i class="el-icon-question"></i>
          </el-tooltip>
          活跃厅店数：<span>{{ indexLists.activeShopNumShow ? indexLists.activeShopNumShow : 0 }}</span> 家</p>
      </div>
      <div>
        <p><i class="el-icon-question" title="线上店备案的员工总数"></i> 登记员工数：<span>{{ indexLists.workerNumShow ? indexLists.workerNumShow : 0 }}</span> 人</p>
      </div>
      <div>
        <p><i class="el-icon-question" title="线上店粉丝总量"></i> 线上店粉丝数：<span>{{ indexLists.fansNumShow ? indexLists.fansNumShow : 0 }}</span> 人</p>
      </div>
  </div>
  </div>
</template>
<script>
export default {
  props: ['indexList', 'params', 'otherData'],
  data () {
    return {
      otherDatas: []
    }
  },
  watch: {
    otherData: {
      handler(val) {
        this.otherDatas = val
      }
    }
  },
  computed: {
    // 处理统计时间
    newStartDate() {
      if (this.params.dateType === 'day') {
        const newDate = this.params.startDate
        return newDate
      } else {
        const newDate = this.params.startDate1
        return newDate
      }
    },
    // 防止数据没加载完成报错
    indexLists() {
      return this.indexList || {}
    }
  },
  created() {
    console.log(this.params, 'paramsparams')
  },
  mounted() {
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-icon-question:before{
  color: #999999;
}
.date{
  font-size: 13px;
  color: #C0C2C6;
}
.shopNumContent{
  height: 80px;
  margin-top: 10px;
  border: 1px solid #E7E9EE;
  display: flex;
  align-items: center;
  justify-content: center;
  div{
    flex: 1;
    text-align: center;
    font-size: 17px;
    font-weight: 600;
    border-right: 1px solid #D9DADD;
    color: #333333;
    span{
      color: red;
    }
  }
  div:last-child{
    border: none;
  }
}
</style>
