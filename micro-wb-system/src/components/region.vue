<template>
  <div>
    <template v-if="status === 'info'">
      {{ publishProvinceChoose  == 1 ? '全国': mapName(provinceCodes,provinceMap) }}
    </template>
    <template v-else>
      <div>
        <el-radio-group v-model="publishProvinceChoose" @change="publishProvinceChange">
          <el-radio label="1" >全国</el-radio>
          <el-radio label="0" >分省</el-radio>
        </el-radio-group>
      </div>
      <div v-if="publishProvinceChoose !== '1'">
        <el-checkbox-group v-model="provinceCodes" @change="provinceChange">
          <el-checkbox v-for="(item,key) in provinceMap" :label="key.toString()" :key="key" >{{item}}</el-checkbox>
        </el-checkbox-group>
      </div>
    </template>
  </div>
</template>

<script>
import aspUtils from '@/components/aspire/asp-api/asp-Utils.js'
export default {
  name: 'region',
  props: {
    value: {
      type: Array,
      default: () => ([])
    },
    status: {
      type: String,
      default: () => ('edit')
    }
  },
  data() {
    return {
      provinceMap: Object.freeze(aspUtils.asp_CfgData().asp_Provice_List),
      publishProvinceChoose: '1',
      provinceCodes: ['-1'],
      a: 0
    }
  },
  watch: {
    value(newVal) {
      this.init(newVal)
    }
  },
  mounted() {
    if (this.value && this.value.length) {
      this.init(this.value)
    }
  },
  methods: {
    init(value) {
      if (value && value.length) {
        this.provinceCodes = value
        if (this.provinceCodes[0] !== '-1') {
          this.publishProvinceChoose = '0'
        } else {
          this.publishProvinceChoose = '1'
        }
      } else {
        this.provinceCodes = ['-1']
        this.publishProvinceChoose = '1'
      }
    },
    provinceChange() {
      this.$emit('input', this.provinceCodes)
    },
    publishProvinceChange() {
      if (this.publishProvinceChoose === '1') {
        this.provinceCodes = ['-1']
        console.log(this.provinceCodes, '---')
        this.$emit('input', this.provinceCodes)
      } else {
        this.provinceCodes = []
      }
    },
    mapName(items, data) {
      if (items && items.length) {
        return items.map(key => data[key]).join(',')
      } else {
        return ''
      }
    }
  }
}
</script>
<style scoped>
.el-checkbox-group{
  margin-top:10px;
}
.el-radio-group{
  margin-top:10px;
}
</style>
<style scoped>
.el-checkbox-group{
  margin-top:10px;
}
.el-radio-group{
  margin-top:10px;
}
</style>
