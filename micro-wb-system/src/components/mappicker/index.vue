<template>
  <div class="map">
    <baidu-map
      :ak="ak"
      :center="center"
      :zoom="zoom"
      :scroll-wheel-zoom="true"
      @ready="onReady"
      class="bmap-container">
      <!-- 地图类型控件 -->
      <bm-map-type :map-types="['BMAP_NORMAL_MAP']" anchor="BMAP_ANCHOR_TOP_LEFT"></bm-map-type>

      <!-- 缩放控件 -->
      <bm-navigation anchor="BMAP_ANCHOR_TOP_RIGHT"></bm-navigation>

      <!-- 定位控件 -->
      <bm-geolocation
        anchor="BMAP_ANCHOR_BOTTOM_RIGHT"
        :showAddressBar="true"
        :auto-location="true"
        @locationSuccess="locationSuccess"
        @locationError="locationError">
      </bm-geolocation>
      <!-- 搜索功能 -->

      <!-- 标记点 -->
      <!-- <bm-marker
        v-if="currentMarker.position"
        :position="currentMarker.position"
        :dragging="true"
        animation="BMAP_ANIMATION_BOUNCE"
        @click="showInfo">
        <bm-info-window
          :show="showWindow"
          @close="showWindow = false"
          @open="showWindow = true">
          <div>{{ currentMarker.address }}11</div>
        </bm-info-window>
      </bm-marker> -->
      <div class="marker-info">
        <b>当前选择位置:</b>
        <template v-if="selection">
          <p>{{ selection.title }}&nbsp;&nbsp;&nbsp;&nbsp;经度：{{ selection.longitude }}&nbsp;&nbsp;纬度：{{  selection.latitude }}&nbsp;&nbsp;座标系：{{ crsType }}</p>
          <p>{{ selection.address }}</p>
        </template>
        <el-button :type="selection ? 'primary': 'cancel'"  @click="sendPosition(selection)">确定选点</el-button>
      </div>
      <div class="search-results-container">
        <el-input v-model="searchText" width="100%" placeholder="请输入搜索内容"></el-input>
        <div class="search-results">
          <bm-local-search
            :keyword="keyword"
            :auto-viewport="true"
            :selectFirstResult="true"
            @searchcomplete="searchComplete"
            @markersset="markersSet"
            @infohtmlset="selected"
          />
        </div>
      </div>
    </baidu-map>

  </div>
</template>

<script>
// import { BaiduMap, BmGeolocation, BmLocalSearch } from 'vue-baidu-map'
import BaiduMap from 'vue-baidu-map/components/map/Map.vue'
import BmGeolocation from 'vue-baidu-map/components/controls/Geolocation.vue'
import BmLocalSearch from 'vue-baidu-map/components/search/LocalSearch.vue'
import BmNavigation from 'vue-baidu-map/components/controls/Navigation.vue'
import BmMapType from 'vue-baidu-map/components/controls/MapType.vue'
import gcoord from 'gcoord'
// import BmMarker from 'vue-baidu-map/components/overlays/Marker.vue'
// import BmInfoWindow from 'vue-baidu-map/components/overlays/InfoWindow.vue'

const ak = 'VJMcQpaFzhWwlFlMGiBpD2h44IRqrEy4'
//   const ak = 'nAt2EPPY8SaWAbjJiy0rqfdTOHBWIi2X'
export default {
  name: 'MapPicker',
  components: {
    BaiduMap,
    // BmView,
    BmGeolocation,
    BmLocalSearch,
    BmNavigation,
    BmMapType
    // BmMarker,
    // BmInfoWindow
  },
  props: {
    city: {
      type: String,
      default: '中国'
    },
    defaultSearchTxt: {
      type: String,
      default: ''
    },
    crsType: {
      type: gcoord.CRSTypes,
      default: gcoord.WGS84
    }
  },
  data () {
    return {
      ak,
      center: '', // 深圳世界之窗坐标
      zoom: 15,
      keyword: [],
      showWindow: false,
      currentMarker: null,
      addressList: [],
      searchText: '',
      selection: null
    }
  },
  watch: {
    searchText: {
      immediate: true,
      handler (val) {
        if (val) {
          this.keyword = val.split('|')
        }
      }
    },
    city: {
      immediate: true,
      handler (val) {
        if (val) {
          this.center = val
        }
      }
    },
    defaultSearchTxt: {
      immediate: true,
      handler (val) {
        if (val) {
          this.searchText = val
        }
      }
    },
    searchResults(val) {
      console.log(val, 'searchResults')
    },
    currentMarker(val) {
      const data = JSON.parse(JSON.stringify(val))
      let result = []
      switch (this.crsType) {
        case gcoord.WGS84:
          result = gcoord.transform(
            [data.position.longitude, data.position.latitude],
            gcoord.BD09, // 当前坐标系:坐标系：WGS84、BD09、GCJ02
            gcoord.WGS84 // 目标坐标系
          )
          data.position = {
            latitude: result[1].toFixed(7),
            longitude: result[0].toFixed(7)
          }
          break
        case gcoord.GCJ02:
          result = gcoord.transform(
            [data.position.longitude, data.position.latitude],
            gcoord.BD09, // 当前坐标系:坐标系：WGS84、BD09、GCJ02
            gcoord.GCJ02 // 目标坐标系
          )
          data.position = {
            latitude: result[1].toFixed(7),
            longitude: result[0].toFixed(7)
          }
          break
      }
      this.selection = {
        ...data,
        ...data.position
      }
    }
  },
  methods: {
    onReady (map) {
      this.map = map
    },
    selectResult (result) {
      if (result && result.point) {
        this.selected(result)
        this.center = result.point
        this.zoom = 16
      }
    },
    selected (info) {
      if (info && info.point) {
        this.currentMarker = {
          position: {
            longitude: info.point.lng,
            latitude: info.point.lat
          },
          address: info.address,
          title: info.title
        }
        this.showWindow = true
      }
    },
    sendPosition(info) {
      if (info) {
        this.$emit('selected', info)
      }
    },
    locationSuccess (point) {
      this.center = {
        lng: point.point.lng,
        lat: point.point.lat
      }
    },
    locationError (err) {
      console.error('定位失败', err)
    },
    searchComplete (results) {
      if (results) {
        this.addressList = results[0]?.as || []
        this.zoom = 13
      } else {
        this.addressList = []
      }
    },
    markersSet (markers) {
      // if (markers && markers.length > 0) {
      //   this.center = markers[0].getPosition()
      // }
    },
    showInfo () {
      this.showWindow = !this.showWindow
    }
  }
}
</script>
<style lang="scss" scoped>
.map{
  width: 1000px;
  height: 500px;
  position: relative;
  display: flex;

  .bmap-container {
    width: 700px;
    height: 420px;
    border: 1px solid #ccc;

    ::v-deep .marker-info{
      position: relative;
      width:980px;
      height:70px;
      margin-left:-1px;
      padding:5px;
      border: 1px solid #ccc;
      b {
        font-weight: bold;
      }
      p {
        margin:5px;
      }
      .el-button {
        position: absolute;
        right: 10px;
        top: 20px;
        z-index:1000;
      }
    }
  }

  .search-results-container {
    position: absolute;
    right:0;
    bottom:0;
    top:0;
    width: 280px;
    height: 500px;
    padding: 0 10px;
    display: flex;
    flex-direction: column;
    .el-input {
      width:100%;
    }
  }
  .search-results {
    height: 360px;
    background: white;
    padding: 10px 0;
    overflow: hidden;
    pointer-events: auto;
    ::v-deep ol {
      height: 340px;
      overflow-y: auto;
    }
  }
  .search-result-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
  }
  .search-result-item:hover {
    background-color: #f5f7fa;
  }
  .result-title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
  }
  .result-address {
    font-size: 12px;
    color: #666;
  }
  .no-results {
    text-align: center;
    color: #999;
    padding: 20px 0;
  }

}
</style>
