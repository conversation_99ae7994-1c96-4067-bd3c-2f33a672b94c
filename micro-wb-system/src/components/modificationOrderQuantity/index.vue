<template>
  <section class="modificationOrderQuantity">
    <el-dialog
      title="地图图例"
      :visible.sync="dialogVisible"
      width="48%"
      :before-close="handleClose"
    >
      <el-form ref="form" :model="form" :rules="rules">
        <ul v-loading="loading">
          <li class="mapLegend">
            <!-- <div class="title"></div> -->
            <div class="con">
              <el-tabs class="eltabs" type="border-card">
                <el-tab-pane label="日视图-对应订单量">
                  <dt v-for="(item, index) in form.mapLegendD" :key="index">
                    <el-color-picker
                      :value="form.mapLegendColour[index]"
                      class="color"
                      :disabled="colorPicker"
                      @change="handleActiveChange($event, index)"
                      size="medium"
                      :predefine="predefineColors"
                    >
                    </el-color-picker>
                    <!-- <template v-if="index !== form.mapLegendD.length - 1"> -->
                    <el-input
                      size="mini"
                      :readonly="index !== 0"
                      :disabled="index === 0"
                      class="ipt"
                      placeholder=""
                      v-model.number="item.begin"
                      clearable
                    >
                    </el-input>
                    <!-- </template> -->
                    <!-- <template v-else>
                          <el-form-item
                            class="fromItemBegin ipt"
                            :prop="`mapLegendD[${index}].begin`"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: (rule, value, callback) => {
                                  let endval = form.mapLegendD[index - 1].end
                                  if (parseInt(value) <= endval) {
                                    callback(callback(`当前值应大于${endval}`))
                                  } else {
                                    callback()
                                  }
                                }
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.begin"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template> -->

                    <span>-</span>

                    <template v-if="index !== form.mapLegendD.length - 1">
                      <el-form-item
                        class="ipt"
                        :prop="`mapLegendD[${index}].end`"
                        :rules="[
                          { required: true, message: '不能为空' },
                          { type: 'number', message: '必须为数字值' },
                          {
                            validator: validateCom1Fun,
                          },
                        ]"
                      >
                        <el-input
                          size="mini"
                          class="ipt"
                          placeholder=""
                          v-model.number="item.end"
                          clearable
                        >
                        </el-input>
                      </el-form-item>
                    </template>
                    <template v-else>
                      <el-form-item class="ipt">
                        <el-input
                          size="mini"
                          class="ipt"
                          :disabled="true"
                          style="font-size: 20px"
                          placeholder="∞"
                        >
                        </el-input>
                      </el-form-item>
                    </template>
                  </dt>
                </el-tab-pane>

                <el-tab-pane label="月视图-对应订单量">
                  <dt :key="index" v-for="(item, index) in form.mapLegendM">
                    <el-color-picker
                      v-model="form.mapLegendColour[index]"
                      class="color"
                      @change="handleActiveChange($event, index)"
                      :disabled="colorPicker"
                      size="medium"
                      @input="handleInput"
                      :predefine="predefineColors"
                    >
                    </el-color-picker>
                    <!-- <template v-if="index !== form.mapLegendM.length - 1"> -->
                    <el-input
                      size="mini"
                      :readonly="index !== 0"
                      :disabled="index === 0"
                      class="ipt"
                      placeholder=""
                      v-model.number="item.begin"
                      clearable
                    >
                    </el-input>
                    <!-- </template> -->
                    <!-- <template v-else>
                          <el-form-item
                            :prop="`mapLegendM[${index}].begin`"
                            class="fromItemBegin ipt"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: (rule, value, callback) => {
                                  let endval = form.mapLegendM[index - 1].end
                                  if (parseInt(value) <= endval) {
                                    callback(callback(`当前值应大于${endval}`))
                                  } else {
                                    callback()
                                  }
                                }
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.begin"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template> -->

                    <span>-</span>

                    <template v-if="index !== form.mapLegendM.length - 1">
                      <el-form-item
                        class="ipt"
                        :prop="`mapLegendM[${index}].end`"
                        :rules="[
                          { required: true, message: '不能为空' },
                          { type: 'number', message: '必须为数字值' },
                          {
                            validator: validateCom3Fun,
                          },
                        ]"
                      >
                        <el-input
                          size="mini"
                          class="ipt"
                          placeholder=""
                          v-model.number="item.end"
                          clearable
                        >
                        </el-input>
                      </el-form-item>
                    </template>
                    <template v-else>
                      <el-form-item class="ipt">
                        <el-input
                          size="mini"
                          class="ipt"
                          :disabled="true"
                          style="font-size: 20px"
                          placeholder="∞"
                        >
                        </el-input>
                      </el-form-item>
                    </template>
                  </dt>
                </el-tab-pane>
                <el-tab-pane label="季度视图-对应订单量">
                  <dt :key="index" v-for="(item, index) in form.mapLegendQ">
                    <el-color-picker
                      v-model="form.mapLegendColour[index]"
                      @change="handleActiveChange($event, index)"
                      class="color"
                      :disabled="colorPicker"
                      size="medium"
                      :predefine="predefineColors"
                    >
                    </el-color-picker>
                    <!-- <template v-if="index !== form.mapLegendQ.length - 1"> -->
                    <el-input
                      size="mini"
                      :readonly="index !== 0"
                      :disabled="index === 0"
                      class="ipt"
                      placeholder=""
                      v-model.number="item.begin"
                      clearable
                    >
                    </el-input>
                    <!-- </template> -->
                    <!-- <template v-else>
                          <el-form-item
                            :prop="`mapLegendQ[${index}].begin`"
                            class="fromItemBegin ipt"
                            :rules="[
                              { required: true, message: '不能为空' },
                              { type: 'number', message: '必须为数字值' },
                              {
                                validator: (rule, value, callback) => {
                                  let endval = form.mapLegendQ[index - 1].end
                                  if (parseInt(value) <= endval) {
                                    callback(callback(`当前值应大于${endval}`))
                                  } else {
                                    callback()
                                  }
                                }
                              }
                            ]"
                          >
                            <el-input
                              size="mini"
                              class="ipt"
                              placeholder=""
                              v-model.number="item.begin"
                              clearable
                            >
                            </el-input>
                          </el-form-item>
                        </template> -->
                    <span>-</span>
                    <template v-if="index !== form.mapLegendD.length - 1">
                      <el-form-item
                        class="ipt"
                        :prop="`mapLegendQ[${index}].end`"
                        :rules="[
                          { required: true, message: '不能为空' },
                          { type: 'number', message: '必须为数字值' },
                          {
                            validator: validateCom4Fun,
                          },
                        ]"
                      >
                        <el-input
                          size="mini"
                          class="ipt"
                          placeholder=""
                          v-model.number="item.end"
                          clearable
                        >
                        </el-input>
                      </el-form-item>
                    </template>
                    <template v-else>
                      <el-form-item class="ipt">
                        <el-input
                          size="mini"
                          class="ipt"
                          :disabled="true"
                          style="font-size: 20px"
                          placeholder="∞"
                        >
                        </el-input>
                      </el-form-item>
                    </template>
                  </dt>
                </el-tab-pane>
              </el-tabs>
            </div>
          </li>

          <li class="submitLi">
            <div class="title">
              <el-button
                size="mini"
                class="submitBtn"
                type="primary"
                :loading="remindLoading"
                @click="handleSubmit"
                >提交</el-button
              >
            </div>
          </li>
        </ul>
      </el-form>
    </el-dialog>
  </section>
</template>

<script>
import _ from 'lodash'

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      show: this.dialogVisible,
      form: {
        mapLegendD: [],
        mapLegendW: [],
        mapLegendM: [],
        mapLegendQ: [],
        mapLegendColour: []
      },
      rules: {
        begin: [
          // { required: true, message: '必填项，请维护', trigger: 'blur' }
          { validator: this.validateCom, trigger: 'blur' }
          // { validator: this.validateMin, trigger: 'blur' }
        ],
        end: [
          { required: true, message: '必填项，请维护', trigger: 'blur' }
          // { validator: this.validateCom, trigger: 'blur' },
          // { validator: this.validateMax, trigger: 'blur' }
        ]
      },

      colorPicker: true,
      remindLoading: false,
      id: '',

      loading: true,

      departmentId: '',

      /* 预定义颜色 */
      predefineColors: [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ]
    }
  },

  watch: {
    async dialogVisible(val) {
      this.getData()
      this.show = val
    }
  },

  mounted() {
  },

  methods: {
    /* 提交按钮 */
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.remindLoading = true

          const id = this.id

          // 处理地图图例数据
          let mapColourGetters = {}
          const mapColour = {}

          for (let index = 0; index < 4; index++) {
            const d = JSON.parse(
              JSON.stringify(this.form.mapLegendD)
            ).reverse()[index]
            const w = JSON.parse(
              JSON.stringify(this.form.mapLegendW)
            ).reverse()[index]
            const m = JSON.parse(
              JSON.stringify(this.form.mapLegendM)
            ).reverse()[index]
            const q = JSON.parse(
              JSON.stringify(this.form.mapLegendQ)
            ).reverse()[index]

            for (const key in d) {
              if (!d[key]) {
                if (key === 'begin') {
                  d[key] = '0'
                } else {
                  d[key] = ''
                }
              }
            }
            for (const key in w) {
              if (!d[key]) {
                if (key === 'begin') {
                  d[key] = '0'
                } else {
                  d[key] = ''
                }
              }
            }
            for (const key in m) {
              if (!d[key]) {
                if (key === 'begin') {
                  d[key] = '0'
                } else {
                  d[key] = ''
                }
              }
            }
            for (const key in q) {
              if (!d[key]) {
                if (key === 'begin') {
                  d[key] = '0'
                } else {
                  d[key] = ''
                }
              }
            }

            mapColourGetters[index] = {
              colour: JSON.parse(JSON.stringify(this.form.mapLegendColour))[
                index
              ],
              d,
              m,
              q
              // w
            }
          }

          mapColourGetters = JSON.parse(
            JSON.stringify(Object.values(mapColourGetters))
          )
          for (let i = 1; i < 5; i++) {
            mapColour[i] = JSON.parse(JSON.stringify(mapColourGetters[i - 1]))
          }

          // console.log('数据啊', mapColour)

          this.$aspHttps
            .asp_Post(
              this.$apiConfig.level1cloudstorePathPreFix +
                '/databoard/conifg/save',
              {
                id,
                mapColour,
                mapColourFlag: true
              }
            )
            .then((res) => {
              this.remindLoading = false

              if (res.status !== '200') {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
                return
              }

              this.getData()

              this.handleClose()
              this.$emit('configSuccess')

              this.$message({
                message: res.message,
                type: 'success'
              })
            })
        } else {
          this.$message({
            message: '提交不成功, 请检查是否填写有误',
            type: 'error'
          })
        }
      })
    },

    /* 请求数据 */
    getData() {
      this.loading = true

      this.$aspHttps
        .asp_Post(
          this.$apiConfig.level1cloudstorePathPreFix +
            '/databoard/conifg/getInfo',
          {}
        )
        .then((res) => {
          if (res.status === '400' && res.message === '获取用户信息失败') {
            this.$router.push('/login')
            return
          }

          const data = _.get(res, 'data', {})

          // 处理-地图图例
          const mapLegend = data.mapColour
          mapLegend.length = 5
          const mapLegendfilterArr = Array.from(mapLegend).filter(
            (item) => item
          )
          // 日
          const mapLegendD = mapLegendfilterArr
            .map((item, index) => {
              const newObj = {}
              if (item.d) {
                for (const key in item.d) {
                  if (
                    index === mapLegendfilterArr.length - 1 &&
                    key === 'begin'
                  ) {
                    newObj[key] = 0
                  } else {
                    newObj[key] = item.d[key] ? parseInt(item.d[key]) : ''
                  }
                }
              } else {
                newObj.begin = 0
                newObj.end = 0
              }
              return newObj
            })
            .reverse()
          mapLegendD[3].end = ''
          // 周
          const mapLegendW = mapLegendfilterArr
            .map((item, index) => {
              const newObj = {}
              if (item.w) {
                for (const key in item.w) {
                  if (
                    index === mapLegendfilterArr.length - 1 &&
                    key === 'begin'
                  ) {
                    newObj[key] = 0
                  } else {
                    newObj[key] = item.w[key] ? parseInt(item.w[key]) : ''
                  }
                }
              } else {
                newObj.begin = 0
                newObj.end = 0
              }
              return newObj
            })
            .reverse()
          mapLegendW[3].end = ''
          // 月
          const mapLegendM = mapLegendfilterArr
            .map((item, index) => {
              const newObj = {}
              if (item.m) {
                for (const key in item.m) {
                  if (
                    index === mapLegendfilterArr.length - 1 &&
                    key === 'begin'
                  ) {
                    newObj[key] = 0
                  } else {
                    newObj[key] = item.m[key] ? parseInt(item.m[key]) : ''
                  }
                }
              } else {
                newObj.begin = 0
                newObj.end = 0
              }
              return newObj
            })
            .reverse()
          mapLegendM[3].end = ''
          // 季度
          const mapLegendQ = mapLegendfilterArr
            .map((item, index) => {
              const newObj = {}
              if (item.q) {
                for (const key in item.q) {
                  if (
                    index === mapLegendfilterArr.length - 1 &&
                    key === 'begin'
                  ) {
                    newObj[key] = 0
                  } else {
                    newObj[key] = item.q[key] ? parseInt(item.q[key]) : ''
                  }
                }
              } else {
                newObj.begin = 0
                newObj.end = 0
              }
              return newObj
            })
            .reverse()
          mapLegendQ[3].end = ''
          // 颜色
          // const mapLegendColour = mapLegendfilterArr.map((item) => {
          //   return item.colour
          // })
          // const mapLegendColour = ['#0050AB', '#0485BD', '#13B2F1', '#0BE8E2']
          const mapLegendColour = ['#4574CA', '#67C5F2', '#73D8A8', '#A8DDB4'].reverse()

          this.form.mapLegendD = mapLegendD
          this.form.mapLegendW = mapLegendW
          this.form.mapLegendM = mapLegendM
          this.form.mapLegendQ = mapLegendQ
          this.form.mapLegendColour = mapLegendColour

          // 处理当前id
          this.id = data.id

          this.$nextTick(() => {
            this.$refs.form.validate()
          })

          setTimeout(() => {
            this.loading = false
          }, 300)
        })
        .catch(() => {
          console.log('失败')
          this.loading = false
        })
    },

    /* 日视图校验 */
    validateCom1Fun(rule, value, callback) {
      this.commonValidate(
        {
          rule,
          value,
          callback
        },
        'mapLegendD'
      )
    },
    /* 周视图校验 */
    validateCom2Fun(rule, value, callback) {
      this.commonValidate(
        {
          rule,
          value,
          callback
        },
        'mapLegendW'
      )
    },
    /* 月视图校验 */
    validateCom3Fun(rule, value, callback) {
      this.commonValidate(
        {
          rule,
          value,
          callback
        },
        'mapLegendM'
      )
    },
    /* 季度视图校验 */
    validateCom4Fun(rule, value, callback) {
      this.commonValidate(
        {
          rule,
          value,
          callback
        },
        'mapLegendQ'
      )
    },

    /* 颜色选择后触发 */
    handleActiveChange(color, index) {
      this.$nextTick(() => {
        this.$set(this.form.mapLegendColour, index, color)
        this.$forceUpdate()
      })
    },

    handleInput() {
      this.$forceUpdate()
    },

    /* 是否是集团账户 */
    isGroup() {
      return new Promise((resolve) => {
        this.$aspHttps
          .asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/getCurrent')
          .then((response) => {
            if (this.$reponseStatus(response)) {
              const zxs = ['北京', '天津', '上海', '重庆']

              this.departmentId = zxs.includes(
                response.data.listDivisionName[0]
              )
              resolve()
            }
          })
          .catch(() => {
            this.loading = false
            resolve()
          })
      })
    },

    commonValidate({ rule, value, callback }, name) {
      const index = rule.field.match(/\[(\d+)\]/i)[1]

      const currentVal = this.$data.form[name][parseInt(index)]
      let currentEndVal = currentVal.end
      const currentBeginVal = currentVal.begin

      // 结束值不能小于等于开始值
      if (currentEndVal <= currentBeginVal) {
        callback(new Error('结束值不能小于等于开始值'))
        return
      }

      // 最多为4个, 所以到第4个暂停赋值
      if (parseInt(index) + 1 > 3) {
        callback()
        return
      }

      // 结束值为空或为0就为空
      if (currentEndVal === '' || currentEndVal === 0) {
        currentEndVal = ''
      } else {
        currentEndVal += 1
      }

      // if (
      //   this.$data.form[name][parseInt(index) + 1].begin > currentEndVal &&
      //   parseInt(index) + 1 === 3
      // ) {
      // } else {
      // }
      this.$data.form[name][parseInt(index) + 1].begin = currentEndVal

      callback()
    },

    handleClose() {
      this.$emit('changeShow', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.modificationOrderQuantity {
  .submitLi{
    margin-bottom: 10px;
  }

  .mapLegend{
    margin:20px 0;
  }

  .mapLegend {
    .con {
      dt {
        .fromItemBegin {
          .ipt {
            // width: 76px;
          }
        }
        display: flex;
        align-items: center;
        margin-bottom: 18px;
        ::v-deep .el-color-picker--medium .el-color-picker__mask {
          display: none;
        }
        ::v-deep .el-form-item__error {
          position: absolute;
          width: 300px;
          line-height: 1;
          padding-top: 4px;
        }
        .color {
          display: flex;
          align-items: center;
          margin-right: 10px;
        }
        ::v-deep .el-color-picker__trigger {
          width: 30px;
          height: 30px;
        }
        .ipt {
          width: 100px;
        }
        span {
          margin: 0 10px;
        }
      }
      .eltabs {
        width: 620px;
        ::v-deep .el-tabs__item {
          font-size: 13px;
        }
      }
    }
  }

  ::v-deep .el-checkbox__input {
    display: inline-flex;
    align-items: center;
  }

  ::v-deep .el-color-picker__color {
    border: 0 !important;
  }

  ::v-deep .el-checkbox {
    display: inline-flex;
    align-items: center;
  }
  .submitBtn {
    width: 100px;
  }
  .submitLi {
    display: flex;
    justify-content: center;
  }

  .el-checkbox {
    margin-right: 10px;
    margin-bottom: 10px;
    margin-left: 0;
  }
  .el-checkbox.is-bordered + .el-checkbox.is-bordered {
    margin-left: 0;
  }
  .commpnStyleWrapper {
    .content {
      overflow: hidden;
      overflow-y: scroll;
      ul {
        padding: 0px 20px;
        padding-top: 20px;
        padding-bottom: 40px;
        margin: 0;
        li {
          list-style: none;
          display: flex;
          margin-bottom: 30px;
          &:last-child {
            margin-bottom: 0;
          }
          &.flexColumn {
            flex-direction: column;
            .con {
              margin-top: 10px;
            }
          }

          &.topStyle {
            dl {
              display: flex;
              .tops {
                display: flex;
                align-items: center;
              }
              .l {
              }
              .r {
                flex: 1;
                min-width: 0;
                & >>> {
                  .el-checkbox {
                    margin-right: 10px;
                    margin-bottom: 10px;
                    margin-left: 0;
                  }
                }
                .tip {
                  text-align: left;
                  font-size: 13px;
                  color: #888;
                }
              }
            }
          }

          .title {
            font-size: 14px;
            color: #606266;
            margin-right: 30px;
            &.center {
              display: flex;
              align-items: center;
            }
          }
          .con {
            flex: 1;
            min-width: 0;

            .group {
              margin-right: 20px;
            }
          }

          &.mapLegend {
            .con {
              dl {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                margin-top: 0;

                .color {
                  // background: #3c067a;
                  margin-right: 20px;
                  // box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                  &.c1 {
                    background: #7915f5;
                  }
                  &.c2 {
                    background: #367baf;
                  }
                  &.c3 {
                    background: #95d1f4;
                  }
                }
                .tit {
                  font-size: 12px;
                  color: #333;
                }
                .ipt {
                  width: 80px;
                  margin: 0 5px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
