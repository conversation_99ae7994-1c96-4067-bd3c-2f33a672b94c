/* eslint-disable */

import prng4 from './prng4.js'

var rng_state
var rng_pool
var rng_pptr
function rng_seed_int(a) {
    rng_pool[rng_pptr++] ^= a & 255
    rng_pool[rng_pptr++] ^= (a >> 8) & 255
    rng_pool[rng_pptr++] ^= (a >> 16) & 255
    rng_pool[rng_pptr++] ^= (a >> 24) & 255
    if (rng_pptr >= prng4.rng_psize) {
        rng_pptr -= prng4.rng_psize
    }
}
function rng_seed_time() {
    rng_seed_int(new Date().getTime())
} if (rng_pool == null) {
    rng_pool = new Array()
    rng_pptr = 0
    var t
    if (navigator.appName == 'Netscape' && navigator.appVersion < '5' && window.crypto) {
        var z = window.crypto.random(32)
        for (t = 0;
            t < z.length;
            ++t) {
            rng_pool[rng_pptr++] = z.charCodeAt(t) & 255
        }
    } while (rng_pptr < prng4.rng_psize) {
        t = Math.floor(65536 * Math.random())
        rng_pool[rng_pptr++] = t >>> 8
        rng_pool[rng_pptr++] = t & 255
    } rng_pptr = 0
    rng_seed_time()
} function rng_get_byte() {
    if (rng_state == null) {
        rng_seed_time()
        rng_state = prng4.prng_newstate()
        rng_state.init(rng_pool)
        for (rng_pptr = 0;
            rng_pptr < rng_pool.length;
            ++rng_pptr) {
            rng_pool[rng_pptr] = 0
        } rng_pptr = 0
    } return rng_state.next()
} function rng_get_bytes(b) {
    var a
    for (a = 0;
        a < b.length;
        ++a) {
        b[a] = rng_get_byte()
    }
} function SecureRandom() { } SecureRandom.prototype.nextBytes = rng_get_bytes

export default { SecureRandom }
