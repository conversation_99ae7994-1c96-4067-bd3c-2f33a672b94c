function Arcfour() {
  this.i = 0;
  this.j = 0;
  this.S = [];
}
function ARC4init(d) {
  var c, a, b;
  for (c = 0; c < 256; ++c) {
    this.S[c] = c;
  }
  a = 0;
  for (c = 0; c < 256; ++c) {
    a = (a + this.S[c] + d[c % d.length]) & 255;
    b = this.S[c];
    this.S[c] = this.S[a];
    this.S[a] = b;
  }
  this.i = 0;
  this.j = 0;
}
function ARC4next() {
  var a;
  this.i = (this.i + 1) & 255;
  this.j = (this.j + this.S[this.i]) & 255;
  a = this.S[this.i];
  this.S[this.i] = this.S[this.j];
  this.S[this.j] = a;
  return this.S[(a + this.S[this.i]) & 255];
}
Arcfour.prototype.init = ARC4init;
Arcfour.prototype.next = ARC4next;
// eslint-disable-next-line
function prng_newstate() {
  return new Arcfour();
}
// eslint-disable-next-line
var rng_psize = 256

export default { rng_psize, prng_newstate };
