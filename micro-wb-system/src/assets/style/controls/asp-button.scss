/**
* Created by TurboC on 2019/08/01.
* 按钮样式
*/
@import '../../../../../assets/defines.scss';

.webbas {
    //实心带图标按钮样式
    .solid-with-icon-btn {
        font-size: 14px !important;
        padding: 7px 15.5px !important;
        border-radius: 4px !important;
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        color: rgba(255, 255, 255, 1) !important;
    }
    .solid-with-icon-btn:hover {
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        opacity: 0.9 !important;
    }
    .solid-with-icon-btn:active {
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        opacity: 1 !important;
    }
    .solid-with-icon-btn:focus {
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        opacity: 1 !important;
    }
    //空心带图标按钮样式
    .hollow-with-icon-btn {
        font-size: 14px !important;
        padding: 7px 15.5px !important;
        color: rgba(70, 118, 229, 1) !important;
        border-color: #419FFF !important;
    }
    .hollow-with-icon-btn:hover {
        border-color: #419FFF !important;
    }
    //实心不带图标按钮样式
    .solid-no-icon-btn {
        font-size: 14px !important;
        padding: 7px 25px !important;
        border-radius: 4px !important;
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        color: rgba(255, 255, 255, 1) !important;
    }
    .solid-no-icon-btn:hover {
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        opacity: 0.9 !important;
    }
    .solid-no-icon-btn:active {
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        opacity: 1 !important;
    }
    .solid-no-icon-btn:focus {
        background: linear-gradient(360deg, rgb(149, 150, 152) 0%, rgba(111, 158, 240, 1) 100%) !important;
        opacity: 1 !important;
    }
    //空心不带图标按钮样式
    .hollow-no-icon-btn {
        font-size: 14px !important;
        padding: 7px 25px !important;
        color: rgba(70, 118, 229, 1) !important;
        border-color: #419FFF !important;
    }
    .hollow-no-icon-btn:hover {
        border-color: #419FFF !important;
    }
    .hollow-no-icon-btn-white {
        font-size: 14px !important;
        padding: 7px 25px !important;
        color: rgba(70, 118, 229, 1) !important;
        border-color: #419FFF !important;
        background-color: #fff !important;
    }
    .hollow-no-icon-btn-white:hover {
        border-color: #419FFF !important;
    }
    //实心字数较多的按钮样式
    .solid-no-icon-many-btn {
        font-size: 14px !important;
        padding: 7px 15px !important;
        border-radius: 4px !important;
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        color: rgba(255, 255, 255, 1) !important;
    }
    .solid-no-icon-many-btn:hover {
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        opacity: 0.9 !important;
    }
    .solid-no-icon-many-btn:active {
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        opacity: 1 !important;
    }
    .solid-no-icon-many-btn:focus {
        background: linear-gradient(360deg, rgba(69, 118, 228, 1) 0%, rgba(111, 158, 240, 1) 100%) !important;
        opacity: 1 !important;
    }
    //空心字数较多按钮样式
    .hollow-no-icon-many-btn {
        font-size: 14px !important;
        padding: 7px 15px !important;
        color: rgba(70, 118, 229, 1) !important;
        border-color: #419FFF !important;
    }
    .hollow-no-icon-many-btn:hover {
        border-color: #419FFF !important;
    }
    //无按钮样式
    .text-font-btn {
        font-size: 14px !important;
        padding: 7px 25px !important;
        color: $--color-primary !important;
    }
    // 按钮居中
    .center_button {
        text-align: center !important;
        margin-top: 15px !important;
    }
    // 单选按钮纵向布局
    .radio-column .el-radio {
        width: 100%;
        line-height: 2;
    }
    // 新增表格操作栏按钮样式
    .add-table-btn-css {
        font-size: 17px;
        color: #53607e;
    }
}
