/**
* Created by TurboC on 2020/03/23.
* 重写附件样式
*/
.webbas {
    .el-transfer-panel {
        width: 45%;
    }
    .el-transfer__buttons {
        padding: 0;
    }
    .el-transfer__buttons .el-button {
        background: linear-gradient(360deg, #4576e4 0%, #6f9ef0 100%);
    }
    .el-transfer__buttons .el-button.el-button--primary.is-disabled{
        color: #FFF;
        background: #98C1ED;
        border-color: #98C1ED;
    }
    /*定义穿梭框宽度*/
    .el-transfer {
        // .el-transfer-panel {
        //     width: 40%;
        // }
        .el-transfer__buttons {
            width: 10%;
            text-align: center;
            .el-button {
                padding: 6px 15px;
            }
            .el-button:nth-child(2) {
                margin: 0px;
                display: inline-block;
            }
            .el-button [class*=el-icon-]+span {
                margin: 0px;
            }
        }
    }
}


