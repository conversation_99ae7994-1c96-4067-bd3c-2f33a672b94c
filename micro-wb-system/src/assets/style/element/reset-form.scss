/**
* Created by TurboC on 2019/08/01.
* 重写表单样式
*/
.webbas {
    /*优化查询菜单前面标题栏展示样式*/
    .el-form-item__content {
        .search-label {
            line-height: 32px;
            padding: 0px 20px;
            border: 1px solid #dcdfe6;
            border-right: 0;
            border-radius: 4px 0 0 4px;
            background-color: #f5f7fa;
            color: #909399;
            margin-right: -5px;
            cursor: text;
            display: table-cell;
        }
        .search-label + .el-select > .el-input--suffix > .el-input__inner {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
    }
    .el-form {
        width: 100%;
    }
    .el-form-item {
        margin-bottom: 0;
        margin-right: 0;
        width: 100%;
    }
    .el-form-item__label {
        float: left;
        width: 160px;
        text-align: right;
        line-height: 30px;
        padding: 0 3px 0 0;
        font-weight: bold;
        color: #333333;
    }
    .el-form-item__content {
        width: calc(100% - 160px);
        text-align: left;
        float: left;
        line-height: 30px;
        a.el-link.el-link--primary {
            line-height: 16px;
            margin-right: 10px;
            display: inline-block;
            max-width: calc(100% - 80px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
    .el-form-item__error {
        line-height: inherit;
        padding-top: 0;
        position: relative;
    }
}

