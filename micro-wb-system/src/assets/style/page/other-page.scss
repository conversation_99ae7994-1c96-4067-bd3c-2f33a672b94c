/**
* Created by TurboC on 2020/03/06.
* 不清楚使用作用的样式
*/
.webbas {
    // 解决多输入框并排排版错位问题
    .middle-select—input0 .el-form-item__content {
        .el-input {
            width: 100%!important;
        }
    }
    .middle-select—input .el-form-item__content {
        width: 100%;
        .el-select {
            width: 40%!important;
        }
        .el-button.solid-no-icon-many-btn.el-button--default {
            width: 55%!important;
        }
    }

    // 需要排查为什么添加
    .modify-add {
        background: #f2dede;
    }
    .modify-delete {
        text-decoration: line-through;
    }
    .modify-color {
        color: #e25856;
    }
    .thead-required-css div:before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
    }
    .operation-cursor {
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
    }
    // 批量导入错误信息table
    .table-error-detail {
        .el-table {
            max-height: 600px;
            overflow: auto;
        }
    }

    // 基础信息类目树增删修图标样式
    .baseTreeOperate {
        i[class^="el-icon"] {
            margin-left: 5px;
            font-size: 15px;
        }
    }
}

.width-100-css {
    width: 100%;
}
