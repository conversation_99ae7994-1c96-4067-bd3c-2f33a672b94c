/**
* Created by TurboC on 2019/08/01.
* 全局页面背景样式
*/
.webbas {
    // 页面背景
    .background-css {
        background-color: #fff;
        padding: 5px 15px 5px 15px;
        border: 1px solid #dee8f8;
        margin-bottom: 15px;
        min-height: 500px;
    }

    // 折叠框相关样式
    .wb-collapse-first { // 一级折叠框
    	border-bottom: none;
    }
    .wb-collapse-first > .el-collapse-item {
        border-left: none;
        border-right: none;
    }
    .wb-collapse-first > .el-collapse-item > div > .el-collapse-item__header {
        background: #f8fbfc;
        border: 1px solid $wb-theme-color;
        font-size: 16px;
        font-weight: 500;
        color: $wb-theme-color;
        padding: 5px 15px;
        height: 22px;
    }
}
