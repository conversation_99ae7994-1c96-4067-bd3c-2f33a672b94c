/**
* Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/09/25.
* 日历页面样式
*/
.webbas {
    .el-calendar-table .el-calendar-day {
        position: relative;
        padding: 2px;
        height: 50px;
    }
    .dayOff {
        color: #e02d2d;
        font-weight: bold;
    }
    .dayOff::before{
        content: '\4F11';
        color: white;
        position: absolute;
        top: 0px;
        left: 0px;
        background: red;
        width: 14px;
        font-size: 12px;
        font-weight: 700;
    }
    .unifyDay {
        color: #e02d2d;
        font-weight: bold;
    }
    .unifyDay::before{
        content: '\4F11';
        color: white;
        position: absolute;
        top: 0px;
        left: 0px;
        background: red;
        width: 14px;
        font-size: 12px;
        font-weight: 700;
    }
    .workDay {
        color: green !important;
        font-weight: bold;
    }
    .workDay::before{
        content: '\73ED';
        color: white;
        position: absolute;
        top: 0px;
        left: 0px;
        background: green;
        width: 14px;
        font-size: 12px;
    }
    .el-calendar__header{
        display: none;
    }
    .el-calendar__body {
        padding: 0px;
    }
    .el-calendar-table thead th {
        color: #0072E3;
    }
    .el-calendar-table tr td:first-child {
        border-left: none;
    }
    .el-calendar-table tr td {
        border-right: none;
    }
    .next {
        display: none;
    }
    .prev {
        border-right: none;
        pointer-events: none;
    }
    .calendar-style {
        padding: 5px 15px 5px 15px !important;
        box-shadow: 0px 0px 0px 0px #d4d5d6;
    }
    .el-calendar {
        font-size: 14px;
        border: 1px solid #EBEEF5;
    }
    .calendar-style span {
        display: block;
        text-align: center;
        margin-top: 4px;
    }
    .calendar-title {
        background-color: #0072E3;
    }
    .calendar-title div {
        line-height: 45px;
        color: #ffffff;
        display: inline-block;
    }
    .calendar-months {
        font-size: 18px;
        margin-left: 20px;
    }
    .calendar-holiday {
        font-size: 14px;
        float: right;
        margin-right: 20px;
    }
    .calendar-font-10-css {
        font-size: 10px
    }
}
