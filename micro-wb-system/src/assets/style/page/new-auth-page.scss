/**
* Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/09/26.
* tree样式
*/
.webbas {
    .webbas-tree-css {
      background-color: #fff;
      box-shadow: 0px 0px 0px 0px #d4d5d6;
      margin-bottom: 0px;
      // .tree-title {
      //     font-size: 16px;
      //     font-weight: bold;
      //     padding: 15px;
      //     border-bottom: 1px solid #dee8f8
      // }
      .tree-title span {
          color: #537ee4;
      }
      .el-tree-node__expand-icon.is-leaf {
          opacity: 0;
          color: transparent;
          cursor: default;
      }
      .el-tree-node {
          white-space: nowrap;
          display: block;
          outline: none;
      }
      .el-tree-node__content{
          clear: both;
      }
      .levelname {
          line-height: 28px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-top: 5px;
          padding-bottom: 5px;
      }
      // 重写样式--yuxuan--2021-09-06
      // .treeNodeLeafParent { // 改成手写
      //   float: left;
      //   width: 180px;
      // }
      .el-tree-node__content {
        padding-left: 0!important;
      }
      .el-tree-node>.el-tree-node__children {
        display: block;
        padding-left: 18px;
      }
    }
}
