/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2020/06/29.
* 机构、部门管理相关页面样式
*/
.webbas {
    .dept-config {
        .tree {
            overflow: auto;
            max-height: 660px;
        }
        .position-title {
            position: absolute;
            z-index: 1000;
            top: 15px;
            right: 30px;
        }
        .el-tree {
            .el-checkbox__input {
                display: none;
            }
            min-width: 100%;
            display: inline-block !important;
        }
        .el-table .cell .icon-style > span {
            border-radius: 100%;
            width: 10px;
            height: 10px;
            display: inline-block;
        }
        .el-tree-node {
            width: 100%;
        }
        .custom-tree-node {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            padding-right: 8px;
            margin-left: 8px;
        }
        .deptName {
            font-size: 14px;
            align-items: center;
        }
        .deptTreeOperate {
            width: 75px;
            padding: 5px;
            align-items: center;
            position: relative;
            right: 0;
            color: #3a8ee6;
            text-align: center;
            i[class^="el-icon"] {
                margin-left: 10px;
                font-size: 15px;
                &:first-child {
                    margin-left: 0;
                }
            }
            i[class^="el-icon-noth"] {
                margin-left: 25px;
            }
        }
        .item-box {
            border-radius: 2px;
            border: 1px solid #cccccc;
            font-size: 14px;
            .item-content {
                width: 100%;
                display: flex;
                border-bottom: 1px solid #cccccc;
                div {
                    flex: 1;
                    span:nth-child(1) {
                        padding: 10px;
                        display: inline-block;
                        width: 130px;
                        background-color: #f0f4f8;
                        text-align: left;
                    }
                    span:nth-child(2) {
                        padding: 10px;
                        text-align: left;
                    }
                }
            }
            .item-content-bottom {
                padding: 10px;
                min-height: 50px;
                width: 100%;
                overflow: hidden;
            }
        }
        .el-button-group > button {
            border-color: #3a8ee6;
        }
        .el-button-group > button:nth-child(1) {
            margin-right: 10px;
        }

        .clearfix .el-form > .el-form-item {
            margin-bottom: 0px;
        }
        .el-col.el-col-8 > .el-card {
            height: 658px;
        }
        .form-text-belongOrg {
            line-height: 34px;
            word-break: break-all;
        }
		.form-text-role {
            padding: 7px 0;
            line-height: 26px;
        }
        .box-card-tree {
          ::v-deep .el-card__body{
            .el-tree-node__content:hover{
                 color: #3a8ee6;
                .deptTreeOperate{
                    display: block;
                }
            }
            .el-tree-node__expand-icon{
                padding: 2px;
            }
            .el-icon-plus{
                background-color: #E0E7F4;
                color: #fff;
                border-radius: 2px;
            }
            .el-icon-plus:before {
                font-size: 14px;
            }
            .expanded.el-icon-plus{
                transform: rotate(0)!important;
                font-size: 10px;
                &::before{
                    content: "\e6d8"!important;
                }
            }
            .is-leaf{
                background: transparent;
            }
        }
        }
    }
    .color-auth-css {
        color: #537ee4
    }
}