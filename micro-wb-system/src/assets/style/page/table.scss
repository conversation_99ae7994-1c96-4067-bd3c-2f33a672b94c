/**
* Created by TurboC on 2019/08/01.
* 列表页面样式
*/
.webbas {
    .table-css {
        width: 99%;
        margin: 10px;
    }
    .table-css {
        font-size: 14px;
        text-align: center;
        border: 1px solid #E1E7F3;
        border-collapse: collapse;
    }
    .table-css th, .table-css td {
        border: 1px solid #E1E7F3;
    }

    .modify-add {
        background: #f2dede;
    }
    .modify-delete {
        text-decoration: line-through;
    }
    .modify-color {
        color: #e25856;
    }
    .thead-required-css div:before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
    }
    .operation-cursor {
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
    }
    .warn-tip {
        color: blue;
        line-height: inherit;
        padding-top: 0;
        position: relative;
    }
    .feedback-his {
        margin-bottom: 5px;
        .feedback-time {
            border: 1px solid #dfe9fc;
            border-bottom: unset;
            line-height: 32px;
            color: #e25856;
            padding: 0 5px;
        }
    }
}
