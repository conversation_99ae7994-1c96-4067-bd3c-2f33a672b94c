/**
* Created by TurboC on 2019/08/01.
* 列表页面样式
*/
.webbas {
    height: 100%;
    .list-page-css {
        height: calc(100% - 38px);
        border-bottom: none;
        .el-card {
            height: 100%;
        }
        .el-card__header {
            padding: 7px 13px;
            font-size: 14px;
            font-weight: bold;
            color: #606266;
        }
        .el-card__body {
            padding: 5px;
            height: calc(100% - 38px);
            overflow: auto;
        }
        .el-card__body::-webkit-scrollbar {
            width: 5px; // 横向滚动条
            height: 11px; // 纵向滚动条
        }
        .el-tree-node > .el-tree-node__children {
            overflow: inherit;
        }
    }
    .list-page-css > .el-col {
        height: 100%;
    }
    .list-page-content-css {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background: #f5f8fa;
    }
    .query-area-btn-css {
        margin-right: 0;
    }
    .query-area-content-css {
        background-color: #fff;
        padding: 5px 10px 4px 10px;
        box-shadow: 0px 0px 0px 0px #d4d5d6;
        border: 1px solid #dee8f8;
        margin-bottom: 8px;
        .el-tabs.el-tabs--top { // tab的间距
            margin-bottom: 4px;
            margin-top: 0px;
        }
        .el-form-item__label {
            width: 70px;
            padding: 0;
        }
        .el-form-item__content {
            width: calc(100% - 70px);
        }
        .el-col + .el-col {
            padding-left: 15px;
        }
        .el-row + .el-row {
            margin-top: 5px;
        }
        .query-area-btn-css .el-form-item__content {
            width: 100%;
            text-align: right;
        }
    }

    .list-area-css {
        display: flex;
        height: 10%;
        flex-direction: column;
        flex-grow: 1;
        background-color: #fff;
        padding: 4px 10px 5px 10px;
        box-shadow: 0px 0px 0px 0px #d4d5d6;
        border: 1px solid #dee8f8;
    }

    .list-top-btn { // 按钮与表单的间距
        margin-bottom: 7px;
        overflow: visible;
    }
    .list-table-css {
        flex-grow: 1;
        overflow: auto;
        .el-button + .el-button {
            margin-left: 0;
        }
    }
    .list-statistics-page-css {
        height: auto;
        position: relative;
        .statistics-date-color {
            color: #0066FF;
            .statistics-align-css{
                width: 98px;
                display:inline-block;
                text-align: right
            }
        }
    }
    .list-page-content-css .list-statistics-button-css {
        padding: 5px 0px;
        display: inline-block;
        top: 2px;
        position: absolute;
        right: 0px;
    }

    .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
        background-color: #fff;
    }

    .list-statistics-table-css{
        .el-table th.gutter {
            display: table-cell !important;
            width: 17px !important
        }
        .el-table colgroup.gutter{
            display: table-cell !important;
            width: 17px !important
        }
        .el-table .el-table__header-wrapper {
            //overflow: hidden;
            thead {
                color: rgba(83, 96, 126, 1);
            }
        }
        .is-group {
            th {
                background-color: #E8F0FC;
                border-top: 1px solid rgba(195, 213, 255, 0.5);
            }
        }
        .el-table__body-wrapper::-webkit-scrollbar {
            width: 10px; // 横向滚动条
            height: 10px; // 纵向滚动条 必写
        }
    }
}


// 成员管理归属部门弹出框样式
.icon-absolute-css { // 输入框中的icon位置重置
    position: absolute;
    z-index: 1;
    right: 0;
}
.popover-tree {
    max-height: 200px;
    overflow: auto;
    // scrollbar-width: none;
    -ms-overflow-style: none;
}
.popover-tree::-webkit-scrollbar {
    width: 5px;
}
