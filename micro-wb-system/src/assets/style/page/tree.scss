/**
* Created by TurboC on 2019/08/01.
* 个性label长度
*/
.webbas {
  .dept-config {
    .tree {
      overflow: auto;
      max-height: 660px;
    }
    .el-tree {
      .el-checkbox__input {
        display: none;
      }
      min-width: 100%;
      display: inline-block !important;
      .el-tree-node {
        > .el-tree-node__content {
          // background-color: #f5f7fa;
          .tree-node-can-edit {
            color: #3a8ee6;
          }
        }
      }
    }
    .el-table .cell .icon-style > span {
      border-radius: 100%;
      width: 10px;
      height: 10px;
      display: inline-block;
    }
    .el-tree-node {
      width: 100%;
    }
    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      padding-right: 8px;
      margin-left: 8px;
    }
    .deptName {
      font-size: 14px;
      align-items: center;
    }
    .deptTreeOperate {
      width: 75px;
      padding: 5px;
      align-items: center;
      position: relative;
      right: 0;
      color: #3a8ee6;
      text-align: center;
      i[class^='el-icon'] {
        margin-left: 10px;
        font-size: 15px;
        &:first-child {
          margin-left: 0;
        }
      }
      i[class^='el-icon-noth'] {
        margin-left: 25px;
      }
    }
    .el-button-group > button {
      border-color: #3a8ee6;
    }
    .el-button-group > button:nth-child(1) {
      margin-right: 10px;
    }
    .clearfix .el-form > .el-form-item {
      margin-bottom: 0px;
    }
    .el-col.el-col-7 > .el-card {
      max-height: 626px;
      overflow: auto;
    }
    .box-card-tree {
      ::v-deep .el-card__body {
        .el-tree-node__content:hover {
          color: #3a8ee6;
          .deptTreeOperate {
            display: block;
          }
        }
        .el-tree-node__expand-icon {
          padding: 2px;
        }
        .el-icon-plus {
          background-color: #e0e7f4;
          color: #fff;
          border-radius: 2px;
        }
        .el-icon-plus:before {
          font-size: 14px;
        }
        .expanded.el-icon-plus {
          transform: rotate(0) !important;
          font-size: 10px;
          &::before {
            content: '\e6d8' !important;
          }
        }
        .is-leaf {
          background: transparent;
        }
      }
    }
  }
}
