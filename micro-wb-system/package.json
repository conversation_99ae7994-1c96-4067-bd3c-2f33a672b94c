{"name": "system", "author": "TurboC", "description": "webbas权限管理模块", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@babel/runtime": "^7.1.2", "asp-smart-layout": "3.0.5", "asp-smart-ui": "^1.0.43", "axios": "^1.7.7", "babel-polyfill": "^6.26.0", "concurrently": "^5.0.2", "core-js": "^3.4.4", "crypto-js": "4.0.0", "element-ui": "^2.15.1", "es6-promise": "^4.2.8", "gcoord": "^1.0.7", "js-base64": "^3.7.2", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "mockjs": "^1.1.0", "qrcode": "^1.5.4", "qrcode.vue": "^3.6.0", "rxjs": "^6.5.4", "tinymce": "^5.10.3", "vue": "2.6.14", "vue-baidu-map": "^0.21.22", "vue-json-viewer": "^2.2.21", "vue-qr": "^4.0.9", "vue-router": "^3.1.6", "vue-simple-uploader": "^0.7.6", "vuex": "^3.1.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^5.0.1", "eslint": "^6.7.2", "eslint-plugin-import": "2.27.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "sass": "1.26.5", "sass-loader": "^8.0.2", "terser-webpack-plugin": "^4.2.3", "vue-template-compiler": "2.6.14", "webpack": "4.47", "webpack-theme-color-replacer": "^1.3.26"}}