# 微前端
## 启动指南
  1. 当为第一次加载启动项目，需先在根目录下，执行：cnpm i 加载全局依赖；
  2. 再继续执行各服务包加载依赖，统一执行指令：cnpm run install-all
    也可以执行按需加载更新依赖：cnpm run install:项目名称  （参考package.json)
  3. 启动项目，统一执行指令：cnpm run start-all 或者/ cnpm run serve-all
    也可以执行按需分批启动项目：cnpm run start:项目名称 或者/ cnpm run serve:项目名称  （参考package.json)
  4. 打包项目，统一执行指令：cnpm run build-all
    也可以执行按需分批启动项目：cnpm run build:项目名称  （参考package.json)

## 全局配置信息配置说明：（config.json)
```javascript
{
  "pageConfig": { // 页面信息配置
    "homePage": { // home主页面信息配置
      "microAppPre": "/eomp/", // home主页面所在的应用；（一般为应用前缀）
      "routerPath": "/home", // home主页面的页面路由地址
      "routerName": "home", // home主页面的页面路由name
      "pageId": "wb_01", // home主页面的页面资源id（或所对应的菜单资源id）
      "titleName": "首页" // home主页面的面包屑名称
    },
    "loginPage": { // 登录页面相关配置，其他配置同上
      "microAppPre": "/login/",
      "routerPath": "/login",
      "routerName": "login",
      "titleName": "登陆"
    },
    "e404Page": { // 404页面相关配置，其他配置同上
      "microAppPre": "/login/",
      "routerPath": "/404",
      "routerName": "404",
      "titleName": "404"
    },
    "iframePage": { // iframe嵌套页面相关配置，其他配置同上
      "microAppPre": "/system/",
      "routerPath": "/frame/framePage",
      "routerName": "framePage",
      "titleName": "iframe"
    },
    "datePanel": { //  日期面板页面相关配置，其他配置同上
      "isShowDatePanel": true,
      "microAppPre": "/system/",
      "routerPath": "/system/calendar",
      "routerName": "calendar",
      "titleName": "日期面板"
    }
  },
  "mainFrameConfig": { // 主应用相关配置
    "devPort": 8010 // 主应用本地运行开发环境所对应的端口
  },
  "noAuthMicroApps": [ // 无登录信息/未登录时的相关应用配置--这里以login登录应用为例
    {
      "microAppName": "login", // 子应用的名称
      "microAppPre": "/login/", // 子应用的代理前缀
      "microAppEnter": "/subapp/login/", // 子应用的访问前缀（主要为主应用访问）
      "isMicroApp": true, // 是否为主应用默认访问应用（即未指定访问某个子应用，且进入主应用时，默认访问当前子应用）
      "devPort": 8011 // 子应用本地运行开发环境所对应的端口
    }
  ],
  "authMicroApps": [ // 登录后的可访问应用--这里以system系统设置应用为例
    {
      "microAppName": "system", // 系统设置应用的名称
      "microAppPre": "/system/", // 系统设置应用的代理前缀
      "microAppEnter": "/subapp/system/", // 系统设置应用的访问前缀（主要为主应用访问）
      "isMicroApp": false, // 是否为主应用默认访问应用（即未指定访问某个子应用，且进入主应用时，默认访问当前子应用）
      "devPort": 8012 // 系统设置应用本地运行开发环境所对应的端口
    }
  ],
  "proxyConfig": { // 本地环境接口代理转发
    "/proxy_webbas": { // 本地访问代理前缀
      "target": "http://10.12.12.221:7001", // 本地访问代理所指向的后端服务
      "changeOrigin": true, // 建议开启，设置为true，否则会造成代理失败的问题
      "pathRewrite": { // 重写转发代理前缀，若无需重写可不用配置此项
        "^/proxy_webbas": "/"
      }
    }
  },
  "apiConfig": { // 接口请求相关配置
    "proxyConfig": [ // 接口代理前缀相关配置（主要用于本地代理前缀和NG代理前缀统一动态切换用的），按项目需要配置
      {
        "localProxy": "/proxy_webbas", // 本地代理前缀
        "nginxProxy": "/nginx_webbas" // NG代理前缀
      },
      {
        "localProxy": "/proxy_eomp",
        "nginxProxy": "/nginx_eomp"
      }
    ],
    // 请求后端服务接口前缀配置---按项目实际需求配置
    "authPathPrefix": "/proxy_webbas/web/auth/v1",
    "supportPathPrefix": "/proxy_webbas/web/support/v1",
    "managerPathPrefix": "/proxy_webbas/web/partner/v1",
    "safePathPrefix": "/proxy_webbas/web/security/v1",
    "authUaPrefix": "/proxy_webbas/web/ua/v1"
  },
  "projectConfig": { // 项目基本配置信息
    "projectNavMemoryKey": "webbas_navMemory", // 当前工程的路由缓存名称，用于刷新时保持当前路由地址（主要记录菜单级路由地址；原因与菜单定位保持一致）
    "multiMenu": false, // 是否开启多系统开关配置,默认为单系统（false)
    "defaultNodeMenuId": "多系统根级菜单节点Id", // 开启多系统菜单后，会针对不同应用服务，需要配置多个应该的根级菜单，此处为登录后默认展示的应用的根级菜单；（用途：过滤出当前默认应用菜单，多系统菜单切换呈现等）
    "domain": "admin", // 当前系统所属域（默认为admin）
    "operator": "domain" // 存储和读取登录信息相关的默认key, // 存储和读取登录信息相关的默认key
    "themeColor": "#3498DB", // 默认主题色
    "themeSwitch": false, // 主题功能开关配置（首先根据登录接口返回的platformConfig的user-default-skin和user-default-navigator配置来判断是否支持主题功能，如果支持则根据此开关来决定是否开启主题功能，默认不开启）
    "haveMenuSpan": { //有顶部横向菜单模式，以下配置项根据实际项目配置
      "logoTitleSpan": 7, // 左侧LOGO及title部分宽度（span）
      "multiSysBtnSpan": 0, // 多系统按钮部分宽度（span）
      "topMenuSpan": 10, // 顶部菜单部分宽度（span）
      "personalSpan": 7 // 主题图标、个人中心部分宽度（span）
    },
    "noMenuSpan": { // 无顶部横向菜单模式，以下配置项根据实际项目配置
      "logoTitleSpan": 12, // 左侧LOGO及title部分宽度（span）
      "multiSysBtnSpan": 0, // 多系统按钮部分宽度（span）
      "topMenuSpan": 0, // 顶部菜单部分宽度（span）
      "personalSpan": 12 // 主题图标、个人中心部分宽度（span）
  }
}
```

## 爬坑记录
1. 想要添加页面用到的接口或添加左侧导航的路由, 要在代码里面写路由地址与组件, 然后使用超级管理员在 系统管理 -> 资源管理 -> 资源列表 -> 新增资源, 增加路由地址与当前路由用到的接口
2. 想要在左侧导航显示<线上店管理>, 需要配置当前账号的权限, 系统管理 -> 机构角色管理 -> 新增一个角色(线上店管理) -> 给当前角色分配权限(线上店管理) -> 点击部门管理 -> 找到对应角色 -> 查看 -> 分配角色权限(线上店管理) -> 保存 -> 清理缓存并重新登录即可 

## staff资源列表
```javascript
"线上店管理":{
    "资源ID": "sd_03200000",
    "资源名称": "线上店管理",
    "资源描述": "用于管理线上店",
    "所属领域": "管理员域",
    "所属领域模块": "auth",
    "父资源ID": "",
    "资源类别": "菜单",
    "资源KEY": "sd_03200000",
    "图标": "icon-line-113",
    "访问地址URL": "/operator/#/management/onlineshop",
    "菜单按钮依赖关系": "",
    "菜单列表展示顺序": 6000,
    "请求方法": "GET"
}
```

## 测试账号
1.超级管理员账号
webbas
Bg#work688

2.全国
tuhao
QWas135!#&

3.北京账号
JS_001
Test139#

4.北京账号
BJ_001
Test139#

