
class ReplaceStringPlugin {
  /**
   * 
   * @param {ReplaceOption} options 
   */
  constructor(options) {
    // console.log('ReplaceStringPlugin', options)
    this._rules = options.rules
    this._miles = 0
  }

  apply (compiler) {
    compiler.hooks.emit.tapAsync('ReplaceStringPlugin', (compilation, callback) => {
      const start = Date.now()
      Object.keys(compilation.assets).forEach((key) => {
        if (key.endsWith('.js')) {
          let content = compilation.assets[key].source()
          try {
            this._rules.forEach(rule => {
              content = this._handleContent(content, key, rule)
            })
          } catch(e) {
            compilation.errors.push(e)
          }
          compilation.assets[key] = {
            source: () => content,
            size: () => content.length
          }
        }
      })
      console.log(`Plugin:ReplaceStringPlugin:: [_handleContent] 共耗时：${this._miles}ms`)
      console.log(`Plugin:ReplaceStringPlugin:: [hooks:tapAsync] 共耗时：${Date.now() - start}ms`)
      callback()
    })
  }

  /**
   * 处理需要替换的字符串
   * @param {*} content fileContent
   * @param {*} key fileName
   * @returns content
   */
  _handleContent (content, key, rule) {
    if (typeof content !== 'string') return content
    if (rule.include) {
      // include 不匹配，忽略规则
      if (rule.include instanceof RegExp && !key.match(rule.include)) {
        return content
      }
      // include 不匹配，忽略规则
      if (typeof rule.include === 'string' && key.indexOf(rule.include) === -1) {
        return content
      }
    }
    if (rule.test instanceof Array) {
      rule.test.forEach(rexRep => {
        // console.log('_handleContent::match', rexRep, rule.target)
        content = this._replaceStr(content, key, rexRep, rule.target)
      })
    } else {
      console.error('config `test` must be Array')
      throw new Error('config `test` must be Array, please ensure that the configuration of [ReplaceStringPlugin] is correct.')
    }
    return content
  }

  /**
   * 替换字符串
   * @param {*} content
   * @param {*} key
   * @param {*} stringToReplace
   * @param {*} replaceStr
   * @returns
   **/
  _replaceStr (content, key, stringToReplace, replaceStr) {
    const start = Date.now()
    // console.log('key:', key, ';stringToReplace: ', stringToReplace)
    if (
      (stringToReplace instanceof RegExp && content.match(stringToReplace)) ||
      (typeof stringToReplace === 'string' && content.indexOf(stringToReplace) > -1)
    ) {
      console.log('key:', key, ';regExpOrString: ', stringToReplace)
      content = content.replace(stringToReplace, ($1) => {
        const result = replaceStr || ''
        console.log(`匹配到的字符串：${$1},替换为：${result}`)
        return result
      })
    }
    this._miles += Date.now() - start
    return content
  }
}

module.exports = ReplaceStringPlugin