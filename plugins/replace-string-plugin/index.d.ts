declare class ReplaceStringPlugin {
  constructor(option: ReplaceStringPluginOption)
}
/**
 * 匹配的pattern
 */
type ReplacePattern = string | RegExp

/**
 * 匹配的规则配置
 *
 */
interface RuleItem {
  // 匹配的正则表达式或字符串
  /**
   * @example 多个字符串：['new RegExp("^(.*?:)", "u")', 'new RegExp("^(.*?:)","u")']
   * @example 多个正则：[
                /\/opt\/aspire\/product\/rancher([\w\-/.]+)/g,
                /http:\/\/10.[\w./:-]+/g
              ]
    * @example 字符串与正则混合：[
                /http:\/\/10.[\w./:-]+/g,
                'password'
              ]
   */
  test: ReplacePattern[]
  // 包含的文件，默认为全部js文件
  include?: string
  // 需要替换的字符串，默认为空字符串
  target?: string
}

/**
 * 插件配置项
 */
interface ReplaceStringPluginOption {
  /**
   * 插件规则
   * @example 
   * rules: [{
              test: ['new RegExp("^(.*?:)", "u")', 'new RegExp("^(.*?:)","u")'],
              include: 'element-templates-validator',
              target: 'new RegExp("^(.*?:)")'
            },
            {
              test: [
                /\/opt\/aspire\/product\/rancher([\w\-/.]+)/g,
                /http:\/\/10.[\w./:-]+/g
              ],
              include: 'element-templates-validator'
            }]
  */
  rules: Array<RuleItem>
}
