<?xml version="1.0" encoding="UTF-8"?>
<metadata module="auth" domain="admin">
	<resource id="wb_ex_2" resourceKey="wb_ex_2" type="exclude" name="获取登录后全局数据" url="/portal/main/getMainPageData"/>
    <resource id="wb_ex_3" resourceKey="wb_ex_3" type="exclude" name="退出登录" url="/portal/logout"/>
    <resource id="wb_ex_4" resourceKey="wb_ex_4" type="exclude" name="查找当前账户信息" url="/web/partner/v1/user/get"/>
    <resource id="wb_ex_5" resourceKey="wb_ex_5" type="exclude" name="修改当前账户信息" url="/web/partner/v1/user/updateCurrent"/>
    <resource id="wb_ex_6" resourceKey="wb_ex_6" type="exclude" name="修改当前账户密码" url="/web/partner/v1/user/changePwd"/>
    <resource id="wb_ex_7" resourceKey="wb_ex_7" type="exclude" name="检查用户邮箱是否被使用" url="/web/partner/v1/user/checkEmail"/>
    <resource id="wb_ex_8" resourceKey="wb_ex_8" type="exclude" name="检查手机号是否被使用" url="/web/partner/v1/user/checkMobile"/>
    <resource id="wb_ex_9" resourceKey="wb_ex_9" type="exclude" name="新增暂存附件" url="/web/support/v1/attachment/add"/>
    <resource id="wb_ex_10" resourceKey="wb_ex_10" type="exclude" name="查询正式文件列表" url="/web/support/v1/attachment/list"/>
    <resource id="wb_ex_11" resourceKey="wb_ex_11" type="exclude" name="生效单个附件" url="/web/support/v1/attachment/formalFile"/>
    <resource id="wb_ex_12" resourceKey="wb_ex_12" type="exclude" name="下载单个附件" url="/web/support/v1/attachment/downloadFile"/>
    <resource id="wb_ex_13" resourceKey="wb_ex_13" type="exclude" name="生效附件组" url="/web/support/v1/attachment/formalGroup"/>
    <resource id="wb_ex_14" resourceKey="wb_ex_14" type="exclude" name="查询附件类型" url="/web/support/v1/attachment/viewType"/>
    <resource id="wb_ex_15" resourceKey="wb_ex_15" type="exclude" name="查询地域数据" url="/web/support/v1/division/listDivisionTree"/>
    <resource id="wb_ex_16" resourceKey="wb_ex_16" type="exclude" name="获取待办信息" url="/web/operating/v1/pendTask/list"/>
	<resource id="wb_ex_17" resourceKey="wb_ex_17" type="exclude" name="获取跑马灯信息" url="/web/operating/v1/annoucement/horseLantern/list"/>
    <resource id="wb_ex_18" resourceKey="wb_ex_18" type="exclude" name="获取跑马灯详情" url="/web/operating/v1/annoucement/horseLantern/detail"/>
    <resource id="wb_ex_19" resourceKey="wb_ex_19" type="exclude" name="获取找回密码短信验证码" url="/web/partner/v1/user/findPwdSmsCode"/>
    <resource id="wb_ex_20" resourceKey="wb_ex_20" type="exclude" name="验证短信验证码" url="/web/partner/v1/user/validateSmsCode"/>
    <resource id="wb_ex_21" resourceKey="wb_ex_21" type="exclude" name="找回密码重设密码" url="/web/partner/v1/user/modifyLostedPwd"/>
    <resource id="wb_ex_22" resourceKey="wb_ex_22" type="exclude" name="静态密码验证" url="/web/ua/v1/login/verityPwd"/>
    <resource id="wb_ex_23" resourceKey="wb_ex_23" type="exclude" name="密钥生成" url="/web/ua/v1/login/sendSmsCodeLogin"/>
	<resource id="wb_ex_24" resourceKey="wb_ex_24" type="exclude" name="密钥验证" url="/web/ua/v1/login/login4A"/>
    <resource id="wb_ex_25" resourceKey="wb_ex_25" type="exclude" name="修改自服务主帐号密码" url="/web/ua/v1/login/modifyPwd"/>
    <resource id="wb_ex_26" resourceKey="wb_ex_26" type="exclude" name="自服务重置密码" url="/web/ua/v1/login/sendSmsCodePassword"/>
    <resource id="wb_ex_27" resourceKey="wb_ex_27" type="exclude" name="自服务重置密码" url="/web/ua/v1/login/resetPwd"/>
    <resource id="wb_ex_28" resourceKey="wb_ex_28" type="exclude" name="登录验证" url="/web/ua/v1/login/verify"/>
    <resource id="wb_ex_29" resourceKey="wb_ex_29" type="exclude" name="登录成功用户实体" url="/web/ua/v1/login/loginInfo"/>
	<resource id="wb_ex_30" resourceKey="wb_ex_30" type="exclude" name="查询跑马灯公告列表" url="/web/partner/v1/annoucement/listHorseLantern"/>
	<resource id="wb_ex_31" resourceKey="wb_ex_31" type="exclude" name="查看跑马灯公告详情" url="/web/partner/v1/annoucement/getHorseLanternDetail"/>
    <resource id="wb_ex_32" resourceKey="wb_ex_32" type="exclude" name="查询字典类型" url="/web/support/v1/dict/getType"/>
    <resource id="wb_ex_33" resourceKey="wb_ex_33" type="exclude" name="单点登录获取按钮信息" url="/web/auth/v1/login/getUserRight"/>
    <resource id="wb_ex_34" resourceKey="wb_ex_34" type="exclude" name="查询用户角色列表" url="/web/partner/v1/user/listUserRoles"/>
	<resource id="wb_ex_35" resourceKey="wb_ex_35" type="exclude" name="查询部门" url="/web/partner/v1/department/listDepartmentTree"/>
	<resource id="wb_ex_36" resourceKey="wb_ex_36" type="exclude" name="查询用户是否存在" url="/web/partner/v1/user/checkUserName"/>
	<resource id="wb_ex_37" resourceKey="wb_ex_37" type="exclude" name="登录秘钥" url="/web/support/v1/domainConfig/listAll"/>
	<resource id="wb_ex_38" resourceKey="wb_ex_38" type="exclude" name="节假日查询" url="/web/support/v1/holiday/listHolidays"/>
	<resource id="wb_ex_39" resourceKey="wb_ex_39" type="exclude" name="系统隐藏功能" url="/web/partner/v1/resource/listHideResource"/>
	<resource id="wb_ex_40" resourceKey="wb_ex_40" type="exclude" name="查询全量字典类型" url="/web/support/v1/dict/listAll"/>
    <resource id="wb_ex_41" resourceKey="wb_ex_41" type="exclude" name="查找当前账户信息-新接口" url="/web/partner/v1/user/getCurrent"/>
    <resource id="wb_ex_42" resourceKey="wb_ex_42" type="exclude" name="更新当前用户黑名单" url="/web/partner/v1/userBlacklist/saveCurrentUserBlacklist"/>
    <resource id="wb_ex_43" resourceKey="wb_ex_43" type="exclude" name="获取当前用户黑名单详情" url="/web/partner/v1/userBlacklist/listCurrentBlacklist"/>
    <resource id="wb_ex_46" resourceKey="wb_ex_46" type="exclude" name="获取有效字典集合" url="/web/support/v1/dict/getValidDictMap"/>
    <resource id="wb_ex_47" resourceKey="wb_ex_47" type="exclude" name="用户常用功能和皮肤导航设置查询" url="/web/partner/v1/userSettings/get"/>
    <resource id="wb_ex_48" resourceKey="wb_ex_48" type="exclude" name="用户常用功能保存" url="/web/partner/v1/userSettings/save"/>
    <resource id="wb_ex_49" resourceKey="wb_ex_49" type="exclude" name="用户导航设置保存" url="/web/partner/v1/userSettings/saveNavigator"/>
    <resource id="wb_ex_50" resourceKey="wb_ex_50" type="exclude" name="用户皮肤设置保存" url="/web/partner/v1/userSettings/saveSkin"/>
    
	
    <resource id="wb_01" resourceKey="wb_01" type="menu" name="首页" icon="el-icon-homepage" url="/home" order="10"/>
	<!-- 节假日配置 -->
    <resource id="wb_010101" parentId="wb_01" resourceKey="wb_010101" type="button"  name="节假日配置" url="/web/support/v1/holiday/setHolidays"/>
	
    <resource id="wb_02" resourceKey="wb_0201" type="menu" name="系统管理" icon="el-icon-setup" url="/system"  order="11"/>

	
	<!-- 部门管理 -->
    <resource id="wb_090101" parentId="wb_02" resourceKey="wb_090101" type="menu" name="部门管理" url="/operator/#/system/deptForDept" order="1102"/>
    <resource id="wb_090102" parentId="wb_090101" resourceKey="wb_090102" type="button" name="获取机构所属角色和用户信息" url="/web/partner/v1/organization/getOrganizationContext"/>
	<resource id="wb_090135" parentId="wb_090102" resourceKey="wb_090135" type="included" name="获取机构详情" url="/web/partner/v1/organization/get"/>
    <resource id="wb_090122" parentId="wb_090102" resourceKey="wb_090122" type="included" name="查询机构部门树信息" url="/web/partner/v1/department/listDepartmentTree"/>
    <resource id="wb_090133" parentId="wb_090102" resourceKey="wb_090133" type="included" name="查询机构部门下的用户信息列表" url="/web/partner/v1/userOrganization/queryOrganizationUserList"/>
    <resource id="wb_090121" parentId="wb_090102" resourceKey="wb_090121" type="included" name="获取部门详情" url="/web/partner/v1/department/get"/>
    <resource id="wb_090131" parentId="wb_090102" resourceKey="wb_090131" type="included" name="检查部门名称是否被使用" url="/web/partner/v1/department/checkDepartmentName"/>
	
    <resource id="wb_090117" parentId="wb_090101" resourceKey="wb_090117" type="button" name="新增部门" url="/web/partner/v1/department/insert" relation="wb_090102"/>
    <resource id="wb_090118" parentId="wb_090101" resourceKey="wb_090118" type="button" name="编辑部门" url="/web/partner/v1/department/update" relation="wb_090102"/>
    <resource id="wb_090119" parentId="wb_090101" resourceKey="wb_090119" type="button" name="删除部门" url="/web/partner/v1/department/delete" relation="wb_090102"/>
	
    <resource id="wb_090120" parentId="wb_090101" resourceKey="wb_090120" type="button" name="新增成员" url="/web/partner/v1/user/insert" relation="wb_090102"/>
    <resource id="wb_090110" parentId="wb_090120" resourceKey="wb_090110" type="included" name="获取机构combo列表" url="/web/partner/v1/organization/listUserOrganizationForCombo"/>
	
	<resource id="wb_090108" parentId="wb_090101" resourceKey="wb_090108" type="button" name="账号配置角色" url="/web/partner/v1/userRole/updateUserRoles" relation="wb_090102"/>
    <resource id="wb_090112" parentId="wb_090108" resourceKey="wb_090112" type="included" name="查询用户机构关联角色信息" url="/web/partner/v1/organizationRole/listRole"/>
	
	
	<!-- 机构成员管理 -->
    <resource id="wb_100101" parentId="wb_02" resourceKey="wb_100101" type="menu" name="机构成员管理" url="/operator/#/system/staffForDept" order="1104" />
    <resource id="wb_100102" parentId="wb_100101" resourceKey="wb_100102" type="button" name="查询" url="/web/partner/v1/userOrganization/listPage"/>
    <resource id="wb_100113" parentId="wb_100102" resourceKey="wb_100113" type="included" name="获取用户机构列表" url="/web/partner/v1/organization/listUserOrganizationForCombo"/>
    <resource id="wb_100115" parentId="wb_100102" resourceKey="wb_100115" type="included" name="查询机构部门树信息" url="/web/partner/v1/department/listDepartmentTree"/>
    <resource id="wb_100110" parentId="wb_100102" resourceKey="wb_100110" type="included" name="查询用户角色列表" url="/web/partner/v1/userOrganization/listUserRoles"/>
	<resource id="wb_100130" parentId="wb_100102" resourceKey="wb_100130" type="included" name="查询用户详情(脱敏)" url="/web/partner/v1/userOrganization/getDesensitizationUserInfo"/>
    <resource id="wb_100114" parentId="wb_100102" resourceKey="wb_100114" type="included" name="获取机构详情" url="/web/partner/v1/organization/get"/>
    <resource id="wb_100116" parentId="wb_100102" resourceKey="wb_100116" type="included" name="获取部门详情" url="/web/partner/v1/department/get"/>
    <resource id="wb_100126" parentId="wb_100102" resourceKey="wb_100126" type="included" name="接收信息配置" url="/web/partner/v1/userBlacklist/saveUserBlacklist"/>
    <resource id="wb_100127" parentId="wb_100102" resourceKey="wb_100127" type="included" name="获取接收信息配置详情" url="/web/partner/v1/userBlacklist/listBlacklist"/>

    <resource id="wb_100119" parentId="wb_100101" resourceKey="wb_100119" type="button" name="新增用户信息" url="/web/partner/v1/userOrganization/insert" relation="wb_100102"/>
    <resource id="wb_100103" parentId="wb_100101" resourceKey="wb_100103" type="button" name="编辑成员" url="/web/partner/v1/userOrganization/update" relation="wb_100102"/>
	<resource id="wb_10010301" parentId="wb_100103" resourceKey="wb_10010301" type="included" name="查询用户详情" url="/web/partner/v1/userOrganization/getById"/>
    <resource id="wb_100105" parentId="wb_100101" resourceKey="wb_100105" type="button" name="删除成员" url="/web/partner/v1/userOrganization/delete" relation="wb_100102"/>
    <resource id="wb_100108" parentId="wb_100101" resourceKey="wb_100108" type="button" name="启用/禁用用户" url="/web/partner/v1/userOrganization/active" relation="wb_100102"/>
    <resource id="wb_100104" parentId="wb_100108" resourceKey="wb_100104" type="included" name="禁用成员" url="/web/partner/v1/userOrganization/inactive"/>
	<resource id="wb_100118" parentId="wb_100101" resourceKey="wb_100118" type="button" name="重置密码" url="/web/partner/v1/userOrganization/resetPwd" relation="wb_100102"/>
    <resource id="wb_100123" parentId="wb_100101" resourceKey="wb_100123" type="button" name="导出用户权限信息" url="/web/partner/v1/userOrganization/exportUserRight" relation="wb_100102"/>
	<resource id="wb_100122" parentId="wb_100101" resourceKey="wb_100122" type="button" name="导出用户信息" url="/web/partner/v1/userOrganization/export" relation="wb_100102"/>
    <resource id="wb_100106" parentId="wb_100101" resourceKey="wb_100106" type="button" name="账号配置角色" url="/web/partner/v1/userRole/updateUserRoles" relation="wb_100102"/>
	<resource id="wb_100124" parentId="wb_100101" resourceKey="wb_100124" type="button" name="解锁锁定用户" url="/web/partner/v1/userOrganization/unlock" relation="wb_100102" />
	<resource id="wb_100125" parentId="wb_100101" resourceKey="wb_100125" type="button" name="查询账号登录日志" url="/web/support/v1/component/log/listLogByUserId" relation="wb_100102" />

    <!-- 机构角色管理 -->
    <resource id="wb_070101" parentId="wb_02" resourceKey="wb_070101" type="menu" name="机构角色管理" url="/operator/#/system/roleForDept" order="1106"/>
    <resource id="wb_070103" parentId="wb_070101" resourceKey="wb_070103" type="button" name="查询" url="/web/partner/v1/roleOrganization/listPage"/>
    <resource id="wb_070104" parentId="wb_070101" resourceKey="wb_070104" type="button" name="新增角色" url="/web/partner/v1/roleOrganization/insert" relation="wb_070103"/>
    <resource id="wb_070105" parentId="wb_070101" resourceKey="wb_070105" type="button" name="编辑角色" url="/web/partner/v1/roleOrganization/update" relation="wb_070103"/>
    <resource id="wb_070106" parentId="wb_070101" resourceKey="wb_070106" type="button" name="删除角色" url="/web/partner/v1/roleOrganization/delete" relation="wb_070103"/>
    <resource id="wb_070107" parentId="wb_070101" resourceKey="wb_070107" type="button" name="角色分配权限" url="/web/partner/v1/roleOrganization/updateRoleResource" relation="wb_070103"/>
    <resource id="wb_070109" parentId="wb_070107" resourceKey="wb_070109" type="included" name="角色权限列表" url="/web/partner/v1/roleOrganization/listRoleResourceTree"/>

	<!-- 日志管理  -->
	<resource id="wb_050101" parentId="wb_02" resourceKey="wb_050101" type="menu" name="日志管理" url="/operator/#/system/log" order="1107"/>
	<resource id="wb_050103" parentId="wb_050101" resourceKey="wb_050103" type="button" name="查询" url="/web/support/v1/component/log/listLog"/>
    <resource id="wb_050104" parentId="wb_050103" resourceKey="wb_050104" type="included" name="逐级查询日志类型" url="/web/support/v1/component/log/listForCombo"/>
    <resource id="wb_050105" parentId="wb_050103" resourceKey="wb_050105" type="included" name="查询日志类型" url="/web/support/v1/component/log/getLogTypes"/>
	<resource id="wb_050107" parentId="wb_050103" resourceKey="allOrganization" type="dataright" name="全量数据权限" />
	<!-- 资源管理
    <resource id="wb_080101" parentId="wb_02" resourceKey="wb_080101" type="menu" name="资源管理" icon="icon-sitemap" url="/operator/#/system/resources" order="1108"/>
    <resource id="wb_080106" parentId="wb_080101" resourceKey="wb_080106" type="button" name="获取资源列表" url="/web/partner/v1/resource/listPageResource"/>
    <resource id="wb_080102" parentId="wb_080106" resourceKey="wb_080102" type="included" name="查询资源详情" url="/web/partner/v1/resource/get"/>
    <resource id="wb_080103" parentId="wb_080101" resourceKey="wb_080103" type="button" name="添加资源" url="/web/partner/v1/resource/insert" relation="wb_080106"/>
    <resource id="wb_080104" parentId="wb_080101" resourceKey="wb_080104" type="button" name="修改资源" url="/web/partner/v1/resource/update" relation="wb_080106"/>
    <resource id="wb_080105" parentId="wb_080101" resourceKey="wb_080105" type="button" name="删除资源" url="/web/partner/v1/resource/delete" relation="wb_080106"/>
    <resource id="wb_080110" parentId="wb_080101" resourceKey="wb_080110" type="button" name="资源导出" url="/web/partner/v1/resource/exportResource" relation="wb_080106"/>
	<resource id="wb_080111" parentId="wb_080101" resourceKey="wb_080111" type="button" name="更改资源状态" url="/web/partner/v1/resource/updateStatus" relation="wb_080106"/>
	<resource id="wb_080112" parentId="wb_080103" resourceKey="wb_080112" type="included" name="添加数据权限" url="/web/partner/v1/resource/insertDataRight"/>
    
    <resource id="wb_080107" parentId="wb_080101" resourceKey="wb_080107" type="button" name="获取资源历史列表" url="/web/partner/v1/resource/listPageResourceHisory"/>
	<resource id="wb_080113" parentId="wb_080107" resourceKey="wb_080113" type="included" name="获取资源列表" url="/web/partner/v1/resource/listPageResource"/>
    <resource id="wb_080108" parentId="wb_080107" resourceKey="wb_080108" type="included" name="资源管理历史详情" url="/web/partner/v1/resource/getHistory" />
	<resource id="wb_080109" parentId="wb_080101" resourceKey="wb_080109" type="button" name="历史导出" url="/web/partner/v1/resource/exportResourceHistory" relation="wb_080107"/>
    -->
	<!-- 资源管理 -->
    <resource id="wb_080101" parentId="wb_02" resourceKey="wb_080101" type="menu" name="资源管理" icon="icon-sitemap" url="/resources" order="1108"/>
	<resource id="wb_080102" parentId="wb_080101" resourceKey="wb_080102" type="menu" name="资源列表" url="/operator/#/system/resources"/>
	<resource id="wb_080103" parentId="wb_080102" resourceKey="wb_080103" type="button" name="获取资源列表" url="/web/partner/v1/resource/listPageResource"/>
    <resource id="wb_080104" parentId="wb_080103" resourceKey="wb_080104" type="included" name="查询资源详情" url="/web/partner/v1/resource/get"/>
    <resource id="wb_080105" parentId="wb_080102" resourceKey="wb_080105" type="button" relation="wb_080103" name="添加资源" url="/web/partner/v1/resource/insert"/>
    <resource id="wb_080106" parentId="wb_080102" resourceKey="wb_080106" type="button" relation="wb_080103" name="修改资源" url="/web/partner/v1/resource/update"/>
    <resource id="wb_080107" parentId="wb_080102" resourceKey="wb_080107" type="button" relation="wb_080103" name="删除资源" url="/web/partner/v1/resource/delete"/>
    <resource id="wb_080108" parentId="wb_080102" resourceKey="wb_080108" type="button" relation="wb_080103" name="资源导出" url="/web/partner/v1/resource/exportResource"/>
	<resource id="wb_080109" parentId="wb_080102" resourceKey="wb_080109" type="button" relation="wb_080103" name="更改资源状态" url="/web/partner/v1/resource/updateStatus"/>
	<resource id="wb_080110" parentId="wb_080105" resourceKey="wb_080110" type="included" name="添加数据权限" url="/web/partner/v1/resource/insertDataRight"/>
	<resource id="wb_080116" parentId="wb_080106" resourceKey="wb_080116" type="included" name="添加数据权限" url="/web/partner/v1/resource/insertDataRight"/>
	
	<resource id="wb_080111" parentId="wb_080101" resourceKey="wb_080111" type="menu" name="资源历史列表" url="/operator/#/system/resourcesForHis"/>
	<resource id="wb_080112" parentId="wb_080111" resourceKey="wb_080112" type="button" name="获取资源历史列表" url="/web/partner/v1/resource/listPageResourceHisory"/>
	<resource id="wb_080113" parentId="wb_080112" resourceKey="wb_080113" type="included" name="获取资源列表" url="/web/partner/v1/resource/listPageResource"/>
    <resource id="wb_080114" parentId="wb_080112" resourceKey="wb_080114" type="included" name="资源管理历史详情" url="/web/partner/v1/resource/getHistory" />
	<resource id="wb_080115" parentId="wb_080111" resourceKey="wb_080115" type="button" relation="wb_080112" name="历史导出" url="/web/partner/v1/resource/exportResourceHistory"/>

    <!-- 参数配置 -->
    <resource id="wb_110101" parentId="wb_02" resourceKey="wb_110101" type="menu" name="参数配置" url="/operator/#/system/paramsConfig" order="1109" />
    <resource id="wb_110102" parentId="wb_110101" resourceKey="wb_110102" type="button" name="查询" url="/web/support/v1/platformConfig/listPage"/>
    <resource id="wb_110103" parentId="wb_110102" resourceKey="wb_110103" type="included" name="查询详情" url="/web/support/v1/platformConfig/get"/>
    <resource id="wb_110104" parentId="wb_110101" resourceKey="wb_110104" type="button" name="修改" url="/web/support/v1/platformConfig/update" relation="wb_110102"/>
    
	<!-- 公告管理 -->
	<resource id="wb_060101" parentId="wb_02" resourceKey="wb_060101" type="menu" name="公告管理" url="/operator/#/system/annoucement" order="1110"/>
	
	<resource id="wb_060102" parentId="wb_060101" resourceKey="wb_060102" type="menu" name="公告管理" url="/operator/#/system/annoucement/manager" />
	<resource id="wb_060111" parentId="wb_060102" resourceKey="wb_060111" type="button" name="查询草稿" url="/web/partner/v1/annoucement/pageListDraft" />
	<resource id="wb_060103" parentId="wb_060102" resourceKey="wb_060103" type="button" name="新增公告" url="/web/partner/v1/annoucement/insert"/>
	<resource id="wb_060104" parentId="wb_060102" resourceKey="wb_060104" type="button" name="编辑公告" url="/web/partner/v1/annoucement/update"/>
	<resource id="wb_060125" parentId="wb_060102" resourceKey="wb_060125" type="button" name="公告回显" url="/web/partner/v1/annoucement/get"/>
	<resource id="wb_060105" parentId="wb_060102" resourceKey="wb_060105" type="included" name="查询详情" url="/web/partner/v1/annoucement/getDetailById"/>
	<resource id="wb_060106" parentId="wb_060102" resourceKey="wb_060106" type="included" name="获取阅读详情" url="/web/partner/v1/annoucement/getDetailWithReadById"/>
	<resource id="wb_060123" parentId="wb_060102" resourceKey="wb_060123" type="included" name="获取公告阅读情况" url="/web/partner/v1/annoucement/listPageReadDetail"/>
	<resource id="wb_060124" parentId="wb_060102" resourceKey="wb_060124" type="included" name="获取公告操作记录" url="/web/partner/v1/annoucement/pageListHistory"/>
	<resource id="wb_060107" parentId="wb_060102" resourceKey="wb_060107" type="button" name="导出查阅" url="/web/partner/v1/annoucement/exportRead"/>
	<resource id="wb_060108" parentId="wb_060102" resourceKey="wb_060108" type="button" name="导出公告" url="/web/partner/v1/annoucement/export"/>
	<resource id="wb_060112" parentId="wb_060102" resourceKey="wb_060112" type="button" name="删除公告" url="/web/partner/v1/annoucement/delete"/>
	<resource id="wb_060113" parentId="wb_060102" resourceKey="wb_060113" type="button" name="撤回公告" url="/web/partner/v1/annoucement/recall"/>
	<resource id="wb_060122" parentId="wb_060102" resourceKey="wb_060122" type="button" name="取消发布" url="/web/partner/v1/annoucement/publishCancel"/>
	<resource id="wb_060126" parentId="wb_060102" resourceKey="wb_060126" type="button" name="导出回复" url="/web/partner/v1/annoucement/exportReply"/>
	
	<resource id="wb_060120" parentId="wb_060102" resourceKey="wb_060120" type="menu" name="我创建的" url="/operator/#/system/annoucement/manager/create" />
	<resource id="wb_060109" parentId="wb_060120" resourceKey="wb_060109" type="button" name="查询" url="/web/partner/v1/annoucement/pageList"/>
	
	<resource id="wb_060121" parentId="wb_060102" resourceKey="wb_060121" type="menu" name="我审核的" url="/operator/#/system/annoucement/manager/audit" />
	<resource id="wb_060114" parentId="wb_060121" resourceKey="wb_060114" type="button" name="审核公告" url="/web/partner/v1/annoucement/audit"/>
	<resource id="wb_060110" parentId="wb_060121" resourceKey="wb_060110" type="button" name="查询" url="/web/partner/v1/annoucement/pageListAudit"/>
    <resource id="wb_060128" parentId="wb_060102" resourceKey="wb_060128" type="included" name="查询抄送人" url="/web/partner/v1/user/list"/>
    <resource id="wb_060127" parentId="wb_060102" resourceKey="wb_060127" type="included" name="查询机构列表" url="/web/partner/v1/organization/list"/>
	
	<resource id="wb_060115" parentId="wb_060101" resourceKey="wb_060115" type="menu" name="公告查询" url="/operator/#/system/annoucement/read" />
	<resource id="wb_060119" parentId="wb_060115" resourceKey="wb_060119" type="button" name="查询" url="/web/partner/v1/annoucement/pageListAllPublish"/>
	<resource id="wb_060129" parentId="wb_060119" resourceKey="wb_060129" type="included" name="回复详情" url="/web/partner/v1/annoucement/listReply"/>
	<resource id="wb_060130" parentId="wb_060115" resourceKey="wb_060130" type="button" name="公告回复" url="/web/partner/v1/annoucement/replyAnnoucement"/>


	<!-- 字典表维护 -->
	<resource id="wb_120101" parentId="wb_02" resourceKey="wb_120101" type="menu" name="字典表维护" url="/operator/#/system/dict" order="1111"/>
    <resource id="wb_120102" parentId="wb_120101" resourceKey="wb_120102" type="button" name="字典表维护列表" url="/web/support/v1/dict/listType" />
	<resource id="wb_120104" parentId="wb_120101" resourceKey="wb_120104" type="button" relation="wb_120102" name="字典表维护修改" url="/web/support/v1/dict/updateType"/>

	<!-- 虚拟组管理 -->
	<resource id="wb_130101" parentId="wb_02" resourceKey="wb_130101" type="menu" name="虚拟组管理" url="/operator/#/system/virtualGroup" order="1112"/>
    <resource id="wb_130102" parentId="wb_130101" resourceKey="wb_130102" type="button" name="查询虚拟组列表" url="/web/partner/v1/virtual/listPageVirtualGroup" />
	<resource id="wb_130103" parentId="wb_130101" resourceKey="wb_130103" type="button" relation="wb_130102" name="新增虚拟组" url="/web/partner/v1/virtual/insertVirtualGroup"/>
	<resource id="wb_130110" parentId="wb_130102" resourceKey="wb_130110" type="included" name="获取机构combo列表" url="/web/partner/v1/organization/listForCombo"/>
	<resource id="wb_130111" parentId="wb_130102" resourceKey="wb_130111" type="included" name="获取每个机构节点后的部门树" url="/web/partner/v1/department/listDepartmentTree"/>
	<resource id="wb_130104" parentId="wb_130101" resourceKey="wb_130104" type="button" relation="wb_130102" name="删除虚拟组" url="/web/partner/v1/virtual/deleteVirtualGroup"/>
    <resource id="wb_130105" parentId="wb_130101" resourceKey="wb_130105" type="button" relation="wb_130102" name="导出列表" url="/web/partner/v1/virtual/exportVirtualGroup"/>
    <resource id="wb_130106" parentId="wb_130101" resourceKey="wb_130106" type="button" relation="wb_130102" name="更新虚拟组" url="/web/partner/v1/virtual/updateVirtualGroup"/>
    <resource id="wb_130107" parentId="wb_130102" resourceKey="wb_130107" type="included" name="分页查询虚拟组用户" url="/web/partner/v1/virtual/listPageVirtualGroupUser"/>
	<resource id="wb_130108" parentId="wb_130102" resourceKey="wb_130108" type="included" name="查询虚拟组用户" url="/web/partner/v1/virtual/listVirtualGroupUser"/>
	<resource id="wb_130109" parentId="wb_130102" resourceKey="wb_130109" type="included" name="查询组织部门下的用户信息列表" url="/web/partner/v1/user/queryOrganizationUserListForCombo"/>
    
    
     <!-- xxl-job -->
   <resource id="wb_xxl" parentId="wb_02"  resourceKey="wb_xxl"  type="menu"  name="定时任务" icon="" order="1113" url="" />
    <resource id="wb_xxl_01" parentId="wb_xxl" resourceKey="wb_xxl_01"  type="menu" name="执行器管理" url="/xxl/jobgroup/" order="0"/>
        <resource id="wb_xxl_01_01" parentId="wb_xxl_01" resourceKey="wb_xxl_01_01"  type="button" name="修改" url="/xxl/jobgroup/update" />
        <resource id="wb_xxl_01_02" parentId="wb_xxl_01" resourceKey="wb_xxl_01_02"  type="button" name="删除" url="/xxl/jobgroup/remove" />
        <resource id="wb_xxl_01_03" parentId="wb_xxl_01" resourceKey="wb_xxl_01_03"  type="button" name="新增" url="/xxl/jobgroup/save" />
    <resource id="wb_xxl_02" parentId="wb_xxl" resourceKey="wb_xxl_02"  type="menu" name="任务列表" url="/xxl/jobinfo/" order="1"/>
        <resource id="wb_xxl_02_01" parentId="wb_xxl_02" resourceKey="wb_xxl_02_01"  type="button" name="查询" url="/xxl/jobinfo/pageList" />
        <resource id="wb_xxl_02_0101" parentId="wb_xxl_02_01" resourceKey="wb_xxl_02_0101"  type="included" name="查询" url="/xxl/jobinfo" />
        <resource id="wb_xxl_02_02" parentId="wb_xxl_02" resourceKey="wb_xxl_02_02"  type="button" name="新增" url="/xxl/jobinfo/add" />
        <resource id="wb_xxl_02_03" parentId="wb_xxl_02" resourceKey="wb_xxl_02_03"  type="button" name="修改" url="/xxl/jobinfo/update" />
        <resource id="wb_xxl_02_04" parentId="wb_xxl_02" resourceKey="wb_xxl_02_04"  type="button" name="删除" url="/xxl/jobinfo/remove" />
        <resource id="wb_xxl_02_05" parentId="wb_xxl_02" resourceKey="wb_xxl_02_05"  type="button" name="停止" url="/xxl/jobinfo/stop" />
        <resource id="wb_xxl_02_06" parentId="wb_xxl_02" resourceKey="wb_xxl_02_06"  type="button" name="执行" url="/xxl/jobinfo/trigger" />
        <resource id="wb_xxl_02_07" parentId="wb_xxl_02" resourceKey="wb_xxl_02_07"  type="button" name="启动" url="/xxl/jobinfo/start" />
        <resource id="wb_xxl_02_08" parentId="wb_xxl_02" resourceKey="wb_xxl_02_08"  type="button" name="日志" url="/xxl/joblog" />
        <resource id="wb_xxl_02_09" parentId="wb_xxl_02" resourceKey="wb_xxl_02_09"  type="button" name="代码编辑" url="/xxl/jobcode" />
        <resource id="wb_xxl_02_10" parentId="wb_xxl_02" resourceKey="wb_xxl_02_10"  type="button" name="代码保存" url="/xxl/jobcode/save" />
    <resource id="wb_xxl_03" parentId="wb_xxl" resourceKey="wb_xxl_03"  type="menu" name="任务日志" url="/xxl/joblog/" order="2"/>
        <resource id="wb_xxl_03_01" parentId="wb_xxl_03" resourceKey="wb_xxl_03_01"  type="button" name="查询  " url="/xxl/joblog/pageList" />
        <resource id="wb_xxl_03_02" parentId="wb_xxl_03" resourceKey="wb_xxl_03_02"  type="button" name="清理" url="/xxl/joblog/clearLog" />
        <resource id="wb_xxl_03_03" parentId="wb_xxl_03" resourceKey="wb_xxl_03_03"  type="button" name="根据分组查询" url="/xxl/joblog/getJobsByGroup" />
        <resource id="wb_xxl_03_04" parentId="wb_xxl_03" resourceKey="wb_xxl_03_04"  type="button" name="日志详情" url="/xxl/joblog/logDetailPage" />
        <resource id="wb_xxl_03_04_01" parentId="wb_xxl_03_04" resourceKey="wb_xxl_03_04_01"  type="included" name="执行日志" url="/xxl/joblog/logDetailCat" />
    <!-- 主题设置 -->
	<resource id="wb_140101" parentId="wb_02" resourceKey="wb_140101" type="menu" name="主题设置" url="/operator/#/system/theme" order="1114"/>
        <resource id="wb_14010101" parentId="wb_140101" resourceKey="wb_14010101" type="button" name="系统皮肤列表" url="/web/partner/v1/userSettings/listSystemSkin" />
        <resource id="wb_14010102" parentId="wb_140101" resourceKey="wb_14010102" type="button" relation="wb_14010101" name="隐藏此主题" url="/web/partner/v1/userSettings/inactiveSkin"/>
            <resource id="wb_1401010201" parentId="wb_14010102" resourceKey="wb_1401010201" type="included" name="启用皮肤" url="/web/partner/v1/userSettings/activeSkin"/>
        <resource id="wb_14010103" parentId="wb_140101" resourceKey="wb_14010103" type="button" relation="wb_14010101" name="设置系统默认色系" url="/web/partner/v1/userSettings/saveDefaultSkin"/>


  <resource id="wb_150101" parentId="wb_02" resourceKey="wb_150101" type="menu" name="业务对象" url="/" order="1115"/>
    <resource id="wb_15010101" parentId="wb_150101" resourceKey="wb_15010101" type="menu" name="业务实体" url="/operator/#/system/ida" order="111501"/>
      <resource id="wb_1501010101" parentId="wb_15010101" resourceKey="wb_1501010101" type="button" name="业务实体分页列表" url="/web/ida/bus/businessTable/pageList" order=""/>
      <resource id="wb_1501010102" parentId="wb_15010101" resourceKey="wb_1501010102" type="button" name="业务实体新增" url="/web/ida/bus/businessTable/save" order=""/>
      <resource id="wb_1501010103" parentId="wb_15010101" resourceKey="wb_1501010103" type="button" name="业务实体删除" url="/web/ida/bus/businessTable/delete" order=""/>
      <resource id="wb_1501010104" parentId="wb_15010101" resourceKey="wb_1501010104" type="button" name="业务实体生成表" url="/web/ida/bus/businessTable/createTable" order=""/>
      <resource id="wb_1501010105" parentId="wb_15010101" resourceKey="wb_1501010105" type="button" name="业务实体详情" url="/web/ida/bus/businessTable/get" order=""/>
    <resource id="wb_15010102" parentId="wb_150101" resourceKey="wb_15010102" type="menu" name="业务对象" url="/operator/#/system/business-object" order="111502"/>
      <resource id="wb_1501010201" parentId="wb_15010102" resourceKey="wb_1501010201" type="included" name="业务实体分页列表" url="/web/ida/bus/businessTable/pageList" order=""/>
      <resource id="wb_1501010202" parentId="wb_15010102" resourceKey="wb_1501010202" type="button" name="业务对象分页列表" url="/web/ida/bus/businessObject/pageList" order=""/>
      <resource id="wb_1501010203" parentId="wb_15010102" resourceKey="wb_1501010203" type="button" name="业务对象新增" url="/web/ida/bus/businessObject/save" order=""/>
      <resource id="wb_1501010204" parentId="wb_15010102" resourceKey="wb_1501010204" type="button" name="业务对象数据结构" url="/web/ida/bus/businessObject/getObjStruct" order=""/>
      <resource id="wb_1501010205" parentId="wb_15010102" resourceKey="wb_1501010205" type="button" name="业务对象删除" url="/web/ida/bus/businessObject/delete" order=""/>
      <resource id="wb_1501010206" parentId="wb_15010102" resourceKey="wb_1501010206" type="button" name="业务对象详情" url="/web/ida/bus/businessObject/get" order=""/>
</metadata>
