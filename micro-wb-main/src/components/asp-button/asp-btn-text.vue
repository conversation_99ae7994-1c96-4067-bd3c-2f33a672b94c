/** * Created by TurboC on 2019/08/12. * 按钮 */
<template>
  <el-button :disabled="disabled" type="text" @click="click()"
    >{{ name }}
  </el-button>
</template>

<script>
export default {
  name: 'AspBtnText',
  props: {
    name: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  mounted() {},
  created() {},
  methods: {
    click() {
      this.$emit('click', {})
    }
  }
}
</script>
