/** * Created by aspire on 2019/08/12. * 按钮 */
<template>
  <el-button
    :icon="icon"
    v-loading="loading"
    :disabled="disabled"
    class="hollow-with-icon-btn"
    size="small"
    @click="click()"
    >{{ name }}
  </el-button>
</template>

<script>
export default {
  name: 'AspBtnHollow',
  props: {
    name: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  mounted() {},
  created() {},
  methods: {
    click() {
      this.$emit('click', {})
    }
  }
}
</script>
