import aspBtnSolid from './asp-button/asp-btn-solid'
import aspBtnHollow from './asp-button/asp-btn-hollow'
import aspBtnText from './asp-button/asp-btn-text'
import aspDialog from './asp-dialog/asp-dialog'

const components = [
  aspDialog,
  aspBtnSolid,
  aspBtnHollow,
  aspBtnText
]

const install = Vue => {
  window.$version = { vue: Vue.version, ele: '1.0' }
  components
    .filter(v => typeof v !== 'function')
    .forEach(v => Vue.component(v.name, v))
}

export default install
