/**
 * @author: TurboC
 * @date: 2020-12-16 20:18:14
 */
import Vue from 'vue'
import App from '@/App.vue'
import router from '@/router'
import store from '@/store'
import Cookies from 'js-cookie'
import 'babel-polyfill' // 所有微应用里只有包含一次

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import '@/assets/index.scss'
import '../../../assets/index.scss'

// 全局设置
import AspUi from '@/components'
import directiveEvent from '@/directive'
import util from '@/utils/util'
import menu from '@/utils/menu'
import menuTreeUtil from '@/utils/menuTreeUtil'
import aspHttps from '@/utils/http'
import areas from '@/utils/areasUtil'
import validation from '@/utils/validation'
import {
  registerMicroApps, // 注册子应用方法
  runAfterFirstMounted, // 有个子应用加载完毕回调
  setDefaultMountApp, // 设默认启用的子应用
  addGlobalUncaughtErrorHandler, // 添加全局未捕获异常处理器
  start // 启动qiankun
} from 'qiankun'
import './permission'
import actions from '@/micro/actions'
import { table, form } from 'asp-smart-layout'
const option = {
  NODE_ENV: process.env.NODE_ENV
}
Vue.use(table, option)
Vue.use(form, option)
const {
  noAuthMicroApps,
  authMicroApps,
  mainFrameConfig,
  pageConfig,
  apiConfig,
  projectConfig,
  excludeAssetList
} = require('./../../../config.json')
Vue.prototype.$mainFrameConfig = mainFrameConfig
Vue.prototype.$aspHttps = aspHttps
Vue.prototype.$aspHttps.$store = store
Vue.prototype.$reponseStatus = response => {
  return aspHttps.reponseStatus(response)
}
Vue.prototype.$actions = actions
Vue.config.productionTip = false
Vue.config.devtools = true
Vue.config.debug = true
Vue.prototype.$Cookies = Cookies
Vue.prototype.$util = util
Vue.prototype.$menu = menu
Vue.prototype.$menuTreeUtil = menuTreeUtil
Vue.prototype.$validation = validation
Vue.prototype.$areas = areas
Vue.prototype.$breadcrumbReturnBtn = false
Vue.prototype.mywindow = window
directiveEvent.init(Vue)
Vue.use(ElementUI)
Vue.use(AspUi)

const mainApp = new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#main-container')

const appRouterTable = []
let defaultMcrioUrl = ''

/**
 * @name 声明要传递给子应用的信息
 * @param data 主应要传递给子应用的数据类信息
 */
const props = {
  main_tools: {
    appRouterTable,
    menuTreeUtil,
    menu,
    util,
    areas,
    validation,
    store,
    router,
    sessionStorage,
    aspHttps,
    reponseStatus: Vue.prototype.$reponseStatus,
    ssoIndexConfig: {}
  },
  mainFrameConfig,
  pageConfig,
  apiConfig,
  projectConfig
}

/**
 * @name 启用微前端应用
 * @param {Array} list 应用注册表信息
 */
const microRegister = (list) => {
  // 获取OA代理前缀
  let _PCC_Base_Prx = ''
  const cloneWin = window
  let locatName = 'loca'
  let hreName = 'hr'
  // console.log('enter into main')
  if (window._AN_base_host && window._AN_this_url === '/prx/000/') {
    locatName = locatName + 'tion'
    hreName = hreName + 'ef'
    const baseUrlPrx = cloneWin[locatName][hreName].split(window._AN_this_url)[0]
    _PCC_Base_Prx = `${baseUrlPrx}${window._AN_this_url}${window._AN_base_host.replace(':/', '')}`
    // 备份ssoIndex路由信息
    let ssoIndexUrl = window.location.href
    ssoIndexUrl = ssoIndexUrl.replace(window._AN_base_host, '')
    props.main_tools.ssoIndexConfig = {
      _PCC_Base_Prx,
      baseUrlPrx,
      _AN_this_url: window._AN_this_url,
      _AN_base_host: window._AN_base_host,
      ssoIndexUrl
    }
  }
  /**
   * @name 处理子应用注册表数据
   */
  const apps = [] // 子应用数组盒子
  list.forEach(item => {
    apps.push({
      name: item.microAppName,
      entry: process.env.NODE_ENV === 'development' ? ('http://localhost:' + item.devPort) : (_PCC_Base_Prx + item.microAppEnter),
      container: '#subapp-viewport',
      activeRule: item.microAppPre,
      props: {
        ...props,
        microAppPre: item.microAppPre,
        microAppEnter: item.microAppEnter
      }
    })
    if (item.isMicroApp) {
      defaultMcrioUrl = item.microFirstUrl ? item.microAppPre + '#' + item.microFirstUrl : item.microAppPre
    }
  })

  /**
   * @name 注册子应用
   * @param apps - Array<RegistrableApp> - 必选，微应用的一些注册信息
   * @param lifeCycles - LifeCycles - 可选，全局的微应用生命周期钩子
   */
  registerMicroApps(apps, {
    beforeLoad: [
      app => {
        // console.log('micro-wb-main before load', app)
      }
    ],
    beforeMount: [
      app => {
        // console.log('micro-wb-main before mount', app)
      }
    ],
    afterUnmount: [
      app => {
        // console.log('micro-wb-main after unload', app)
      }
    ]
  })

  /**
   * 设置默认子应用
   */
  if (defaultMcrioUrl.length <= 0) {
    defaultMcrioUrl = list[0].microFirstUrl ? list[0].microAppPre + '#' + list[0].microFirstUrl : list[0].microAppPre
  }

  /**
   * @name 启动微前端
   * @param opts - Options 可选
   */
  const startObj = { prefetch: true }
  if (_PCC_Base_Prx) {
    startObj.getPublicPath = (entry) => {
      // const { origin } = new URL(entry, location.href)
      // _PCC_Base_Prx = `${origin}${_PCC_Base_Prx}`
      return entry
    }
  }
  startObj.excludeAssetFilter = assetUrl => {
    if (excludeAssetList &&  excludeAssetList.length > 0) {
      return excludeAssetList.some((w) => {
        return assetUrl.includes(w);
      });
    }
  }
  start(startObj)

  /**
   * @name 设置默认进入的子应用
   * @param appLink - string - 必选，需要进入的子应用路由前缀
   */
  // 若存在动态默认地址，则直接使用默认动态地址
  if (props.main_tools.ssoIndexConfig.ssoIndexUrl) {
    defaultMcrioUrl = props.main_tools.ssoIndexConfig.ssoIndexUrl
  }
  setDefaultMountApp(defaultMcrioUrl)

  /**
   * @name 微前端启动进入第一个子应用后回调函数, 比如开启一些监控或者埋点脚本。
   * @param effect - () => void - 必选
   */
  runAfterFirstMounted(app => {
    // console.log('micro-wb-main runAfterFirstMounted, app: ', app)
  })

  // 设置全局未捕获一场处理器
  addGlobalUncaughtErrorHandler(event => {
    // console.log('micro-wb-main addGlobalUncaughtErrorHandler, event: ', event)
  })
}

// 注册一个观察者函数
actions.onGlobalStateChange((state, prevState) => {
  // console.log('micro-wb-main主应用观察者, 改变前的值为 ', prevState)
  // console.log('micro-wb-main主应用观察者, 改变后的值为 ', state)
})

/**
 * @name 验证用户身份并注册微应用
 */
const startMicroApp = () => {
  // 页面信息配置
  Vue.prototype.$pageConfig = pageConfig
  // api接口参数配置
  Vue.prototype.$apiConfig = apiConfig
  // 项目配置信息
  Vue.prototype.$projectConfig = projectConfig
  // 微应用配置
  const token = sessionStorage[projectConfig.operator]
  if (token) { // 已登录状态获取服务端微应用注册表
    if (projectConfig.isMicroServer && process.env.NODE_ENV !== 'development') {
      microRegister(getNewMicroList())
    } else if (checkSsoIndexMicro(noAuthMicroApps)) {
      microRegister(noAuthMicroApps)
    } else {
      microRegister(authMicroApps)
    }
  } else { // 默认加载未登录时无需服务端获取的微应用
    microRegister(noAuthMicroApps)
  }
}

// 检测当前地址是否符合单点登录情况(已登录过的)，符合自动加载到当前微应用区域；
const checkSsoIndexMicro = (list = []) => {
  let result = false
  // 获取当前地址栏信息---（用于用于单点登录二次跳转登录验证）
  const pathname = window.location.pathname
  const windowHash = window.location.hash
  // 当前浏览器的微应用信息是否符合,当前情况
  list.forEach(item => {
    if (item.microAppPre === pathname && windowHash.indexOf('#/ssoIndex?') === 0) result = true
  })
  return result
}

const getNewMicroList = () => {
  // 微应用配置
  const newMicroList = []
  const microList = sessionStorage.microAppList ? JSON.parse(sessionStorage.microAppList) : []
  microList.forEach(item => {
    const newItem = {}
    newItem.microAppName = item.name
    newItem.microAppPre = item.routeName
    newItem.microAppEnter = item.path
    newItem.isMicroApp = item.defaultApp
    newItem.microFirstUrl = item.defaultPage
    newMicroList.push(newItem)
  })
  return newMicroList.length === 0 ? authMicroApps : newMicroList
}

// 监听浏览器返回按钮，设置选中菜单
// TODO 浏览器返回键仍然存在漏洞
window.onpopstate = function (event) {
  // pushState或者replaceState不处理
  if (event && event.singleSpaTrigger) {
    return
  }
  // console.log('location: ' + document.location + ', state: ' + JSON.stringify(event))
  // console.log('mainApp $store', mainApp.$store)

  const pathname = location.pathname.split('/')[1]

  // context为空，默认不处理
  if (!pathname) {
    return
  }

  // 获取hash的systemId
  const sysId = util.getHashQueryString('_s')
  // console.log('sysId:', sysId, 'location.hash:', location.hash)
  // to sysId 和from sysId，忽略本次操作
  if (mainApp.$store.state.menuApp.currentRootId === util.encryptCode(sysId)) {
    return
  }
  // 查找当前内存中子应用对象，
  const to = authMicroApps.find(item => util.encryptCode(item.systemId) === sysId)

  // console.log(authMicroApps, mainApp.$store.state.menuApp.currentRootId)
  if (!to) {
    return
  }
  // console.log('to:', to)
  // console.log('target:', target)
  // console.log('pathname:', pathname)
  if (to.systemId) {
    mainApp.$store.commit('menuApp/setCurrentRootId', to.systemId)
    let path = location.pathname
    if (location.hash.indexOf('?') > -1) {
      path += location.hash.slice(0, location.hash.indexOf('?'))
    }
    mainApp.$store.commit('app/setCurrentPageName', path)
  } else {
    // console.log('to.systemId： ', to.systemId)
  }
  // console.log('subAppPath:', subAppPath)
  // mainApp.$store.commit('menuApp/setCurrentRootId', 'CPCC_node_00')
}

export default startMicroApp
