/**
 * @author: TurboC
 * @date: 2020-12-16 20:18:14
 * @name 启动微应用间通信机制
 * @param {Function} initGlobalState 官方通信函数
 * @description 注意：主应用是从框架中导出的initGlobalState方法，
 * @description 注意：微应用是附加在props上的onGlobalStateChange, setGlobalState方法(只需主应用注册了通信才会有)
 */
import { initGlobalState } from 'qiankun'

const initialState = {
  msg_type: '',
  contentH: 0,
  breadcrumbReturnBtn: false
}
const actions = initGlobalState(initialState)

export default actions
