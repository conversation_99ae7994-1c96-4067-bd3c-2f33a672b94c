/**
 * @author: <PERSON><PERSON>
 * @date: 2020-12-16 20:18:14
 */
import router from '@/router'
// import store from '@/store'
// import menu from '@/utils/menu'

router && router.afterEach((to, from) => {
  // console.log('micro-wb-main router.afterEach(), to: ', to)
  // console.log('micro-wb-main router.afterEach(), from: ', from)
  if (document.querySelector('.frame-main-content')) {
    document.querySelector('.frame-main-content').scrollTop = 0
  }
})

router && router.beforeEach((to, from, next) => {
  // console.log('micro-wb-main router.beforeEach(), to: ', to)
  // console.log('micro-wb-main router.beforeEach(), from: ', from)
  if (to.path === '/') {
    next()
    return false
  }
  if (!to.path) {
    return false
  }
})