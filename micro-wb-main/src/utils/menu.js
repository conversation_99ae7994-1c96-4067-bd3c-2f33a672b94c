/**
 * Created by TurboC on 2019/08/8.
 * 菜单控制
 */
import util from './util'
import store from '../store'
import breadcrumbUtils from '../views/frame/breadcrumb-area/breadcrumbUtils'
import aspHttps from '@/utils/http'

const { apiConfig, projectConfig, authMicroApps, pageConfig } = require('./../../../config.json')
const configMenu = require('./../../../configMenu.json')
const extendMenu = require('./../../../assets/open-life-cycle/main/menu/extendMenu.json')

const menu = {}

/**
 * 动态路由加载---菜单路由
 * @method loadRoutes
 * @params {Object} menuList {Array}
 * @return callback 回调函数
 * @desc 根据后端菜单树 构建鉴权菜单
 */
menu.loadRoutes = (vm, menuList, callback) => {
  const buildMenuList = []
  let routerIndex = 0
  let test = []
  let count = 1
  // 构建子级
  let status = true
  // 按照菜单结构构建第一层路由配置信息
  menuList.forEach((item, index) => {
    routerIndex = index
    buildMenuList.push(parentBuild(item))
    if (item.children) {
      buildMenu(item, buildMenuList[index].children)
    }
  })

  // 构建路由回调
  callback(buildMenuList)

  // 构建父级(第一级)
  function parentBuild (item) {
    test = [item.text]
    return {
      path: item.url,
      name: item.url ? item.url.replace('/', '') : item.url,
      title: item.text,
      children: []
    }
  }

  function buildMenu (data, tempList) {
    data.children.forEach((item) => {
      if (test.length !== 1 && status) {
        test.splice(test.length - 1, 1)
      }
      if (item.children && item.children.length !== 0) {
        test.push(item.text)
        status = false
        count++
        buildMenu(item)
        count--
        test.splice(count, 1)
      } else {
        status = true
        test.push(item.text)
        const pathSplit = item.url.split('/')
        const name = pathSplit[pathSplit.length - 1]
        buildMenuList[routerIndex].children.push({
          path: item.url,
          name: name,
          title: test.join(','),
          meta: { id: item.id }
        })
      }
    })
    return tempList
  }
}

/*
 * menu {vue,Object} 获取菜单结构第一级
 * return {String} 路由地址
 * */
menu.getMenuIds = vim => {
  const currentPath = vim.$store.state.breadcrumbApp.currentPath
  const menuTree = sessionStorage.menuTree && JSON.parse(sessionStorage.menuTree)
  const navMenu =
    sessionStorage.topMenuList && JSON.parse(sessionStorage.topMenuList)
  let allMenu = menuTree || []
  allMenu = navMenu ? navMenu.concat(menuTree) : allMenu
  let menuIds = {}
  if (util.isEmptyArray(currentPath) || util.isEmptyArray(allMenu)) {
    return menuIds
  }

  function getId (menuTree) {
    menuTree.forEach(item => {
      if (item.leaf) {
        if (item.url === currentPath[currentPath.length - 1].name) {
          menuIds.menuId = item.id
          menuIds.parentId = item.parentId ? item.parentId : item.id
        }
      } else {
        menuIds = getId(item.children)
      }
    })
    return menuIds
  }
  return getId(allMenu)
}

/**
 * 加载路由信息，记录菜单位置，标记菜单信息
 */
menu.openNewPage = (vm, name) => {
  vm.$nextTick(() => {
    // 将url-name转换成菜单url
    if (!name) return
    name = menu.currentRouterPrefix(name, true)
    // 记录面包屑，并返回当前路由信息
    name = breadcrumbUtils.setCurrentPathBreadcrumb(vm, name)
    // 动态设置菜单展开 --- 不可再用NavMemory，NavMemory记录为当前页面路由信息，支持刷新页面功能
    // const navMemoryKey = vm.$projectConfig.projectNavMemoryKey
    // const NavMemory = (sessionStorage.getItem(navMemoryKey) && JSON.parse(sessionStorage.getItem(navMemoryKey))) || {}
    // name = NavMemory.menuPath || name
    // 刷新菜单同步通过改为currentPath支持
    const currentPath = (sessionStorage.currentPath && JSON.parse(sessionStorage.currentPath)) || []
    const currentMenuPath = (currentPath.length > 0 && currentPath[currentPath.length - 1]) || {}
    name = currentMenuPath.name || name
    // 更新url前缀
    name = menu.currentRouterPrefix(name, true)
    const vim = vm
    vm.$nextTick(() => {
      vim.$store.commit('app/setTabSwitch', {
        openSideMenu: {
          menuId: menu.getMenuIds(vim).menuId,
          parentMenuId: menu.getMenuIds(vim).parentId,
          openIds: menu.getOpenIds(menu.getMenuIds(vim).menuId)
        }
      })
    })
    // 记录当前路由信息---与菜单url保持一致
    vm.$store.commit('app/setCurrentPageName', name)
  })
  // setTimeout(() => {
  //   // 将url-name转换成菜单url
  //   if (!name) return
  //   name = menu.currentRouterPrefix(name, true)
  //   // 记录面包屑，并返回当前路由信息
  //   name = breadcrumbUtils.setCurrentPathBreadcrumb(vm, name)
  //   // 动态设置菜单展开 --- 不可再用NavMemory，NavMemory记录为当前页面路由信息，支持刷新页面功能
  //   // const navMemoryKey = vm.$projectConfig.projectNavMemoryKey
  //   // const NavMemory = (sessionStorage.getItem(navMemoryKey) && JSON.parse(sessionStorage.getItem(navMemoryKey))) || {}
  //   // name = NavMemory.menuPath || name
  //   // 刷新菜单同步通过改为currentPath支持
  //   const currentPath = (sessionStorage.currentPath && JSON.parse(sessionStorage.currentPath)) || []
  //   const currentMenuPath = (currentPath.length > 0 && currentPath[currentPath.length - 1]) || {}
  //   name = currentMenuPath.name || name
  //   // 更新url前缀
  //   name = menu.currentRouterPrefix(name, true)
  //   const vim = vm
  //   vm.$nextTick(() => {
  //     vim.$store.commit('app/setTabSwitch', {
  //       openSideMenu: {
  //         menuId: menu.getMenuIds(vim).menuId,
  //         parentMenuId: menu.getMenuIds(vim).parentId,
  //         openIds: menu.getOpenIds(menu.getMenuIds(vim).menuId)
  //       }
  //     })
  //   })
  //   // 记录当前路由信息---与菜单url保持一致
  //   vm.$store.commit('app/setCurrentPageName', name)
  //   // doStr
  //   // console.log('openNewPage')
  // }, 50)
}

// 4A登陆：过滤掉个性化的权限
menu.operateFilter4AButton = (paraName, vm) => {
  const url = vm.$apiConfig.managerPathPrefix + '/resource/listHideResource'
  const param = {
    systemName: paraName
  }
  aspHttps.asp_PostForm(url, param).then(response => {
    if (aspHttps.reponseStatus(response)) {
      const filterButton = response.data || {}
      // 存储数据
      sessionStorage.filter4AButton = JSON.stringify(filterButton)
      // 开始过滤
      const tmpData = sessionStorage[vm.$projectConfig.operator]
      const compose = tmpData ? JSON.parse(tmpData) : {}
      const buttonRight = compose.authInfo || []
      for (const prop of Object.keys(filterButton)) {
        const filterArray = filterButton[prop] || []
        const rightArray = buttonRight[prop] || []
        if (!util.isEmptyArray(filterArray) && !util.isEmptyArray(rightArray)) {
          buttonRight[prop] = buttonRight[prop].filter(item => {
            return filterButton[prop].indexOf(item) < 0
          })
        }
      }
      compose.authInfo = buttonRight
      sessionStorage.setItem(vm.$projectConfig.operator, JSON.stringify(compose))
    }
  })
}

// 存储菜单信息
menu.loadMenuInStore = vm => {
  // 存储菜单信息-纵向菜单
  const leftMenuTree = sessionStorage.menuTree ? JSON.parse(sessionStorage.menuTree) : []
  vm.$store.commit('app/setLeftMenuList', leftMenuTree)
  // 存储菜单信息-横向菜单
  const topMenuList = sessionStorage.topMenuList ? JSON.parse(sessionStorage.topMenuList) : []
  vm.$store.commit('app/setTopMenuList', topMenuList)
  // 存储自定义等其他layout菜单
  const layoutMenuList = sessionStorage.layoutMenuTree ? JSON.parse(sessionStorage.layoutMenuTree) : []
  vm.$store.commit('menuApp/setLayoutMenuList', layoutMenuList)
  // 存储顶部菜单（1.一级（）菜单在顶部、二三级菜单在左侧；2.一二三级菜单都在顶部）
  const topAndLeftMenuList = topMenuList
  vm.$store.commit('app/setTopAndLeftMenuList', topAndLeftMenuList.concat(leftMenuTree))
  // 存储左侧菜单（一级菜单在顶部、二三级菜单在左侧）
  const currLeftMenuList = sessionStorage.topMenuList && sessionStorage.setLeftForTopMenuList ? JSON.parse(sessionStorage.setLeftForTopMenuList) : []
  vm.$store.commit('app/setLeftForTopMenuList', currLeftMenuList)
  // 构建全量菜单
  const AllMenu = vm.$store.state.app.leftMenuList.concat(
    vm.$store.state.app.topMenuList
  )
  // 存储主题配置信息
  vm.$store.commit('app/setNavigatorCode', sessionStorage.themeConfig && JSON.parse(sessionStorage.themeConfig).navigatorCode)
  vm.$store.commit('app/setUserSettings', sessionStorage.themeConfig && JSON.parse(sessionStorage.themeConfig).userSettings)
  if (vm.$store.state.app.navigatorCode === '1') {
    vm.$store.commit('app/setTopAndLeftMenuList', '')
  }
  menu.loadRoutes(vm, AllMenu, menuListRouter => {
    vm.$store.commit('app/setAddRouter', menuListRouter)
  })
  // 记录菜单标记展示代办信息
  menu.getMenuExtendIds(vm, AllMenu, menuExtendIds => {
    vm.$store.commit('menuApp/setMenuExtendIds', menuExtendIds)
  })
}

// 使用了webbas2.0权限控制协议，需要权限转换。
// 目前只提供给pcc的bootStarp页面进行按钮鉴权操作
menu.transformButtonInfo = buttonRight => {
  const buttonInfo = {}
  for (const item in buttonRight) {
    const buttonKeys = buttonRight[item]
    buttonInfo[item] = true
    buttonKeys.forEach(i => {
      buttonInfo[i] = true
    })
  }
  // 记录权限按钮值
  sessionStorage.buttonInfo = JSON.stringify(buttonInfo)
}

// curMenuOrder {Array} 配置自定义菜单顺序及内容（横、纵向菜单）
menu.curMenuOrder = (option, navDefaultOrder) => {
  const result = []
  navDefaultOrder.forEach(id => {
    const target = option.filter(mid => {
      return id === mid.id
    })
    if (target.length !== 0) {
      result.push(target[0])
    }
  })
  return result
}

// type===0时是左菜单，type===1时是顶菜单
menu.menuDataFilter = (option, type) => {
  let result = []
  result = option.filter(mid => {
    if (type === 0) {
      return !Object.prototype.hasOwnProperty.call(mid, 'position') || mid.position === 'left'
    } else if (type === 1) {
      return Object.prototype.hasOwnProperty.call(mid, 'position') && mid.position !== 'left'
    }
  })
  return result
}

// 同域下横向菜单构建
menu.menuTreeData = (vim, menuTree) => {
  // 个性化菜单
  menuTree = [...extendMenu, ...menuTree]
  // 构建定制横向菜单内容
  const topMenuList = menu.menuDataFilter(menuTree, 1)
  sessionStorage.topMenuList = JSON.stringify(topMenuList)
  // 构建定制纵向菜单内容
  menuTree = menu.menuDataFilter(menuTree, 0)
  return menuTree
}

/**
 * 通过资源id展开菜单
 */
menu.getOpenIds = menuId => {
  const menuTree = sessionStorage.menuTree && JSON.parse(sessionStorage.menuTree)
  const navMenu =
    sessionStorage.topMenuList && JSON.parse(sessionStorage.topMenuList)
  let allMenu = menuTree || []
  allMenu = navMenu ? navMenu.concat(menuTree) : allMenu
  let opendIds = menu.getUpNodeIdsById(menuId, allMenu)
  opendIds = util.isEmptyArray(opendIds) ? [] : opendIds
  return opendIds
}

/**
 * 通过Url获取菜单--向全量菜单allMenuTree获取
 * @param url
 */
menu.getMenuByUrlAll = url => {
  const allMenuTree = (sessionStorage.allMenuTree && JSON.parse(sessionStorage.allMenuTree)) || []
  let menu = {}

  function getMenu (tree) {
    tree.forEach(item => {
      if (item.leaf && item.url && item.url === url) {
        menu = item
      } else if (item.children) {
        menu = getMenu(item.children)
      }
    })
    return menu
  }

  return getMenu(allMenuTree)
}

/**
 * 通过Url获取菜单
 * @param url
 */
menu.getMenuByUrl = url => {
  const menuTreeStr = sessionStorage.getItem('menuTree') // 当前左侧菜单
  const menuTreeTop = sessionStorage.getItem('topMenuList') // 当前横向菜单
  const layoutMenuTree = sessionStorage.getItem('layoutMenuTree') // 当前自定义菜单
  const configMenuList = menu.getConfigMenuList()
  // const menuTreeList = menuTreeStr ? JSON.parse(menuTreeStr) : []
  const menuTreeList = [...menuTreeStr ? JSON.parse(menuTreeStr) : [], ...menuTreeTop ? JSON.parse(menuTreeTop) : [], ...layoutMenuTree ? JSON.parse(layoutMenuTree) : [], ...configMenuList]
  let menuData = {}

  function getMenu (tree) {
    tree.forEach(item => {
      if (item.leaf && item.url && item.url === url) {
        menuData = item
      } else if (item.children) {
        menuData = getMenu(item.children)
      }
    })
    return menuData
  }

  return getMenu(menuTreeList)
}

/**
* 通过资源id获取菜单
* @param menuId
*/
menu.getMenuById = menuId => {
  const menuTreeStr = sessionStorage.getItem('menuTree') // 当前左侧菜单
  const menuTreeTop = sessionStorage.getItem('topMenuList') // 当前横向菜单
  const layoutMenuTree = sessionStorage.getItem('layoutMenuTree') // 当前自定义菜单
  const configMenuList = menu.getConfigMenuList()
  // const menuTreeList = menuTreeStr ? JSON.parse(menuTreeStr) : []
  const menuTreeList = [...menuTreeStr ? JSON.parse(menuTreeStr) : [], ...menuTreeTop ? JSON.parse(menuTreeTop) : [], ...layoutMenuTree ? JSON.parse(layoutMenuTree) : [], ...configMenuList]
  let menuData = {}

  function getMenu (tree) {
    tree.forEach(item => {
      if (item.id && item.id === menuId) {
        menuData = item
      } else if (item.children) {
        menuData = getMenu(item.children)
      }
    })
    return menuData
  }

  return getMenu(menuTreeList)
}

/**
 * 获取配置菜单
 * @param {*} mKey 制定标识key
 * @returns []
 */
menu.getConfigMenuList = mKey => {
  if (!configMenu) return []
  let confMenu = []
  if (mKey && configMenu[mKey]) {
    return configMenu[mKey] || []
  }
  Object.keys(configMenu).forEach(key => {
    if (configMenu[key]) confMenu = confMenu.concat(configMenu[key])
  })
  return confMenu
}

/**
 * 通过树形节点Id，索取树形节点数据
 * @param id 树形节点Id
 * @param nodeTree 完整树形节点数据
 */
menu.getNodeById = (id, nodeTree) => {
  let nodeData = {}
  // 节点id是否存在
  if (!id) {
    return nodeData
  }
  // 完整树形节点数据是否存在
  if (util.isEmptyArray(nodeTree)) {
    return nodeData
  }

  function getDataById (data) {
    data.forEach(item => {
      if (item.id === id) {
        nodeData = item
      } else {
        if (!item.leaf) nodeData = getDataById(item.children)
      }
    })
    return nodeData
  }

  return getDataById(nodeTree)
}

/**
 * 通过树形节点Id，向上索取树形节点数据
 * @param id 树形节点Id
 * @param nodeTree 完整树形节点数据
 */
menu.getUpNodeTreeById = (id, nodeTree) => {
  let resultNodeTree = {}
  // 节点id是否存在
  if (!id) {
    return resultNodeTree
  }
  // 完整树形节点数据是否存在
  if (util.isEmptyArray(nodeTree)) {
    return resultNodeTree
  }
  // 获取当前节点
  const nodeData = menu.getNodeById(id, nodeTree)
  // 清理子集
  if (!(Object.keys(nodeData).length === 0)) nodeData.children = []

  function getDataByPId (node, tree) {
    // 根级节点返回当前
    if (!node.parentId || node.level === '0') return node

    tree.forEach(item => {
      if (item.id === node.parentId) {
        item.children = []
        item.children.push(node)
        resultNodeTree = getDataByPId(item, nodeTree)
      } else {
        if (!item.leaf) resultNodeTree = getDataByPId(node, item.children)
      }
    })
    return resultNodeTree
  }
  return getDataByPId(nodeData, nodeTree)
}

/**
 * 通过树形节点Id，向上索取树形节点id
 * @param id 树形节点Id
 * @param nodeTree 完整树形节点数据
 */
menu.getUpNodeIdsById = (id, nodeTree) => {
  const nodeIds = []
  // 节点id是否存在
  if (!id) {
    return nodeIds
  }
  // 完整树形节点数据是否存在
  if (util.isEmptyArray(nodeTree)) {
    return nodeIds
  }
  const nodeData = menu.getUpNodeTreeById(id, nodeTree)
  if (Object.keys(nodeData).length === 0) return nodeIds
  getNodeIds(nodeData)

  function getNodeIds (data) {
    nodeIds.push(data.id)
    if (!data.leaf) getNodeIds(data.children[0])
  }

  return nodeIds
}

/**
// TODO--PCC项目个性化
 * 项目个性化 针对部分存量项目元数据url前缀已经固定，
 * 这里实际路由访问前缀发生变化，需要按指定方式进行切换
 * 默认正向转换
 * @param {*} index 当前访问的url
 * @param {*} isTurnBack 是否反向转换，默认为false;
 * @returns
 */
menu.currentRouterPrefix = (index, isTurnBack = false) => {
  const { routerConfig = [] } = apiConfig
  const microList = sessionStorage.microAppList ? JSON.parse(sessionStorage.microAppList) : []
  routerConfig.forEach(item => {
    let nginxRouter = item.nginxRouter
    microList.forEach(microItem => {
      if (microItem.name === item.microAppName) {
        nginxRouter = microItem.routeName
      }
    })
    if (isTurnBack) {
      if (item.nginxRouter.trim().length > 0 && index.indexOf(item.nginxRouter.trim()) === 0) {
        index = index.replace(nginxRouter, item.localRouter)
      }
    } else {
      if (item.localRouter.trim().length > 0 && index.indexOf(item.localRouter.trim()) === 0) {
        index = index.replace(item.localRouter, nginxRouter)
      }
    }
  })
  return index
}

/**
// TODO--PCC项目个性化
 * 项目个性化 iframe 加载的url
 * 前提菜单showType=framePage；【为frame加载页面】
 * @param {*} index
 * @returns
 */
menu.framePROTagIndex = (index) => {
  let index0 = index
  // PCC网关页面地址  加 /pccWeb前缀--变为/pcc/pccWeb;
  if (index0.indexOf('/pcc/') === 0) {
    index0 = index0.replace('/pcc/', '/pcc/pccWeb/')
  } else if (index0.indexOf('/ui-manage/') === 0) {
    // 工作流加前缀  加 /pcc
    index0 = apiConfig.curPathPrefix + index0
  }
  return index0
}

/**
// TODO--PCC项目个性化
 * 项目个性化 iframe 加载的url，反向转换
 * 前提菜单showType=framePage；【为frame加载页面】
 * @param {*} index
 * @returns
 */
menu.frameUnPROTagIndex = (index) => {
  let index0 = index
  // PCC网关页面地址  加 /pcc/pccWeb前缀--变为/pcc;
  if (index0.indexOf('/pcc/pccWeb/') === 0) {
    index0 = index0.replace('/pcc/pccWeb/', '/pcc/')
  } else if (index0.indexOf('/pcc-bof/ui-manage/') === 0) {
    // 工作流剔除前缀  剔除 /pcc
    index0 = index0.replace('/pcc-bof/ui-manage/', '/ui-manage/')
  }
  return index0
}

/**
 * 当前真实url，主要用于转换iframe类型的url
 * @param {*} path 当前路由url
 * @returns 返回当前真实url
 */
menu.currentTagPath = (path) => {
  // 当地址为framePage时需取url参数进行判断
  let frameTagUrl = util.getUrlQueryString('iframe')
  // 获取实际url
  if (frameTagUrl) {
    frameTagUrl = frameTagUrl && decodeURIComponent(frameTagUrl)
    frameTagUrl = menu.frameUnPROTagIndex(frameTagUrl)
    path = frameTagUrl
  }
  return path
}

/**
 * 记录当前路由导航信息，并同步菜单路由信息
 * @param {*} vm 当前this指向
 * @param {*} path 当前路由信息
 */
menu.setMenuNavMemoryPath = (vm, path) => {
  const tmpPath = vm.$microAppPre + '#' + path
  let menuPath = tmpPath // 菜单url
  if (path === '/frame/framePage') {
    // 获取真正菜单url
    menuPath = util.getUrlQueryString('iframe') || tmpPath
  }
  vm.$main_tools.store.commit('app/setNavMemory', { router: { path: tmpPath }, menuPath })
  // 加载页面并更新面包屑
  menu.openNewPage(vm.$main_tools.router.app, tmpPath)
}

/**
 * 当前子应用离开时，缓存记录系统历史面包屑信息
 * @param {*} props 当前应用信息
 */
menu.storeHistoryCurrentPath = (props) => {
  // 进行开启多系统切换场景使用
  if (!projectConfig.multiMenu) return false
  // console.log('menu.storeHistoryCurrentPath', props, authMicroApps)
  let systemId = ''
  !util.isEmptyArray(authMicroApps) && authMicroApps.forEach(item => {
    if (item.microAppPre === props.microAppPre) {
      systemId = item.systemId
    }
  })
  // 获取到系统Id后，缓存当前系统最近一次面包屑信息
  // console.log('menu.storeHistoryCurrentPath====》》》》》systemId', systemId)
  const sysCurrentPath = (sessionStorage.sysCurrentPath && JSON.parse(sessionStorage.sysCurrentPath)) || { hisSystemId: systemId }
  const currentPath = (sessionStorage.currentPath && JSON.parse(sessionStorage.currentPath)) || []
  if (sysCurrentPath && systemId && sysCurrentPath[systemId]) {
    sysCurrentPath[systemId].currentPath = currentPath
  } else {
    sysCurrentPath[systemId] = { currentPath }
  }
  // 记录历史系统Id（也称：离开当前应用时，当前应用所属的系统Id）
  sysCurrentPath.hisSystemId = systemId
  // console.log('menu.storeHistoryCurrentPath ====》》》》》sysCurrentPath', sysCurrentPath)
  // 再缓存记录
  sessionStorage.sysCurrentPath = JSON.stringify(sysCurrentPath)
}

/**
 * 第一次进入当前子应用时，判断进入当前应用的url是否为菜单url
 * 若是菜单url触发菜单事件；
 * 若不是菜单url，则更新面包屑和菜单定位；
 * @param {*} props 当前应用信息
 */
menu.updateHistoryCurrentPath = (vm, props) => {
  // 进行开启多系统切换场景使用
  if (!projectConfig.multiMenu) return false
  // console.log('menu.updateHistoryCurrentPath', props, authMicroApps)
  let systemId = ''
  !util.isEmptyArray(authMicroApps) && authMicroApps.forEach(item => {
    if (item.microAppPre === props.microAppPre) {
      systemId = item.systemId
    }
  })
  // 获取到系统Id后，更新当前系统面包屑信息
  // console.log('menu.updateHistoryCurrentPath====》》》》》systemId', systemId)
  const sysCurrentPath = (sessionStorage.sysCurrentPath && JSON.parse(sessionStorage.sysCurrentPath)) || {}
  // 若上一应用离开时所属系统与当前应用所属系统是同一系统时，不做任何操作；
  if (sysCurrentPath.hisSystemId === systemId) return false
  // 若上一应用离开时所属系统与当前应用所属系统不是同一系统时；
  let path = window.location.pathname + window.location.hash
  const framePage = props.microAppPre + '#/frame/framePage'
  // 过滤iframePage
  if (path === framePage) {
    // 获取真正菜单url
    path = util.getUrlQueryString('iframe') || path
  }
  // console.log('menu.updateHistoryCurrentPath', path)
  // 若进入当前应用的url为菜单url，则更新系统菜单，执行菜单url事件；
  path = menu.currentRouterPrefix(path, true)
  const menuData = menu.getMenuByUrlAll(path)
  if (menuData && menuData.url === path) {
    vm.$main_tools.store.commit('app/setMultiSystem', { systemId, menuUrl: path })
  } else {
    // 若进入当前应用的url不是菜单url，则更新历史系统面包屑为当前系统应用面包屑
    const currentPath = sysCurrentPath[systemId]
    path = util.isEmptyArray(currentPath) ? path : currentPath[currentPath.length - 1].name
    // 更新系统面包屑-并定位菜单
    menu.openNewPage(vm.$main_tools.router.app, path)
  }
}

/**
 * 获取标记为待办信息的菜单资源信息
 * @param {*} vm this指向
 * @param {*} allMenu 全量菜单
 * hasExtend-1 菜单标记为展示待办信息
 */
menu.getMenuExtendIds = (vm, allMenu = [], callback) => {
  const MenuExtendIds = []
  getIds(allMenu)
  callback(MenuExtendIds)
  function getIds (list) {
    list.forEach(item => {
      if (item.hasExtend === '1') MenuExtendIds.push(item.id)
      if (item.children && item.children.length !== 0) getIds(item.children)
    })
  }
}

/**
 * @description: 菜单帮助信息附件全量列表
 * @param {*} vm this指向
 * @return {*}
 */
menu.getAllMenuHelpInfoList = vm => {
  aspHttps.asp_PostForm(apiConfig.supportPathPrefix + '/prompt/workbook/getAllResourceWorkbook').then(response => {
    if (aspHttps.reponseStatus(response)) {
      const menuHelpInfoList = response.data || [] // 菜单帮助信息数组
      store.commit('app/setMenuHelpInfoList', menuHelpInfoList)
    }
  })
}

/**
 * @description: 页面字段帮助信息全量列表
 * @param vm this指向
 * @return {*}
 */
menu.getAllPageHelpInfoList = vm => {
  aspHttps.asp_PostForm(apiConfig.supportPathPrefix + '/prompt/listAll').then(response => {
    if (aspHttps.reponseStatus(response)) {
      const pageHelpInfoList = response.data || [] // 字段帮助信息数组
      store.commit('app/setPageHelpInfoList', pageHelpInfoList)
    }
  })
}

/**
 * @description: 传入权限数组或单个字符串检测按钮权限
 * @param {Any} resId 权限参数（若为数组可传第二、三个参数）
 * @param {Any} all 是否需要校验数组匹配情况。默认为false，返回整个结果数组
 * @param {Any} flag 至少一个匹配传true，至少一个不匹配传false。默认为 true
 * @return {*} true/false 或者 ['true', 'false']
 */
menu.checkButtonAuth = (resId, all = false, flag = true) => {
  const IS_ARRAY = Array.isArray(resId)
  const IS_STRING = util.isString(resId)
  if (!IS_ARRAY && !IS_STRING) return
  const buttonInfo = typeof menu.getItem('buttonInfo') === 'object' ? menu.getItem('buttonInfo') : JSON.parse(menu.getItem('buttonInfo'))
  if (IS_ARRAY) {
    return all ? resId.some(item => !!buttonInfo[item] === flag) : resId.map(i => !!buttonInfo[i])
  } else {
    return !!buttonInfo[resId]
  }
}

/**
 * @description: 封装 sessionStorage 获取
 * @param {String} key 必传
 * @return {Boolean}
 */
menu.getItem = (key) => {
  return JSON.parse(sessionStorage.getItem(key))
}

/**
 * @description: 通过url执行菜单点击动作
 * @param {*} vm this指向
 * @param {*} index 当前路由地址
 * @return {*}
 * @author: yuxuan <EMAIL>
 */
menu.clickMenu = (vm, index) => {
  let menuItem = vm.$menu.getMenuByUrl(index)
  if (Object.keys(menuItem).length === 0) {
    menuItem = vm.$menu.getMenuById(index)
  }
  if (menuItem && menuItem.showType === 'singleDialog') {
    vm.$store.commit('mainApp/setMenuDialogConfig', {
      visible: !vm.$store.state.mainApp.menuDialogConfig.visible,
      title: menuItem.text,
      jsonPath: menuItem.url
    })
    return
  }
  if (menuItem && menuItem.showType === 'onlyMenu') {
    return
  }
  // 校验cookie
  // vm.checkCookies()
  if (index === '' || index === undefined) {
    // 跳到主页
    vm.$emit('watchHomeBtn', 0)
  }
  if (menuItem.id === pageConfig.homePage.pageId) {
    // 跳到主页
    vm.$emit('watchHomeBtn', 0)
  } else {
    /*
     * @routerUrl 路由地址
     * 当前系统:相对路径其他系统:http://
     * 默认加载菜单第一项
     *
     * */
    vm.$store.commit('app/setTabUrl', { path: menuItem.url })
  }
}

export default menu
