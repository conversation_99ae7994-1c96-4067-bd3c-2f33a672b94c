export default {
  mobile (rule, value, callback) {
    const { platformConfig } = JSON.parse(sessionStorage.getItem('LISTALL'))
    const reg = new RegExp(platformConfig.mobileComplex.replace(/\//g, ''))
    // let reg = /^(1[3|4|5|6|7|8|9]\d{9})$/
    if (!value) {
      return callback(new Error('手机号码不能为空'))
    }

    if (!reg.test(value)) {
      // callback(new Error('请输入正确的手机号码格式'))
      callback(new Error(platformConfig.mobileComplexTip))
    } else {
      callback()
    }
  },
  email (rule, value, callback) {
    const { platformConfig } = JSON.parse(sessionStorage.getItem('LISTALL'))
    const reg = new RegExp(platformConfig.emailComplex.replace(/\//g, ''))
    // let reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
    if (!value) {
      return callback(new Error('email不能为空'))
    }
    // 模拟异步验证效果
    setTimeout(() => {
      if (!reg.test(value)) {
        // callback(new Error('输入正确的邮箱格式'))
        callback(new Error(platformConfig.emailComplexTip))
      } else {
        callback()
      }
    }, 500)
  },
  checkMobile (rule, value, callback) {
    const reg = /^1\d{10}$/
    if (!value) {
      return callback(new Error('手机号码不能为空'))
    }

    if (!reg.test(value)) {
      callback(new Error('请输入正确的手机号码格式'))
    } else {
      callback()
    }
  },
  checkEmail (rule, value, callback) {
    const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
    if (!value) {
      return callback(new Error('email不能为空'))
    }
    // 模拟异步验证效果
    setTimeout(() => {
      if (!reg.test(value)) {
        callback(new Error('输入正确的邮箱格式'))
      } else {
        callback()
      }
    }, 500)
  },
  checkSpecial (rule, value, callback) {
    const reg = /[''<>]/
    if (reg.test(value)) {
      callback(new Error("不能输入 <> \" '这些字符!"))
    } else {
      callback()
    }
  },
  isNumber (rule, value, callback) {
    const reg = /^[0-9]*$/
    if (!reg.test(value)) {
      callback(new Error('请输入数字!'))
    } else {
      callback()
    }
  },
  checkPasswordLogin (rule, value, callback) {
    return callback()
  },
  checkPassword (rule, value, callback) {
    const { platformConfig } = JSON.parse(sessionStorage.getItem('LISTALL'))
    const tip = platformConfig['password-complex-tip'] || ''
    const reg = new RegExp(platformConfig['password-complex'].replace(/\//g, ''))
    if (value && !reg.test(value)) {
      callback(new Error(tip))
    } else {
      callback()
    }
  },
  checkUsername (rule, value, callback) {
    const reg = /[''<>]/
    if (!value) {
      return callback(new Error('请输入用户名'))
    }
    if (reg.test(value)) {
      callback(new Error("不能输入 <> \" '这些字符!"))
    } else {
      callback()
    }
  },
  // 对于必填项，校验全为空格时报不能为空
  checkNotEmpty (rule, value, callback) {
    if (!value) {
      callback(new Error('不可为空'))
    }
    const reg = /^[ ]+$/
    if (reg.test(value)) {
      callback(new Error('不可为空'))
    } else {
      callback()
    }
  },
  checkPassword4A (rule, value, callback) {
    const reg = /^[^\u4e00-\u9fa5]{6,20}$/
    if (!reg.test(value)) {
      // callback(new Error('密码由8-20位大写字母、小写字母、数字、特殊字符组成!'))
      callback(new Error('密码由6-20位非中文字符组成'))
    } else {
      callback()
    }
  },
  checkUsername4A (rule, value, callback) {
    const reg = /[''<>]/
    if (!value) {
      return callback(new Error('请输入用户名'))
    }

    if (reg.test(value)) {
      callback(new Error("不能输入 <> \" '这些字符!"))
    } else {
      callback()
    }
  }
}

/**
 * <AUTHOR>
 * @date   19.6.17
 * @description 正则校验提供给所有页面使用
 */
/* 邮箱 */
export function validateEmail (str) {
  const reg = /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/
  return reg.test(str)
}

/* 手机号 */
export function validateMobile (str) {
  // 字符串有8到20个数字，允许有减号，加号，中英文逗号
  const filtStr = str.replace(/-|\+|,|，/g, '')
  const reg = /^(\d{8,20})$/g
  return reg.test(filtStr)
}

/* 员工手机号 */
export function validateStaffMobile (str) {
  // 字符串有6到20个数字
  const reg = /^(\d{6,20})$/g
  return reg.test(str)
}

/* 电话号码 */
export function validateTel (str) {
  const reg = /^(\+\d{2}-)?(0\d{2,3}-)\d{7,8}$/
  return reg.test(str)
}

/* 判断身份证 */
export function validateIdCard (str) {
  const reg = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/
  return reg.test(str)
}

/* 字符长度限制 */
export function validateStrLength (str, minLen = 0, maxLen) {
  str = str ? str.trim() : ''
  const len = str ? str.length : 0
  if (maxLen === undefined) return len >= minLen
  return len >= minLen && len <= maxLen
}

/* 数字限制 */
export function validateNum (num, min = 0, max) {
  const n = Number(num)
  max = max === undefined ? min : max
  return !isNaN(n) && n >= min && n <= max
}

/**
 * 带提示的检验
 */

/* 手机号 */
export const validateTipsMobile = required => (rule, value, callback) => {
  if (!required && !value) callback()
  if (!validateMobile(value)) {
    return callback(new Error('请输入正确的手机号'))
  }
  callback()
}

/* 电话号码 */
export const validateTipsTel = required => (rule, value, callback) => {
  if (!required && !value) callback()
  if (!validateTel(value)) {
    return callback(new Error('电话号码格式不正确'))
  }
  callback()
}

/* 邮箱 */
export const validateTipsEmail = required => (rule, value, callback) => {
  if (!required && !value) callback()
  if (!validateEmail(value)) {
    return callback(new Error('请输入正确的邮箱'))
  }
  callback()
}

/* 身份证 */
export const validateTipsIdCard = required => (rule, value, callback) => {
  if (!required && !value) callback()
  if (!validateIdCard(value)) {
    return callback(new Error('身份证格式不正确'))
  }
  callback()
}

/* 字符串 */
export const validateTipsStrLen = (required, minLen, maxLen) => (
  rule,
  value,
  callback
) => {
  if (!required && !value) callback()
  if (!validateStrLength(value, minLen, maxLen)) {
    const tips =
      maxLen === undefined
        ? `请控制在${minLen}个字符以内`
        : `请控制在${minLen}-${maxLen}个字符以内`
    return callback(new Error(tips))
  }
  callback()
}

/* 验证数字 */
export const validateTipsNum = (required, min, max) => (
  rule,
  value,
  callback
) => {
  if (!required && !value) callback()
  if (!validateNum(value, min, max)) {
    return callback(new Error('请输入正确的数字'))
  }
  callback()
}
