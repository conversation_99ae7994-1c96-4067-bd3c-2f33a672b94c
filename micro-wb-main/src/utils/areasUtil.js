import util from '@/utils/util.js'
export default {
  /**
   * 获取当前id对应的节点list
   * @param {*} Ids 
   * @param {*} areaData 
  * @returns {Array} id对应的节点list
   */
  getJsonListByIds (Ids, areaData) {
    areaData = areaData || (() => {
      this.initAreasData(util.getDivisions())
      return localStorage.baseAreas
    })()
    if (!Ids || Ids.length === 0) return []
    const list = Ids.map(id => { return this.getDownJsonById(id, areaData) }) || []
    return list
  },
  /**
   * 通过指定Ids向上获取完整的json数据结构
   * @param Ids 指定的id数组
   * @param areaData 指定的地域对象
   * @param hasChildren 是否含有子集,true含有,false不含有
   * @returns {Array} 当为null值时表明division数据不正确或者查不到数据
   */
  getTotalJsonByIds (Ids, areaData, hasChildren) {
    areaData = areaData || (() => {
      this.initAreasData(util.getDivisions())
      return localStorage.baseAreas
    })()
    let nodes = []
    const dataNodes = []
    let dataStr = ''
    // 通过Ids获取各自Id的地域节点数据
    for (let i = 0; i < Ids.length; i++) {
      const downData = this.getDownJsonById(Ids[i], areaData)
      if (downData === null || downData === undefined) {
        continue
      }
      dataNodes.push(downData)
    }
    if (dataNodes.length === 0) {
      return null
    }
    dataStr = JSON.stringify(dataNodes)
    nodes = this.handlerCheckedData(dataStr, areaData, hasChildren).Nodes
    return nodes
  },
  /**
   * 整理已选中的地域数据（有级别level方法）
   * @param nodeStr 所有已选中的节点数据
   * @param areaData 完整的地域json数据
   * @param hasChildren 是否含有子集,true含有,false不含有
   * @returns {{Ids: Array, Nodes: Array}} 返回有效的已选中的地域Id数组，完整可展示的地域json数据
   */
  handlerCheckedData (nodeStr, areaData, hasChildren) {
    const checkedNodes = JSON.parse(nodeStr)
    const provinceNodes = [] // 中间变量数组省级节点数组,level=1
    let cityNodes = [] // 中间变量数组城市节点数组,level=2
    let regionNodes = [] // 中间变量数组地区节点数组,level=3
    let checkedPram = {
      Ids: [],
      Nodes: []
    }
    for (let i = 0; i < checkedNodes.length; i++) {
      let middelNode = {}
      // 判断是否选中了全国
      if (checkedNodes[i].level === '0') {
        const countryArea = JSON.parse(areaData)
        if (!hasChildren) {
          countryArea[0].children = []
        }
        checkedPram = {
          Ids: [checkedNodes[i].id],
          Nodes: countryArea
        }
        return checkedPram
      }
      if (checkedNodes[i].level === '1') {
        middelNode = checkedNodes[i]
        middelNode = this.clearNodesChildren(middelNode)
        provinceNodes.push(middelNode)
      }
      if (checkedNodes[i].level === '2') {
        middelNode = checkedNodes[i]
        middelNode = this.clearNodesChildren(middelNode)
        cityNodes.push(middelNode)
      }
      if (checkedNodes[i].level === '3') {
        middelNode = checkedNodes[i]
        middelNode = this.clearNodesChildren(middelNode)
        regionNodes.push(middelNode)
      }
    }
    // 整理,清理选中节点中市下的区
    regionNodes = this.clearSameData(cityNodes, regionNodes)
    // // 整理，清理选中节点中省下的市
    cityNodes = this.clearSameData(provinceNodes, cityNodes)
    // // 重构选中的节点id
    checkedPram.Ids = this.rebuildCheckedIds(provinceNodes, cityNodes, regionNodes)
    // // 重构选中节点的树形结构数据
    checkedPram.Nodes = this.rebuildCheckedNodes(provinceNodes, cityNodes, regionNodes)
    return checkedPram
  },
  /**
   * 清除当前节点的子节点
   * @param nodes 当前地域节点数据
   * @returns {*} 返回已清除的数据
   */
  clearNodesChildren (nodes) {
    if (nodes.children !== undefined) {
      nodes.children = []
    }
    return nodes
  },
  /**
   * 清理下一级重复数据
   * @param parentNodes 上一级地域的json节点数据
   * @param nodes 当前地域的节点json数据
   * @returns {Array} 返回当前节点json，不再与上一级节点存在重叠区域
   */
  clearSameData (parentNodes, nodes) {
    let middelData = []
    if (parentNodes.length > 0) {
      middelData = this.clearData(parentNodes[0], nodes)
      for (let i = 1; i < parentNodes.length; i++) {
        middelData = this.clearData(parentNodes[i], middelData)
      }
    } else {
      middelData = nodes
    }
    return middelData
  },
  /**
   * 多次清理函数
   * @param parentNode
   * @param nodes
   * @returns {Array}
   */
  clearData (parentNode, nodes) {
    const middelData = []
    for (let i = 0; i < nodes.length; i++) {
      if (parentNode.id === nodes[i].parentId) {
        continue
      }
      middelData.push(nodes[i])
    }
    return middelData
  },
  /**
   * 重构选中节点Id数据(提交给后台用的，也可再做展示用）
   * @param provinceNodes
   * @param cityNodes
   * @param countyNodes
   * @returns {Array}
   */
  rebuildCheckedIds (provinceNodes, cityNodes, countyNodes) {
    const ids = []
    for (let i = 0; i < provinceNodes.length; i++) {
      ids.push(provinceNodes[i].id)
    }
    for (let i = 0; i < cityNodes.length; i++) {
      ids.push(cityNodes[i].id)
    }
    for (let i = 0; i < countyNodes.length; i++) {
      ids.push(countyNodes[i].id)
    }
    return ids
  },
  /**
   * 重构选中的节点数据
   * @param provinceNodes
   * @param cityNodes
   * @param countyNodes
   * @returns {Array}
   */
  rebuildCheckedNodes (provinceNodes, cityNodes, countyNodes) {
    // 清空数据
    let nodeDatas = []
    let data = {}
    // 将区级节点数据重构至市级节点数据
    nodeDatas = this.repairNodes(cityNodes, countyNodes)
    if (nodeDatas.length > 0) {
      cityNodes = nodeDatas
    }
    // 将市级节点数据重构至省级节点数据
    nodeDatas = this.repairNodes(provinceNodes, cityNodes)
    if (nodeDatas.length > 0) {
      provinceNodes = nodeDatas
    }
    // 将省级节点数据重构至国家级
    nodeDatas = []
    data = this.getUpJsonById(provinceNodes[0].parentId, '', true)
    data.children = provinceNodes
    nodeDatas.push(data)
    return nodeDatas
  },
  /**
   * 修复节点树形结构，修复当前地域节点json数据结构至上一级地域
   * @param parentNodes
   * @param nodes
   * @returns {*}
   */
  repairNodes (parentNodes, nodes) {
    for (let i = 0; i < nodes.length; i++) {
      let pNodes = {}
      let isTrue = true
      for (let j = 0; j < parentNodes.length; j++) {
        if (nodes[i].parentId === parentNodes[j].id) {
          parentNodes[j].children.push(nodes[i])
          isTrue = false
          break
        }
      }
      // 不存在就新增节点
      if (isTrue) {
        if (nodes[i].parentId === undefined || nodes[i].parentId === null || this.getDownJsonById(nodes[i].parentId, '') === null) {
          continue
        }
        pNodes = this.getDownJsonById(nodes[i].parentId, '')
        if (pNodes === undefined || pNodes === null) {
          continue
        }
        pNodes.children = []
        pNodes.children.push(nodes[i])
        parentNodes.push(pNodes)
      }
    }
    return parentNodes
  },
  /**
   * 通过地域Id对baseAreas向下获取对应Json数据格式,如：id为省Id，则获取该省份及下属所有地域的json格式数据
   * @param id 地域Id
   * @param baseAreas 地域数据，可直接填‘’，默认为全部
   * @returns {{}} 当为null值时表明division数据不正确或者查不到数据
   */
  getDownJsonById (id, baseAreas) {
    let areaData = []
    if (!baseAreas) {
      areaData = localStorage.baseAreas ? JSON.parse(localStorage.baseAreas) : (() => {
        this.initAreasData(util.getDivisions())
        return JSON.parse(localStorage.baseAreas)
      })()
    } else {
      areaData = JSON.parse(baseAreas)
    }
    let jsonData = {}
    for (let i = 0, length = areaData.length; i < length; i++) {
      jsonData = this.getDownJson(areaData[i], id)
      if (jsonData !== null && jsonData !== {}) {
        return jsonData
      }
    }
    return null
  },
  /**
   * 对当前data向下获取json对象
   * @param data 当前节点数据
   * @param id 查询的地域ID
   * @returns {*} 当为null值时表明division数据不正确或者查不到数据
   */
  getDownJson (data, id) {
    let jsonData = {}
    if (data.id === id) {
      return data
    } else if (!data.leaf && data.leaf !== 'true') {
      for (let i = 0, length = data.children.length; i < length; i++) {
        jsonData = this.getDownJson(data.children[i], id)
        if (jsonData !== null && jsonData !== undefined && jsonData !== {}) {
          return jsonData
        }
      }
      return null
    } else {
      return null
    }
  },
  /**
   * 通过地域Id对baseAreas向上获取对应完整json格式数，如：id为区的Id,则对应衍射获取其上级市，上上级省，甚至全国的json数据
   * @param id 地域Id
   * @param baseAreas 地域数据，可直接填‘’，默认为全部
   * @param isTrue isTrue是否包含全国，true包含全国，false不包含全国
   * @returns {*} 当为null值时表明division数据不正确或者查不到数据
   */
  getUpJsonById (id, baseAreas, isTrue) {
    let areaData = ''
    if (baseAreas === null || baseAreas === '') {
      areaData = localStorage.baseAreas
    } else {
      areaData = baseAreas
    }
    // find idData first
    const idData = this.getDownJsonById(id, areaData)
    if (idData === null || idData === undefined) {
      return null
    }
    const parentData = JSON.parse(areaData)[0]
    idData.children = []
    // 若其基础地域的id与当前地域的id相同，则为同地域数据
    if (parentData.id === id) {
      return idData
    }
    // 是否为全国,或者为省份时
    if ((!isTrue && idData.level === '1') || (idData.level === '0')) {
      return idData
    }
    return this.getUpJsonByPId(idData, areaData, isTrue)
  },
  /**
   * 对baseAreas向上获取idData的父级的json对象
   * @param idData 当前节点数据
   * @param isTrue 是否包含全国
   * @returns {*} 当为null值时表明division数据不正确或者查不到数据
   */
  getUpJsonByPId (idData, baseAreas, isTrue) {
    const pId = idData.parentId
    const pIdData = this.getDownJsonById(pId, baseAreas)
    if (pIdData === null || pIdData === undefined) {
      return null
    }
    pIdData.children = []
    pIdData.children.push(idData)
    // 是否为全国,或者为省份时
    if ((!isTrue && pIdData.level === '1') || (pIdData.level === '0')) {
      return pIdData
    }
    return this.getUpJsonByPId(pIdData, baseAreas, isTrue)
  },
  /**
   * 对child获取下级地域ID
   * @param child
   * @returns {Array}
   */
  getChildId (child) {
    const ids = []
    const id = child.id
    ids.push(id)
    if (child.children.length > 0) {
      const cIds = this.getChildId(child.children[0])
      cIds.forEach(function (item) {
        ids.push(item)
      })
    }
    return ids
  },
  /**
   * 根据地域Id对baseAreas获取向上完整结构数组，如：id为区，则向上获取baseAreas的市和省的Id，组成数据
   * @param id 地域id
   * @param isTrue 是否包含全国，true包含，false不包含
   * @returns {Array} 当为null值时表明division数据不正确或者查不到数据
   */
  getTotalIds (id, baseAreas, isTrue) {
    const IDs = []
    const idData = this.getUpJsonById(id, baseAreas, isTrue)
    if (idData === null || idData === undefined) {
      return null
    }
    const cId = idData.id
    IDs.push(cId)
    if (idData.children.length > 0) {
      const cIds = this.getChildId(idData.children[0])
      cIds.forEach(function (item) {
        IDs.push(item)
      })
    }
    return IDs
  },
  // 获取完整地域数据并封装成json结构数据
  initAreasData (data) {
    const baseAreas = []
    let country = {}
    let children = []
    let countryId = ''
    const provinceAreas = []
    const provinceChildren = []
    data.forEach(function (item) {
      if (item.id === '000' || (item.parentId === null && item.leaf.toString() === 'true')) {
        country = item
        countryId = item.id
        children = [...util.isEmptyArray(item.children) ? [] : item.children]
      } else {
        children.push({ ...{ parentId: countryId }, ...item })
        provinceChildren.push({ ...{ parentId: countryId }, ...item })
      }
    })
    baseAreas.push({ ...{}, ...country, ...{ leaf: false, level: '0', children } })
    provinceAreas.push({ ...{}, ...country, ...{ leaf: false, level: '0', children: provinceChildren } })
    // 存储地域属性树形结构
    localStorage.baseAreas = JSON.stringify(baseAreas)
    // 存储地域属性树形结构 --- 只有中央和省级
    localStorage.provinceAreas = JSON.stringify(provinceAreas)
  }
}
