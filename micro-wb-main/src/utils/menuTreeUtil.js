/**
 * Created by TurboC on 2019/08/8.
 * 菜单控制
 */
import util from './util'
import menu from './menu'
import store from '../store'
const { projectConfig } = require('./../../../config.json')
const extendMenu = require('./../../../assets/open-life-cycle/main/menu/extendMenu.json')

const menuTreeUtil = {}

/**
 * 动态路由加载---菜单路由
 * @method loadRoutes
 * @params {Object} menuList {Array}
 * @return callback 回调函数
 * @desc 根据后端菜单树 构建鉴权菜单
 */
menuTreeUtil.loadRoutes = (vm, menuList, callback) => {
  const buildMenuList = []
  let routerIndex = 0
  let test = []
  let count = 1
  let status = true
  // 按照菜单结构构建第一层路由配置信息
  menuList.forEach((item, index) => {
    routerIndex = index
    buildMenuList.push(parentBuild(item))
    if (item.children) {
      buildMenu(item, buildMenuList[index].children)
    }
  })

  // 构建路由回调
  callback(buildMenuList)

  // 构建父级(第一级)
  function parentBuild (item) {
    test = [item.text]
    return {
      path: item.url,
      name: item.url ? item.url.replace('/', '') : item.url,
      title: item.text,
      children: []
    }
  }

  // 构建子级
  function buildMenu (data, tempList) {
    data.children.forEach((item) => {
      if (test.length !== 1 && status) {
        test.splice(test.length - 1, 1)
      }
      if (item.children && item.children.length !== 0) {
        test.push(item.text)
        status = false
        count++
        buildMenu(item)
        count--
        test.splice(count, 1)
      } else {
        status = true
        test.push(item.text)
        const pathSplit = item.url.split('/')
        const name = pathSplit[pathSplit.length - 1]
        buildMenuList[routerIndex].children.push({
          path: item.url,
          name: name,
          title: test.join(','),
          meta: { id: item.id }
        })
      }
    })
    return tempList
  }
}

/*
 * menu {vue,Object} 获取菜单结构第一级
 * return {String} 路由地址
 * */
menuTreeUtil.getMenuIds = vim => {
  const currentPath = vim.$store.state.app.currentPath
  const menuTree = sessionStorage.menuTree && JSON.parse(sessionStorage.menuTree)
  const navMenu =
    sessionStorage.topMenuList && JSON.parse(sessionStorage.topMenuList)
  let allMenu = menuTree || []
  allMenu = navMenu ? navMenu.concat(menuTree) : allMenu
  let menuIds = {}
  if (util.isEmptyArray(currentPath) || util.isEmptyArray(allMenu)) {
    return menuIds
  }

  function getId (menuTree) {
    menuTree.forEach(item => {
      if (item.leaf) {
        if (item.url === currentPath[currentPath.length - 1].name) {
          menuIds.menuId = item.id
          menuIds.parentId = item.parentId ? item.parentId : item.id
        }
      } else {
        menuIds = getId(item.children)
      }
    })
    return menuIds
  }
  return getId(allMenu)
}



// 存储菜单信息
menuTreeUtil.loadMenuInStore = vm => {
  const $store = vm.$store || vm
  // 存储菜单信息-纵向菜单
  const leftMenuTree = JSON.parse(sessionStorage.menuTree || '[]')
  $store.commit('app/setLeftMenuList', leftMenuTree)
  // 存储菜单信息-横向菜单
  const topMenuList = JSON.parse(sessionStorage.topMenuList || '[]')
  $store.commit('app/setTopMenuList', topMenuList)
  // 构建全量菜单
  const AllMenu = $store.state.app.leftMenuList.concat($store.state.app.topMenuList).concat($store.state.menuApp.layoutMenuList)
  menuTreeUtil.loadRoutes(vm, AllMenu, menuListRouter => {
    $store.commit('app/setAddRouter', menuListRouter)
  })
}

// curMenuOrder {Array} 配置自定义菜单顺序及内容（横、纵向菜单）
menuTreeUtil.curMenuOrder = (option, navDefaultOrder) => {
  const result = []
  navDefaultOrder.forEach(id => {
    const target = option.filter(mid => {
      return id === mid.id
    })
    if (target.length !== 0) {
      result.push(target[0])
    }
  })
  return result
}

// type===0时是左菜单，type===1时是顶菜单
menuTreeUtil.menuDataFilter = (option, type) => {
  let result = []
  result = option.filter(mid => {
    if (type === 0) {
      return !Object.prototype.hasOwnProperty.call(mid, 'position') || mid.position === 'left'
    } else if (type === 1) {
      return Object.prototype.hasOwnProperty.call(mid, 'position') && mid.position !== 'left'
    }
  })
  return result
}

/**
 * 同域下横向菜单构建
 * @param vim 当前this指向
 * @param menuTree 系统菜单
 */
menuTreeUtil.menuTreeData = (vim, menuTree) => {
  // 个性化菜单
  menuTree = [...extendMenu, ...menuTree]
  // 构建定制横向菜单内容
  const topMenuList = menuTreeUtil.menuDataFilter(menuTree, 1)
  sessionStorage.topMenuList = JSON.stringify(topMenuList)
  // 构建定制纵向菜单内容
  menuTree = menuTreeUtil.menuDataFilter(menuTree, 0)
  return menuTree
}

/**
 * 通过资源id展开菜单
 */
menuTreeUtil.getOpenIds = menuId => {
  const menuTree = sessionStorage.menuTree && JSON.parse(sessionStorage.menuTree)
  const navMenu =
    sessionStorage.topMenuList && JSON.parse(sessionStorage.topMenuList)
  let allMenu = menuTree || []
  allMenu = navMenu ? navMenu.concat(menuTree) : allMenu
  let opendIds = menu.getUpNodeIdsById(menuId, allMenu)
  opendIds = util.isEmptyArray(opendIds) ? [] : opendIds
  return opendIds
}

/**
 * 通过Url获取菜单
 * @param url
 */
menuTreeUtil.getMenuByUrl = url => {
  const menuTreeStr = sessionStorage.getItem('menuTree')
  const menuTreeTop = sessionStorage.getItem('topMenuList')
  // const menuTreeList = menuTreeStr ? JSON.parse(menuTreeStr) : []
  const menuTreeList = [...menuTreeStr ? JSON.parse(menuTreeStr) : [], ...menuTreeTop ? JSON.parse(menuTreeTop) : []]
  let menu = {}

  function getMenu (tree) {
    tree.forEach(item => {
      if (item.leaf && item.url && item.url === url) {
        menu = item
      } else if (item.children) {
        menu = getMenu(item.children)
      }
    })
    return menu
  }

  return getMenu(menuTreeList)
}

/**
 * 通过资源id获取菜单
 * @param menuId
 */
menuTreeUtil.getMenuById = menuId => {
  const menuTreeStr = sessionStorage.getItem('menuTree')
  const menuTreeTop = sessionStorage.getItem('topMenuList')
  // const menuTreeList = menuTreeStr ? JSON.parse(menuTreeStr) : []
  const menuTreeList = [...menuTreeStr ? JSON.parse(menuTreeStr) : [], ...menuTreeTop ? JSON.parse(menuTreeTop) : []]
  let menu = {}

  function getMenu (tree) {
    tree.forEach(item => {
      if (item.id && item.id === menuId) {
        menu = item
      } else if (item.children) {
        menu = getMenu(item.children)
      }
    })
    return menu
  }

  return getMenu(menuTreeList)
}

/**
 * 通过资源Id、菜单url进行打开菜单加载页面操作
 * @param {*} id 菜单资源id
 * @param {*} url 菜单url
 */
menuTreeUtil.openMenuPageByIdOrUrl = (id = '', url = '') => {
  let currentMenu = {}
  if (id) currentMenu = menuTreeUtil.getMenuById(id)
  if (url) currentMenu = menuTreeUtil.getMenuByUrl(url)
  if (!currentMenu || !currentMenu.url) return
  store.commit('app/setTabUrl', { path: currentMenu.url })
}

/**
 * 通过根级菜单节点获取菜单
 * @param {*} allMenuTree 全量菜单
 * @param {*} nodeMenuId 根级菜单节点（虚拟菜单）
 */
menuTreeUtil.getCurrentMenu = (allMenuTree, nodeMenuId) => {
  if (util.isEmptyArray(allMenuTree)) {
    allMenuTree = (sessionStorage.allMenuTree && JSON.parse(sessionStorage.allMenuTree)) || []
  }
  let menuTree = []
  if (!nodeMenuId) menuTree = allMenuTree
  allMenuTree.forEach(item => {
    if (item.showType === 'rootNode' && item.id === nodeMenuId) {
      // 获取当前有效菜单/当前需展示的菜单信息，并标记一级菜单为根级菜单
      item.children.forEach(itemCell => {
        itemCell.level = '0' // 标记菜单级为根级
        menuTree.push(itemCell)
      })
    }
  })
  // 附加操作--若默认根节点在已获取菜单下不存在，则默认获取存量菜单的第一个根节点
  if (nodeMenuId && menuTree.length === 0 && allMenuTree.length > 0) {
    nodeMenuId = allMenuTree[0].id
    // 重新获取menuTree
    allMenuTree[0].children.forEach(itemCell => {
      itemCell.level = '0' // 标记菜单级为根级
      menuTree.push(itemCell)
    })
  }
  // 同时记录多根级菜单/虚拟菜单信息
  menuTreeUtil.getOtherNodeMenuList(nodeMenuId)
  menuTree = menuTreeUtil.menuTreeData(null, menuTree)
  return menuTree
}

/**
 * 获取当前最新菜单信息--并对菜单进行封装处理
 * @param {*} menuTree 当前菜单信息
 * @param {*} nodeMenuId 当前根级节点菜单信息
 * @returns menuTree
 */
menuTreeUtil.getMenuTree = (menuTree, nodeMenuId = '') => {
  // 过滤配置化菜单，（非常规化配置菜单）
  menuTree = menuTreeUtil.filterLayoutMenu(menuTree)
  // 对菜单进行封装处理
  if (projectConfig.multiMenu) { // 开启多系统菜单切换
    // 缓存全量菜单
    sessionStorage.allMenuTree = JSON.stringify(menuTree)
    menuTree = menuTreeUtil.getCurrentMenu(menuTree, nodeMenuId || projectConfig.defaultNodeMenuId)
  } else { // 独立菜单切换
    menuTree = menu.menuTreeData(null, menuTree)
  }
  return menuTree
}

/**
 * 获取其他根级菜单
 * @param {*} nodeMenuId 当前展示的根级菜单
 * @returns 其他根级菜单
 */
menuTreeUtil.getOtherNodeMenuList = (nodeMenuId) => {
  const allMenuTree = (sessionStorage.allMenuTree && JSON.parse(sessionStorage.allMenuTree)) || []
  const nodeMenuConfig = (sessionStorage.nodeMenuConfig && JSON.parse(sessionStorage.nodeMenuConfig)) || {}
  nodeMenuId = nodeMenuId || nodeMenuConfig.currentNodeId || projectConfig.defaultNodeMenuId
  let nodeMenuList = []
  const otherNodeMenuList = []
  let currentNodeTitle = ''
  allMenuTree.forEach(item => {
    if (item.showType === 'rootNode' && item.id === nodeMenuId) {
      // 获取当前系统的title
      currentNodeTitle = item.text
      nodeMenuList.push({
        id: item.id,
        icon: item.icon,
        text: item.text,
        children: item.children
      })
    }
    otherNodeMenuList.push({
      id: item.id,
      icon: item.icon,
      text: item.text,
      children: item.children
    })
  })
  // 当当前系统没有菜单时，说明默认菜单不存在，需重新获取默认第一个菜单
  if (nodeMenuList && nodeMenuList.length === 0 && otherNodeMenuList.length > 0) {
    nodeMenuId = otherNodeMenuList[0].id
    currentNodeTitle = otherNodeMenuList[0].text
    nodeMenuList = otherNodeMenuList[0].children
    otherNodeMenuList.shift()
  }
  // 同时记录多根级菜单/虚拟菜单信息
  store.commit('menuApp/setNodeMenuLit', nodeMenuList)
  store.commit('menuApp/setOtherNodeMenuLit', otherNodeMenuList)
  store.commit('menuApp/setCurrentRootId', nodeMenuId)
  // 缓存当前数据
  sessionStorage.nodeMenuConfig = JSON.stringify({
    currentNodeId: nodeMenuId,
    currentNodeTitle: currentNodeTitle,
    otherNodeMenuList
  })
  return otherNodeMenuList
}

/**
 * 更新系统菜单（无刷新更新）【仅对多系统生效】
 * */
menuTreeUtil.updateSysMenu = (vim, { sysId = '', leftMenuTree = [], topMenuList = [], menuUrl = '' }) => {
  // 是否开启多系统
  if (!projectConfig.multiMenu) return
  if (!sysId && util.isEmptyArray(leftMenuTree) && util.isEmptyArray(topMenuList)) return
  if (sysId) {
    // 通过系统id更新系统缓存
    menuTreeUtil.getCurrentMenu([], sysId)
  }
  // 获取获取最新系统菜单
  if (util.isEmptyArray(leftMenuTree)) leftMenuTree = (sessionStorage.menuTree && JSON.parse(sessionStorage.menuTree)) || []
  if (util.isEmptyArray(topMenuList)) topMenuList = (sessionStorage.topMenuList && JSON.parse(sessionStorage.topMenuList)) || []
  // 更新系统菜单
  store.commit('app/setLeftMenuList', leftMenuTree) // 纵向菜单
  store.commit('app/setTopMenuList', topMenuList) // 横向菜单
  menuTreeUtil.loadMenuInStore(vim)
  // 触发菜单事件
  if (menuUrl) {
    vim.$nextTick(() => {
      menuTreeUtil.openMenuPageByIdOrUrl('', menuUrl)
    })
  }
}

/**
 * 根据一级菜单获取下级菜单
 * @param {全量菜单}} menuTree
 * @param {点击的一级菜单} currModuleName
 * @returns
 */
menuTreeUtil.getMenuList = (menuTree, currModuleName) => {
  const leftMenuList = []
  menuTree && menuTree.forEach(item => {
    if (item.id === currModuleName) {
      // 主题3模式，二三级菜单展示在左侧
      if (item.children && item.children.length > 0) {
        item.children.forEach(child => {
          leftMenuList.push(child)
        })
      } else {
        leftMenuList.push(item)
      }
    }
  })
  return leftMenuList
}

/**
 * 过滤掉无用的菜单
 * 当当前菜单为虚拟菜单时，而其子级菜单无任何菜单信息，则清除当前虚拟菜单；
 * @param {*} menuTree 当前菜单信息
 * @returns menuTree
 */
menuTreeUtil.filterInvalidMenu = (menuTree = []) => {
  // 未开启虚拟菜单无需过滤
  if (!projectConfig.multiMenu) return menuTree
  // 对菜单进行过滤掉无用的菜单
  return menuTree.filter(item => {
    const { showType, children } = item
    if (showType === 'rootNode' && children && children.length > 0) return item
  })
}

/**
 * 提前自定义菜单
 * @param {*} menuTree 菜单
 */
menuTreeUtil.filterLayoutMenu = (menuTree) => {
  // console.log('filterLayoutMenu', menuTree)
  const layoutMenuTree = []
  if (projectConfig.multiMenu) { // 开启多系统菜单切换
    menuTree = menuTree.filter(item => {
      if (item.showType === 'rootNode') {
        // 获取当前有效菜单/当前需展示的菜单信息，并标记一级菜单为根级菜单
        item.children = formatLayoutList(item.children)
      } else if (item.layout) {
        item.layout = menuTreeUtil.isJSONString(item.layout) && JSON.parse(item.layout)
        item.layoutFlag = menuTreeUtil.isJSONString(item.layout)
        layoutMenuTree.push(item)
      }
      return !item.layout
    })
  } else { // 未开启时
    menuTree = formatLayoutList(menuTree)
  }
  // 记录自定义菜单 && 同时避免覆盖
  if (!sessionStorage.layoutMenuTree) {
    sessionStorage.layoutMenuTree = JSON.stringify(layoutMenuTree)
  }
  return menuTree

  function formatLayoutList (list) {
    list = list.filter(item => {
      if (item.layout) {
        item.layoutFlag = menuTreeUtil.isJSONString(item.layout)
        item.layout = menuTreeUtil.isJSONString(item.layout) && JSON.parse(item.layout)
        layoutMenuTree.push(item)
      }
      return !item.layout
    })
    return list
  }
}

/**
 * @description: 判断String字符串是否是JSON格式
 * @param {*} str 字符串
 * @return {*} true/false
 * @author: yuxuan <EMAIL>
 */
menuTreeUtil.isJSONString = (str) => {
  if (!str) return false
  try {
    JSON.parse(str)
  } catch (error) {
    return false
  }
  return true
}
export default menuTreeUtil
