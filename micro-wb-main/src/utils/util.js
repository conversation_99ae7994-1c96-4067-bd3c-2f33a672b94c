/**
 * Created by aspire on 2018/9/24.
 */
import { changeThemeColor } from './themeColorClient'
import { asp_Encrypt } from 'asp-smart-ui/lib/utils'
const encrypt_key = '0'
const util = {}
const { pageConfig } = require('./../../../config.json')

util.getUrlQueryString = (name) => {
  const query = window.location.hash.split('?')
  if (query && query.length === 2) {
    const vars = query[1].split('&')
    for (let i = 0; i < vars.length; i++) {
      var pair = vars[i].split('=')
      if (pair[0] === name) {
        return pair[1]
      }
    }
  }
  return (false)
}
/**
 * 判断数组是否为空
 * @param arr
 * @returns {boolean} true：为空，false：不为空
 */
util.isEmptyArray = arr => {
  let result = true
  if (Object.prototype.toString.call(arr) === '[object Array]') {
    if (arr && arr.length > 0) {
      result = false
    }
  } else {
    //
  }
  return result
}

/**
 * 判断对象是否为空
 * @param obj
 * @returns {boolean} true：为空，false：不为空
 */
util.isEmptyObject = (obj) => {
  return Object.keys(obj).length === 0
}

/**
 * 通过codeType从字典数据中获取需要的数据
 * @param vim
 * @param codeType
 * @returns {[]|*[]}
 */
util.getFinalDictByType = (vim, codeType) => {
  const codeData = util.getCodeData(vim)
  if (!util.isEmptyObject(codeData) && codeType) {
    const dictType = JSON.parse(sessionStorage.getItem('LISTALL')).platformConfig.dictDisableShowType
    let localCodeData = codeData[codeType] || []
    if (dictType === 'invisible') { // 'invisible'  不展示status为0的字典数据
      localCodeData = localCodeData.filter(val => val.status !== '0')
    }
    if (dictType === 'notOptional') { // notOptional 展示但是禁用status为0的字典数据
      localCodeData = localCodeData.map(val => {
        val.disabled = val.status === '0'
        return val
      })
    }
    return localCodeData
  }
  return []
}

/**
 * 从缓存中获取字典数据
 * @param vim
 * @param o
 * @returns {{}|''|any}
 */
util.getCodeData = (vim, o = true) => {
  const codeData = sessionStorage.getItem('codeData') && JSON.parse(sessionStorage.getItem('codeData'))
  if (codeData) {
    return codeData
  } else {
    vim.$message.warning('获取字典数据失败，请重新尝试该操作！')
    return {}
  }
}

/**
 * 跳转至登录页面重新登录
 */
util.relogin = () => {
  const path = pageConfig.loginPage.microAppPre + '#' + pageConfig.loginPage.routerPath
  const title = pageConfig.loginPage.titleName
  // 若当前页面就是登陆页面则不做任何操作
  if (window.location.pathname !== pageConfig.loginPage.microAppPre) {
    window.history.pushState({}, title, path)
  }
  sessionStorage.clear()
  window.location.reload()
}

/**
 * 获取指定url上的参数值
 * @param name 当前指定url上的key值
 * @param query 当前指定url
 * @returns result 结果值，若没有默认返回false
 */
util.getUrlQueryString = (name, query = '') => {
  // 当query不存在时， 默认使用当前浏览器url
  query = query ? query.split('?') : window.location.hash.split('?')
  if (query && query.length === 2) {
    const vars = query[1].split('&')
    for (let i = 0; i < vars.length; i++) {
      var pair = vars[i].split('=')
      if (pair[0] === name) {
        return pair[1]
      }
    }
  }
  return (false)
}

/**
 * 通过codeType从字典数据中获取需要的数据
 * @param vim
 * @param codeType
 * @returns {[]|*[]}
 */
util.getCodeValueByType = (vim, codeType) => {
  const codeData = util.getCodeData(vim)
  if (!codeData) {
    return []
  }
  const localCodeData = codeType ? codeData[codeType] : codeData
  return localCodeData
}

util.changeThemeColorHandler = (color, fn = function () { }) => {
  if (!color) return
  changeThemeColor(color).then(t => {
    fn()
  })
}

/**
 * @description: 更新主题配置信息
 * @param {*} vim 当前this指向
 * @return {*}
 * @author: yuxuan <EMAIL>
 */
util.updateThemeCongfig = (vim) => {
  // 更新配置
  const themeConfig = sessionStorage.themeConfig && JSON.parse(sessionStorage.themeConfig)
  const themeSwitch = sessionStorage.themeSwitch && JSON.parse(sessionStorage.themeSwitch)
  vim.$store.commit('menuApp/setThemeConfig', Object.assign({}, themeConfig, { themeSwitch }))
}

/**
 * @description: 判断是否是字符串
 * @param {Any} str
 * @return {Boolean} true、false
 */
util.isString = (str) => {
  return (typeof str === 'string') && str.constructor === String
}

/**
 * 单向加密菜单ID
 * @param {*} str 菜单ID
 * @returns 加密后的菜单ID
 */
util.encryptCode = (str) => {
  if (!str) {
    return ''
  }
  const encryptStr = asp_Encrypt(str, encrypt_key)
  // 转义除以下字符, 只保留不会被encodeUriComponent的字符
  const regex = /[^A-Za-z0-9-_.!~*'()]/g
  return encryptStr.replace(regex, 'z')
}

/**
 * 获取hash上的参数
 * @param {*} name 参数名
 * @returns 参数值
 */
util.getHashQueryString = (name) => {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  if (window.location.hash.indexOf('?') > -1) {
    const r = window.location.hash.split('?')[1].match(reg)
    if (r !== null) return decodeURIComponent(r[2])
  }
  return ''
}
export default util
