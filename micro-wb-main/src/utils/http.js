/**
 * http 请求入口
 * Create by TurboC on 2019/02/17.
 * ================================================================ */
import espromise from 'es6-promise'
import axios from 'axios'
import qs from 'qs'
import { Message } from 'element-ui'
import { asp_Decrypt, asp_Encrypt } from 'asp-smart-ui/lib/utils/algorithm'
espromise.polyfill()

const aspHttps = {}
const { pageConfig, apiConfig, projectConfig } = require('./../../../config.json')
aspHttps.$store = null

// 全局请求头设置
axios.defaults.headers = {
  'Content-Type': 'application/json;charset=utf-8'
}
// 响应头是否已加密
function checkEncrypted (response) {
  const headers = response.headers
  return (
    headers['CONTENT-ENCRYPTED'] === '1' || headers['content-encrypted'] === '1'
  )
}

// 封装fetchPlus方法
aspHttps.asp_FetchPlus = (url, params = null) => {
  if (params !== null) {
    const list = []
    for (const key in params) {
      const item = params[key]
      if (typeof item === 'object') {
        for (const key2 in item) {
          list.push(key + '%5B%27' + key2 + '%27%5D=' + item[key2])
        }
      } else {
        list.push(key + '=' + params[key])
      }
    }
    url = url + '?' + list.join('&')
  }
  return new Promise((resolve) => {
    axios.get(url)
      .then(response => {
        resolve(response.data)
      })
      .catch(err => {
        const { status = 400, statusText = '请求异常' } = err.request || err
        const response = { status: status, message: statusText }
        resolve(response)
      })
  })
}

// 封装fetch方法
aspHttps.asp_Fetch = (url, params = null) => {
  if (params !== null) {
    params = Object.keys(params).map(function (key) {
      return encodeURIComponent(key) + '=' + encodeURIComponent(params[key])
    }).join('&')
    url = url + '?' + params
  }
  return new Promise((resolve) => {
    axios.get(url)
      .then(response => {
        resolve(response.data)
      })
      .catch(err => {
        const { status = 400, statusText = '请求异常' } = err.request || err
        const response = { status: status, message: statusText }
        resolve(response)
      })
  })
}
// 封装get方法
aspHttps.asp_Get = (url, params = null) => {
  if (params !== null && Object.keys(params).length > 0) {
    params = Object.keys(params).map(function (key) {
      return encodeURIComponent(key) + '=' + encodeURIComponent(params[key])
    })
      .join('&')
    url = url + '?' + params
  }
  // return new Promise((resolve, reject) => {
  return new Promise((resolve) => {
    axios.get(url)
      .then(response => {
        resolve(response.data)
      })
      .catch(err => {
        const { status = 400, statusText = '请求异常' } = err.request || err
        const response = { status: status, message: statusText }
        resolve(response)
        // reject(err)
      })
  })
}

// 封装post请求(json类型)
aspHttps.asp_Post = (url, data) => {
  // return new Promise((resolve, reject) => {
  return new Promise((resolve) => {
    axios.post(url, data)
      .then(
        response => {
          resolve(response.data)
        },
        err => {
          const { status = 400, statusText = '请求异常' } = err.request || err
          const response = { status: status, message: statusText }
          resolve(response)
          // reject(err)
        }
      )
      .catch(err => {
        const { status = 400, statusText = '请求异常' } = err.request || err
        const response = { status: status, message: statusText }
        resolve(response)
        // reject(err)
      })
  })
}
// 封装post请求(form类型)
aspHttps.asp_PostForm = (url, data, isHeader = false) => {
  const headers = { 'Content-type': 'application/x-www-form-urlencoded' }
  // return new Promise((resolve, reject) => {
  return new Promise((resolve) => {
    axios.post(url, qs.stringify(data), { headers: headers })
      // axios.post(url, data, { headers: headers })
      .then(response => {
        resolve(isHeader ? response : response.data)
      }, err => {
        const { status = 400, statusText = '请求异常' } = err.request || err
        const response = { status: status, message: statusText }
        resolve(response)
        // reject(err)
      })
      .catch(err => {
        const { status = 400, statusText = '请求异常' } = err.request || err
        const response = { status: status, message: statusText }
        resolve(response)
        // reject(err)
      })
  })
}
// 封装asyncPost的请求(post的同步请求，请求过程是阻塞的)
aspHttps.asp_AsyncPost = (url, data) => {
  const xhr = new XMLHttpRequest()
  let result
  xhr.open('POST', url, false)
  // 添加http头，发送信息至服务器时内容编码类型
  xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded')
  xhr.onreadystatechange = function () {
    if (xhr.readyState === 4 && (xhr.status === 200 || xhr.status === 304)) {
      result = xhr.responseText
    }
  }
  xhr.send(data)
  return result
}

// 封装delete请求
aspHttps.asp_RequestDelete = (url, data) => {
  // return new Promise((resolve, reject) => {
  return new Promise((resolve) => {
    axios.delete(url, data)
      .then(response => {
        resolve(response.data)
      }, err => {
        const { status = 400, statusText = '请求异常' } = err.request || err
        const response = { status: status, message: statusText }
        resolve(response)
        // reject(err)
      })
      .catch(err => {
        const { status = 400, statusText = '请求异常' } = err.request || err
        const response = { status: status, message: statusText }
        resolve(response)
        // reject(err)
      })
  })
}
// 文件上传
aspHttps.asp_FileUpload = (url, data, type) => {
  console.log(type, 'dataaa')
  // return new Promise((resolve, reject) => {
  return new Promise((resolve) => {
    axios.post(url, data, { headers: { 'Content-Type': 'multipart/form-data','type': type } }).then(response => {
      resolve(response.data)
    }).catch(err => {
      const { status = 400, statusText = '请求异常' } = err.request || err
      const response = { status: status, message: statusText }
      resolve(response)
      // reject(err)
    })
  })
}

// 文件下载方法
aspHttps.asp_Post_file = (url, data) => {
  const typeDic = {
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    doc: 'application/msword',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  }
  // return new Promise((resolve, reject) => {
  const headers = { 'Content-type': 'application/json' }
  return new Promise((resolve) => {
    axios.post(url, data, { headers: headers, responseType: 'blob' })
      .then((response) => {
        // [data.data] 这个可能是data也可能是data.data具体和后端返回体有关
        if (response.headers['content-disposition']) {
          var blob = new Blob([response.data], { type: typeDic[response.headers['content-disposition'].split('.')[1]] })
          var url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          document.body.appendChild(a)
          a.style.display = 'none'
          a.href = url
          a.download = data.downLoadName
          a.click()
          URL.revokeObjectURL(a.href)
          document.body.removeChild(a)
        } else {
          response.data.text().then(res => {
            const data = JSON.parse(res)
            if (aspHttps.reponseStatus(data)) {
              resolve(data)
            }
          })
        }
      }, (err) => {
        const { status = 400, statusText = '请求异常' } = err.request || err
        const response = { status: status, message: statusText }
        resolve(response)
        // reject(err)
      }
      )
      .catch((err) => {
        const { status = 400, statusText = '请求异常' } = err.request || err
        const response = { status: status, message: statusText }
        resolve(response)
        // reject(err)
      })
  })
}
// 文件下载
// 文件下载2
aspHttps.asp_FileDownload = (url, data, fileName) => {
  return axios({
    method: 'post',
    url: url,
    data: data,
    responseType: 'arraybuffer'
  })
    .then((response) => {
      if (response.headers['content-disposition'] && response.headers['content-disposition'].split) {
        fileName = decodeURIComponent(
          // 获取文件名
          response.headers['content-disposition'].split('=')[1]
        )
      }

      const blob = new Blob([response.data])
      var eleLink = document.createElement('a')
      eleLink.download = fileName
      eleLink.style.display = 'none'
      eleLink.href = URL.createObjectURL(blob)
      eleLink.click()
      // }).catch(error => {})
    })
    .catch((err) => {
      console.log(err)
    })
}


// 文件下载--自动link下载
aspHttps.asp_FileDownloadSyncLink = (url, data, fileName) => {
  const CancelToken = axios.CancelToken
  const source = CancelToken.source()
  const axiosInstance = new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url: url,
      data: data,
      responseType: 'blob',
      cancelToken: source.token
    }).then(response => {
      // responseType为json时，说明接口报错了,不下载文件
      if (parseInt(response.status) === 200 && response.data.type !== 'application/json') {
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        // 同步下载请求成功后返回true
        resolve({
          result: true,
          response
        })
      } else {
        // 同步下载请求失败后返回false
        resolve({
          result: false,
          response
        })
      }
    }).catch((error) => {
      // 当正常导出接口的http状态码为504时，触发异步导出
      if (error?.response?.status === 504) {
        resolve({
          result: false,
          response: error?.response,
          gatewayTimeout: true
        })
      } else {
        resolve({
          result: false,
          error
        })
      }
    })
  })
  return {
    axiosInstance,
    source
  }
}

// 文件下载
aspHttps.asp_FileDownloadSync = ({ url, data, method = 'post' }) => {
  const source = axios.CancelToken.source()
  const axiosConfig = {
    method,
    url,
    data: data,
    responseType: 'blob',
    cancelToken: source.token
  }
  if (/^get$/i.test(method)) axiosConfig.params = data
  const axiosResult = new Promise((resolve, reject) => {
    axios(axiosConfig).then(response => {
      // responseType为json时，说明接口报错了,不下载文件
      if (parseInt(response.status) === 200 && response.data.type !== 'application/json') {
        // 同步下载请求成功后返回true
        resolve({
          type: 'sync',
          result: true,
          responseBody: response.data,
          response: response
        })
      } else {
        // 同步下载请求失败后返回false
        resolve({
          type: 'sync',
          result: false,
          response,
          responseBody: response.data
        })
      }
    }).catch((error) => {
      if (error && error.__CANCEL__) return
      resolve({
        type: 'sync',
        result: false,
        error
      })
    })
  })
  return {
    axiosResult,
    source
  }
}

/** 异步导出 */
aspHttps.asp_ExportAsync = (option) => {
  const { vm, exportUrl, exportAsyncUrl, data, asyncData, timeout, method } = option
  return new Promise((resolve, reject) => {
    const callback = {
      result: false,
      type: 'sync',
      response: null,
      responseBody: null,
      onerror: null,
      onsuccess: null
    }
    if (!vm) {
      throw new Error('vm is not defined')
    }
    let timer = null // 异步导出轮询计时器
    let isSyncFinsh = false // 同步导出是否已完成
    clearTimeout(timer) // 清空计时器
    const { source, axiosResult } = aspHttps.asp_FileDownloadSync({ url: exportUrl, data, method })
    axiosResult.then(({ result, response, responseBody, error }) => {
      // 同步接口响应了,中断异步下载定时器
      clearTimeout(timer)
      // 接受同步下载返回的result
      // true: 同步下载已完成，不执行异步下载
      // false: 同步下载未完成或者已失败，执行异步下载
      isSyncFinsh = result
      callback.result = result
      callback.response = response
      callback.responseBody = responseBody
      resolve(callback) // 给外部用的Promise返回值

      // 同步接口报错，提示报错信息
      // if (result) return
      // if (response) {
      //   // 此时data格式为Blob，解析responseData为JSON
      //   const reader = new FileReader()
      //   reader.readAsText(response.data, 'utf-8')
      //   reader.onload = () => {
      //     const { message } = JSON.parse(reader.result)
      //     vm.$message && vm.$message({
      //       message: `${message}`,
      //       type: 'error'
      //     })
      //   }
      //   reader.onerror = (event) => {
      //     const { message = '未知错误' } = event
      //     vm.$message({
      //       message: `${message}`,
      //       type: 'error'
      //     })
      //   }
      // }
    })
    /**
     * 开始触发异步导出的倒计时
     * @param {*} waitTime 触发异步导出接口的剩余时长
     */
    async function startTimeoutToExportAsync (waitTime = 0) {
      waitTime++
      // 如果等待时间大于配置的最大时长 并且 同步导出未完成，则请求异步接口
      if (waitTime > timeout && !isSyncFinsh) {
        source && source.cancel() // 清空同步请求,开始异步请求

        // 调用异步导出接口
        const responseBody = await vm.$aspHttps.asp_Post(exportAsyncUrl, asyncData)
        callback.responseBody = responseBody
        callback.type = 'async'
        if (vm.$reponseStatus(responseBody)) {
          // 调用异步请求成功后返回true
          callback.result = true
          resolve(callback)
        } else {
          callback.result = false
          resolve(callback)
        }
        timer = null // 计时器清空
      } else {
        timer = setTimeout(() => {
          startTimeoutToExportAsync(waitTime)
        }, 1000)
      }
    }
    startTimeoutToExportAsync() // 异步导出方法轮询，符合异步导出条件后调用接口
  })
}
// 封装导出方法
aspHttps.asp_ExportGetOpen = (exportUrl) => {
  aspHttps.asp_ExportGet(exportUrl, true)
}

// 封装导出方法
aspHttps.asp_ExportGet = (exportUrl, openNewPage = false) => {
  let whiteOn = false
  let securityObject = sessionStorage.getItem('securityObject')
  if (securityObject) {
    securityObject = JSON.parse(securityObject)
  }
  // 验证白名单匹配信息
  whiteOn = aspHttps.matchWhiteOn(exportUrl, securityObject)
  // 获取OA代理前缀
  const _PCC_Base_Prx = aspHttps.getOABasePrx()
  exportUrl = _PCC_Base_Prx + exportUrl
  // 确保token不被完成消耗
  const tokenArry = JSON.parse(sessionStorage.getItem('tokenList')) || []
  if (tokenArry.length === 1) {
    // alert('网络异常，请重新刷新！')
    return false
  }
  // 拼接token
  const tmpToken = getToken()
  if (tmpToken) {
    exportUrl += '&X-AUTH-HEADER-TOKEN=' + tmpToken
  }
  const paraIndex = exportUrl.indexOf('?')
  const urlLen = exportUrl.length
  if (paraIndex > 0 && !whiteOn && securityObject && Object.prototype.hasOwnProperty.call(securityObject, 'on') && securityObject.on === 1) {
    let paraStr = exportUrl.substr(paraIndex + 1, urlLen)
    // 数据加密处理
    if (securityObject) {
      if (Object.prototype.hasOwnProperty.call(securityObject, 'on') && securityObject.on === 1 && Object.prototype.hasOwnProperty.call(securityObject, 'aesKeyValue')) {
        paraStr = aspHttps.asp_Encrypt(paraStr, securityObject.aesKeyValue)
      }
    }
    exportUrl = exportUrl.substr(0, paraIndex) + '?' + encodeURI(paraStr)
  }
  if (openNewPage) {
    window.open(exportUrl)
  } else {
    window.location.href = exportUrl
  }
}
// url中拼接token
aspHttps.asp_TokenUrl = (url) => {
  let whiteOn = false
  let securityObject = sessionStorage.getItem('securityObject')
  if (securityObject) {
    securityObject = JSON.parse(securityObject)
  }
  // 验证白名单匹配信息
  whiteOn = aspHttps.matchWhiteOn(url, securityObject)
  const tmpToken = getToken()
  let urlToken = ''
  if (tmpToken) {
    urlToken = 'X-AUTH-HEADER-TOKEN=' + tmpToken
  }
  // 数据加密处理
  if (!whiteOn && securityObject && Object.prototype.hasOwnProperty.call(securityObject, 'on') && securityObject.on === 1 && Object.prototype.hasOwnProperty.call(securityObject, 'aesKeyValue')) {
    urlToken = aspHttps.asp_Encrypt(urlToken, securityObject.aesKeyValue)
  }
  if (tmpToken) {
    url = url + '?' + urlToken
  }
  return url
}

// 解密方法
aspHttps.asp_Decrypt = (dataValue, keyValue) => {
  return asp_Decrypt(dataValue, keyValue)
}
// 加密方法
aspHttps.asp_Encrypt = (dataValue, keyValue) => {
  return asp_Encrypt(dataValue, keyValue)
}

// 解密方法plus
aspHttps.asp_DecryptPlus = (data) => {
  try {
    let securityObject = sessionStorage.getItem('securityObject')
    securityObject = securityObject && JSON.parse(securityObject)
    if (securityObject &&
      Object.prototype.hasOwnProperty.call(securityObject, 'on') &&
      securityObject.on === 1 &&
      Object.prototype.hasOwnProperty.call(securityObject, 'aesKeyValue') &&
      data
    ) {
      data = aspHttps.asp_Decrypt(
        data,
        securityObject.aesKeyValue
      )
      if (aspHttps.isValidJSON(data)) {
        data = JSON.parse(data)
      }
    }
    return data
  } catch (error) {
    return data
  }
}

// 加密方法plus
aspHttps.asp_EncryptPlus = (data) => {
  try {
    let securityObject = sessionStorage.getItem('securityObject')
    securityObject = securityObject && JSON.parse(securityObject)
    if (securityObject &&
      Object.prototype.hasOwnProperty.call(securityObject, 'on') &&
      securityObject.on === 1 &&
      Object.prototype.hasOwnProperty.call(securityObject, 'aesKeyValue') &&
      data
    ) {
      data = aspHttps.asp_Encrypt(
        data,
        securityObject.aesKeyValue
      )
    }
    return data
  } catch (error) {
    return data
  }
}

/** 判断是否为有效JSON格式 */
aspHttps.isValidJSON = (text) => {
  try {
    JSON.parse(text)
    return true
  } catch (e) {
    if (e instanceof SyntaxError) {
      return false
    }
    // 重新抛出其他非JSON解析错误
    throw e
  }
}

// 封装响应判断
aspHttps.reponseStatus = (response, showMessage = true) => {
  let retValue = false
  if (response && Object.prototype.hasOwnProperty.call(response, 'success')) {
    retValue = response.success.toString() === 'true'
    if (!(response.success.toString() === '401') && !(response.success.toString() === '901') && !retValue && showMessage) {
      if (Object.prototype.hasOwnProperty.call(response, 'message')) {
        Message.error(response.message)
      } else {
        Message.error('后端未提供message数据!')
      }
    }
  } else if (response && Object.prototype.hasOwnProperty.call(response, 'code')) {
    retValue = response.code.toString() === '200'
    if (!(response.code.toString() === '401') && !(response.code.toString() === '901') && !retValue && showMessage) {
      if (Object.prototype.hasOwnProperty.call(response, 'message')) {
        Message.error(response.message)
      } else {
        Message.error('后端未提供message数据!')
      }
    }
  } else if (response && Object.prototype.hasOwnProperty.call(response, 'status')) {
    retValue = response.status.toString() === '200'
    if (!(response.status.toString() === '401') && !(response.status.toString() === '901') && !retValue && showMessage) {
      if (Object.prototype.hasOwnProperty.call(response, 'message')) {
        Message.error(response.message)
      } else {
        Message.error('后端未提供message数据!')
      }
    }
  }
  return retValue
}

// token处理逻辑
function getToken () {
  const tokenList = JSON.parse(sessionStorage.getItem('tokenList')) || []
  if (tokenList.length) {
    const newToken = tokenList.shift()
    sessionStorage.setItem('tokenList', JSON.stringify(tokenList))
    return newToken
  } else {
    return null
  }
}
function setToken (newTokenList) {
  const tokenList = JSON.parse(sessionStorage.getItem('tokenList')) || []
  if (newTokenList) {
    for (let i = 0; i < newTokenList.length; i++) {
      tokenList.push(newTokenList[i])
    }
    sessionStorage.setItem('tokenList', JSON.stringify(tokenList))
  }
}
function clearTokenList () {
  sessionStorage.setItem('tokenList', JSON.stringify([]))
}

// http request 拦截器
axios.interceptors.request.use(function (config) {
  // console.log(aspHttps.$store)
  // 追加token
  let { secretWord, secretHeader, secretType } = aspHttps.$store.state.vuexStorage
  secretWord = aspHttps.asp_DecryptPlus(secretWord)
  secretHeader = aspHttps.asp_DecryptPlus(secretHeader)
  if (secretType === 'header' && secretHeader) {
    config.headers[secretHeader] = secretWord
  }
  // 追加功能
  config.headers['x-request-nonce'] = aspHttps.getuuID()
  config.headers['x-request-timestamp'] = new Date().getTime()
  // 替换代理前缀
  config.url = aspHttps.getProcessEnv(config.url)
  // 是否在数据加密白名单中
  let whiteOn = false
  let securityObject = sessionStorage.getItem('securityObject')
  if (securityObject) {
    securityObject = JSON.parse(securityObject)
  }
  // 验证白名单匹配信息
  whiteOn = aspHttps.matchWhiteOn(config.url, securityObject, config.headers)
  // 获取OA代理前缀
  const _PCC_Base_Prx = aspHttps.getOABasePrx()
  // 替换代理前缀
  config.url = _PCC_Base_Prx + aspHttps.getProcessEnv(config.url)
  // data数据加密处理
  if (config.method === 'post' && typeof (config.data) !== 'undefined') {
    if (!whiteOn && securityObject && Object.prototype.hasOwnProperty.call(securityObject, 'on') && securityObject.on === 1 && Object.prototype.hasOwnProperty.call(securityObject, 'aesKeyValue')) {
      config.data = aspHttps.asp_Encrypt(config.data, securityObject.aesKeyValue)
    }
  }
  // url数据加密处理
  const paraIndex = config.url.indexOf('?')
  const urlLen = config.url.length
  if (paraIndex > 0 && !whiteOn) {
    let paraStr = config.url.substr(paraIndex + 1, urlLen)
    if (securityObject && Object.prototype.hasOwnProperty.call(securityObject, 'on') && securityObject.on === 1 && Object.prototype.hasOwnProperty.call(securityObject, 'aesKeyValue')) {
      paraStr = aspHttps.asp_Encrypt(paraStr, securityObject.aesKeyValue)
    }
    config.url = config.url.substr(0, paraIndex) + '?' + paraStr
  }
  // crsf token
  const tmpToken = getToken()
  if (tmpToken) {
    config.headers = Object.assign(config.headers, { 'x-auth-header-token': tmpToken })
  }
  // return requestStatus ? { message: '重复登录' } : config
  return config
}, err => {
  if (err && err.config) {
    switch (err.config.status) {
      case 400: err.message = '请求错误(400)'
        break
      case 401: err.message = '未授权，请重新登录(401)'
        break
      case 403: err.message = '拒绝访问(403)'
        break
      case 404: err.message = '请求出错(404)'
        break
      case 408: err.message = '请求超时(408)'
        break
      case 500: err.message = '服务器错误(500)'
        break
      case 501: err.message = '服务未实现(501)'
        break
      case 502: err.message = '网络错误(502)'
        break
      case 503: err.message = '服务不可用(503)'
        break
      case 504: err.message = '网络超时(504)'
        break
      case 505: err.message = 'HTTP版本不受支持(505)'
        break
      default: err.message = `连接出错(${err.config.status})!`
    }
  } else {
    err.message = '连接服务器失败!'
  }
  return Promise.reject(err)
})
// http response 拦截器
axios.interceptors.response.use(
  response => {
    // data数据解密
    if (checkEncrypted(response) && Object.prototype.hasOwnProperty.call(response, 'data')) {
      let securityObject = sessionStorage.getItem('securityObject')
      if (securityObject) {
        securityObject = JSON.parse(securityObject)
        if (Object.prototype.hasOwnProperty.call(securityObject, 'on') &&
          securityObject.on === 1 &&
          Object.prototype.hasOwnProperty.call(securityObject, 'aesKeyValue')) {
          response.data = aspHttps.asp_Decrypt(response.data, securityObject.aesKeyValue)
          response.data = JSON.parse(response.data)
        }
      }
    }
    // 临时兼容处理，处理部分接口异常现象
    if (response.data && !response.data.status && response.status && response.status.toString() === '200') {
      apiConfig.unUsableStatusUrlList && apiConfig.unUsableStatusUrlList.forEach(item => {
        if (response.config.url.indexOf(item) >= 0) {
          Object.assign(response.data, { data: response.data, status: 200 })
        }
      })
    }
    // 登录后错误信息
    if (response && Object.prototype.hasOwnProperty.call(response, 'data') &&
      Object.prototype.hasOwnProperty.call(response.data, 'status') &&
      response.data.status.toString() === '401') {
      // 自定义登出动作
      aspHttps.doLogOut()
    }
    // 保存token
    const hasProperty = Object.prototype.hasOwnProperty.call(response.headers, 'x-auth-header-token') ||
      Object.prototype.hasOwnProperty.call(response.headers, 'X-AUTH-HEADER-TOKEN')
    if (hasProperty) {
      const hasTokenStatus = Object.prototype.hasOwnProperty.call(response.headers, 'X-AUTH-HEADER-TOKEN-STATUS') ||
        Object.prototype.hasOwnProperty.call(response.headers, 'x-auth-header-token-status')
      if (hasTokenStatus) {
        const value = response.headers['X-AUTH-HEADER-TOKEN-STATUS'] ||
          response.headers['x-auth-header-token-status']
        if (value === '1') {
          clearTokenList()
        }
      }
      const respHeader = response.headers['x-auth-header-token'] ||
        response.headers['X-AUTH-HEADER-TOKEN']
      if (respHeader.length) {
        setToken(respHeader.split('_'))
      }
    }
    return response
  },
  error => {
    return Promise.reject(error)
  }
)

aspHttps.doLogOut = () => {
  // sessionStorage.clear()
  const strArr = window.location.href.split('?')
  const sso = qs.parse(strArr[1]).sso
  if (sso && sso === '1') {
    const microList = sessionStorage.microAppList ? JSON.parse(sessionStorage.microAppList) : []
    let microAppPre = pageConfig.e404Page.microAppPre
    microList.forEach(microItem => {
      if (microItem.name === pageConfig.e404Page.microAppName) {
        microAppPre = microItem.routeName
      }
    })
    let path = microAppPre + '#' + pageConfig.e404Page.routerPath
    if (projectConfig.customLogout) {
      path = `${apiConfig.authPathPrefix}/login/logout`
      path = aspHttps.getProcessEnv(path)
    }
    sessionStorage.clear()
    window.history.pushState({}, pageConfig.e404Page.titleName, path)
    window.location.reload()
  } else {
    const microList = sessionStorage.microAppList ? JSON.parse(sessionStorage.microAppList) : []
    let microAppPre = pageConfig.loginPage.microAppPre
    microList.forEach(microItem => {
      if (microItem.name === pageConfig.loginPage.microAppName) {
        microAppPre = microItem.routeName
      }
    })
    let path = microAppPre + '#' + pageConfig.loginPage.routerPath
    const title = pageConfig.loginPage.titleName
    // 若当前页面就是登陆页面则不做任何操作, projectConfig.customLogout
    if (process.env.NODE_ENV !== 'development' && projectConfig.customLogout) {
      path = `${apiConfig.authPathPrefix}/login/logout`
      path = aspHttps.getProcessEnv(path)
      sessionStorage.clear()
      window.history.pushState({}, title, path)
      window.location.reload()
    } else if (window.location.pathname !== pageConfig.loginPage.microAppPre) {
      // 若当前页面就是登陆页面则不做任何操作
      sessionStorage.clear()
      window.history.pushState({}, title, path)
      window.location.reload()
    }
  }
}

/**
 * 判断url是否匹配白名单（支持白名单正则匹配）
 * return true-不加密，false-加密
 */
aspHttps.matchWhiteOn = (url, securityObject) => {
  if (!securityObject) return true
  if (securityObject.on !== 1) return true
  // 过滤代理前缀及参数，当前地址与白名单配置地址完全匹配时才不加密
  let whiteOn = false
  // 替换代理前缀
  apiConfig && apiConfig.proxyConfig && apiConfig.proxyConfig.forEach(item => {
    if (url.indexOf(item.localProxy) > -1) {
      url = url.replace(item.localProxy, '')
    } else if (url.indexOf(item.nginxProxy) > -1) {
      url = url.replace(item.nginxProxy, '')
    }
  })
  // 剔除请求参数
  url = url.indexOf('?') > 0 ? url.substr(0, url.indexOf('?')) : url
  // 进行白名单匹配
  securityObject.whiteList = aspHttps.asp_DecryptPlus(securityObject.whiteList)
  for (let i = 0; Object.prototype.hasOwnProperty.call(securityObject, 'whiteList') && i < securityObject.whiteList.length; i++) {
    // 判断当前白名单是否为正则匹配
    if (securityObject.whiteList[i].indexOf('reg:') > -1) {
      // 获取正则url
      const whiteList = securityObject.whiteList[i].split(':')
      // 进行匹配判断
      if (url.match(whiteList[1]) && url.match(whiteList[1]).length > 0) {
        whiteOn = true
        break
      }
    } else if (url === securityObject.whiteList[i]) {
      // 否则直接进行url判断
      whiteOn = true
      break
    }
  }
  return whiteOn
}

/**
 * @description: 生成附件下载相关地址url
 * @param {*} url 完整下载地址
 * @return {*}
 */
aspHttps.aspProductAttUrl = url => {
  // 替换代理前缀
  const result = aspHttps.getProcessEnv(url)
  return result
}

/**
 * @description: 根据环境返回当前环境正确地址
 * @param {*} url 原地址
 * @return {*}
 */
aspHttps.getProcessEnv = url => {
  if (!url) return ''
  let r = url
  // 生产环境修改为正确url
  if (process.env.NODE_ENV === 'production') {
    apiConfig && apiConfig.proxyConfig && apiConfig.proxyConfig.forEach(item => {
      r = r.replace(item.localProxy, item.nginxProxy)
    })
  }
  return r
}

/** 获取OA代理前缀 */
aspHttps.getOABasePrx = () => {
  // console.log('main http')
  let _PCC_Base_Prx = ''
  if (window._AN_base_host && window._AN_this_url === '/prx/000/') {
    _PCC_Base_Prx = `${window._AN_this_url}${window._AN_base_host.replace(':/', '')}`
  }
  return _PCC_Base_Prx
}

/**
 * 从写window.location方法
 * url 作为入参
 * 有url 参数数执行 window . location . href 操作跳转
 * 没有url 参数时，执行 返回当前地址参数值
 */
aspHttps.windowLocationHref = (url) => {
  let locationName = 'loca'
  let hrefName = 'hre'
  locationName = locationName + 'tion'
  hrefName = hrefName + 'f'
  if (url) {
    // 追加oA前缀
    window.location.href = aspHttps.getOABasePrx() + url
  } else {
    return window[locationName][hrefName]
  }
}

/** 生成唯一标识符UUID---用于解决重放攻击 */
aspHttps.getuuID = () => {
  let d = new Date().getTime()
  if (window.performance && typeof window.performance.now === "function") {
    d += performance.now() // use high-precision timer if available
  }
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (d + Math.random() * 16) % 16 | 0
    d = Math.floor(d / 16)
    return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16)
  })
  return uuid
}

export default aspHttps
