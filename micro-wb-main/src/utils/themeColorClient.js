/*
 * @Author: yuxuan <EMAIL>
 * @Date: 2022-04-13 16:29:52
 * @LastEditors: yuxuan
 * @LastEditTime: 2023-03-02 19:03:18
 * @Description: file content
 */
import client from 'webpack-theme-color-replacer/client'
import forElementUI from 'webpack-theme-color-replacer/forElementUI'
const { projectConfig, mainFrameConfig } = require('./../../../config.json')
export let curColor = projectConfig.themeColor

// 动态切换主题色
export function changeThemeColor (newColor) {
  const options = {
    newColors: [...forElementUI.getElementUISeries(newColor), '#ff0000', '#ffff00'],
    changeUrl: (url) => {
      url = `/${mainFrameConfig.mainAppName}/${url}`
      return url
    }
  }
  return client.changer.changeColor(options, Promise)
    .then(t => {
      curColor = newColor
      sessionStorage.setItem('theme_color', curColor)
    })
}
