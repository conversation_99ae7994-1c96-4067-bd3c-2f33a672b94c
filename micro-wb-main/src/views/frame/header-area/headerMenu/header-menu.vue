/** * Created by yuxuan on 2019/09/04. * 导航头部,横向菜单组件 */
<template>
  <div class="classifyScroll">
    <div class="classifys">
      <div class="asp-header-menu">
        <el-menu v-if="$store.state.app.navigatorCode === '1'"
                 :default-active="navIndex"
                 class="el-menu-vertical"
                 mode="horizontal"
                 @select="handleSelect">
          <headerMenuModel :menu-data="navList"
                           style="display: flex; overflow: hidden;"></headerMenuModel>
        </el-menu>
        <div v-else-if="$store.state.app.navigatorCode === '2'"
             class="dataNavList">
          <left-menu :menu-list="navList" />
        </div>
        <ul v-else-if="$store.state.app.navigatorCode === '3'"
            ref="dataNavList"
            class="dataNavList">
          <li v-for="(ml,index) in navList"
              :key="index"
              :class="{active:ml.id === topNavIndex}"
              :style="{transform:'translateX(-' + move + 'px)'}"
              class="dataNavListItem classifys-item"
              @click="clickTopMenu(ml.id,index,ml.url)">
            <i :class="ml.icon"
               class="iconfont max-font"
               style="font-size: 14px"></i>
            <span style="color:#fff;">
              {{ ml.text }}
            </span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import headerMenuModel from './header-menu-model'
import { debounce } from '@/utils/index.js'
import LeftMenu from '../../left-area/left-menu.vue'
const { pageConfig, projectConfig } = require('./../../../../../../config.json')
export default {
  name: 'HeaderMenu',
  components: { headerMenuModel, LeftMenu },
  props: {},
  data () {
    return {
      navIndex: '',
      topNavIndex: '',
      move: 0,
      menuHistory: ''
    }
  },
  computed: {
    navList () {
      let newNavList = []
      if (['2', '3'].indexOf(this.$store.state.app.navigatorCode) > -1) {
        newNavList = this.$store.state.app.topAndLeftMenuList.map((item) => {
          return item
        })
      } else {
        newNavList = this.$store.state.app.topMenuList.map((item) => {
          return item
        })
      }
      this.$nextTick(() => {
        this.$emit('setShowRightIcon', newNavList)
      })
      return newNavList
    }
  },
  watch: {
    '$store.state.app.navMemory.router.path' (val) {
      this.navIndex = 'false'
      if (val) {
        // 当地址为framePage时需取url参数进行判断
        const frameTagUrl = this.$menu.currentTagPath(val)
        this.navIndex = frameTagUrl
        this.$menu.openNewPage(this, frameTagUrl)
      }
    },
    '$store.state.app.navigatorCode' (val) {
      if (val) {
        const key = projectConfig.projectNavMemoryKey
        const navMemory = sessionStorage[key] ? JSON.parse(sessionStorage[key]) : {}
        const topNavIndex = navMemory.topNavIndex
        this.topNavIndex = topNavIndex
        if (topNavIndex.indexOf('/home') > -1) {
          this.topNavIndex = pageConfig.homePage.pageId
        }
        this.clickTopMenu(this.topNavIndex)
      }
    }
  },
  mounted () {
    // TODO---刷新定位会丢失（包括首页）
    this.navIndex = 'false'
  },
  created () {
    // 获取历史记录
    const key = projectConfig.projectNavMemoryKey
    const navMemory = sessionStorage[key] ? JSON.parse(sessionStorage[key]) : {}
    let routeUrl = navMemory.router.path
    // 如果是iframePage页面，则捕获真实url（frameTagUrl）
    if (routeUrl === pageConfig.iframePage.routerPath) {
      routeUrl = navMemory.frameTagUrl
    }
    const topMenuList = sessionStorage.topMenuList ? JSON.parse(sessionStorage.topMenuList) : []
    // this.navIndex = -1
    if (topMenuList && topMenuList.length > 0) {
      topMenuList.forEach((item, index) => {
        if (item.url === routeUrl) {
          this.navIndex = item.url
        }
      })
    }
    // this.$store.commit('app/setShowRouterUrl', { navIndex: this.navIndex })
  },
  methods: {
    /**
     * 顶部菜单切换
     */
    clickTopMenu (id) {
      this.topNavIndex = id
      const currLeftMenuList = this.$menuTreeUtil.getMenuList(this.$store.state.app.topAndLeftMenuList, id)
      sessionStorage.setLeftForTopMenuList = JSON.stringify(currLeftMenuList)
      this.$store.commit('app/setLeftForTopMenuList', currLeftMenuList) // 纵向菜单
      this.$store.commit('app/setNavMemory', {
        topNavIndex: this.topNavIndex
      }) // 存储当前top菜单信息
    },
    // 检测cookie值：bootstrap页面登陆超时，后端负责清除Cookies信息
    checkCookies () {
      const cookieData = Cookies.get('LMSG')
      if (!cookieData) {
        const microList = sessionStorage.microAppList ? JSON.parse(sessionStorage.microAppList) : []
        let microAppPre = pageConfig.loginPage.microAppPre
        microList.forEach(microItem => {
          if (microItem.name === pageConfig.loginPage.microAppName) {
            microAppPre = microItem.routeName
          }
        })
        sessionStorage.clear()
        const path = microAppPre + '#' + pageConfig.loginPage.routerPath
        const title = pageConfig.loginPage.titleName
        window.history.pushState({}, title, path)
        window.location.reload()
      }
    },
    // 刷新页面
    reload () {
      this.$emit('pageReload')
    },
    // @横向菜单切换
    // 导航菜单切换 navMenuTabs
    // id {Number}  当前导航id
    // index {Number}  当前导航索引
    // handleSelect (id, index, url) {
    handleSelect (index, indexPath) {
      const _this = this
      this.doMenuSelect = debounce(() => {
        _this.handleMenuSelect(index, indexPath)
      }, 500)
      this.doMenuSelect()
    },
    /**
     * 触发菜单点击事件
     */
    handleMenuSelect (index, indexPath) {
      // console.log(index, indexPath)
      const menuItem = this.$menu.getMenuByUrl(index)
      if (menuItem && menuItem.showType === 'singleDialog') {
        this.$store.commit('mainApp/setMenuDialogConfig', {
          visible: !this.$store.state.mainApp.menuDialogConfig.visible,
          title: menuItem.text,
          jsonPath: menuItem.url
        })
        return
      }
      // 校验cookie
      this.checkCookies()
      if (index === '' || index === undefined) {
        // 跳到主页
        this.$emit('watchHomeBtn', 0)
      }
      if (menuItem.id === pageConfig.homePage.pageId) {
        // 跳到主页
        this.$emit('watchHomeBtn', 0)
      } else {
        /*
         * @routerUrl 路由地址
         * 当前系统:相对路径其他系统:http://
         * 默认加载菜单第一项
         *
         * */
        this.$store.commit('app/setTabUrl', { path: index })
      }
      // 当前导航索引
      this.navIndex = index
    }
  }
}
</script>