/** * 菜单元素 * TurboC: 重写 */
<template>
  <div>
    <template v-for="item in menuData" class="classifys">
      <el-submenu
        v-if="item.children"
        :popper-append-to-body="true"
        :name="item.text"
        :index="item.id"
        :key="item.id"
        :class="isCollapse ? '' : 'submenu-title-ellipsis'"
      >
        <template slot="title" class="collapse-hidden">
          <!--<div :class="!item.parentId ? 'wb-menu-withicon' : ''">-->
          <span
            v-show="item.id"
            :class="item.id ? item.id : ''"
            class="wb-menu-withicon"
            style="color:#fff;"
          >
            <i :class="item.icon" class="iconfont max-font"></i>
          </span>
          <span :title="
              getMenuSpanTile(item.text)
                ? item.text
                : ''
            " class="hiddeName" style="color:#fff;">
            {{ item.text }}
          </span>
        </template>
        <LeftMenuModel
          :menu-data="item.children"
          :level-num="levelNum + 1"
          :collapse="isCollapse"
        ></LeftMenuModel>
      </el-submenu>
      <el-menu-item
        v-else
        :name="item.text"
        :index="item.url"
        :key="item.id"
        :route="item.url"
        :class="isCollapse ? '' : 'menu-item-title-ellipsis'"
        class="classifys-item"
      >
        <span v-show="item.id" :class="item.id ? item.id : ''">
          <i :class="item.icon" class="iconfont max-font" style="color:#fff;"></i>
        </span>
        <span slot="title">
          <!--<span :title="getMenuSpanTile('.title_' + item.id, item.text)? item.text : ''" :class="'title_' + item.id" class="el-menu-title-span">
                        {{ item.text }}
                    </span>-->
          <span
            :title="getMenuSpanTile(item.text) ? item.text : ''"
            class="el-menu-title-span"
            style="color:#fff;"
          >
            {{ item.text }}
          </span>
        </span>
        <!--<i :class="item.parentId ? 'el-icon-arrow-right' : 'icon-show'" class="el-submenu__icon-arrow"></i>-->
      </el-menu-item>
    </template>
  </div>
</template>

<script>
export default {
  name: 'LeftMenuModel',
  props: {
    menuData: {
      type: Array,
      default: null
    },
    levelNum: {
      type: Number,
      default: 0
    },
    isCollapse: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // 勿动，控制菜单栏悬浮显隐配置项
      levelNums: { 0: 10, 1: 10, 2: 9, 3: 9 }
    }
  },
  computed: {},
  methods: {
    getMenuSpanTile (title) {
      const limitTitleWidth = this.fontSize(this.levelNums[this.levelNum]) || 0
      const titleWidth = this.fontSize(title.length) || 0
      return titleWidth > limitTitleWidth
    },
    /**
     * 计算文字样式宽度
     * @param num     字体个数
     * @param flag    有无排序
     * @returns {*}   返回具体长度
     */
    fontSize (num, flag) {
      let size = num * 14 + 6 + 1
      if (flag === 1) {
        size += 14
      }
      return size + 5
    }
  }
}
</script>

<style scoped></style>
