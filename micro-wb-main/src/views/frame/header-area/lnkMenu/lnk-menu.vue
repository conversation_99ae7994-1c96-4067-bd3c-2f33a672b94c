<!--
 * @Author: yuxuan <EMAIL>
 * @Date: 2022-12-10 15:11:07
 * @LastEditors: yuxuan
 * @LastEditTime: 2023-03-18 18:49:20
 * @Description: 个人中心菜单
-->
<template>
  <div class=""
       style="display:flex">
    <template v-for="lnk in lnkMenu">
      <el-tooltip class="item"
                  :effect="lnk.effect || 'dark'"
                  :content="lnk.text"
                  :key="lnk.id"
                  placement="bottom-start">
        <div style="margin: auto;cursor: pointer"
             @click="handleSelect(lnk)"><i :class="[lnk.iconOnly ? '' : 'iconfont max-font', lnk.icon]"
             style="font-size: 18px;margin-right: 8px;"></i></div>
      </el-tooltip>
      <!-- <i :title="lnk.text" :key="lnk.id" :class="[lnk.iconOnly ? '' : 'iconfont', lnk.icon]" style="font-size: 18px;margin-right: 8px;padding-right: 10px" @click="handleSelect(lnk)"></i> -->
    </template>
  </div>
</template>

<script>
import { mapState } from 'vuex'
const { lnkMenuConfig } = require('./../../../../../../configMenu.json')

export default {
  name: 'lnkMenu',
  components: {},
  props: {
    areaHeightNumber: {
      type: String,
      default: '48px'
    },
    listAll: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  computed: {
    ...mapState({
      lnkMenuList: state => state.menuApp.lnkMenuList ? state.menuApp.lnkMenuList : []
    }),
    lnkMenu () {
      // 过滤配置的个人信息默认菜单
      const { platformConfig = {} } = this.listAll
      let menuData = JSON.parse(JSON.stringify(lnkMenuConfig)) // 获取默认配置菜单
      // 获取个人信息自定义的元数据菜单
      this.lnkMenuList.forEach(item => {
        let index = ''
        if (item.layout && item.layout.index && !isNaN(item.layout.index)) {
          index = Math.round(item.layout.index)
        }
        if (index === '') {
          menuData.push(Object.assign({}, item, { effect: 'dark' }))
        } else {
          menuData.push(Object.assign({}, item, { effect: item.layout.effect || 'dark' }))
        }
      })
      // 对配置信息进行过滤-- 过滤配置的个人信息默认菜单
      menuData = menuData.filter(item => {
        return !(item.hiddenAlias && item.hiddenStatus && platformConfig[item.hiddenAlias] === item.hiddenStatus)
      })
      return menuData
    }
  },
  data () {
    return {}
  },
  watch: {},
  mounted () {
    // console.log('aaaaa')
  },
  created () { },
  methods: {
    getHeaderStyle () {
      // if (this.areaHeightNumber) {
      //   return { height: this.areaHeightNumber, 'line-height': this.areaHeightNumber }
      // }
      return {}
    },
    handleSelect (lnk) {
      // console.log('handleSelect', lnk)
      // 非菜单交互事件
      // this.$emit('handleCommand', key)
      this.$menu.clickMenu(this, lnk.url)
    }
  }
}
</script>