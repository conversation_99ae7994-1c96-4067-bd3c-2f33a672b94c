/** * Created by yuxuan on 2021/09/04. * 导航头部,多系统菜单切换按钮 */
<template>
  <div v-if="nodeMenuList && nodeMenuList.length > 0"
       class="container">
    <i class="el-icon-arrow-left marginBottom0"
       @click="handleArrow('left')"
       :style="{ visibility: tagBoxStyle.showLeft && showSlideBtn ? 'initial': 'hidden' }"></i>
    <div class="slide-box"
         ref="slide">
      <div class="tag-box"
           ref="tag"
           :style="tagBoxStyle">
        <span class="tag"
              :class="{ 'active': item.id === currentId}"
              v-for="item in nodeMenuList"
              :key="item.id"
              :command="item.id"
              @click="handleCommand(item.id)">{{item.text}}</span>
      </div>
    </div>
    <i class="el-icon-arrow-right marginBottom0"
       @click="handleArrow('right')"
       :style="{ visibility: tagBoxStyle.showRight && showSlideBtn ? 'initial': 'hidden' }"></i>
  </div>
</template>

<script>
import { throttle } from '@/utils'
// import Cookies from 'js-cookie'
const { projectConfig } = require('./../../../../../../config.json')
export default {
  name: 'multiSysBtn',
  components: {},
  props: {},
  data () {
    return {
      multiMenuBtnLabel: projectConfig.multiMenuBtnLabel || '切换功能区',
      buttonLoading: false,
      nodeMenuList: this.$store.state.menuApp.otherNodeMenuLit || [],
      nodeMenu: {},
      tagBoxStyle: {
        left: 0,
        showLeft: true,
        showRIght: false
      },
      showSlideBtn: false,
      slideDValWidth: 0
    }
  },
  computed: {
    currentId () {
      return this.$store.state.menuApp.currentRootId
    }
  },
  watch: {
    // 外部事件来触发系统切换事件
    '$store.state.app.multiSystem.systemId' (val) {
      if (val) {
        this.changeCurrentMenu({ id: val, menuUrl: this.$store.state.app.multiSystem.menuUrl })
      }
    },
    '$store.state.menuApp.currentRootId' (val) {
      if (val) {
        this.changeCurrentMenu({ id: val, menuUrl: this.$store.state.app.multiSystem.menuUrl, noRefresh: true })
      }
    }
  },
  mounted () {
    // 初始化信息
    setTimeout(() => {
      this.resizeMenuWidth()
    })
    const onresize = throttle(() => {
      this.resizeMenuWidth()
      // console.log('onresize')
    }, 100)
    window.onresize = onresize
  },
  created () {
    // 初始化信息
    this.initNodeMenu()
  },
  methods: {
    // 切换菜单
    handleCommand (menuId) {
      // 获取数据属性
      let currentMenu = {}
      this.nodeMenuList.forEach(item => {
        if (item.id === menuId) {
          currentMenu = item
        }
      })
      // 更新标题
      this.$emit('updateTitle', currentMenu.text)
      this.changeCurrentMenu(currentMenu)
    },

    /**
     * 初始化当前系统菜单切换按钮
     */
    initNodeMenu () {
      this.nodeMenuList = this.$menuTreeUtil.getOtherNodeMenuList()
      if (this.nodeMenuList && this.nodeMenuList.length === 1) {
        this.nodeMenu = this.nodeMenuList[0]
      }
    },
    /**
     * 切换当前系统及菜单
     * @param id 指定系统id
     * @param menuUrl 切换系统后指定跳转菜单url
     */
    changeCurrentMenu ({ id = undefined, menuUrl = '', noRefresh }) {
      const sysId = id || this.nodeMenu.id
      const allMenuTree = (sessionStorage.allMenuTree && JSON.parse(sessionStorage.allMenuTree)) || []
      const menuTree = this.$menuTreeUtil.getMenuTree(allMenuTree, sysId)
      // 获取当前系统下菜单信息
      // console.log('this.nodeMenuList', menuTree)
      // 切换菜单缓存
      sessionStorage.menuTree = JSON.stringify(menuTree)
      // 当前默认展示菜单（默认是当前已有菜单的第一个）
      const curDefMenu = this.getFirstChild(menuTree)
      // const pathList = { PCC_node_00: '/pcc/#/home', CPCC_node_00: '/cpcc/#/home' }
      // 获取菜单url/或是默认菜单url
      menuUrl = menuUrl || curDefMenu.url
      // 更新系统菜单，并进行菜单跳转【默认跳转至当前拥有菜单的第一个菜单页面】
      this.$menuTreeUtil.updateSysMenu(this, { sysId, menuUrl: noRefresh ? '' : menuUrl })
      this.buttonLoading = false
      // 清除触发菜单跳转事件（menuUrl仅用一次）
      this.$store.commit('app/setMultiSystem', { menuUrl: '' })
      // 更新按钮信息
      this.initNodeMenu()
      // this.goHome()
    },
    goHome () {
      // TODO --- 需要切换初始化地址
      const pathList = { PCC_node_00: '/pcc/#/home', CPCC_node_00: '/cpcc/#/home' }
      // window.history.pushState({}, 'title', this.nodeMenu.id && pathList[this.nodeMenu.id])
      this.$store.commit('app/setTabUrl', { path: pathList[this.nodeMenu.id] })
      const _this = this
      this.$nextTick(() => {
        _this.buttonLoading = false
        // 刷新页面
        window.location.reload()
      })
    },
    /**
     * 获取当前第一个叶子级节点
     * @params {*} menuTree 待处理菜单
     * return * 第一个叶子级节点
     */
    getFirstChild (menuTree = []) {
      let result = null
      if (menuTree[0] && menuTree[0].children) result = this.getFirstChild(menuTree[0].children)
      else if (menuTree[0]) result = menuTree[0]
      return result
    },
    /**
     * 点击滑动箭头
     */
    handleArrow (orient) {
      const step = this.slideDValWidth > 210 ? 210 : this.slideDValWidth < 0 ? 0 : this.slideDValWidth
      // console.log('step:', step)
      let left = parseInt(this.tagBoxStyle.left)
      if (orient === 'left') {
        left -= step
        if (left <= -this.slideDValWidth) {
          left = -this.slideDValWidth
          this.tagBoxStyle.showLeft = false
        }
        this.tagBoxStyle.showRight = true
      } else if (orient === 'right') {
        left += step
        if (left >= 0) {
          this.tagBoxStyle.showRight = false
          left = 0
        }
        this.tagBoxStyle.showLeft = true
      }
      // console.log('left:', left, this.slideDValWidth)
      this.tagBoxStyle.left = left + 'px'
      // sessionStorage.setItem('headerTagBoxStyle', JSON.stringify(this.tagBoxStyle))
    },
    /**
     * 是否显示左右滑动的按钮
     */
    getSlideBtnFlag () {
      if (this.$refs.tag && this.$refs.slide && this.$refs.tag.clientWidth > this.$refs.slide.clientWidth + 10) {
        // console.log('this.$refs.tag.clientWidth:', this.$refs.tag.clientWidth, ';this.$refs.slide.clientWidth:', this.$refs.slide.clientWidth)
        return true
      }
      return false
    },
    /**
     * scroll的差值
     */
    getSlideDValWidth () {
      if (this.$refs.tag && this.$refs.slide && this.$refs.tag.clientWidth > this.$refs.slide.clientWidth) {
        return this.$refs.tag.clientWidth - this.$refs.slide.clientWidth + 10
      }
      return 0
    },
    /**
     * 重新计算宽度，达到左右箭头显隐效果
     */
    resizeMenuWidth () {
      this.showSlideBtn = this.getSlideBtnFlag()
      this.slideDValWidth = this.getSlideDValWidth()
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  display: flex;
  align-items: center;
  .slide-box {
    overflow: hidden;
    width: calc(100% - 32px);
  }
  .tag-box {
    display: flex;
    align-items: center;
    height: 48px;
    left: 0px;
    position: relative;
    transition: all 0.35s;
    width: max-content;
    .tag {
      border-radius: 36px;
      padding: 2px 16px;
      line-height: 24px;
      min-width: max-content;
      margin: 0px 4px;
      &:hover,
      &.active {
        background: #fff;
        color: #5584ff;
      }
    }
  }
  .marginBottom0 {
    margin-bottom: 0;
  }
}
</style>
