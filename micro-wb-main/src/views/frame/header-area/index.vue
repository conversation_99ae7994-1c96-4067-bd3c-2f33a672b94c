/** * Created by TurboC on 2019/08/01. * 导航头部 */
<template>
  <div :style="setDynamicStyle()"
       class="asp-header-area">
    <el-row style="display: flex;flex-direction: row;flex-wrap: nowrap;justify-content: space-between;">
      <!-- <el-row> -->
      <div style="min-width: 337px;"
           :class="multiMenuFlag? 'multi_menu_title' : ''"
           class="asp-header-area-left">
        <img src="../../../../../assets/img/headlogo.png"
             :style="[{display: showLogo ?'inline-block':'none'}]"
             alt="logo" />
        <strong>{{ showTitle }}</strong>
        <i :style="{
            transform: 'rotateZ(' + (isCollapse ? '90' : '0') + 'deg)',
            transition: 'transform 0.15s linear'
          }"
           class="iconfont max-font el-icon-other"
           @click="navCollapse"></i>
      </div>
      <div v-if="multiMenuFlag"
           style="min-height: 48px;"
           :style="headerMenuStyle"
           class="asp-header-area-middle multiSysBtnBox">
        <multiSysBtn @updateTitle="updateTitle"></multiSysBtn>
      </div>
      <div v-if="!multiMenuFlag"
           style="min-height: 48px;"
           :style="headerMenuStyle"
           class="asp-header-area-middle multiSysBtnBox"
           :class="this.navigatorCode === '2' ? 'asp-header-area-middle-menu' : ''">
        <HeaderNavMenu ref="headerNavMenu"
                       @watchHomeBtn="goHome"
                       @pageReload="pageReload"></HeaderNavMenu>
      </div>
      <div class="asp-header-area-right"
           style="float: right;display: flex;justify-content: flex-end;">
        <theme-manage></theme-manage>
        <lnkMenu :areaHeightNumber="getAreaHeightPX()"
                 :listAll="listAllConfig"></lnkMenu>
        <el-link :underline="false"
                 @click="setDate">{{ currentDate }}</el-link>
        <userInfoMenu :areaHeightNumber="getAreaHeightPX()"
                      :userMessage="userMessage"
                      :listAll="listAllConfig"
                      @handleCommand="handleCommand"></userInfoMenu>
        <el-dropdown v-if="false"
                     @command="handleCommand"
                     @visible-change="visibleChange">
          <div class="el-dropdown-link"
               style="display: flex;align-items: center;">
            <i class="iconfont el-icon-mine max-font"></i>
            <span class="avatar"
                  :title="userMessage.name"
                  style="max-width: 115px; word-break: break-all; overflow: hidden; text-overflow: ellipsis;
                         display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1; text-align: left; white-space:normal;">
              {{ userMessage.name }}
            </span>
            <i class="el-icon-arrow-down el-icon--right"></i>
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="personInfo">个人中心</el-dropdown-item>
            <el-dropdown-item command="changePWD">修改密码</el-dropdown-item>
            <el-dropdown-item v-if="receiveStatus"
                              command="receiveInfo">接收信息配置</el-dropdown-item>
            <el-dropdown-item command="singout">退出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </el-row>
    <PersonInfo :dialog-param="personInfoParam"></PersonInfo>
    <ReceiveInfo :dialog-param="receiveInfoParam"></ReceiveInfo>
  </div>
</template>

<script>
import HeaderNavMenu from './headerMenu'
// import HeaderNavMenu from './headerMenu/header-menu'
import lnkMenu from './lnkMenu/lnk-menu'
import userInfoMenu from './userInfoMenu/user-info-menu'
import multiSysBtn from './multiSysBtn/multiSysBtn'
import PersonInfo from '@/views/setting/personInfo'
import ReceiveInfo from '@/views/setting/receiveInfo'
import ThemeManage from '@/views/setting/themeManage'
import { mapState } from 'vuex'
const {
  projectConfig,
  apiConfig,
  pageConfig
} = require('./../../../../../config.json')
const VAR_TRUTH_STR = 1
export default {
  name: 'HeaderArea',
  components: {
    HeaderNavMenu,
    multiSysBtn,
    PersonInfo,
    ReceiveInfo,
    ThemeManage,
    userInfoMenu,
    lnkMenu
  },
  props: {
    isCollapses: {
      type: Boolean,
      default: false
    }
  },
  data () {
    const domain = sessionStorage[projectConfig.operator] ? JSON.parse(sessionStorage[projectConfig.operator]) : {}
    const nowDate = new Date()
    const month = nowDate.getMonth() + 1
    const listAll = sessionStorage.LISTALL ? JSON.parse(sessionStorage.LISTALL) : {}
    return {
      listAllConfig: listAll,
      multiMenuFlag: projectConfig.multiMenu,
      userMessage: {
        name: domain.userInfo.name
      },
      userInfo: domain.userInfo,
      isChangePWD: true,
      personInfoParam: {
        title: '', // 标题名称
        staffModelVisible: false, // 打开修改界面
        status: '', // 操作类型
        id: '' // 用户Id
      },
      receiveInfoParam: {
        modelVisible: false
      },
      isCollapse: this.isCollapses,
      currentDate: nowDate.getFullYear() + '年' + month + '月' + nowDate.getDate() + '日',
      receiveStatus: false,
      showRightIcon: false, // 是否显示右箭头
      maxClickNum: 0, // 最大点击次数
      lastLeft: 0, // 上次滑动距离
      clickNum: 0, // 点击次数
      newShowTitle: ''
    }
  },
  computed: {
    showLogo () {
      let showLogo = false
      // const { domainConfig, platformConfig } = this.listAllConfig
      // if (platformConfig.showLogo) showLogo = platformConfig.showLogo === 'true'
      // if (domainConfig.showLogo) showLogo = domainConfig.showLogo === 'true'
      return showLogo
    },
    // 计算系统标题
    showTitle () {
      const { domainConfig, platformConfig } = this.listAllConfig
      const titleStr = domainConfig.title || platformConfig['portal-name'] // 满足主动切换时的实时更新
      document.title = titleStr
      return titleStr
    },
    // 计算系统标题
    showSysTitle () {
      let titleStr = this.newShowTitle || ''
      // 判断是否有动态标题展示--支持刷新场景
      const nodeMenuConfig = (sessionStorage.nodeMenuConfig && JSON.parse(sessionStorage.nodeMenuConfig)) || {}
      titleStr = nodeMenuConfig.currentNodeId && nodeMenuConfig.currentNodeTitle ? nodeMenuConfig.currentNodeTitle : titleStr
      return titleStr
    },
    headerMenuStyle () {
      return {
        width: '200px'
      }
    },
    // LOGO及title部分宽度
    logoTitleSpan () {
      return this.navigatorCode === '1' ? projectConfig.noMenuSpan.logoTitleSpan : projectConfig.haveMenuSpan.logoTitleSpan
    },
    // 多系统按钮部分宽度
    multiSysBtnSpan () {
      return this.navigatorCode === '1' ? projectConfig.noMenuSpan.multiSysBtnSpan : projectConfig.haveMenuSpan.multiSysBtnSpan
    },
    // 顶部菜单部分宽度
    topMenuSpan () {
      return this.navigatorCode === '1' ? projectConfig.noMenuSpan.topMenuSpan : projectConfig.haveMenuSpan.topMenuSpan
    },
    // 主题图标、个人中心部分宽度
    personalSpan () {
      return this.navigatorCode === '1' ? projectConfig.noMenuSpan.personalSpan : projectConfig.haveMenuSpan.personalSpan
    },
    ...mapState({
      navigatorCode: state => state.app.navigatorCode
    })
  },
  watch: {
    isCollapses (val) {
      this.isCollapse = val
    }
  },
  created () {
    // 预判入口来自单点登录的，如：4A的自登陆
    const compose = sessionStorage[projectConfig.operator] ? JSON.parse(sessionStorage[projectConfig.operator]) : {}
    if (compose && compose.ssoFrom && compose.ssoFrom === '$$project_name$$') {
      // 4A 单点登录, 1:自登陆；2:sso单点登录；3:嵌套登录
      if ((compose.loginType4A === '1' || compose.loginType4A === '2') && this.listAllConfig.domainConfig.systemId) {
        const filterButton = sessionStorage.filter4AButton && JSON.parse(sessionStorage.filter4AButton)
        const unAuthCheck4A = filterButton.unAuthCheck4A || []
        unAuthCheck4A.filter(item => {
          return item.changePWD === VAR_TRUTH_STR
        })
        this.isChangePWD = !(unAuthCheck4A.changePWD === VAR_TRUTH_STR)
      }
    }
  },
  methods: {
    handleSelect (key, keyPath) {
      // console.log('handleSelect', key, keyPath)
      // 非菜单交互事件
      this.handleCommand(key)
    },
    /**
     * 打开系统技术支持弹窗
     */
    handleSystemSupport () {
      this.$store.commit('mainApp/setMenuDialogConfig', {
        visible: true,
        title: '系统技术支持',
        jsonPath: '/comDialogConf/pcc/technicalSupport.json?dialogType=form&width=800px'
      })
    },
    // 更新title
    updateTitle (title) {
      this.newShowTitle = title || ''
    },
    // 点击左箭头（右侧滚动）
    leftClick () {
      // 当点击次数大于0时才触发，这样当点击次数clickNum等于1的到时候，clickNum--等于0.根据计算属性的判断会隐藏掉左箭头
      if (this.clickNum > 0) {
        // 获取当前元素宽度
        const width = document.querySelectorAll('.classifys-item')[this.clickNum - 1].offsetWidth
        // 公示：滚动距离（元素的magin-left值） = 它自己的长度 + 上一次滑动的距离
        document.getElementsByClassName('classifys')[0].style.marginLeft = `${this.lastLeft + width}px`
        this.lastLeft = width + this.lastLeft
        // 点击次数-1
        this.clickNum--
        // 如果点击次数小于最大点击次数，说明最后一个元素已经不在可是区域内了，显示右箭头
        if (this.clickNum < this.maxClickNum) {
          this.showRightIcon = true
        }
      }
    },
    visibleChange (val) {
      if (val) {
        // 是否展示接收信息配置
        const userForbidStatus = this.listAllConfig.platformConfig.userForbidStatus
        this.receiveStatus = userForbidStatus && userForbidStatus !== 'disable'
      }
    },
    setDynamicStyle () {
      return { 'min-height': this.getAreaHeightPX(), 'line-height': this.getAreaHeightPX() }
    },
    getAreaHeightPX () {
      return this.getAreaHeightNumber() + 'px'
    },
    getAreaHeightNumber () {
      return 48
    },
    // 跳到主页
    goHome (pageNum) {
      this.$emit('watchHomeBtn', pageNum)
    },
    // 页面刷新
    pageReload () {
      this.$emit('pageReload')
    },
    // 用户切换路由指向
    // handleCommand {String} switch 指向跳转位置
    handleCommand (command) {
      switch (command) {
        case 'singout':
          this.loginOut()
          break
        case 'personInfo':
          this.personInfoParam = {
            title: '修改个人信息',
            status: 'personInfo',
            staffModelVisible: true,
            id: this.userInfo.id
          }
          break
        case 'changePWD':
          this.personInfoParam = {
            title: '修改密码',
            status: 'changePWD',
            staffModelVisible: true,
            id: this.userInfo.id
          }
          break
        case 'receiveInfo':
          this.receiveInfoParam = {
            modelVisible: true
          }
          break
      }
    },
    // 退出登录
    loginOut () {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0)'
      })
      const _this = this
      let path = pageConfig.loginPage.microAppPre + '#' + pageConfig.loginPage.routerPath
      /** 开启自定义登出，就会走logOut重定向自定义页面 */
      if (projectConfig.customLogout && process.env.NODE_ENV !== 'development') {
        path = `${apiConfig.authPathPrefix}/login/logout`
        path = this.$aspHttps.getProcessEnv(path)
        logOut(path)
        return
      }
      this.$aspHttps.asp_Post(apiConfig.authPathPrefix + '/login/logout').then(response => {
        // status状态为0的，为接口重定向的特殊状态
        if (_this.$aspHttps.reponseStatus(response)) {
          path = pageConfig.loginPage.microAppPre + '#' + pageConfig.loginPage.routerPath
        }
      }).finally(() => {
        logOut(path)
      })
      function logOut (path) {
        _this.$store.commit('app/setIFrameUrl', '') // 退出登录将iframe框架的url置空
        _this.$store.commit('app/setLeftForTopMenuList', []) // 退出登录将左侧菜单置空
        sessionStorage.clear()
        loading.close()
        window.history.pushState({}, 'title', path)
        window.location.reload()
      }
    },
    // 日历页面
    setDate () {
      const microList = sessionStorage.microAppList ? JSON.parse(sessionStorage.microAppList) : []
      let microAppPre = pageConfig.datePanel.microAppPre
      microList.forEach(microItem => {
        if (microItem.name === pageConfig.datePanel.microAppName) {
          microAppPre = microItem.routeName
        }
      })
      const path = microAppPre + '#' + pageConfig.datePanel.routerPath
      const name = pageConfig.datePanel.routerName
      const title = pageConfig.datePanel.titleName
      window.history.pushState({}, title, path)
      this.$store.commit('breadcrumbApp/setCurrentPath', [{ title: title }])
      this.menuHistory = name + Math.random()
      this.$store.commit('app/setNavMemory', {
        router: {
          path: path
        }
      })
    },
    // 菜单栏文字 icon 切换
    navCollapse () {
      this.isCollapse = !this.isCollapse
      this.$emit('watchCollapse', this.isCollapse)
    }
  }
}
</script>
<style lang="scss" scoped>
.multiSysBtnBox {
  overflow: hidden;
  flex: 1;
  padding: 0 12px;
  margin-right: auto;
  max-width: max-content;
}
</style>
