<!--
 * @Author: yuxuan <EMAIL>
 * @Date: 2022-12-10 15:11:07
 * @LastEditors: yuxuan
 * @LastEditTime: 2023-03-18 18:49:33
 * @Description: 个人中心菜单
-->
<template>
  <div class="asp-user-info-menu"
       :style="getHeaderStyle()">
    <!--  虽然会报错，但是可以配置悬浮和点击事件 :menu-trigger="['click','hover']" -->
    <el-menu ref="elMenu"
             class="el-menu-vertical"
             mode="horizontal"
             @select="handleSelect">
      <el-submenu :popper-append-to-body="true"
                  index="userInfo"
                  popper-class="asp-user-info-submenu">
        <template slot="title">
          <div class="el-dropdown-link"
               style="display: flex;align-items: center;">
            <i class="iconfont el-icon-mine max-font"></i>
            <span class="avatar"
                  :title="userMessage.name"
                  style="max-width: 115px; word-break: break-all; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1; text-align: left; white-space:normal;">
              {{ userMessage.name }}
            </span>
            <i class="el-icon-arrow-down el-icon--right"></i>
          </div>
        </template>
        <userInfoMenuModel :menuData="userInfoMenu"
                           :menuRef="this.$refs.elMenu"
                           pIndex="userInfo"></userInfoMenuModel>
        <div x-arrow=""
             class="popper__arrow"
             style="left: 59px;"></div>
      </el-submenu>
    </el-menu>
  </div>
</template>

<script>
import userInfoMenuModel from './user-info-menu-model.vue'
import { mapState } from 'vuex'
const { userInfoConfig } = require('./../../../../../../configMenu.json')
// import userInfoMenuSub from './user-info-menu-sub.vue'

export default {
  name: 'userInfoMenu',
  components: {
    userInfoMenuModel
  },
  props: {
    userMessage: {
      type: Object,
      default () {
        return {}
      }
    },
    areaHeightNumber: {
      type: String,
      default: '48px'
    },
    listAll: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  computed: {
    ...mapState({
      userInfoMenuList: state =>
        state.menuApp.userInfoMenuList ? state.menuApp.userInfoMenuList : []
    }),
    userInfoMenu () {
      // 过滤配置的个人信息默认菜单
      const { platformConfig = {} } = this.listAll
      let menuData = JSON.parse(JSON.stringify(userInfoConfig)) // 获取默认配置菜单
      // 获取个人信息自定义的元数据菜单
      this.userInfoMenuList.forEach(item => {
        let index = ''
        if (item.layout && item.layout.index && !isNaN(item.layout.index)) {
          index = Math.round(item.layout.index)
        }
        if (index === '') {
          menuData.push(item)
        } else {
          menuData.splice(index, 0, item)
        }
      })
      // 对配置信息进行过滤-- 过滤配置的个人信息默认菜单
      menuData = menuData.filter(item => {
        return !(item.hiddenAlias && item.hiddenStatus && platformConfig[item.hiddenAlias] === item.hiddenStatus)
      })
      return menuData
    }
  },
  data () {
    return {
      activeIndex: 'userInfo'
    }
  },
  watch: {},
  mounted () { },
  created () { },
  methods: {
    handleMouseleave (data) {
      // console.log('handleMouseleave0', data)
    },
    getHeaderStyle () {
      if (this.areaHeightNumber) {
        return { height: this.areaHeightNumber, 'line-height': this.areaHeightNumber }
      }
      return {}
    },
    handleSelect (key, keyPath) {
      // console.log('handleSelect', key, keyPath)
      // 非菜单交互事件
      this.$emit('handleCommand', key)
      this.$menu.clickMenu(this, key)
    }
  }
}
</script>