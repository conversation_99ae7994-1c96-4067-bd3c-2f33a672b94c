<!--
 * @Author: yuxuan <EMAIL>
 * @Date: 2022-12-10 15:11:07
 * @LastEditors: yuxuan
 * @LastEditTime: 2022-12-15 11:06:30
 * @Description: 个人中心菜单
-->
<template>
  <div @mouseenter="onMouseEnter"
       @mouseleave="onMouseLeave">
    <template v-for="item in menuData">
      <el-submenu v-if="item.children"
                  :popper-append-to-body="true"
                  :name="item.text"
                  :index="item.id"
                  :key="item.id"
                  popper-class="asp-user-info-submenu">
        <template slot="title">
          <span :title="getMenuSpanTile(item.text) ? item.text : ''"
                style="max-width: 100px; word-break: break-all; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1; text-align: left; white-space:normal;">
            {{ item.text }}
          </span>
        </template>
        <userInfoMenuModel :menu-data="item.children"
                           :menuRef="menuRef"
                           :pIndex="item.id"
                           :level-num="levelNum + 1"
                           :key="'parent_'+item.id"
                           @leaveParent="onMouseLeave"
                           @childClose="childClose"></userInfoMenuModel>
      </el-submenu>
      <el-menu-item v-else
                    :name="item.text"
                    :index="item.id"
                    :key="item.id">
        <span slot="title">
          <span :title="getMenuSpanTile(item.text) ? item.text : ''">
            {{ item.text }}
          </span>
        </span>
      </el-menu-item>
    </template>
  </div>
</template>

<script>
export default {
  name: 'userInfoMenuModel',
  components: {},
  props: {
    menuRef: {
      type: Object,
      default: null
    },
    pIndex: {
      type: String,
      default: ''
    },
    menuData: {
      type: Array,
      default: null
    },
    levelNum: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      // 勿动，控制菜单栏悬浮显隐配置项
      levelNums: { 0: 10, 1: 10, 2: 9, 3: 9 },
      actived: false
    }
  },
  computed: {
    opened () {
      return this.$parent.opened
    }
  },
  watch: {
    opened (val) {
      if (!val) {
        // 判断下父级菜单状态是否存在is_opened属性，若无则判定
        this.$emit('childClose', this.pIndex)
      }
    }
  },
  mounted () { },
  created () { },
  methods: {
    onMouseEnter () {
      this.actived = true
      // 触发父节点的onMouseLeave
      this.$emit('leaveParent')
    },
    onMouseLeave () {
      this.actived = false
    },
    /**
     * @description: 子菜单收起关闭父级菜单
     * @param {*} index 父级菜单ID
     * @return {*}
     * @author: yuxuan <EMAIL>
     */
    childClose (index) {
      if (index && !this.actived && this.menuRef) {
        // 关闭当前菜单
        this.menuRef.close(this.pIndex)
      }
    },
    getMenuSpanTile (title) {
      const limitTitleWidth = this.fontSize(7) || 0
      const titleWidth = this.fontSize(title.length) || 0
      return titleWidth > limitTitleWidth
    },
    /**
     * 计算文字样式宽度
     * @param num     字体个数
     * @param flag    有无排序
     * @returns {*}   返回具体长度
     */
    fontSize (num, flag) {
      let size = num * 14 + 6 + 1
      if (flag === 1) {
        size += 14
      }
      return size + 5
    }
  }
}
</script>