/** * 面包削导航 */
<template>
  <el-breadcrumb separator-class="el-icon-arrow-right">
    <el-breadcrumb-item
      v-for="(item, index) in breadcrumbType"
      :key="index"
      :to="item.path"
      >{{ item.title }}</el-breadcrumb-item
    >
  </el-breadcrumb>
</template>

<script>
export default {
  name: 'Breadcrumb',
  props: {
    currentPath: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      // 面包屑
      breadcrumbType:
        sessionStorage.currentPath && JSON.parse(sessionStorage.currentPath)
    }
  },
  watch: {
    // $route(to, from) {
    // },
    // 监听 currentPath值的变化
    currentPath(val) {
      if (this.$util.isEmptyArray(val)) {
        val =
          sessionStorage.currentPath && JSON.parse(sessionStorage.currentPath)
      }
      this.breadcrumbType = []
      this.$nextTick(() => {
        this.breadcrumbType = val
      })
    },
    '$router' (to, from) {
      // console.log('micro-wb-main breadcrumb route to', to)
      // console.log('micro-wb-main breadcrumb route from', from)
    }
  }
}
</script>
