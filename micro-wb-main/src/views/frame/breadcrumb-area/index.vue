/** * Created by TurboC on 2019/08/01. * 面包导航条 */
<template>
  <div v-if="getAreaHeightNumber() > 0"
       :style="setDynamicStyle()"
       class="asp-breadcrumb-area">
    <div class="position">
      <span>
        <i :title="currentPagename"
           :class="{ active: isCurrentPageHome }"
           class="iconfont el-icon-homepage"
           @click="goHome"></i>
      </span>
      <template v-if="currentPagename !== ''">
        <breadcrumb :current-path="currentPath"></breadcrumb>
      </template>
    </div>
    <template>
      <menu-info :current-pagename="currentPagename"></menu-info>
    </template>
    <!-- {{$breadcrumbReturnBtn}}
    <asp-btn-hollow v-show="isShowReturn"
                    icon="el-icon-close"
                    name="取消"
                    @click="backPreView">
    </asp-btn-hollow> -->
  </div>
</template>

<script>
import breadcrumb from './breadcrumb.vue'
import menuInfo from './menu-info.vue'
import { projectConfig } from '../../../../../config.json'
// import actions from '@/micro/actions'
export default {
  name: 'BreadcrumbArea',
  components: {
    breadcrumb,
    menuInfo
  },
  props: {
    currentPagename: {
      type: String,
      default: ''
    },
    currentPath: {
      type: Array,
      default () {
        return []
      }
    },
    iFrameurl: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      breadcrumbType: sessionStorage.currentPath && JSON.parse(sessionStorage.currentPath)
    }
  },
  watch: {
    // isShowReturn: {
    //   handler (val, oldVal) {
    //     console.log('5555555555555555')
    //   },
    //   immediate: true,
    //   deep: true
    // },
    // $breadcrumbReturnBtn: {
    //   handler (val, oldVal) {
    //     console.log('6666666666666666666')
    //   },
    //   immediate: true,
    //   deep: true
    // }
  },
  mounted () {
    // actions.onGlobalStateChange && actions.onGlobalStateChange((state, prevState) => {
    //   this.isShowReturn = state.breadcrumbReturnBtn
    // })
  },
  computed: {
    isCurrentPageHome () {
      return this.currentPath.length > 0 && this.currentPath[0].title === this.$pageConfig.homePage.titleName
    }
    // isShowReturn () {
    //   console.log('2222222222222222222222222')
    //   return this.$breadcrumbReturnBtn
    // }
  },
  methods: {
    setDynamicStyle () {
      return { height: `${this.getAreaHeightNumber()}px` }
    },
    getAreaHeight () {
      return '36px'
    },
    getAreaHeightNumber () {
      return projectConfig.breadcrumbHeight
    },
    goHome () {
      this.$emit('watchHomeBtn', 0)
    }
    // backPreView () {
    //   window.history.back()
    // }
  }
}
</script>
