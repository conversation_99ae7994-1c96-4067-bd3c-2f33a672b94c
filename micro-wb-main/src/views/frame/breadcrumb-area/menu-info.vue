<!--
 * @Author: <PERSON>
 * @Date: 2022-05-10 11:06:10
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-10-12 10:36:21
 * @Description: 菜单帮助信息下载按钮，有则显示
 * @Version: V1.0
 * @FilePath: /webbas/vue-ui/micro-wb-main/src/views/frame/breadcrumb-area/menu-info.vue
-->
<template>
  <el-link @click="handleUpload(attachFileId)"
           class="download"
           :underline="false"
           v-if="showMenuInfo">{{ title }}<i class="el-icon-download"></i></el-link>
</template>

<script>
import aspHttps from '../../../utils/http'
export default {
  name: 'MenuInfo',
  props: {
    currentPagename: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      fileData: [], // 有附件菜单id集合
      fileInfo: [], // 当前附件信息
      title: '帮助信息', // 附件名称
      attachFileId: '' // 菜单附件下载地址
    }
  },
  watch: {},
  computed: {
    /**
     * @description: 是否显示菜单帮助信息，当菜单附件列表更新或附件组id在列表中时更新状态
     * @param {*}
     * @return {*}
     */
    showMenuInfo () {
      const menuHelpInfoList = this.$store.state.app.menuHelpInfoList // 菜单附件列表
      const fileData = menuHelpInfoList.map(item => item.resourceId) // 有附件菜单id数组
      const menuId = this.$menu.getMenuByUrl(this.currentPagename).id // 通过路由获取菜单id
      if (fileData.indexOf(menuId) > -1) {
        const attachGroupId = menuHelpInfoList[fileData.indexOf(menuId)].attachGroupId // id
        this.$nextTick(() => {
          this.getMenuHelpInfo(attachGroupId)
        })
        return true
      }
      return false
    }
  },
  mounted () {
    // 初次进入，获取 菜单附件信息全量列表 和 页面帮助信息全量列表，并存入缓存
    this.$nextTick(() => {
      // 菜单帮助信息列表
      this.$menu.getAllMenuHelpInfoList(this)
      // 页面帮助信息列表
      this.$menu.getAllPageHelpInfoList(this)
    })
  },
  methods: {
    /**
     * @description: 菜单列表有数据时获取菜单附件信息（只有一个附件）
     * @param id 附件id
     * @return {*}
     */
    getMenuHelpInfo (id) {
      const params = {
        attachGroupId: id
      }
      this.$aspHttps.asp_Post(this.$apiConfig.supportPathPrefix + '/attachment/list', params).then(response => {
        if (this.$reponseStatus(response) && response.data?.length > 0) {
          this.fileInfo = response.data
          this.attachFileId = this.fileInfo[0].attachFileId
        }
      })
    },
    /**
     * @description: 下载文件弹窗
     * @param {*} attachFileId 附件id
     * @return {*}
     */
    handleUpload (attachFileId) {
      const url = aspHttps.aspProductAttUrl(this.$apiConfig.supportPathPrefix + '/attachment/downloadFile?attachFileId=') + attachFileId
      // 新开窗口下载
      this.$aspHttps.asp_ExportGetOpen(url)
    }
  }
}
</script>
