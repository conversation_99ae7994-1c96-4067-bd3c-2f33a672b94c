/** 面包屑工具 */
// import menu from '../../../utils/menu'

const breadcrumbUtils = {}
/**
 * 获取面包屑title
 * @param {*} vm 当前this指向
 * @param {*} name 当前path地址（必须与菜单url一直）
 * @returns name 返回识别到的菜单url
 */
breadcrumbUtils.setCurrentPathBreadcrumb = (vm, name) => {
  const addRouterList = vm.$store.state.app.addRouter.list
  addRouterList.forEach((item, index) => {
    if (JSON.stringify(item).indexOf(name) > -1) {
      if (
        addRouterList[index].children &&
        addRouterList[index].children.length > 0
      ) {
        addRouterList[index].children.forEach(checkSource => {
          if (checkSource.path.toString() === name) {
            vm.$store.commit(
              'breadcrumbApp/setCurrentPath',
              checkSource.title.split(',').map(item => {
                return { title: item, path: '', name: checkSource.path }
              })
            )
            name = checkSource.path.toString()
          }
        })
      } else {
        if (addRouterList[index].path.toString() === name) {
          vm.$store.commit(
            'breadcrumbApp/setCurrentPath',
            addRouterList[index].title.split(',').map(item => {
              return {
                title: item,
                path: '',
                name: addRouterList[index].path
              }
            })
          )
          name = addRouterList[index].path.toString()
        }
      }
    }
  })
  return name
}

export default breadcrumbUtils