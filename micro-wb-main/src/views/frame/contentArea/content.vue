/** * Created by TurboC on 2019/08/01. * 导航头部 */
<template>
  <div>
    <div :style="systemWindow" class="frame-main-content">
      <template v-if="iFrameurl === ''">
        <router-view v-if="menuRoutertab" />
      </template>
      <template v-else>
        <iframe
          :src="iFrameurl"
          frameborder="0"
          width="100%"
          height="100%"
        ></iframe>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Content',
  props: {
    menuRoutertab: {
      type: Boolean,
      default: false
    },
    iFrameurl: {
      type: String,
      default: ''
    },
    visibleHeight: {
      type: Number,
      default: 0.0
    }
  },
  data() {
    return {}
  },
  computed: {},
  methods: {
    // 样式切换
    systemWindow() {
      const tsu = this.iFrameUrl
      return {
        padding: tsu === '' ? '12px' : '0',
        overflow: tsu === '' ? 'auto' : 'hidden',
        height: this.visibleHeight
      }
    }
  }
}
</script>
