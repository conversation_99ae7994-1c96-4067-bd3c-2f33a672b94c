/** * letMenu 主页左侧菜单栏 */
<template>
  <div :style="setDynamicStyle()" class="asp-left-area">
    <left-menu
      :menu-list="menuList"
      :menu-history="menuHistory"
      :is-collapse="isCollapse"
      @reload="reload"
    ></left-menu>
  </div>
</template>

<script>
import LeftMenu from './left-menu'
export default {
  name: 'LeftArea',
  components: {
    LeftMenu
  },
  props: {
    menuList: {
      type: Array,
      default () {
        return []
      }
    },
    menuHistory: {
      type: String,
      default: ''
    },
    isCollapse: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {}
  },
  created() { },
  methods: {
    setDynamicStyle () {
      return { width: this.isCollapse ? '64px' : '220px' }
    },
    getAreaWidth () {
      return this.isCollapse ? '64px' : '220px'
    },
    // 菜单刷新路由
    // reload(payload) {
    reload () {
      this.$emit('watchMenuReload', 0)
    }
  }
}
</script>

<style scoped lang="scss">
</style>
