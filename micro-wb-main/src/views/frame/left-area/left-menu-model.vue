/** * 菜单元素 * TurboC: 重写 */
<template>
  <div>
    <template v-for="item in menuData">
      <el-submenu v-if="item.children"
                  :popper-append-to-body="true"
                  :name="item.text"
                  :index="item.id"
                  :key="item.id"
                  :class="computeSubmenuClass(item)">
        <template slot="title"
                  class="collapse-hidden">
          <!--<div :class="!item.parentId ? 'wb-menu-withicon' : ''">-->
          <div v-show="isNodeMenu(item)"
               :class="item.id && navigatorCode !== '2' ? item.id : ''"
               class="wb-menu-withicon">
            <i :class="item.icon"
               class="iconfont max-font"></i>
          </div>
          <div class=" webbas-badge-count">
            <span :title="
                getMenuSpanTile(item.text)
                  ? item.text
                  : ''
              "
                  class="hiddeName el-menu-title-span">
              {{ item.text }}
            </span>
            <b v-if="item.hasExtend === '1'"
               :class="[getMenuSpanTile(item.text) ? 'webbas-ellipsis-badge-span' : 'webbas-badge-span']"
               :style="getCurrentStyle">
              {{getDealtNum(item)}}</b>
          </div>
        </template>
        <LeftMenuModel :menu-data="item.children"
                       :level-num="levelNum + 1"
                       :collapse="isCollapse"></LeftMenuModel>
      </el-submenu>
      <el-menu-item v-else
                    :name="item.text"
                    :index="item.url"
                    :key="item.id"
                    :route="item.url"
                    :style="leafMenuStyle(item)"
                    :class="computeMenuItemClass(item)">
        <div v-show="isNodeMenu(item)"
             :class="item.id && navigatorCode !== '2' ? item.id : ''">
          <i :class="item.icon"
             class="iconfont max-font"></i>
        </div>
        <template slot="title">
          <div class="webbas-badge-count">
            <span :title="getMenuSpanTile(item.text) ? item.text : ''"
                  class="el-menu-title-span">{{ item.text }}</span>
            <b v-if="item.hasExtend === '1'"
               :class="[getMenuSpanTile(item.text) ? 'webbas-ellipsis-badge-span' : 'webbas-badge-span']"
               :style="getCurrentStyle">
              {{getDealtNum(item)}}</b>
          </div>
        </template>
        <!--<i :class="item.parentId ? 'el-icon-arrow-right' : 'icon-show'" class="el-submenu__icon-arrow"></i>-->
      </el-menu-item>
    </template>
  </div>
</template>

<script>
export default {
  name: 'LeftMenuModel',
  props: {
    menuData: {
      type: Array,
      default: null
    },
    levelNum: {
      type: Number,
      default: 0
    },
    isCollapse: {
      type: Boolean,
      default: false
    },
    navigatorCode: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      // 勿动，控制菜单栏悬浮显隐配置项
      levelNums: { 0: 10, 1: 10, 2: 9, 3: 9 }
    }
  },
  computed: {
    getCurrentStyle () {
      const themeConfig = this.$store.state.menuApp.themeConfig
      if (themeConfig && themeConfig.navigatorCode === '2' && themeConfig.curSkinCode === '#ED6E4D') {
        return { background: '#3498DB' }
      }
      return {}
    },
    isNodeMenu () {
      return function (item) {
        // console.log(item.id, item.parentId, item.text, item.level, item)
        // 根节点(根菜单)
        if (!item.parentId && item.id) return true
        if (item.level === '0') return true
        return false
      }
    },
    leafMenuStyle () {
      return function (item) {
        return {}
      }
    },
    getDealtNum () {
      return function (item) {
        let num = '0'
        const menuDealtNums = this.$store.state.menuApp.menuDealtNum || []
        if (item.id && item.hasExtend) {
          // 获取当前菜单num数据,num数据是数字
          menuDealtNums.forEach(ite => {
            if (ite.resourceId === item.id && !Number.isNaN(parseInt(ite.busNum))) num = ite.busNum
          })
          num = parseInt(num) > 99 ? '99+' : num
        }
        return num
      }
    }
  },
  methods: {
    computeMenuItemClass (item) {
      let className = ''
      if (!this.isCollapse) {
        className = className + 'menu-item-title-ellipsis'
      }
      if (this.isNodeMenu(item)) {
        className = className + ' classifys-item'
      }
      return className
    },
    computeSubmenuClass (item) {
      let className = ''
      if (!this.isCollapse) {
        className = className + 'submenu-title-ellipsis'
      }
      if (this.isNodeMenu(item)) {
        className = className + ' classifys-item'
      }
      return className
    },
    getMenuSpanTile (title) {
      const limitTitleWidth = this.fontSize(this.levelNums[this.levelNum]) || 0
      const titleWidth = this.fontSize(title.length) || 0
      return titleWidth > limitTitleWidth
    },
    /**
     * 计算文字样式宽度
     * @param num     字体个数
     * @param flag    有无排序
     * @returns {*}   返回具体长度
     */
    fontSize (num, flag) {
      let size = num * 14 + 6 + 1
      if (flag === 1) {
        size += 14
      }
      return size + 5
    }
  }
}
</script>

<style scoped lang="scss">
.webbas-badge-count {
  max-width: 150px;
  display: flex;
  .el-menu-title-span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .webbas-ellipsis-badge-span,
  .webbas-badge-span {
    background: #f56c6c;
    color: #fff;
    padding: 0 4px;
    border-radius: 10px;
    height: 18px;
    width: 18px;
    font-size: 12px;
    text-align: center;
    line-height: 16px;
    transform: translate(-6px, 2px);
  }
  .webbas-ellipsis-badge-span {
    transform: translate(-18px, 2px);
  }
  // .webbas-badge-background {
  //   background: #3498DB;
  // }
}
.el-tooltip__popper {
  .webbas-badge-span {
    transform: translate(0px, -9px);
  }
}
// .el-menu-title-span {
//     white-space: nowrap;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     word-break: break-all;
//     display: -webkit-box;
//     -webkit-box-orient: vertical;
//     -webkit-line-clamp: 1;
// }
</style>
