/** * sidebarMenu 主页导航侧栏 * TurboC: 重写 */
<template>
  <el-menu v-if="navigatorCode === '1' ||navigatorCode === '3'"
           :default-openeds="openeds"
           :default-active="active"
           :collapse="isCollapse"
           class="el-menu-vertical"
           @select="handleSelect">
    <left-menu-model :menu-data="menuList"
                     :is-collapse="isCollapse" />
  </el-menu>
  <el-menu v-else-if="navigatorCode === '2'"
           :default-active="active"
           mode="horizontal"
           @select="handleSelect">
    <left-menu-model :menu-data="menuList"
                     :is-collapse="isCollapse"
                     :navigator-code="navigatorCode" />
  </el-menu>
</template>

<script>
import LeftMenuModel from './left-menu-model'
import { mapState } from 'vuex'
import actions from '@/micro/actions'
import { debounce } from '@/utils/index.js'
import util from '@/utils/util'

const {
  projectConfig,
  // apiConfig,
  pageConfig
} = require('./../../../../../config.json')
export default {
  name: 'LeftMenu',
  components: {
    LeftMenuModel
  },
  props: {
    menuList: {
      type: Array,
      default () {
        return []
      }
    },
    menuHistory: {
      type: String,
      default: ''
    },
    isCollapse: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      openeds: [],
      tabRouteUrl: this.menuHistory,
      active: ''
    }
  },
  computed: {
    ...mapState({
      listenActive: state =>
        state.app.navMemory.router.path === pageConfig.iframePage.routerPath
          ? state.app.navMemory.frameTagUrl
          : state.app.navMemory.router.path,
      navigatorCode: state => state.app.navigatorCode
    })
  },
  watch: {
    menuHistory (val) {
      this.tabRouteUrl = val
      this.menuListInit()
    },
    '$store.state.app.tabUrl' (val) {
      this.handleSelect(val.path, ['', val.path])
      this.active = val.path
    },
    '$store.state.app.showRouterUrl.gotoUrl' (val) {
      if (val) {
        const topMenuList = sessionStorage.topMenuList
          ? JSON.parse(sessionStorage.topMenuList)
          : []
        topMenuList.forEach(item => {
          if (item.url === val) {
            this.openeds = []
            this.active = val
          }
        })
      }
    },
    '$store.state.app.tabSwitch.openSideMenu.menuId' (val) {
      if (val) {
        // console.log('$store.state.app.tabSwitch.openSideMenu.menuId', val)
        this.openeds = []
        this.openeds = this.$store.state.app.tabSwitch.openSideMenu.openIds
        this.menuListInit()
      } else {
        this.openeds = []
      }
    },
    '$store.state.app.currentPageName' (val) {
      if (val) {
        // console.log('$store.state.app.currentPageName', val)
        // 当地址为framePage时需取url参数进行判断
        const frameTagUrl = this.$menu.currentTagPath(val)
        const toMenu = this.toMenu(frameTagUrl)
        this.tabRouteUrl = frameTagUrl
        this.$menu.openNewPage(this, frameTagUrl)
        if (toMenu) {
          this.menuListInit()
        }
      }
    }
  },
  created () {
    // console.log('left-menu--create')
    this.menuListInit()
    window.addEventListener('setItemEventRouterName', this.setNemu)
  },
  destroyed() {
    window.removeEventListener('setItemEventRouterName', this.setNemu)
  },
  methods: {
    setNemu(e) {
      if (e.key === 'lylRouterCurrentName') {
        setTimeout(() => {
          this.menuListInit()
        })
      }
    },
    // @openeds {Array} 默认展开的项配置
    // @active {String} 默认页面
    // @openNewPage 的记录打开的路由，面包削设置
    menuListInit () {
      if (this.tabRouteUrl === '') {
        const currentPath = sessionStorage.currentPath ? JSON.parse(sessionStorage.currentPath) : []
        this.tabRouteUrl = currentPath[currentPath.length - 1] && currentPath[currentPath.length - 1].name
      }
      const url = () => {
        return typeof this.menuList[0].children !== 'undefined'
          ? this.menuList[0].children[0].url
          : this.menuList[0].url
      }
      const openedss =
        this.tabRouteUrl === ''
          ? [this.menuList.length > 0 && url()]
          : [this.tabRouteUrl]
      this.active = openedss.join('').split('?')[0]
    },
    // 改变导航状态
    tabNavMemory (index, tagUrl = '', topNavIndex = '') {
      let newTopNavIndex = topNavIndex
      if (topNavIndex === '') {
        newTopNavIndex = sessionStorage[projectConfig.projectNavMemoryKey] && JSON.parse(sessionStorage[projectConfig.projectNavMemoryKey]).topNavIndex
      }
      return this.$store.commit('app/setNavMemory', {
        router: { path: index },
        menuPath: index,
        child: {},
        frameTagUrl: tagUrl
        // topNavIndex: newTopNavIndex
      })
    },
    /**
     *  触发菜单点击事件【加入防抖节流】
     */
    handleSelect (index, indexPath) {
      const _this = this
      this.doMenuSelect = debounce(() => {
        _this.handleMenuSelect(index, indexPath)
      }, 500)
      this.doMenuSelect()
    },
    /**
     * 触发菜单点击事件
     */
    handleMenuSelect (index, indexPath) {
      if (index === undefined || index === null) return
      // 菜单触发弹窗事件
      const item = this.$menu.getMenuByUrl(index)
      // const menuItem = this.$menu.getMenuByUrl(index)
      if (item && item.showType === 'singleDialog') {
        this.$store.commit('mainApp/setMenuDialogConfig', {
          visible: !this.$store.state.mainApp.menuDialogConfig.visible,
          title: item.text,
          jsonPath: item.url
        })
        return
      }
      // 清空keep-alive
      actions.setGlobalState({
        msg_type: 'clearKeepAlive'
      })
      // 直接使用main的store会导致子应用路由缓存失效
      // this.$store.commit('keepAlive/clearIncludes', [])
      // 菜单点击事件
      actions.setGlobalState({
        msg_type: 'menuClick',
        param: { index, indexPath }
      })
      // 清空上次访问记录
      this.tabNavMemory('')
      // 记录当前跳转url地址
      this.$store.commit('app/setShowRouterUrl', { gotoUrl: index })
      // 点击菜单刷新页面
      this.$emit('reload')
      // 预先清理frame上加载地址信息
      this.$store.commit('app/setIFrameUrl', '')
      // 记录当前导航信息
      this.tabNavMemory(index, '', indexPath[0])

      // 本地路由跳转
      let path = index
      let title = 'title'
      // TODO--PCC项目个性化
      if ((item && item.showType === 'iframepage') || index.indexOf('/xxl/') !== -1 || index.indexOf('/ui-manage/') !== -1) {
        // 更新当前导航信息
        // this.tabNavMemory(index)
        // 个性化切换url--为当前实际访问url
        index = this.$menu.framePROTagIndex(index)
        actions.setGlobalState({
          msg_type: 'contentH',
          contentH: sessionStorage.main_content_height ? sessionStorage.main_content_height : '0px'
        })
        const microList = sessionStorage.microAppList ? JSON.parse(sessionStorage.microAppList) : []
        let microAppPre = pageConfig.iframePage.microAppPre
        microList.forEach(microItem => {
          if (microItem.name === pageConfig.iframePage.microAppName) {
            microAppPre = microItem.routeName
          }
        })
        path = microAppPre + '#' + pageConfig.iframePage.routerPath + '?' + 'iframe=' + index
        title = pageConfig.iframePage.titleName
      } else if (item && item.showType === 'singlePage') {
        // TODO--PCC项目个性化。增加菜单跳转--支持单页面跳转（即：无菜单页面）
        // window.location.href = 'http://10.1.203.51:8899' + index
        window.location.href = index
        return
      }
      // 获取当前实际访问的url
      path = this.$menu.currentRouterPrefix(path)
      path = path.indexOf('?') > 0 ? path + '&_t=' + new Date().getTime() : path + '?_t=' + new Date().getTime()
      // 切换页面
      window.history.pushState({}, title, path)
      // 记录当前打开页面 便于主页切换，以及面包屑（当前url为菜单上的）
      this.$menu.openNewPage(this, (indexPath.length && indexPath[indexPath.length - 1]) || index)
    },
    /**
     * check the url is really in the menu
     * @param url
     */
    toMenu (url) {
      if (!url || url === '/') {
        return false
      }
      let result = false
      const menuTree =
        (sessionStorage.menuTree && JSON.parse(sessionStorage.menuTree)) || []
      const navMenu =
        (sessionStorage.topMenuList &&
          JSON.parse(sessionStorage.topMenuList)) ||
        []
      if (this.$util.isEmptyArray(menuTree) && this.$util.isEmptyArray(navMenu)) { return false }
      const allMenu = menuTree.concat(navMenu)

      const _t = this
      checkUrl(allMenu)
      function checkUrl (menuTree) {
        menuTree.forEach(item => {
          // TODO--判断失败（问题点：前缀丢失）
          if (item && item.url && item.url === url) {
            result = true
          }
          if (!result && !_t.$util.isEmptyArray(item.children)) {
            checkUrl(item.children)
          }
        })
      }

      return result
    }
  }
}
</script>
