<template>
    <div :style="{display:(themeSwitch?'inline':'none')}">
        <i slot="reference" title="皮肤中心" class="iconfont el-icon-huanfu" style="padding-right: 10px" @click="openSkinCenter"></i>
        <i slot="reference" title="主题中心" class="iconfont el-icon-zhuti" style="padding-right: 10px" @click="handleClick"></i>
        <i slot="reference" title="快捷菜单" class="iconfont el-icon-caidan" style="padding-right: 10px" @click="openCommonFunc"></i>
        <skin-center :drawer-param="skinCenterParam" @changeColor="changeColor"></skin-center>
        <theme-config-dialog :dialog-param="dialogParam"></theme-config-dialog>
        <common-func :drawer-param="commonFuncParam"></common-func>
    </div>
</template>

<script>

import ThemeConfigDialog from './components/themeConfigDialog'
import SkinCenter from './components/skinCenter'
import CommonFunc from './components/commonFunc.vue'
import { mapState } from 'vuex'
export default {
  name: 'ThemeManage',
  components: {
    ThemeConfigDialog,
    SkinCenter,
    CommonFunc
  },
  data() {
    return {
      curSkinCode: '',
      // userSettings: [],
      dialogParam: { // 主题设置弹窗参数
        modelVisible: false
      },
      skinCenterParam: { // 皮肤中心参数
        visible: false
      },
      commonFuncParam: { // 常用菜单参数
        visible: false
      },
      styleObj: {
        width: '80px',
        height: '80px',
        margin: '2px 4px',
        display: 'inline-block'
      },
      themeSwitch: false
    }
  },
  computed: {
    ...mapState({
      navigatorCode: state => state.app.navigatorCode,
      userSettings: state => state.app.userSettings
    })
  },
  mounted() {
    this.getThemeConfig()
  },
  methods: {
    /**
         * 用户常用功能和皮肤设置查询
         */
    getThemeConfig () {
      this.themeSwitch = sessionStorage.themeSwitch && JSON.parse(sessionStorage.themeSwitch)
      this.curSkinCode = sessionStorage.themeConfig && JSON.parse(sessionStorage.themeConfig).curSkinCode
      this.changeColor(this.curSkinCode, false)
    },
    handleClick () {
      this.dialogParam = {
        modelVisible: true,
        activeName: '1',
        checkedNodes: [...this.userSettings]
      }
    },
    openSkinCenter () {
      this.skinCenterParam = {
        visible: true,
        curSkinCode: this.curSkinCode
      }
    },
    openCommonFunc () {
      this.commonFuncParam = {
        visible: true,
        userSettings: this.userSettings
      }
    },
    changeColor (newColor, flag = true) {
      if (!newColor) return
      this.skinCenterParam.visible = false
      this.curSkinCode = newColor
      if (flag) {
        const url = this.$apiConfig.managerPathPrefix + '/userSettings/saveSkin'
        this.$aspHttps.asp_Post(url, { code: newColor }).then((response) => {
          if (this.$reponseStatus(response)) {
            this.skinCenterParam.visible = false
            this.$util.changeThemeColorHandler(newColor, this.afterChangeColor(newColor))
            const themeConfig = sessionStorage.themeConfig && JSON.parse(sessionStorage.themeConfig)
            themeConfig.curSkinCode = newColor
            sessionStorage.themeConfig = JSON.stringify(themeConfig)
            // 更新配置
            this.$util.updateThemeCongfig(this)
            this.$message.success(response.message)
          } else {
            this.$message.error(response.message)
          }
        })
      } else {
        this.$util.changeThemeColorHandler(newColor, this.afterChangeColor(newColor))
        // 更新配置
        this.$util.updateThemeCongfig(this)
      }
    },
    afterChangeColor (color) {
      this.curSkinCode = color
      this.curColor = color
    }
  }
}
</script>