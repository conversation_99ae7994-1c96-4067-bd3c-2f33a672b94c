<!--
 * @Author: yuxuan <EMAIL>
 * @Date: 2022-10-12 21:17:38
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-11-01 14:30:26
 * @Description: 个人中心--弹窗
-->
<template>
  <asp-dialog v-model="dialogParam.staffModelVisible"
              :title="dialogParam.title"
              :visible.sync="dialogParam.staffModelVisible"
              :width="dialogWidth"
              class="webbas dialog-row-css">
    <template>
      <el-form ref="editStaff"
               :model="editStaff"
               :rules="rules"
               :inline="true">
        <el-row v-if="operateStatusCode === 'edit'"
                class="el-collapse-120">
          <el-col :span="12">
            <el-form-item label="用户名:"
                          prop="userName">
              <el-input v-model.trim="editStaff.userName"
                        auto-complete="off"
                        disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机:"
                          prop="mobile">
              <el-input v-model.trim="editStaff.mobile"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatusCode === 'edit'"
                class="el-collapse-120">
          <el-col :span="12">
            <el-form-item label="固定电话:"
                          prop="telephone">
              <el-input v-model.trim="editStaff.telephone"
                        auto-complete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属组织:"
                          prop="organizationName">
              <el-input v-model="editStaff.organizationName"
                        auto-complete="off"
                        disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatusCode === 'edit'"
                class="el-collapse-120">
          <el-col :span="12">
            <el-form-item label="真实姓名:"
                          prop="realName">
              <el-input v-model.trim="editStaff.realName"
                        placeholder="不能超过20个字符"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别:"
                          prop="sex">
              <el-select v-model="editStaff.sex"
                         placeholder="请选择">
                <el-option key="1"
                           label="男"
                           value="1"></el-option>
                <el-option key="2"
                           label="女"
                           value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatusCode === 'edit'"
                class="el-collapse-120">
          <el-col :span="12">
            <el-form-item label="邮箱:"
                          prop="email">
              <el-input v-model.trim="editStaff.email"
                        auto-complete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <template>
              <el-form-item label="归属部门:"
                            prop="departmentName">
                <el-input v-model="editStaff.departmentName"
                          auto-complete="off"
                          disabled></el-input>
                <!-- <div v-if="editStaff.departmentName"
                     class="asp-header-department-css">
                  <i class="el-select__caret el-input__icon el-icon-circle-close"
                     @click="clearDeptName()"></i>
                </div> -->
                <!-- <el-popover v-model="popoverTreeVisible"
                            placement="bottom"
                            width="160"
                            disabled> -->
                <!-- <div class="overflow-auto-css height-200-css">
                    <el-tree :data="departmentTreeData"
                             :props="defaultProps"
                             :expand-on-click-node="false"
                             @node-click="departTreeNodeClick"></el-tree>
                  </div> -->
                <!-- </el-popover> -->
              </el-form-item>
            </template>
          </el-col>
        </el-row>
        <el-row v-if="operateStatusCode === 'resetPw'"
                class="el-collapse-120">
          <el-col>
            <el-form-item label="旧密码:"
                          prop="oldPassword">
              <el-input v-model="editStaff.oldPassword"
                        type="password"
                        auto-complete="new-password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatusCode === 'resetPw'"
                class="el-collapse-120">
          <el-col>
            <el-form-item label="新密码:"
                          prop="newPassword">
              <el-input v-model="editStaff.newPassword"
                        type="password"
                        auto-complete="new-password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatusCode === 'resetPw'"
                class="el-collapse-120">
          <el-col>
            <el-form-item label="密码确认:"
                          prop="pwdConfirm">
              <el-input v-model="editStaff.pwdConfirm"
                        type="password"
                        auto-complete="new-password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="userDomain === 'admin' && operateStatusCode === 'edit'"
                class="el-collapse-120">
          <el-col :span="12">
            <el-form-item prop="expireDateStr"
                          class="change-date-icon"
                          label="账号有效期:">
              <el-date-picker v-model="editStaff.expireDateStr"
                              :picker-options="pickerOptions"
                              type="date"
                              align="right"
                              placeholder
                              value-format="yyyy-MM-dd"
                              class="width-100-css"
                              disabled></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatusCode === 'edit'"
                class="el-collapse-120">
          <el-col :span="24">
            <el-form-item label="已有角色:"
                          prop="userRoles">
              <el-input v-model="editStaff.userRoles"
                        type="textarea"
                        disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="operateStatusCode === 'edit'"
                class="el-collapse-120">
          <el-col :span="24">
            <el-form-item v-if="userInforMaskItems.indexOf('division') == -1"
                          label="所属地域:"
                          prop>
              <div class="edit-staff-area-info">
                <template v-if="editStaff.listDivisionName.length === 0">
                  <center>{{ emptyText }}</center>
                </template>
                <template v-for="(item, index) in editStaff.listDivisionName">
                  <p :key="index">{{ item.pathName }}</p>
                </template>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer-center">
      <asp-btn-hollow icon="el-icon-close"
                      name="取消"
                      @click="operateEvent('cancel')">
      </asp-btn-hollow>
      <asp-btn-solid :disabled="submitStatus"
                     icon="el-icon-check"
                     name="保存"
                     @click="operateEvent('save')">
      </asp-btn-solid>
    </template>
  </asp-dialog>
</template>

<script>
import validation from '@/utils/validation'
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
const { projectConfig } = require('./../../../../config.json')
export default {
  name: 'PersonInfo',
  // components: { CheckAreas },
  props: {
    dialogParam: {
      type: Object,
      default: null
    }
  },
  data () {
    var validateLoginName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('用户名不能为空'))
      } else {
        var checkLoginNameUrl = this.$apiConfig.managerPathPrefix + '/user/checkUserName'
        const param = {
          domain: this.editStaff.domain,
          userId: this.editStaff.id,
          userName: value
        }
        this.$aspHttps.asp_Post(checkLoginNameUrl, param).then((response) => {
          if (this.$reponseStatus(response)) {
            if (response.data) {
              callback()
            } else {
              callback(new Error('用户名已存在'))
            }
          }
        })
      }
    }
    var validatePhone = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('该字段不能为空'))
      } else {
        var checkPhoneUrl = this.$apiConfig.managerPathPrefix + '/user/checkMobile'
        const param = {
          userId: this.editStaff.id, // 有Id值的不用校验自己的
          mobile: this.editStaff.mobile
        }
        this.$aspHttps.asp_Post(checkPhoneUrl, param).then((response) => {
          if (this.$reponseStatus(response)) {
            if (response.data) {
              callback()
            } else {
              callback(new Error('手机号已存在'))
            }
          }
        })
      }
    }
    var validateEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('该字段不能为空'))
      } else {
        var checkEmailUrl = this.$apiConfig.managerPathPrefix + '/user/checkEmail'
        const param = {
          domain: this.editStaff.domain,
          userId: this.editStaff.id,
          email: value
        }
        this.$aspHttps.asp_Post(checkEmailUrl, param).then((response) => {
          if (this.$reponseStatus(response)) {
            if (response.data) {
              callback()
            } else {
              callback(new Error('邮箱已存在'))
            }
          }
        })
      }
    }
    return {
      initDateStr: '5018-12-31', // 默认有效期3000年后
      userDomain: '', // 当前登录用所属域
      userInforMaskItems: [],
      popoverTreeVisible: false,
      // departmentTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'text'
      },
      submitStatus: false, // 提交按钮限制
      domain: 'admin', // 获取秘钥默认admin
      modulus: '', // 用于密码加密操作
      exponent: '', // 用于密码加密操作
      editStaff: {
        listDivisionName: []
      },
      operateStatusCode: '', // 操作状态参数
      emptyText: '加载中··',
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      rules: {
        userName: [
          { required: true, message: '用户名不能为空', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个汉字', trigger: 'blur' },
          { validator: validation.checkSpecial, trigger: 'blur' },
          { validator: validateLoginName, trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '该字段不能为空', trigger: 'blur' },
          { pattern: /^[^ ]+$/, message: '真实姓名不能包含空格', trigger: 'blur' },
          { max: 20, message: '输入不能超过20个汉字', trigger: 'blur' }
        ],
        organizationId: [
          // { required: true, message: '该字段不能为空', trigger: 'change' }
        ],
        organizationName: [
          { required: true, message: '该字段不能为空', trigger: 'blur' }
        ],
        departmentName: [
          { required: false, message: '该字段不能为空', trigger: 'blur' }
        ],
        telephone: [
          { validator: validation.checkSpecial, trigger: 'blur' },
          { max: 20, message: '输入不能超过20个汉字', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '邮箱不能为空', trigger: 'blur' },
          { max: 100, message: '输入不能超过100个汉字', trigger: 'blur' },
          { validator: validation.email, trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          { validator: validation.mobile, trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        oldPassword: [
          { required: true, message: '旧密码不能为空', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '新密码不能为空', trigger: 'blur' },
          { validator: validation.checkPassword, trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === this.editStaff.oldPassword) {
                callback(new Error('新密码不能与旧密码相同！'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        pwdConfirm: [
          { required: true, message: '确认密码不能为空', trigger: 'blur' },
          { validator: validation.checkPassword, trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== this.editStaff.newPassword) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        sex: [
          // { required: true, message: '性别不能为空', trigger: 'blur' }
        ],
        divisions: [
          // { required: true, message: '地域不能为空', trigger: 'blur' }
        ],
        expireDateStr: [
          { required: true, message: '请选择账号有效期', trigger: ['blur', 'change'] }
        ]
      },
      dialogWidth: '60%'
    }
  },
  computed: {
  },
  watch: {
    'dialogParam.staffModelVisible' (val) {
      if (val) {
        this.$nextTick(() => {
          // 初始化数据
          this.$refs.editStaff.resetFields()
          if (this.dialogParam.status === 'personInfo') { // 修改当前用户
            this.operateStatusCode = 'edit'
            this.emptyText = '加载中··'
            // 查询用户详情
            this.getUserByUserId(this.dialogParam.id)
          } else { // 修改密码
            this.operateStatusCode = 'resetPw'
          }
        })
      } else {
        // 点击取消 数据重置
        this.$refs.editStaff.resetFields()
        this.editStaff.listDivisionName = []
        // Object.assign(this.$data, this.$options.data())
      }
    },
    'operateStatusCode' (val) {
      if (val === 'resetPw') {
        this.dialogWidth = '35%'
      } else {
        this.dialogWidth = '60%'
      }
    }
  },
  created () {
    const domain = sessionStorage[projectConfig.operator] ? JSON.parse(sessionStorage[projectConfig.operator]) : {}
    this.userDomain = domain.userInfo.domain
    this.userInforMaskItems = domain.platformConfig.userInforMaskItems ? domain.platformConfig.userInforMaskItems.split(',') : []
  },
  methods: {
    // 获取资源信息进行加密操作
    getConfig () {
      this.submitStatus = true
      const param = {
        domain: this.domain
      }
      this.$aspHttps.asp_Post(this.$apiConfig.supportPathPrefix + '/domainConfig/listAll', param).then((response) => {
        this.submitStatus = false
        if (this.$reponseStatus(response)) {
          this.modulus = response.data.platformConfig.modulus
          this.exponent = response.data.platformConfig.exponent
          // 获取加密秘钥之后就可以进行保存提交操作
          this.submitOperStaff()
        }
      })
    },
    addRSA (password) {
      // RSA
      asp_RSAKey.RSASetPublic(this.modulus, this.exponent)
      return asp_RSAKey.RSAEncrypt(password)
    },
    /**
         * 通过Id用户详情
         * @param id
         */
    // getUserByUserId(id) {
    getUserByUserId () {
      this.emptyText = '加载中··'
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/getCurrent').then((response) => {
        if (this.$reponseStatus(response)) {
          if (response.data.listDivisionName.length > 0) {
            response.data.listDivisionName = response.data.listDivisionName.map(item => {
              return JSON.parse(item)
            })
          }
          this.reValForm(response.data)
        }
      })
    },
    /**
     * 通过Id用户关联的角色信息(id默认不用传)
     */
    getUserRoles () {
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/user/listCurrentUserRoles').then((response) => {
        if (this.$reponseStatus(response)) {
          // 处理角色数据
          this.editStaff.userRoles = ''
          const roleIdData = []
          response.data.forEach(function (item) {
            if (item.check.toString() === 'true') {
              roleIdData.push(item.name)
            }
          })
          this.editStaff.userRoles = roleIdData.join(',')
        }
      })
    },
    /**
 * 获取部门树形信息
 * @param orgId 机构Id值
 */
    // getDepartTree (orgId) {
    //   this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + '/department/listDepartmentTree', { id: orgId }).then((response) => {
    //     if (this.$reponseStatus(response)) {
    //       this.departmentTreeData = response.data
    //     }
    //   })
    // },
    /**
 * 编辑提交
 * @param param
 */
    operateEvent (param) {
      if (param === 'save') {
        this.submitStatus = true
        this.$refs.editStaff.validate((valid) => {
          this.submitStatus = false
          if (valid) {
            // 提交前发起获取秘钥操作
            this.getConfig()
          }
        })
      } else { // 取消
        // 关闭弹窗 初始化信息
        this.$refs.editStaff.resetFields()
        this.dialogParam.staffModelVisible = false
      }
    },
    /**
         * 提交操作
         */
    submitOperStaff () {
      this.submitStatus = true
      const oper = this.operateStatusCode
      let operUrl = ''
      let param = {}
      switch (oper) {
        case 'edit': {
          operUrl = this.$apiConfig.managerPathPrefix + '/user/updateCurrent'
          // 组织和部门因为不可修改，可以不用上传
          param = {
            id: this.editStaff.id,
            userName: this.editStaff.userName,
            realName: this.editStaff.realName,
            sex: this.editStaff.sex,
            email: this.editStaff.email,
            mobile: this.editStaff.mobile,
            telephone: this.editStaff.telephone,
            // organizationId: this.editStaff.organizationId,
            // organizationName: this.editStaff.organizationName,
            departmentId: this.editStaff.departmentId,
            departmentName: this.editStaff.departmentName,
            divisions: this.editStaff.divisions,
            expireDateStr: this.userDomain === 'admin' ? this.editStaff.expireDateStr : this.initDateStr
          }
          // 更新缓存
          const compose = JSON.parse(sessionStorage.getItem(projectConfig.operator))
          compose.userInfo.name = this.editStaff.realName
          sessionStorage.setItem(projectConfig.operator, JSON.stringify(compose))
          break
        }
        case 'resetPw': {
          if (sessionStorage.getItem('LOGINTYPE') === '4A') {
            operUrl = this.$apiConfig.authUaPrefix + '/login/modifyPwd'
            // 该操作后台自动获取当前登录用户的Id，不用传userId
            param = {
              oldPassword: this.addRSA(this.editStaff.oldPassword),
              newPassword: this.addRSA(this.editStaff.newPassword),
              userName: this.addRSA(JSON.parse(sessionStorage.domain).userInfo.username)
            }
          } else {
            operUrl = this.$apiConfig.managerPathPrefix + '/user/changePwd'
            // 该操作后台自动获取当前登录用户的Id，不用传userId
            param = {
              oldPassword: this.addRSA(this.editStaff.oldPassword),
              newPassword: this.addRSA(this.editStaff.newPassword),
              expireDateStr: this.userDomain === 'admin' ? this.editStaff.expireDateStr : this.initDateStr
            }
          }
          break
        }
      }
      // 通过选择的部门或机构获取当前用户所属的域
      this.$aspHttps.asp_Post(operUrl, param).then((response) => {
        if (this.$reponseStatus(response)) {
          this.$message.success('修改成功！')
          // 调取关闭操作
          this.operateEvent('cancel')
          this.submitStatus = false
        } else {
          this.submitStatus = false
        }
      })
    },
    reValForm (data) {
      this.editStaff = {
        id: data.id,
        userName: data.userName,
        realName: data.realName,
        sex: data.sex,
        email: data.email,
        mobile: data.mobile,
        telephone: data.telephone,
        organizationId: data.organizationId,
        organizationName: data.organizationName,
        departmentId: data.departmentId,
        departmentName: data.departmentName,
        divisions: data.divisions,
        listDivisionName: data.listDivisionName,
        userRoles: '',
        expireDateStr: data.expireDateStr
      }
      if (data.listDivisionName.length === 0) this.emptyText = '暂无数据'
      // 获取用户角色详情（详情可以正常，之所以接口请求，是修改可共用）
      this.getUserRoles()
      // 获取部门列表
      // this.getDepartTree(data.organizationId)
    },
    /**
 * 获取部门信息保存值
 * @param data 选择的树形数据
 */
    departTreeNodeClick (data) {
      if (data.id !== this.editStaff.departmentId) {
        // 修改部门数据，清空已选相关地域数据
        this.clearHasDivisionVal()
        this.clearCheckDivisionVal()
      }
      this.editStaff.departmentName = data.text
      this.editStaff.departmentId = data.id
      this.domain = data.domain
      this.popoverTreeVisible = false
      this.getDeptByDeptId(data.id)
    }
  }
}
</script>
