/**
*/

<template>
    <asp-dialog
        v-model="dialogParam.modelVisible"
        :visible.sync="dialogParam.modelVisible"
        title="接收信息配置"
        width="60%"
        class="webbas"
    >
        <template>
            <ReceiveInfoConfigure ref="receiveInfo"></ReceiveInfoConfigure>
        </template>
        <template slot="footer-center">
            <asp-btn-hollow
                icon="el-icon-close"
                name="取消"
                @click="dialogParam.modelVisible = false"
            >
            </asp-btn-hollow>
            <asp-btn-solid
                :disabled="submitStatus"
                icon="el-icon-check"
                name="保存"
                @click="save"
            >
            </asp-btn-solid>
        </template>
    </asp-dialog>
</template>

<script>
import ReceiveInfoConfigure from './components/receiveInfoConfigure'
export default {
  name: 'ReceiveInfo',
  components: {
    ReceiveInfoConfigure
  },
  props: {
    dialogParam: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      submitStatus: false, // 提交按钮限制
      baseForm: {
        blacklistTypes: [],
        forbidEmailTypes: [],
        forbidSmsTypes: [],
        receiveData: []
      },
      rules: {
      }
    }
  },
  computed: {
  },
  watch: {
    'dialogParam.modelVisible'(val) {
      if (!val) {
        this.$refs.receiveInfo.$refs.baseForm.resetFields()
      }
    }
  },
  created() {
  },
  methods: {
    formatDict(code, dictList) {
      if (!code) {
        return ''
      }
      let dictStr = code
      if (dictList && dictList.length > 0) {
        dictList.forEach((item) => {
          if (item.code === code) {
            dictStr = item.name
          }
        })
      }
      return dictStr
    },
    save() {
      this.$refs.receiveInfo.$refs.baseForm.validate((valid) => {
        if (valid) {
          const blacklistTypes = this.$refs.receiveInfo.baseForm.blacklistTypes
          const receiveData = this.$refs.receiveInfo.baseForm.receiveData
          const targetFo = blacklistTypes.map(val => {
            let forbidBusinessTypes = []
            const rowData = receiveData.filter(value => value.type === val)
            if (receiveData.length && rowData.length) {
              forbidBusinessTypes = rowData[0].forbidBusinessType.map(value => {
                return {
                  code: value,
                  name: this.formatDict(value, this.$refs.receiveInfo[val + 'TypesList'])
                }
              })
            }
            return {
              type: val,
              forbidBusinessTypes
            }
          })
          const param = {
            userBlacklistFos: targetFo
          }
          this.submitStatus = true
          this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix +
                        '/userBlacklist/saveCurrentUserBlacklist', param).then((response) => {
            this.submitStatus = false
            if (this.$reponseStatus(response)) {
              this.$message.success('修改成功！')
              this.dialogParam.modelVisible = false
            }
          })
        }
      })
    }
  }
}
</script>
