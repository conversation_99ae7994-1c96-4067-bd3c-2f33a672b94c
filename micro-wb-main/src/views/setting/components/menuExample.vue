<template>
    <div class="menuExample">
        <el-row>
            <el-col :span="8">
                <div class="menu">
                    <el-container>
                        <el-header>
                            <el-row class="demo-header-area">
                                <el-col :span="this.$store.state.app.topMenuList.length > 0 ? 9 : 12" class="asp-header-area-left">
                                  <img src="../../../../../assets/img/loginlogo.png" alt="logo"/>
                                </el-col>
                                <el-col :span="this.$store.state.app.topMenuList.length > 0 ? 9 : 6">
                                </el-col>
                            </el-row>
                        </el-header>
                        <el-container>
                            <el-aside width="200px">
                                <el-menu
                                    :default-active="activeIndex3"
                                    class="el-menu-demo demo-left-menu"
                                    background-color="#f0f4f8"
                                    @select="handleSelect"
                                >
                                    <el-submenu index="2">
                                        <template slot="title">一级菜单</template>
                                        <el-menu-item index="2-1">二级菜单1</el-menu-item>
                                        <el-menu-item index="2-2">二级菜单2</el-menu-item>
                                        <el-submenu index="2-4">
                                            <template slot="title">二级菜单3</template>
                                            <el-menu-item index="2-4-1">三级菜单1</el-menu-item>
                                            <el-menu-item index="2-4-2">三级菜单2</el-menu-item>
                                        </el-submenu>
                                    </el-submenu>
                                </el-menu>
                            </el-aside>
                            <el-main></el-main>
                        </el-container>
                    </el-container>
                </div>
                <div class="radioItem">
                    <el-radio v-model="radio" label="1">主题1（默认主题）</el-radio>
                </div>
            </el-col>
            <el-col :span="8">
                <div class="menu">
                    <el-row class="demo-header-area">
                        <el-col :span="this.$store.state.app.topMenuList.length > 0 ? 9 : 12" class="asp-header-area-left">
                            <img src="../../../../../assets/img/loginlogo.png" alt="logo"/>
                        </el-col>
                        <el-col :span="this.$store.state.app.topMenuList.length > 0 ? 9 : 6">
                            <el-menu
                                :default-active="activeIndex3"
                                mode="horizontal"
                                style="border-bottom: 0;"
                                @select="handleSelect"
                            >
                                <el-submenu index="2">
                                    <template slot="title">一级菜单</template>
                                    <el-menu-item index="2-1">二级菜单1</el-menu-item>
                                    <el-menu-item index="2-2">二级菜单2</el-menu-item>
                                    <el-submenu index="2-4">
                                        <template slot="title">二级菜单3</template>
                                        <el-menu-item index="2-4-1">三级菜单1</el-menu-item>
                                        <el-menu-item index="2-4-2">三级菜单2</el-menu-item>
                                    </el-submenu>
                                </el-submenu>
                            </el-menu>
                        </el-col>
                    </el-row>
                </div>
                <div class="radioItem">
                    <el-radio v-model="radio" label="2">主题2</el-radio>
                </div>
            </el-col>
            <el-col :span="8">
                <div class="menu">
                    <el-container>
                        <el-header>
                            <el-row class="demo-header-area">
                                <el-col :span="this.$store.state.app.topMenuList.length > 0 ? 9 : 12" class="asp-header-area-left">
                                    <img src="../../../../../assets/img/loginlogo.png" alt="logo"/>
                                </el-col>
                                <el-col :span="this.$store.state.app.topMenuList.length > 0 ? 9 : 6">
                                    <ul class="el-submenu">
                                        <li v-for="(ml,index) in navList"
                                            :key="index"
                                            class="el-submenu__title"
                                            @click="navMenuTabs(ml.id,index,ml.url)"
                                        >
                                            <i :class="ml.icon" class="iconfont max-font" style="font-size: 14px"></i>
                                            {{ ml.text }}
                                        </li>
                                        <i class="el-submenu__icon-arrow el-icon-arrow-down"></i>
                                    </ul>
                                </el-col>
                            </el-row>
                        </el-header>
                        <el-container>
                            <el-aside width="200px">
                                <el-menu
                                    :default-active="activeIndex3"
                                    class="el-menu-demo demo-left-menu"
                                    background-color="#f0f4f8"
                                    @select="handleSelect"
                                >
                                    <el-submenu index="2">
                                        <template slot="title">二级菜单1</template>
                                    </el-submenu>
                                    <el-submenu index="3">
                                        <template slot="title">二级菜单2</template>
                                        <el-submenu index="3-4">
                                            <template slot="title">二级菜单3</template>
                                            <el-menu-item index="3-4-1">三级菜单1</el-menu-item>
                                            <el-menu-item index="3-4-2">三级菜单2</el-menu-item>
                                        </el-submenu>
                                    </el-submenu>
                                </el-menu>
                            </el-aside>
                            <el-main></el-main>
                        </el-container>
                    </el-container>
                </div>
                <div class="radioItem">
                    <el-radio v-model="radio" label="3">主题3</el-radio>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
export default {
  name: 'MenuExample',
  data() {
    return {
      activeIndex1: '1',
      activeIndex2: '1',
      activeIndex3: '1',
      radio: this.$store.state.app.navigatorCode,
      navList: [
        {
          id: 'demo_01',
          text: '一级菜单',
          url: '/operator/#/system/staff'
        }
      ],
      currLeftMenuList: []
    }
  },
  computed: {},
  methods: {
    handleSelect (key, keyPath) {
      // console.log(key, keyPath)
    },
    navMenuTabs (id, index, url) {
      // 纵向菜单
      this.currLeftMenuList = [
        {
          id: 'demo_0101',
          text: '二级菜单1',
          url: '/operator/#/system/staff'
        },
        {
          id: 'demo_0102',
          text: '二级菜单2',
          url: '/operator/#/system/staff'
        },
        {
          id: 'demo_0102',
          text: '二级菜单3',
          url: '/operator/#/system/staff'
        }
      ]
    }
  }
}
</script>