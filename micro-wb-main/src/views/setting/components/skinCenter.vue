<template>
    <el-drawer
        :visible.sync="drawerParam.visible"
        title="皮肤中心"
        size="21%"
        class="huanfu"
        style="text-align: left;"
    >
        <div v-for="item in colorList"
             :key="item.code"
             :style="[styleObj, {background: item.code}]"
             @mouseover="item.btnVisible = true"
             @mouseout="item.btnVisible = false"
        >
            <center>
                <span v-show="item.btnVisible" class="themeText" @click="changeColor(item.code)">
                    {{ item.code === curSkinCode ? '使用中' : '点击使用' }}
                </span>
            </center>
        </div>
    </el-drawer>
</template>

<script>
export default {
  name: 'SkinCenter',
  components: {},
  props: {
    drawerParam: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      curSkinCode: '',
      styleObj: {
        width: '80px',
        height: '80px',
        margin: '2px 4px',
        display: 'inline-block'
      },
      colorList: []
    }
  },
  watch: {
    'drawerParam.visible' (val) {
      if (val) {
        this.curSkinCode = this.drawerParam.curSkinCode
        this.getSkinDict()
      } else {
        this.curSkinCode = ''
        this.colorList = []
      }
    }
  },
  mounted() {},
  methods: {
    /**
         * 获取当前用户主题色列表
         */
    getSkinDict () {
      this.colorList = []
      const codeData = this.$util.getCodeValueByType(this, 'USER_SKIN') || []
      codeData && codeData.forEach(item => {
        if (item.status === '1') { // 只展示有效（没有被隐藏的主题）皮肤
          item.btnVisible = false
          this.colorList.push(item)
        }
      })
    },
    changeColor (newColor) {
      this.$emit('changeColor', newColor)
    }
  }
}
</script>