<template>
    <div style="display: inline;">
        <el-drawer
            :visible.sync="drawerParam.visible"
            :with-header="false"
            :show-close="false"
            class="menu"
        >
            <div style="padding-bottom: 20px;">
                <el-button type="text" disabled>常用菜单</el-button>
                <el-button type="text" style="float: right;" @click="openFunctionDialog">菜单管理</el-button>
            </div>
            <el-divider></el-divider>
            <div v-for="menu in userSettings" :key="menu.id" class="commonFunc">
                <el-button :title="menu.text" type="text" @click="commonFuncClick(menu)">{{ menu.text }}</el-button>
            </div>
        </el-drawer>
        <theme-config-dialog :dialog-param="commonFuncParam"></theme-config-dialog>
    </div>
</template>

<script>
import themeConfigDialog from './themeConfigDialog'
import { mapState } from 'vuex'
export default {
  name: 'CommonFunc',
  components: { themeConfigDialog },
  props: {
    drawerParam: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      commonFuncParam: {
        modelVisible: false
      }
    }
  },
  computed: {
    ...mapState({
      userSettings: state => state.app.userSettings
    })
  },
  created () {
    //
  },
  methods: {
    // 常用功能设置按钮
    openFunctionDialog () {
      this.commonFuncParam = {
        modelVisible: true,
        checkedNodes: [...this.userSettings],
        activeName: '2'
      }
      this.drawerParam.visible = false
    },
    // 常用功能点击
    commonFuncClick (obj) {
      this.$store.commit('app/setTabUrl', { path: obj.url })
      const topAndLeftMenuList = sessionStorage.topAndLeftMenuList && JSON.parse(sessionStorage.topAndLeftMenuList)
      const firstKeys = topAndLeftMenuList.map(item => item.id)
      const currParentId = this.$pageConfig.homePage.pageId
      if (topAndLeftMenuList && topAndLeftMenuList.length > 0) {
        this.getTopNavIndex(topAndLeftMenuList, firstKeys, obj.url, currParentId)
      }
      this.drawerParam.visible = false
    },
    // 获取顶部导航活动菜单
    getTopNavIndex (menuTreeList, firstKeys, url, currParentId) {
      menuTreeList.forEach(item => {
        if (firstKeys.indexOf(item.id) > -1) {
          currParentId = item.id
        }
        if (item.url === url) {
          if (firstKeys.indexOf(item.id) > -1) {
            currParentId = item.id
          }
          const navMemory = sessionStorage[this.$projectConfig.projectNavMemoryKey] && JSON.parse(sessionStorage[this.$projectConfig.projectNavMemoryKey])
          navMemory.topNavIndex = currParentId
          this.$store.commit('app/setNavMemory', navMemory)
        } else if (item.children && item.children.length > 0) {
          this.getTopNavIndex(item.children, firstKeys, url, currParentId)
        }
      })
    }
  }
}
</script>