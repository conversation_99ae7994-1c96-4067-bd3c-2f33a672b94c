<template>
    <div :style="notClick ? 'cursor:not-allowed;' : ''" class="unSchTree">
        <el-row>
            <el-col :span="24" style="text-align: left;">
                <span>
                    说明：通过点击勾选可添加菜单为管理常用操作项，最多添加9个。
                </span>
            </el-col>
        </el-row>
        <el-row :style="notClick ? 'pointer-events: none;' : ''">
            <el-col :span="12" class="tree-scroll">
                <el-tree
                    v-show="falg"
                    ref="treeList"
                    :empty-text="emptyText"
                    :data="treeArray"
                    :props="defaultProps"
                    :default-expand-all = "true"
                    :default-expanded-keys="expandedKeys"
                    :highlight-current="false"
                    show-checkbox
                    node-key="id"
                    @check="handleClickNode"
                >
                </el-tree>
            </el-col>
            <el-col :span="10" style="float:right">
                <span>
                    <b>已选菜单（{{ checkedArr.length }}/9）</b>
                </span>
                <br>
                <div>
                    <div
                        v-for="(obj, index) in checkedArr"
                        :key="index"
                        class="checked-col"
                        @mouseover="addActive($event)"
                        @mouseout="removeActive($event)"
                    >
                        <div class="checked-title">{{ obj.text }}</div>
                        <div class="checked-title-close">
                            <el-button type="text" icon="el-icon-close" style="padding: 6px 3px;" @click="handleClickNode(obj, {})"></el-button>
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
<script>
export default {
  name: 'CommonFuncConfig',
  filters: {},
  props: {
    checkedNodes: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      emptyText: '加载中··',
      // 树展示的数据
      treeArray: [],
      defaultProps: {
        children: 'children',
        label: 'text'
      },
      checkedArr: [],
      expandedKeys: [],
      newData: [],
      falg: false,
      notClick: true
    }
  },
  created() {},
  mounted () {
    this.checkedArr = this.checkedNodes
    this.getTreeData()
  },
  methods: {
    addActive (event) {
      event.currentTarget.className = 'checked-col checked-col-close'
    },
    removeActive (event) {
      event.currentTarget.className = 'checked-col'
    },
    getTreeData() {
      this.treeArray = sessionStorage.getItem('menuTree') && JSON.parse(sessionStorage.getItem('menuTree'))
      // 首页排除，需优化
      const index = this.treeArray.map((item, index) => {
        if (item.id === 'wb_01') {
          return index
        }
      })
      this.treeArray.splice(index, 1)
      this.falg = true
      this.newData = this.checkedArr.map(item => { return item.id })
      this.expandedKeys = [...this.newData]
      const _this = this
      this.$nextTick(() => {
        _this.$refs.treeList.setCheckedKeys(this.newData)
        _this.notClick = false
      })
    },
    /**
         * 树形菜单选中节点
         * @param data 该节点对应的对象
         * @param node 节点组件本身
         */
    handleClickNode (data, node) {
      if (this.checkedArr.length === 0) {
        this.checkedArr.push(data)
        this.newData.push(data.id)
        return
      }
      if (this.newData.indexOf(data.id) === -1) {
        if (this.checkedArr.length >= 9 && this.newData.indexOf(data.id) === -1) {
          this.$message.error('最多只能设置9个常用菜单')
          // 重新设置值
          this.$refs.treeList.setCheckedKeys(this.newData)
          return
        }
        this.newData.push(data.id)
        this.checkedArr.push(data)
      } else {
        this.newData.splice(this.newData.indexOf(data.id), 1)
        this.checkedArr.splice(this.checkedArr.findIndex(item => { return item.id === data.id }), 1)
      }
      // 重新设置值
      this.$refs.treeList.setCheckedKeys(this.newData)
      this.notClick = false
    }
  }
}
</script>