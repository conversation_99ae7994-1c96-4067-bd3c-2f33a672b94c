/**
*/
<template>
    <div>
        <el-form ref="baseForm" :model="baseForm" :rules="rules" :inline="true">
            <el-row v-if="blacklistTypesList.filter(val => val.isShow).length">
                <el-col :span="24">
                    <el-form-item label="" prop="blacklistTypes">
                        <el-checkbox-group v-model="baseForm.blacklistTypes">
                            <el-checkbox v-for="item in blacklistTypesList"
                                         v-show="item.isShow"
                                         :key="item.code"
                                         :value="item.code"
                                         :label="item.code"
                                         :disabled="item.disabled"
                            >{{ `是否拒收${item.name}` }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <template v-if="smsStatus || emailStatus">
                <el-row style="font-weight: bold">
                    <el-col :span="24">
                        请选择不需要接收的消息类型:
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-table
                            ref="receiveData"
                            :data.sync="baseForm.receiveData"
                            border
                        >
                            <el-table-column prop="targetName" min-width="50" label="拒收类型">
                            </el-table-column>
                            <el-table-column label="消息类型" min-width="150">
                                <template slot-scope="scoped">
                                    <el-form-item
                                        :prop="'receiveData.'+scoped.$index+'.forbidBusinessType'"
                                    >
                                        <el-checkbox-group v-model="scoped.row.forbidBusinessType" @change="val => businessTypeChange(val, scoped.row, scoped.$index)">
                                            <el-checkbox v-for="item in scoped.row.typesList"
                                                         :key="item.code"
                                                         :value="item.code"
                                                         :label="item.code"
                                                         :disabled="item.disabled || scoped.row.disabled"
                                            >{{ item.name }}</el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-col>
                </el-row>
                <el-row v-if="configInfo" style="background-color: #fcf8e3;line-height: 20px;">
                    <el-col :span="24">
                        &nbsp;&nbsp;&nbsp;{{ configInfo }}
                    </el-col>
                </el-row>
            </template>
        </el-form>
    </div>
</template>

<script>
export default {
  name: 'ReceiveInfoConfigure',
  props: {
    userId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      baseForm: {
        blacklistTypes: [],
        forbidEmailTypes: [],
        forbidSmsTypes: [],
        receiveData: []
      },
      configInfo: '',
      rules: {
      },
      blacklistTypesList: [], // 拒收类型
      smsTypesList: [], // 拒收短信类型
      smsStatus: false,
      emailTypesList: [], // 拒收邮件类型
      emailStatus: false,
      resetData: []
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.init()
  },
  methods: {
    // 获取短信邮件类型
    init() {
      this.baseForm.receiveData = []
      this.baseForm.receiveData = []
      // 拒收类型字典
      this.blacklistTypesList = this.$util.getFinalDictByType(this, 'WB_BLACKLIST_TYPE')
      if (this.blacklistTypesList.length) {
        // 拒收短信类型字典
        this.smsTypesList = this.$util.getFinalDictByType(this, 'WB_FORBID_BUSSINESS_TYPE_SMS')
        this.smsStatus = !this.$util.isEmptyArray(this.smsTypesList)
        // 拒收邮件类型字典
        this.emailTypesList = this.$util.getFinalDictByType(this, 'WB_FORBID_BUSSINESS_TYPE_EMAIL')
        this.emailStatus = !this.$util.isEmptyArray(this.emailTypesList)
        // eg: 邮件的消息类型有配置字典时，页面屏蔽是否拒收短信复选框
        this.blacklistTypesList = this.blacklistTypesList.map(val => {
          val.isShow = !this[val.code + 'Status']
          return val
        })
        // 是否展示配置表格
        if (this.smsStatus || this.emailStatus) {
          // 获取提示信息
          this.configInfo = JSON.parse(sessionStorage.getItem('LISTALL')).platformConfig.userBlacklistTip
        }
        this.getDetail()
      }
    },
    // businessTypeChange(val, row, index) {
    businessTypeChange(val, row) {
      const blackIndex = this.baseForm.blacklistTypes.indexOf(row.type)
      // 消息类型选中拒收短信邮件复选框也选中，消息类型全部未选中时拒收短信邮件复选框也取消勾选
      if (val.length && blackIndex === -1) {
        this.baseForm.blacklistTypes.push(row.type)
      }
      if (!val.length && blackIndex > -1) {
        this.baseForm.blacklistTypes.splice(blackIndex, 1)
      }
    },
    setData() {
      this.baseForm.blacklistTypes = JSON.parse(JSON.stringify(this.resetData.blacklistTypes))
      this.baseForm.receiveData = JSON.parse(JSON.stringify(this.resetData.receiveData))
    },
    getDetail() {
      let param = {}
      let url = '/userBlacklist/listCurrentBlacklist'
      if (this.userId) {
        url = '/userBlacklist/listBlacklist'
        param = {
          userId: this.userId
        }
      }
      this.$aspHttps.asp_PostForm(this.$apiConfig.managerPathPrefix + url, param).then((response) => {
        if (this.$reponseStatus(response)) {
          const data = response.data || []
          this.baseForm.blacklistTypes = []
          this.baseForm.receiveData = []
          const dataKV = []
          const blacklistKV = this.blacklistTypesList.map(val => val.code)
          data.map(val => {
            if (blacklistKV.indexOf(val.type) > -1 && !this[val.type + 'Status']) {
              this.baseForm.blacklistTypes.push(val.type)
            }
            if (blacklistKV.indexOf(val.type) > -1 && val.forbidBusinessTypes && this[val.type + 'Status']) {
              this.baseForm.blacklistTypes.push(val.type)
            }
            dataKV[val.type] = val.forbidBusinessTypes || []
          })
          // 初始化表格数据
          this.blacklistTypesList.map(val => {
            const typesList = this[val.code + 'TypesList'] || []
            if (typesList.length) {
              this.baseForm.receiveData.push({
                // 是否拒收短信邮件禁用时相关配置行的消息类型也禁用
                disabled: val.disabled || false,
                targetName: val.name,
                type: val.code,
                // 每一行的消息类型字典
                typesList: typesList,
                forbidBusinessType: this.filterForbidBusiness(typesList, dataKV[val.code] || [])
              })
            }
          })
          this.resetData = {
            blacklistTypes: JSON.parse(JSON.stringify(this.baseForm.blacklistTypes)),
            receiveData: JSON.parse(JSON.stringify(this.baseForm.receiveData))
          } // 用于成员详情的数据重置
        }
      })
    },
    // 过滤掉后端返回的垃圾（已失效的）消息类型
    filterForbidBusiness(typesList, forbidBusinessType) {
      const data = []
      const typesListKV = typesList.map(val => val.code)
      forbidBusinessType.map(val => {
        if (typesListKV.indexOf(val) > -1) {
          data.push(val)
        }
      })
      return data
    }
  }
}
</script>
