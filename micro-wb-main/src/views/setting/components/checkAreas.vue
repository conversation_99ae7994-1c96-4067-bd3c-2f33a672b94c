<template>
    <asp-dialog
        v-model="dialogParam.modelVisible"
        :title="dialogParam.title"
        :close-on-click-modal="false"
        :visible.sync="dialogParam.areaModelVisible"
        :lock-scroll="true"
        :append-to-body="true"
        class="webbas"
        width="65%"
    >
        <template>
            <div class="overflow-areas-css">
                <el-tree
                    ref="areaTree"
                    :empty-text="areaTreeCfg.emptyText"
                    :data="areaTreeCfg.areaTreeData"
                    :props="areaTreeCfg.defaultProps"
                    :expand-on-click-node="false"
                    :default-checked-keys="areaTreeCfg.checkedIds"
                    :default-expanded-keys="areaTreeCfg.checkedIds"
                    :render-content="renderContent"
                    class="check-areas-change"
                    node-key="id"
                    default-expand-all
                    show-checkbox
                ></el-tree>
            </div>
        </template>
        <template slot="footer-center">
            <asp-btn-solid
                name="确定"
                @click.native.prevent="operAreas('save')"
            >
            </asp-btn-solid>
            <asp-btn-hollow name="清空" @click="operAreas('clear')"></asp-btn-hollow>
            <asp-btn-hollow name="取消" @click="operAreas('cancel')"></asp-btn-hollow>
        </template>
    </asp-dialog>
</template>

<script>
export const util = {
  // 设置data的Children属性值
  setChildren: (items, areasCheckedNodes, ids) => {
    items.map(item => {
      if (ids.indexOf(item.id) > -1) {
        // 取出相应位置的值
        const obj = util.queryArea(areasCheckedNodes, item.id)
        if (obj.id === item.id) item.children = obj.children
      } else {
        if (item.children && item.children.length > 0) util.setChildren(item.children, areasCheckedNodes, ids)
      }
    })
  },
  queryArea: (areas, id) => {
    for (let i = 0; i < areas.length; i++) {
      if (areas[i].id === id) {
        return areas[i]
      }
    }
    return { id: '' }
  }
}
export default {
  name: 'CheckAreas',
  props: {
    dialogParam: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isSave: false, // 是否能提交
      areaTreeCfg: {
        emptyText: '加载中··',
        checkedIds: [],
        checkData: {}, // 地域树形选中的节点部门数据
        areaTreeData: [], // 地域树形节点数据
        defaultProps: { // 树形组件配置参数
          children: 'children',
          label: 'text'
        }
      },
      allCheckedIds: [], // 所有被选中的节点Id
      areasCheckedNodes: [] // 选中的地域的节点数据
    }
  },
  watch: {
    'dialogParam.areaModelVisible'(val) {
      if (val) {
        // 清除当前缓存数据
        Object.assign(this.$data, this.$options.data())
        this.areaTreeCfg.checkedIds = []

        // 获取地域
        this.getAreasData()
        this.$nextTick(() => {
          var _levelname = document.getElementsByClassName('levelname') // levelname是上面的最底层节点的class属性的名字
          setTimeout(function () { // 等到树都加载完了再去执行的这里
            for (var i = 0; i < _levelname.length; i++) {
              _levelname[i].parentNode.style.cssFloat = 'left' // 最底层的节点，包括多选框和名字都让他左浮动
              _levelname[i].parentNode.style.styleFloat = 'left'
              // width: 200px;
              if (_levelname[i].parentNode.offsetWidth > 150) {
                const node = _levelname[i].childNodes[0]
                node.setAttribute('title', node.innerHTML)
              }
              _levelname[i].parentNode.style.width = '120px'
            }
          }, 0)
        })
      } else {
        // 清除当前缓存数据
        // Object.assign(this.$data, this.$options.data())
      }
    }
  },
  mounted() {
  },
  methods: {
    getAreasData() {
      const areaData = this.$area.getDownJsonById(this.dialogParam.parentDivision, localStorage.baseAreas)
      if (areaData === null) {
        this.$message.warning('地域数据异常请联系管理员！')
        return
      }
      this.areaTreeCfg.checkedIds = this.dialogParam.checkedIds
      this.areaTreeCfg.areaTreeData.push(areaData)
    },
    // 提交选中的地域节点id
    operAreas(val) {
      // 关闭弹窗 初始化信息
      const resetDialog = () => {
        this.dialogParam.areaModelVisible = false
        // 点击取消 清空复选框数据
        // this.$refs.areaTree.setCheckedKeys([])
        // this.areaTreeCfg.checkedIds = []
      }
      switch (val) {
        case 'save': {
          // this.reorganizeCheckedDatas()
          this.getCheckedKeys()
          this.getCheckedNodes()
          if (this.allCheckedIds.length === 0) {
            this.$message.warning('请至少选择一个地区！')
            this.isSave = false
            return
          }
          const data = this.$area.handlerCheckedData(JSON.stringify(this.areasCheckedNodes), localStorage.baseAreas, false)
          const ids = data.Ids
          const areasCheckedNodes = this.areasCheckedNodes
          if (data.Ids.length > 0) {
            try {
              util.setChildren(data.Nodes, areasCheckedNodes, ids)
            } catch (e) {
              //
            }
          }
          this.$emit('updateAreaData', data)
          resetDialog()
          break
        }
        case 'cancel': {
          resetDialog()
          break
        }
        case 'clear': {
          this.$refs.areaTree.setCheckedKeys([])
          this.areaTreeCfg.checkedIds = []
          break
        }
      }
    },
    // 保存选中的地域数据
    reorganizeCheckedDatas() {
      this.getCheckedKeys()
      this.getCheckedNodes()
      if (this.allCheckedIds.length === 0) {
        this.$message.warning('请至少选择一个地区！')
        this.isSave = false
        return
      }
      this.isSave = true
      const data = this.$area.handlerCheckedData(JSON.stringify(this.areasCheckedNodes), localStorage.baseAreas, false)
      this.$emit('updateAreaData', data)
    },
    // 获取选择的节点的id/key
    getCheckedKeys() {
      this.allCheckedIds = this.$refs.areaTree.getCheckedKeys()
    },
    // 获取选中的节点信息
    getCheckedNodes() {
      this.areasCheckedNodes = this.$refs.areaTree.getCheckedNodes(false, false)
    },
    renderContent(h, { node, data, store }) {
      const inner_span = h('span',
        {
          domProps: {
            innerHTML: node.label
          }
        })
      const out_span = (node.isLeaf === true)
        ? h('span', { class: 'levelname' }, [inner_span]) : h('span', {}, [inner_span])
      return out_span
    }
  }
}
</script>

<style>
    /* #check-Areas .levelname span {
        width: 70px;
        display: block;
        line-height: 28px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    } */
</style>
