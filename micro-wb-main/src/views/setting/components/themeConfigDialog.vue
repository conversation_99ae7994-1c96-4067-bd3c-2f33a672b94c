<template>
    <asp-dialog v-model="dialogParam.modelVisible"
                :visible.sync="dialogParam.modelVisible"
                width="65%"
                class="webbas dialog-row-css theme webbas-scroll"
    >
        <template>
            <el-tabs v-model="activeName">
                <el-tab-pane label="导航设置" name="1">
                    <menu-example ref="menuExample"></menu-example>
                </el-tab-pane>
                <el-tab-pane label="快捷菜单" name="2">
                    <common-func-config ref="commonFuncConfig" :checked-nodes="dialogParam.checkedNodes"></common-func-config>
                </el-tab-pane>
            </el-tabs>
        </template>
        <template slot="footer-center">
            <asp-btn-hollow name= "取消" icon="el-icon-close" @click="cancel()"></asp-btn-hollow>
            <asp-btn-solid v-loading="submitStatus" :disabled="submitStatus" name="保存" icon="el-icon-check" @click="save()"></asp-btn-solid>
        </template>
    </asp-dialog>
</template>

<script>
import CommonFuncConfig from './commonFuncConfig'
import MenuExample from './menuExample'
export default {
  name: 'ThemeConfigDialog',
  components: {
    CommonFuncConfig,
    MenuExample
  },
  props: {
    dialogParam: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      activeName: '1',
      submitStatus: false
    }
  },
  computed: {
  },
  watch: {
    'dialogParam.modelVisible'(val) {
      if (val) {
        this.activeName = this.dialogParam.activeName
        this.$nextTick(() => { this.getDetail() })
      }
    }
  },
  created() {},
  methods: {
    getDetail () {
      //
    },
    save() {
      const url = this.activeName === '1' ? '/userSettings/saveNavigator' : '/userSettings/save'
      let params = {}
      if (this.activeName === '1') {
        params = {
          code: this.$refs.menuExample.radio
        }
      } else {
        params = {
          userSettings: []
        }
        this.$refs.commonFuncConfig.checkedArr && this.$refs.commonFuncConfig.checkedArr.length > 0 && this.$refs.commonFuncConfig.checkedArr.forEach((item, index) => {
          params.userSettings.push(
            {
              defaultFlag: item.defaultFlag || '0',
              orderId: index,
              resourceId: item.resourceKey || item.id
            }
          )
        })
      }
      this.$nextTick(() => {
        this.submitStatus = true
        this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + url, params).then((response) => {
          if (this.$reponseStatus(response)) {
            const themeConfig = sessionStorage.themeConfig && JSON.parse(sessionStorage.themeConfig)
            if (this.activeName === '1') {
              themeConfig.navigatorCode = this.$refs.menuExample.radio
              this.$store.commit('app/setNavigatorCode', this.$refs.menuExample.radio)
              if (this.$refs.menuExample.radio === '2' || this.$refs.menuExample.radio === '3') {
                const topMenuList = this.$store.state.app.topMenuList
                this.$store.commit('app/setTopAndLeftMenuList', topMenuList.concat(this.$store.state.app.leftMenuList))
              } else {
                this.$store.commit('app/setTopAndLeftMenuList', '')
              }
            }
            if (this.activeName === '2') {
              themeConfig.userSettings = this.$refs.commonFuncConfig.checkedArr
              this.$store.commit('app/setUserSettings', this.$refs.commonFuncConfig.checkedArr)
            }
            sessionStorage.themeConfig = JSON.stringify(themeConfig)
            // 更新配置
            this.$util.updateThemeCongfig(this)
            this.dialogParam.modelVisible = false
            this.$message.success('设置成功！')
          }
          this.submitStatus = false
        })
      })
    },
    cancel () {
      this.submitStatus = false
      this.dialogParam.modelVisible = false
    }
  }
}
</script>