/** Created by TurboC on 2021/03/12 **/
import Vuex from 'vuex'
import Vue from 'vue'
import getters from './getters'
import VuexPersistence from 'vuex-persistedstate'
Vue.use(Vuex)

// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/)

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

const plugins = [
  VuexPersistence({
    storage: window.sessionStorage,
    key: 'secretConf',
    modules: [modules.vuexStorage],
    reducer: (state) => {
      // console.log('main-》state', state, modules.vuexStorage)
      // for (let key in modules.vuexStorage.state) {
      //   if (state[key] && !modules.vuexStorage.state[key]) {
      //     modules.vuexStorage.state[key] = state[key]
      //   }
      // }
      // 主动读取sessionStorage
      // Object.assign(modules.vuexStorage.state, state.vuexStorage)
      return { ...{ vuexStorage: state.vuexStorage } }
    }
  })
]

const store = new Vuex.Store({
  modules,
  getters,
  plugins
})

export default store
