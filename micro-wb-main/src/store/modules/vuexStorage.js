/**
 * Created by TurboC on 2019/08/01.
 * 项目数据持久化缓存storage
 */
const state = {
  secretWord: '',
  secretHeader: '',
  secretType: ''
}

const mutations = {
  setData: (state, data) => {
    state.secretWord = data.word
    state.secretType = data.type
    state.secretHeader = data.headerWord
  }
}

const actions = {}

const getters = {}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
