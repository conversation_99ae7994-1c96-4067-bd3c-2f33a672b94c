const state = {
  allRouters: [], // 全部镜像下路由信息
  currentRouter: {}, // 当前路由对象
  listAllConfig: {}, // 全局功能配置；如：domainConfig、platformConfig
  menuDialogConfig: { visible: false, jsonPath: '', title: '' } // 菜单事件公共弹窗配置
}

const mutations = {
  // 当前路由对象
  setCurrentRouter(state, pathRouter) {
    state.currentRouter = pathRouter
    sessionStorage.setItem('currentRouter', JSON.stringify(pathRouter))
  },
  setAllRouters(state, routers) {
    if (routers && routers.length > 0) {
      state.allRouters.push(...routers)
    }
  },
  setListAllConfig (state, config) {
    state.listAllConfig = config || {}
  },
  setMenuDialogConfig (state, config) {
    state.menuDialogConfig = config || {}
  }
}

const actions = {}

const getters = {}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}