/**
 * Created by TurboC on 2019/08/01.
 * webbbas框架的store
 */
import Vue from 'vue'
import util from '@/utils/util'
const {
  projectConfig
} = require('./../../../../config.json')

const state = {
  routers: [],
  vm: Vue,
  iFrameUrl: '',
  // 左部菜单
  leftMenuList: [],
  // 顶部菜单
  topMenuList: [],
  // 顶部菜单（一二三级菜单都在顶部导航栏）
  topAndLeftMenuList: [],
  // 左部导航栏（一级菜单在顶部导航栏，二、三级菜单在左侧位置）
  leftForTopMenuList: [],
  // 菜单帮助信息列表
  menuHelpInfoList: [],
  // 页面帮助信息列表
  pageHelpInfoList: [],
  navigatorCode: '1', // 导航方式
  userSettings: [], // 快捷菜单
  themeSwitch: false, // 主题开关 默认false
  // 鉴权路由
  // status 判断路由是否存在
  // list 保存鉴权路由 不包含默认路由
  addRouter: {
    status: false,
    list: []
  },
  // 当前路由
  currentPageName: '',
  // 记录导航信息--加了一个菜单级url--menuPath；用于刷新菜单定位
  navMemory: { navIndex: 0, router: { path: '' }, menuPath: '', frameTagUrl: '' },
  tabSystemUrl: '',
  // 记录展示tab的值',记录main页面来自PCC或VUE的值，是否展示菜单
  tabSwitch: {
    showRecyTab: true,
    pageFrom: 'vue',
    openSideMenu: { menuId: '', parentMenuId: '', openIds: [] }
  },
  // 展示进入的url，记录横向菜单的标记，记录折叠板的状态
  showRouterUrl: { gotoUrl: '', navIndex: -1 },
  tabUrl: '', // 导航点击缓存（并同步菜单事件）
  multiSystem: { systemId: '', menuUrl: '' } // 切换系统事件，含系统id和菜单url（仅对多系统菜单生效）
}

const mutations = {
  // 存储当前开打页面信息
  setTabSwitch (state, param) {
    if (param.showRecyTab !== undefined) {
      state.tabSwitch.showRecyTab = param.showRecyTab
      const tabConponent = document.getElementsByClassName('tab-conponent')[0]
      if (tabConponent) {
        tabConponent.style.border = param.showRecyTab ? '' : 'none'
      }
    }
    if (param.pageFrom) {
      state.tabSwitch.pageFrom = param.pageFrom
    }
    if (param.openSideMenu) {
      state.tabSwitch.openSideMenu.menuId = param.openSideMenu.menuId
      state.tabSwitch.openSideMenu.parentMenuId =
        param.openSideMenu.parentMenuId
      state.tabSwitch.openSideMenu.openIds = util.isEmptyArray(
        param.openSideMenu.openIds
      )
        ? []
        : param.openSideMenu.openIds
    }
  },
  // 存储当前开打页面Url信息
  setShowRouterUrl (state, param) {
    if (param.gotoUrl) {
      state.showRouterUrl.gotoUrl = param.gotoUrl
    }
    if (param.navIndex && param.navIndex.toString()) {
      state.showRouterUrl.navIndex = param.navIndex
    }
  },
  // 设置切换系统事件信息
  setMultiSystem (state, param) {
    if (!projectConfig.multiMenu) return
    if (param.systemId) {
      state.multiSystem.systemId = param.systemId
    }
    if (param.menuUrl) {
      state.multiSystem.menuUrl = param.menuUrl
    }
  },
  setTabUrl (state, param) {
    state.tabUrl = param
  },
  // 存储当前开打页面信息
  setNavMemory: (state, param) => {
    const navMemoryKey = projectConfig.projectNavMemoryKey
    const history = () => {
      return sessionStorage.getItem(navMemoryKey)
        ? JSON.parse(sessionStorage.getItem(navMemoryKey))
        : { navIndex: 0, router: { path: '' }, menuPath: '', frameTagUrl: '' }
    }
    const assign =
      param === 'init'
        ? { navIndex: 0, router: { path: '' }, menuPath: '', frameTagUrl: '' }
        : Object.assign(history(), param)
    sessionStorage.setItem(navMemoryKey, JSON.stringify(assign))
    state.navMemory = history()
  },
  setAddRouter (state, list) {
    state.addRouter = {
      status: list.length !== 0,
      list: list
    }
  },
  // 左部导航栏-纵向
  setLeftMenuList (state, list) {
    state.leftMenuList = list
  },
  // 菜单帮助信息
  setMenuHelpInfoList (state, list) {
    state.menuHelpInfoList = list
  },
  // 页面帮助信息
  setPageHelpInfoList (state, list) {
    state.pageHelpInfoList = list
    const pageHelpInfoList = projectConfig.pageHelpConfig
    sessionStorage.setItem(pageHelpInfoList, JSON.stringify(list))
  },
  // 顶部导航栏-横向
  setTopMenuList (state, list) {
    state.topMenuList = list
    if (list && list.length > 0) {
      state.routers.push(...list)
    }
  },
  // 顶部导航栏（一二三级菜单都在顶部导航栏）-横向
  setTopAndLeftMenuList (state, list) {
    state.topAndLeftMenuList = list
  },
  // 左部导航栏（一级菜单在顶部导航栏，二、三级菜单在左侧位置）-纵向
  setLeftForTopMenuList (state, list) {
    state.leftForTopMenuList = list
  },
  setTabSystemUrl (state, url) {
    state.tabSystemUrl = url
  },
  // 记录当前打开的页面
  setCurrentPageName (state, name) {
    state.currentPageName = name
  },
  // iframe url
  setIFrameUrl (state, url) {
    state.iFrameUrl = url
  },
  // 设置导航方式
  setNavigatorCode (state, navigatorCode) {
    if (!projectConfig.themeSwitch) {
      state.navigatorCode = projectConfig.defaultNavigatorCode
      return
    }
    if (navigatorCode !== undefined) {
      state.navigatorCode = navigatorCode
    }
  },
  // 设置快捷菜单
  setUserSettings (state, userSettings) {
    if (userSettings !== undefined) {
      state.userSettings = userSettings
    }
  },
  // 设置主题开关
  setThemeSwitch (state, themeSwitch) {
    if (themeSwitch !== undefined) {
      state.themeSwitch = themeSwitch
      sessionStorage.setItem('themeSwitch', themeSwitch)
    }
  }
}

const actions = {}

const getters = {}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
