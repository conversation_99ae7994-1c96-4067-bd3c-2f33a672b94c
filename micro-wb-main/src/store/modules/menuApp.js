/*
 * @Author: yuxuan <EMAIL>
 * @Date: 2022-04-13 16:29:52
 * @LastEditors: yuxuan
 * @LastEditTime: 2022-05-12 14:57:20
 * @Description: file content
 */
/**
 * 系统菜单管理模块
 */
const state = {
  // 根级菜单/虚拟菜单数据（非主要业务菜单而是切换系统菜单）
  nodeMenuLit: [],
  otherNodeMenuLit: [],
  menuExtendIds: [], // 需要待办数的菜单Id
  menuDealtNum: [], // 菜单待办数
  themeConfig: { curSkinCode: '', navigatorCode: '', themeSwitch: false }, // 主题配置信息
  currentRootId: '', // 当前选中系统id
  layoutMenuList: [], // 自定义菜单
  userInfoMenuList: [], // 个人信息-自定义菜单
  lnkMenuList: [] // 快捷菜单
}

const mutations = {
  // 设置根级菜单/虚拟菜单数据
  setNodeMenuLit (state, pathArr) {
    state.nodeMenuLit = pathArr
  },
  // 设置其他根级菜单/虚拟菜单数据
  setOtherNodeMenuLit (state, pathArr) {
    state.otherNodeMenuLit = pathArr
  },
  // 设置需要待办数的菜单IDs
  setMenuExtendIds (state, pathArr) {
    state.menuExtendIds = pathArr || []
  },
  // 设置叶子级菜单待办数
  setMenuDealtNum (state, pathArr) {
    state.menuDealtNum = pathArr || []
  },
  // 设置主题配置信息
  setThemeConfig (state, themeConfig) {
    if (!themeConfig) return
    if (Object.prototype.hasOwnProperty.call(themeConfig, 'curSkinCode')) state.themeConfig.curSkinCode = themeConfig.curSkinCode
    if (Object.prototype.hasOwnProperty.call(themeConfig, 'navigatorCode')) state.themeConfig.navigatorCode = themeConfig.navigatorCode
    if (Object.prototype.hasOwnProperty.call(themeConfig, 'themeSwitch')) state.themeConfig.themeSwitch = themeConfig.themeSwitch
  },
  setCurrentRootId (state, currentRootId) {
    state.currentRootId = currentRootId
  },
  // 记录配置菜单
  setLayoutMenuList (state, list) {
    state.layoutMenuList = list
    // 分解layout
    list.forEach(item => {
      if (item.layoutFlag && item.layout.type === 'userInfo') {
        state.userInfoMenuList.push(item)
      }
      if (item.layoutFlag && item.layout.type === 'lnkMenu') {
        state.lnkMenuList.push(item)
      }
    })
  },
  setUserInfoMenuList (state, list) {
    state.userInfoMenuList = list
  },
  setLnkMenuList (state, list) {
    state.lnkMenuList = list
  }
}

const actions = {}

const getters = {}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}