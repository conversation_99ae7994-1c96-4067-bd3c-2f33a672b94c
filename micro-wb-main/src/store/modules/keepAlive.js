/**
 * Created by TurboC on 2019/08/01.
 * 项目keepalive框架的store
 */
const state = {
  includes: [], // 缓存数组
  excludes: [] // 不存在数组
}

const mutations = {
  /**
   * 清除keep-alive的include属性
   * @param {*} state
   */
  clearIncludes (state, arr) {
    state.includes = []
  },
  /**
   * 将组件放入keep-alive的include属性中
   * @param {*} state
   * @param {*} component 需要缓存的组件名称
   */
  pushIncludes (state, component) {
    // const index1 = state.excludes.indexOf(component)
    // index1 !== -1 && state.excludes.splice(index1, 1)
    // const index2 = state.includes.indexOf(component)
    // index2 !== -1 && state.includes.splice(index2, 1)
    !state.includes.includes(component) && state.includes.push(component)
  },
  // 将组件从keep-alive的exclude属性中移除
  popIncludes (state, component) {
    const index = state.includes.indexOf(component)
    index !== -1 && state.includes.splice(index, 1)
  },
  /**
   * 将组件放入keep-alive的exclude属性中
   * @param {*} state
   * @param {*} component 不需要缓存的组件名称
   */
  pushExcludes (state, component) {
    // const index = state.includes.indexOf(component)
    // index !== -1 && state.includes.splice(index, 1)
    !state.excludes.includes(component) && state.excludes.push(component)
  },
  // 将组件从keep-alive的exclude属性中移除
  popExcludes (state, component) {
    const index = state.excludes.indexOf(component)
    index !== -1 && state.excludes.splice(index, 1)
  }
}

const actions = {}

const getters = {}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
