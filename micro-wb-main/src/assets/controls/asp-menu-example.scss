/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2021/06/15.
* 菜单示例样式
*/
@import "../mixins/mixins";
.menuExample {
    margin: 5px;
    .menu {
        height: 300px;
        margin: 15px;
    }
    .radioItem {
        text-align: center;
    }
    .demo-header-area {
      line-height: 48px;
      min-height: 48px;
      font-weight: 600;
      background: $wb-theme-color;
      .el-submenu:hover .el-submenu__title, .el-submenu:focus .el-submenu__title, .el-submenu.is-active .el-submenu__title {
        color: #ffffff !important;
      }
      .el-menu--horizontal>.el-submenu.is-active .el-submenu__title {
        border-bottom: 2px solid #ffffff;
      }
      .el-submenu__title:hover {
        background: $wb-theme-color;
      }
      .el-menu--horizontal {
        border: 0px;
        background: $wb-theme-color;
        .el-submenu {
            .el-submenu__title {
                height: 48px;
                background: $wb-theme-color;
                color: #fff;
                i {
                    color: #fff;
                }
            }
        }
      }
    }
    .el-header {
        padding: 0px;
        .el-submenu__title, i {
          color: #ffffff;
        }
    }
    .el-menu-item {
        height: 40px;
        line-height: 40px;
    }
    .el-menu-item.is-active {
        background-color: #e8eff2;
        color: #5584FF;
        border-right: 4px solid $wb-theme-color;
    }
    .el-submenu {
        text-align: left;
    }
    .el-submenu__title {
        height: 48px;
        line-height: 48px;
        font-size: 13px;
    }
    .demo-left-menu .el-submenu__title {
      color: #787878;
    }
}