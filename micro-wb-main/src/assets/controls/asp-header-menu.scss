/**
* Created by TurboC on 2019/08/01.
* 框架顶部样式
*/
@import "../mixins/mixins";

.asp-header-menu {
    // @include main-layout;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    color: #fff;
    background: $wb-theme-color;
    background-color: $wb-theme-color;
    overflow: hidden;
    overflow-y: auto;
    border: none;
    z-index: 99;
    .el-menu.el-menu--horizontal {
        border-bottom: none;
        .el-menu-item:not(.is-disabled):hover, .el-menu-item:not(.is-disabled):focus {
            background-color: $wb-theme-color;
            color: #fff;
            font-weight: 600;
            border-bottom: 2px solid #fff;
        }
    }
    .el-menu {
        background: $wb-theme-color;
    }
    .el-submenu .el-menu {
        background: $wb-theme-color;
    }
    .el-menu-item, .el-submenu__title {
        height: 40px;
        line-height: 40px;
        font-size: 13px;
        color: #fff;
    }
    .el-submenu__title {
        font-weight: 600;
    }
    .el-menu-item.is-active {
        background-color: $wb-theme-color;
        color: #fff;
        font-weight: 600;
        border-bottom: 2px solid #fff;
    }
    .el-submenu__title:hover, .el-menu-item:hover {
        background-color: $wb-theme-color;
        color: #fff;
        font-weight: 600;
        border-bottom: 2px solid #fff;
    }
    .el-menu-item:focus, .el-submenu__title:focus {
        background-color: $wb-theme-color;
        color: #fff;
        font-weight: 600;
        border-bottom: 2px solid #fff;
    }
    ul li {
        border-bottom: 1px solid #ebebeb;
    }
    .el-menu--collapse .hiddeName {
        display: none;
    }
    .el-menu > div > .el-menu-item {
        font-weight: 600;
        padding-top: 0;
        margin-top: 5px;
        font-size: 14px;
    }
    .el-submenu__title .el-menu-item {
        /* font-size: 16px; */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        /* white-space: normal; */
        word-break: break-all;
    }
}
