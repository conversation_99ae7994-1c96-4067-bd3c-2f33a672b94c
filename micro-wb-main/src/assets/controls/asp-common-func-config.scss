/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2021/06/15.
* 常用菜单设置样式文件
*/
.tree-scroll {
    height: 330px;
    overflow: auto;
    // background: rgba(230, 230, 230, 0.5);
}
.webbas-scroll ::-webkit-scrollbar{
    width: 8px;
}
.webbas-scroll ::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
}
.checked-col {
    /*border: #0099FF 1px solid;*/
    margin-top: 8px;
    width: 70%;
    line-height: 25px;
    height: 25px;
}
.checked-title {
    float: left;
    padding: 0px 5px;
}
.checked-title-close {
    display: none;
    float: right;
    padding: 0px 5px
}
.checked-col-close .checked-title-close {
    display: inline-block;
}
.checked-col:hover {
    background-color: #F5F7FA;
    color: #0099FF;
}
.unSchTree .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner{
        display: inline-block;
    }
    .el-checkbox .el-checkbox__inner{
        display: none;
    }
}
#unSchTree .el-tree-node:nth-child(1)[tabindex="0"] {
    .is-leaf + .el-checkbox .el-checkbox__inner{
        display: none;
    }
}
.unSchTree .el-tree-node__expand-icon.is-leaf {
    opacity: 0;
    color: transparent;
}
.commonFunc {
    display: inline-block;
    padding: 4px 23px;
    .el-button--text {
        color: #787878;
        width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
    }
}

.menu {
    .el-button.is-disabled, .el-button.is-disabled:focus, .el-button.is-disabled:hover {
        float: left;
        cursor: text;
    }
}

.huanfu, .menu {
    .themeText {
        height: 80px;
        line-height: 80px;
        width: 80px;
        float: left;
        color: #ffffff;
    }
    .el-drawer.ltr, .el-drawer.rtl, .el-drawer__container {
        height: 65%;
        overflow: hidden;
        outline-style: none;
    }
    .el-drawer__body {
        padding: 4px;
    }
    .el-drawer__header {
        margin-bottom: 0px;
    }
}
.menu {
    .el-drawer__body {
        padding: 15px;
    }
    .el-drawer__header {
        display: none;
        margin-bottom: 10px;
    }
}
