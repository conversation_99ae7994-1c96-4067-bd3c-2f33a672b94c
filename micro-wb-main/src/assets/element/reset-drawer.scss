/**
* Created by TurboC on 2019/08/01.
* 重写按钮框样式
*/

.huanfu, .menu {
    .themeText {
        height: 80px;
        line-height: 80px;
        width: 80px;
        float: left;
        color: #ffffff;
    }
    .el-drawer.ltr, .el-drawer.rtl, .el-drawer__container {
        height: 65%;
        overflow: hidden;
        outline-style: none;
    }
    .el-drawer__body {
        padding: 4px;
    }
}
.menu {
    .el-drawer__body {
        padding: 15px;
    }
    .el-drawer__header {
        display: none;
        margin-bottom: 10px;
    }
}
