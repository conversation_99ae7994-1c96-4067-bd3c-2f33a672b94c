/**
* Created by TurboC on 2019/08/01.
* 重写菜单样式
*/
.wb-main {
    .el-menu--collapse .el-submenu__title .el-icon-arrow-right {
        right: 10px;
    }
}

.el-menu--horizontal {
    .el-menu--popup-bottom-start {
        height: 350px;
        overflow: auto;
    }
    // 修改滚动条颜色---设置滚动条的样式--start
    ::-webkit-scrollbar {
        width: 7px;
    }
    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background-color: #cccccc;
        //-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5);
    }
    ::-webkit-scrollbar-thumb:window-inactive {
        //background: #878787!important;
        right: 0 !important;
        //border-left: 1px solid #555;
        //border-right: 1px solid #555;
        //background: rgba(255,0,0,0.4);
    }
}