/**
* Created by TurboC on 2019/08/01.
* 框架样式
*/
@import "../../mixins/mixins";

.wb-main{
    height: 100%;
    background: none;
    position:relative;

    // 修改滚动条颜色---设置滚动条的样式--start
    ::-webkit-scrollbar {
        width: 8px;
    }
    /* 滚动槽 */
    ::-webkit-scrollbar-track {
        //-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
        //border-radius: 10px;
    }
    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
        //border-radius: 10px;
        background: #c1c1c1;
        //-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5);
    }
    ::-webkit-scrollbar-thumb:window-inactive {
        //background: #878787!important;
        right: 0 !important;
        //border-left: 1px solid #555;
        //border-right: 1px solid #555;
        //background: rgba(255,0,0,0.4);
    }
}

.frame-main-area {
    @include main-layout;
    .show-overflow-tooltip-partner {
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 400px;
    }
}

.frame-right-area {
    @include main-layout;
    height: 100%;
}

.frame-main-content {
    background: rgba(241, 245, 250, 1);
    height: calc(100% - 25px - 38px);
    overflow: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.frame-main-footer {
    @include main-layout;
    font-size: 12px;
    height: 25px;
    text-align: center;
    line-height: 25px;
    top: auto;
    left: 193px;
}
