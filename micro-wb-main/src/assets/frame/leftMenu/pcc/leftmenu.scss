/**
* Created by TurboC on 2019/08/01.
* 一级菜单高度、一级菜单背景
*/
// @import "../../../mixins/mixins";

.frame-main-menu {
    @include main-layout;
    background: #f9f9f9;
    background-color: #f0f4f8;
    overflow: hidden;
    overflow-y: auto;
    border: 1px solid #dee8f8;
    z-index: 99;

    .el-menu {
        background: #f0f4f8;
    }
    .el-submenu .el-menu {
        background: #f1f1f1;
    }
    .el-menu-item, .el-submenu__title {
        height: 40px;
        line-height: 40px;
        font-size: 13px;
        color: #787878;
    }
    .menu-item-title-ellipsis,.submenu-title-ellipsis .el-submenu__title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        padding-right: 30px;
        //padding-left: 20px;
        //padding-right: 20px;
    }
    .el-submenu__title {
        font-weight: 600;
    }
    .el-menu-item.is-active {
        background-color: #e8eff2;
        color: $wb-theme-color;
        border-right: 4px solid #3878d2;
        .el-submenu__icon-arrow {
            right: 16px;
        }
    }
    .el-submenu__title:hover, .el-menu-item:hover {
        background-color: #e8eff2;
    }
    .el-menu-item:focus, .el-submenu__title:focus {
        background-color: #fdfdfd;
    }
    ul li {
        border-bottom: 1px solid #ebebeb;
    }
    .el-menu--collapse .hiddeName {
        display: none;
    }
}
.frame-main-menu > .el-menu > div > .el-menu-item {
    font-weight: 600;
}

// webbas项目菜单
.wb_01 {
    background: #4fc2f9
}

.wb_02 {
    background: #d5a817;
}
.wb_01, .wb_02 {
    width: 21px;
    height: 21px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    overflow: hidden;
    vertical-align: middle;
    color: #FFF;
    font-size: 1.4rem;
    display: inline-block;
    line-height: 15px;
    text-align: center;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    margin-right: 3px;
    i:before {
        width: 21px;
        height: 21px;
        color: #fff;
    }
}

// PCC项目个性化：PCC项目菜单
.PCC_00, .PCC_01, .PCC_02, .PCC_03, .PCC_05, .PCC_flow_01, .PCC_statist_01, .PCC_FPL_01, .PCC_FPL_02, .PCC_FPL_03, .PCC_FPL_04,
.PCC_FPL_05, .PCC_FPL_06,
.xxl, .PCC_06, .PCC_07, .PCC_09, .PCC_08, .PCC_MATE_01, .PCC_10 , .PS_PCC_00, .PS_PCC_01, .PS_PCC_02,
.PS_PCC_03, .PS_PCC_05, .PS_PCC_07, .PS_PCC_09, .PS_PCC_10, .PCC_search_01, .PS_PCC_search_01, .PCC_DEM_01, .PCC_order_01, .PS_PCC_order_01, .PCC_operateManagement_01{
    width: 21px;
    height: 21px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    overflow: hidden;
    vertical-align: middle;
    color: #FFF;
    font-size: 1.4rem;
    display: inline-block;
    line-height: 15px;
    text-align: center;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    margin-right: 3px;
    i:before {
        width: 21px;
        height: 21px;
        color: #fff;
    }
}
.PCC_00 {
    background: #4fc2f9
}
.PS_PCC_00{
    background: #4fc2f9
}
.PCC_08 {
    background: #63d3ba;
}
.PCC_01 {
    background: #63d3ba;
}
.PS_PCC_01 {
    background: #63d3ba;
}
.PCC_02 {
    background: #ae8ce3;
}
.PS_PCC_02 {
    background: #ae8ce3;
}
.PCC_FPL_01 {
    background: #a77a94;
}
.PCC_FPL_02 {
    background: #A27D35;
}
.PCC_FPL_03 {
    background: #a77a94;
}
.PCC_FPL_04 {
    background: #63d3ba;
}
.PCC_FPL_05 {
    background: #83a0cc;
}
.PCC_FPL_06 {
    background: #FFD700;
}
.xxl {
    background: #63d3ba;
}

.PCC_03 {
    background: #f8b281;
}

.PS_PCC_03 {
    background: #f8b281;
}

.PCC_05 {
    background: #FF3366;
}

.PS_PCC_05 {
    background: #FF3366;
}

.PCC_04 {
    background: #ff7eb3;
}
.PCC_flow_01 {
    background: #ff7eb3;
}
.PCC_statist_01 {
    background: #63d3ba;
}
.PCC_06 {
    background: #6489d7;
}
.PCC_07 {
    background: #95c936;
}

.PS_PCC_07 {
    background: #95c936;
}

.PCC_09 {
    background: #83a0cc;
}

.PS_PCC_09 {
    background: #83a0cc;
}

.PCC_MATE_01 {
    background: #fe9166;
}
.PCC_10 {
    background: #fe9166;
}

.PS_PCC_10 {
    background: #fe9166;
}

.PCC_DEM_01 {
    background: #fe9166;
}
.wf_02 {
    background: #a0d911;
}
.wf_0201 {
    background: #a0cfff;
}
.wf_0202 {
    background: #a77a94;
}
.wf_0401 {
    background: #3c763d;
}
.wf_020201{
    background: #9E9E9E;
}
.wf_020202{
    background: #e2b78d;
}
.PCC_order_01,.PS_PCC_order_01, .PCC_operateManagement_01{
    background: #fe9166;
}
.PCC_search_01, .PS_PCC_search_01{
    background: #95c936;
}

.el-menu--vertical {
    // 修改滚动条颜色---设置滚动条的样式--start
    ::-webkit-scrollbar {
        width: 5px;
    }
    /* 滚动槽 */
    ::-webkit-scrollbar-track {
    }
    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
        background: #DCDFE6;
    }
    .el-menu--popup {
        max-height: 500px;
        overflow-y: scroll;
        .el-menu-item {
            .el-submenu__icon-arrow {
                right: 10px;
            }
        }
    }
}



