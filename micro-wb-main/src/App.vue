/**
 * @author: TurboC
 * @date: 2020-12-16 20:18:14
 */
<template>
  <div v-if="loginStatus"
       id="app">
    <div class="wb-main">
      <div v-if="navigatorCode === '1' || navigatorCode === '3'">
        <header-area v-if="isDomain"
                     ref="aspHeaderArea"
                     :is-collapses="isCollapse"
                     @watchHomeBtn="clickHomeBtn"
                     @pageReload="reload"
                     @watchCollapse="navCollapse">
        </header-area>
        <div :style="mainAreaStyleComputed"
             class="frame-main-area">
          <left-area v-if="isDomain"
                     ref="aspLeftArea"
                     :menu-list="navigatorCode === '1' ? $store.state.app.leftMenuList : $store.state.app.leftForTopMenuList"
                     :menu-history="menuHistory"
                     :is-collapse="isCollapse"
                     @watchMenuReload="menuReload">
          </left-area>
          <div :style="rightAreaStyleComputed"
               class="frame-right-area">
            <breadcrumb-area v-if="isDomain"
                             ref="aspBreadcrumbArea"
                             :current-pagename="$store.state.app.currentPageName"
                             :current-path="$store.state.breadcrumbApp.currentPath"
                             :i-frameurl="$store.state.app.iFrameUrl"
                             @watchHomeBtn="clickHomeBtn">
            </breadcrumb-area>
            <div :style="contentAreaStyleComputed"
                 class="frame-main-content">
              <template>
                <div id="subapp-viewport"></div>
              </template>
            </div>
          </div>
          <div v-show="false">
            <com-dialog></com-dialog>
          </div>
          <div v-if="isDomain"
               :style="[{visibility: showCopyRight ?'unset':'hidden'}]"
               class="frame-main-footer">{{ copyrightContent }}
          </div>
        </div>
      </div>
      <div v-if="navigatorCode === '2'">
        <header-area v-if="isDomain"
                     ref="aspHeaderArea"
                     :is-collapses="isCollapse"
                     :menu-history="menuHistory"
                     @watchHomeBtn="clickHomeBtn"
                     @pageReload="reload"
                     @watchCollapse="navCollapse">
        </header-area>
        <div class="CommonLylConfigMenu2" :class="isCollapse ? 'active' : ''">
          <el-popover
            :visible-arrow="false"
            popper-class="CommonLylConfigMenu2-popover"
            placement="right"
            width="auto"
            ref="CommonLylConfigMenu2Popover"
            trigger="hover"
          >
            <div
              class="CommonLylConfigMenu2-con"
              @click="navCollapse(!isCollapse)"
            >
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div
              v-show="
                currentRouterName !== '/home' &&
                currentRouterName !== '/frame/framePage' &&
                isDomain
              "
              slot="reference"
              class="CommonLylConfigMenu2-left"
            >
              <div class="a">
                <i class="el-icon-caret-left"></i>
              </div>
            </div>
          </el-popover>
        </div>
        <div
          v-if="
            currentRouterName === '/home' ||
            currentRouterName === '/frame/framePage'
          "
        >
          <div
            v-show="isDomain"
            @click="navCollapse(!isCollapse)"
            class="CommonLylConfigMenu"
            :class="isCollapse ? 'active' : ''"
          >
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <div :style="mainAreaStyleComputed"
             class="frame-main-area">
          <div :style="rightAreaStyleComputed"
               class="frame-right-area">
            <breadcrumb-area v-if="isDomain"
                             ref="aspBreadcrumbArea"
                             :current-pagename="$store.state.app.currentPageName"
                             :current-path="$store.state.breadcrumbApp.currentPath"
                             :i-frameurl="$store.state.app.iFrameUrl"
                             @watchHomeBtn="clickHomeBtn">
            </breadcrumb-area>
            <div :style="contentAreaStyleComputed"
                 class="frame-main-content">
              <template>
                <div id="subapp-viewport"></div>
              </template>
            </div>
          </div>
          <div v-show="false">
            <com-dialog></com-dialog>
          </div>
          <div v-if="isDomain"
               :style="footerAreaStyleComputed"
               class="frame-main-footer">{{ copyrightContent }}
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else
       id="subapp-viewport"></div>
</template>

<script>
import actions from '@/micro/actions'
import HeaderArea from '@/views/frame/header-area/index'
import LeftArea from '@/views/frame/left-area/index'
import BreadcrumbArea from '@/views/frame/breadcrumb-area/index'
import comDialog from '@/views/frame/commonDialog/emptyForm'
// import util from '@/utils/util'
import { mapState } from 'vuex'
const {
  authMicroApps,
  projectConfig,
  pageConfig
} = require('../../config.json')
export default {
  name: 'App',
  provide () {
    return {
      reload: this.reload
    }
  },
  watch: {
    visibleHeight () {
      // 路由传给子应用
      actions.setGlobalState({
        msg_type: 'contentH',
        contentH: sessionStorage.main_content_height ? sessionStorage.main_content_height : '0px'
      })
    },
    'navigatorCode' (val) {
      if (val) {
        this.$nextTick(() => {
          this.initLayout()
        })
      }
    }
  },
  components: {
    HeaderArea,
    LeftArea,
    BreadcrumbArea,
    comDialog
  },
  data () {
    return {
      aspHeaderArea: undefined,
      aspLeftArea: undefined,
      aspBreadcrumbArea: undefined,
      menuRouterTab: true,
      isCollapse: false,
      menuHistory: '',
      visibleHeight: this.dynamicContentHeight(),
      navIndex: parseInt(this.$store.state.app.navMemory.navIndex),
      topNavIndex: parseInt(this.$store.state.app.navMemory.topNavIndex)
      // // PCC项目个性化：动态开关Iframe框的滚动条
      // pageOverFlowShow: this.mainPageOverFlow()
    }
  },
  computed: {
    showCopyRight () {
      const LISTALL = sessionStorage.LISTALL ? JSON.parse(sessionStorage.LISTALL) : {}
      const { domainConfig, platformConfig } = LISTALL
      let showCopyRight = true
      if (platformConfig.showCopyRight) showCopyRight = platformConfig.showCopyRight === 'true'
      if (domainConfig.showCopyRight) showCopyRight = domainConfig.showCopyRight === 'true'
      if (projectConfig.footerHeight === 0 ) showCopyRight = false
      return showCopyRight
    },
    copyrightContent () {
      const LISTALL = sessionStorage.LISTALL ? JSON.parse(sessionStorage.LISTALL) : {}
      const { domainConfig, platformConfig } = LISTALL
      const copyright = domainConfig.copyright || platformConfig.copyright || '中国移动·版权所有'
      return copyright
    },
    // 计算内嵌状态
    isDomain () {
      const compose = sessionStorage[projectConfig.operator] ? JSON.parse(sessionStorage[projectConfig.operator]) : {}
      if (compose.ssoFrom && compose.ssoFrom === '$$project_name_sso$$' && compose.ssoType && compose.ssoType === '1') {
        return false
      }
      const parentPathName = window.parent.location.pathname
      const pathName = window.location.pathname
      return parentPathName === pathName
    },
    // 计算登陆状态
    loginStatus () {
      return sessionStorage[projectConfig.operator] !== undefined
    },
    // 计算顶部区域高度
    mainAreaStyleComputed () {
      return { top: this.isDomain && this.aspHeaderArea ? this.aspHeaderArea.getAreaHeightPX() : '0px' }
    },
    // 计算左部菜单宽度
    rightAreaStyleComputed () {
      return { left: this.isDomain && this.aspLeftArea ? this.aspLeftArea.getAreaWidth() : '0px', padding: '0px' }
    },
    // 计算内容可视区域的样式
    contentAreaStyleComputed () {
      const tsu = this.$store.state.app.iFrameUrl
      return {
        padding: tsu === '' ? '8px' : '0',
        overflow: tsu === '' ? 'auto' : 'hidden',
        height: this.visibleHeight
      }
    },
    // 底部区域的样式
    footerAreaStyleComputed () {
      const style = {}
      if (!this.showCopyRight) {
        Object.assign(style, { visibility: 'hidden' })
      }
      if (this.aspLeftArea === undefined) {
        Object.assign(style, { left: '0px' })
      }
      return style
    },
    ...mapState({
      navigatorCode: state => state.app.navigatorCode,
      userSettings: state => state.app.userSettings
    })
  },
  mounted () {
    // console.log('main  app   created', window.location.href)
    // 判断访问的页面是否在注册中的微应用中
    if (!this.loginStatus && authMicroApps.map(item => item.microAppPre).includes(window.location.pathname)) {
      const firstUrl = `${window.location.pathname}${window.location.hash}`
      sessionStorage.setItem('firstUrl', firstUrl)
    } else {
      sessionStorage.removeItem('firstUrl')
    }
    this.initLayout()
    this.aspHeaderArea = this.$refs.aspHeaderArea
    this.aspLeftArea = this.$refs.aspLeftArea
    this.aspBreadcrumbArea = this.$refs.aspBreadcrumbArea
    // 监听页面窗口大小变化
    window.onresize = () => {
      // 小于1280 激活折叠窗口
      // const width = document.documentElement.clientWidth
      // this.isCollapse = this.isCollapse ? this.isCollapse : width < 1280
      // 监听高度
      this.visibleHeight = this.dynamicContentHeight()
      sessionStorage.setItem('main_content_height', this.visibleHeight)
    }
    /** 获取并计算窗口高度，并记录备份 */
    this.visibleHeight = this.dynamicContentHeight()
    sessionStorage.setItem('main_content_height', this.visibleHeight)
    // 存储菜单信息及构建菜单
    if (!this.$store.state.app.addRouter.status) {
      this.$menu.loadMenuInStore(this)
    }
    /** 监控浏览器前进后端事件 */
    document.onmouseover = function () {
      // console.log('mouse do sth inner!!!!!!')
      window.innerDocClick = true
    }
    document.onmouseleave = function () {
      // console.log('mouse do sth outer!!!!!!')
      window.innerDocClick = false
    }
    // 此处hashchange事件只会在浏览器的历史记录发生变化（激活）菜蔬触发
    window.addEventListener('hashchange', () => {
      if (!window.innerDocClick) {
        // console.log('window.addEventListener("hashchange") click outside window')
        // 点击浏览器触发页面跳转事件
        actions.setGlobalState({
          msg_type: 'browserHashchange'
        })
      }
    }, false)
    const _this = this
    this.$nextTick(() => {
      _this.initMenuDealtNums()
    })
    // 监听sessionStorage的setitem事件
    window.addEventListener('setItemEventRouterName', this.setNemu)
  },
  destroyed() {
    window.removeEventListener('setItemEventRouterName', this.setNemu)
  },
  created () {
  },
  methods: {
    setNemu(e) {
      if (e.key === 'lylRouterCurrentName') {
        this.currentRouterName = e.newValue
        // console.log('触发了', this.currentRouterName)
      }
    },
    // 计算页面布局
    initLayout () {
      this.aspHeaderArea = this.$refs.aspHeaderArea
      this.aspLeftArea = this.$refs.aspLeftArea
      this.aspBreadcrumbArea = this.$refs.aspBreadcrumbArea
      this.visibleHeight = this.dynamicContentHeight()
    },
    clickHomeBtn () {
      const microList = sessionStorage.microAppList ? JSON.parse(sessionStorage.microAppList) : []
      let microAppPre = pageConfig.homePage.microAppPre
      microList.forEach(microItem => {
        if (microItem.name === pageConfig.homePage.microAppName) {
          microAppPre = microItem.routeName
        }
      })
      const routerPath = microAppPre + '#' + pageConfig.homePage.routerPath
      const routerName = pageConfig.homePage.routerName
      const titleName = pageConfig.homePage.titleName
      window.history.pushState({}, titleName, routerPath)
      // window.location.reload()
      this.$store.commit('breadcrumbApp/setCurrentPath', [
        {
          title: titleName
        }
      ])
      this.menuHistory = routerName + Math.random()
    },
    // 菜单栏文字 icon 切换
    navCollapse (val) {
      this.isCollapse = val

      this.$nextTick(() => {
        if (this.$refs.aspHeaderArea) {
          this.$refs.aspHeaderArea.msRefresh()
        }

        const footerDom = document.querySelector('.frame-main-footer')
        if (footerDom && this.$route.path === '/') {
          footerDom.style.display = 'none'
        }

        this.$refs.CommonLylConfigMenu2Popover.doClose()
      })
    },
    // 菜单刷新路由
    menuReload () {
      this.reload()
    },
    // 计算内容可视区域的高度
    dynamicContentHeight () {
      const clientHeight = document.documentElement.clientHeight
      let headerAreaHeight = 0
      let footerAreaHeight = 0
      if (this.aspHeaderArea !== undefined && this.loginStatus) {
        headerAreaHeight = this.isDomain ? this.aspHeaderArea.getAreaHeightNumber() : 0
        footerAreaHeight = projectConfig.footerHeight
      }
      let breadcrumbAreaHeight = 0
      if (this.aspBreadcrumbArea !== undefined && this.loginStatus) {
        breadcrumbAreaHeight = this.isDomain ? this.aspBreadcrumbArea.getAreaHeightNumber() : 0
      }
      const contentHeight = clientHeight - headerAreaHeight - breadcrumbAreaHeight - footerAreaHeight + 'px'
      return contentHeight
    },
    // 菜单刷新路由
    reload () {
      this.menuRouterTab = false
      this.$nextTick(() => {
        this.menuRouterTab = true
      })
    },
    /**
     * 初始化菜单待办数
     */
    initMenuDealtNums () {
      const _t = this
      const menuDealtResourceIds = this.$store.state.menuApp.menuExtendIds || [] // 获取需要代办数的菜单ids
      if (this.$util.isEmptyArray(menuDealtResourceIds)) return // 当没有需要获取代办数时，无需请求接口
      this.$aspHttps.asp_Post(this.$apiConfig.dealtsPathPrefix + '/getResourceExtend', { resourceIds: menuDealtResourceIds }).then((response) => {
        if (_t.$reponseStatus(response) && !_t.$util.isEmptyArray(response.data)) {
          _t.$store.commit('menuApp/setMenuDealtNum', response.data || [])
        }
      })
    }
  }
}
</script>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
}
#subapp-viewport {
  height: 100%;
}
#subapp-viewport > div {
  height: 100%;
}
</style>

<style lang="scss">
.lylCommonWrapperPadding {
  .wbMainWrapper .frame-main-area .frame-right-area .frame-main-content {
    padding: 8px;
  }
}

.CommonLylConfigMenu2-con {
  border: 1px solid rgba(0, 0, 0, 0.6);
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  border-radius: 7px;
  padding: 4px 8px;
  cursor: pointer;
  align-items: center;
  width: 40px;
  height: 34px;
  span {
    border-radius: 10px;
    background: #f7fcff;
    width: 100%;
    height: 2.4px;
  }
}
.CommonLylConfigMenu2-popover {
  min-width: 0px !important;
  padding: 0px !important;
  background: transparent !important;
  border: 0 !important;
  border-radius: 7px;
}
.CommonLylConfigMenu2-left {
  .a {
    width: 13px;
    background: #333;
    height: 30px;
    border-radius: 5px 0 0 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #333;
    i {
      color: #fff;
    }
  }
}
.CommonLylConfigMenu2 {
  position: fixed;
  right: 0px;
  top: 8px;
  z-index: 9999;
  &.active {
    top: 97px;
  }
}

.CommonLylConfigMenu {
  width: 40px;
  height: 34px;
  cursor: pointer;
  position: fixed;
  right: 8px;
  top: 8px;
  z-index: 9999;
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 7px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  padding: 4px 8px;
  &.active {
    top: 97px;
  }
  span {
    border-radius: 10px;
    background: #f7fcff;
    width: 100%;
    height: 2.4px;
  }
}

.wbMainWrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  .frame-main-area {
    position: relative;
    flex: 1;
    min-height: 0;
    .frame-right-area {
      display: flex;
      flex-direction: column;
      position: static;
      .frame-main-content {
        flex: 1;
        min-height: 0;
        // padding: 8px;
      }
    }
  }
}
</style>
