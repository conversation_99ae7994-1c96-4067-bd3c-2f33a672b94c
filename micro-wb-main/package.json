{"name": "main", "author": "TurboC", "description": "主框架", "port": 8010, "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@better-scroll/core": "^2.4.2", "asp-smart-layout": "3.0.5", "asp-smart-ui": "^1.1.4", "axios": "^1.7.7", "babel-polyfill": "^6.26.0", "concurrently": "^5.0.2", "core-js": "^3.4.4", "crypto-js": "4.0.0", "custom-event-polyfill": "^1.0.7", "element-theme-chalk": "^2.15.6", "element-ui": "2.15.5", "es6-promise": "^4.2.8", "fetch-polyfill": "^0.8.2", "js-base64": "^3.7.2", "js-cookie": "^2.2.1", "mockjs": "^1.1.0", "qiankun": "2.9.3", "qs": "^6.13.0", "rxjs": "^6.5.4", "terser-webpack-plugin": "^4.2.3", "vue": "2.6.14", "vue-router": "^3.1.6", "vuex": "^3.1.3", "vuex-persistedstate": "^4.1.0", "webpack-theme-color-replacer": "^1.3.26", "whatwg-fetch": "^3.6.2", "tinymce": "^5.10.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^5.0.1", "eslint": "^6.7.2", "eslint-plugin-import": "2.27.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "happypack": "^5.0.1", "sass": "1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "2.6.14", "webpack-bundle-analyzer": "^4.4.2", "webpack": "4.47"}, "rules": {"parser": "babel-es<PERSON>"}}