{"pageConfig": {"loginPage": {"microAppName": "login", "microAppPre": "/yundian/osm/login/", "routerPath": "/login", "routerName": "login", "titleName": "登陆"}, "loginIndex": {"microAppName": "sso", "microAppPre": "/yundian/osm/sso/", "routerPath": "/loginIndex", "routerName": "loginIndex", "titleName": "登陆"}, "ssoIndex": {"microAppName": "sso", "microAppPre": "/yundian/osm/sso/", "routerPath": "/ssoIndex", "routerName": "ssoIndex", "titleName": "登陆"}, "e404Page": {"microAppName": "login", "microAppPre": "/yundian/osm/login/", "routerPath": "/404", "routerName": "404", "titleName": "404"}, "homePage": {"microAppName": "system", "microAppPre": "/yundian/osm/system/", "routerPath": "/home", "routerName": "home", "pageId": "wb_01", "titleName": "首页"}, "iframePage": {"microAppName": "system", "microAppPre": "/yundian/osm/system/", "routerPath": "/frame/framePage", "routerName": "framePage", "titleName": "iframe"}, "datePanel": {"microAppName": "system", "microAppPre": "/yundian/osm/system/", "routerPath": "/system/calendar", "routerName": "calendar", "titleName": "日期面板", "isShowDatePanel": true}}, "appServerMicroApps": {"serverAppName": "本地代理转发服务", "devPort": 5000}, "mainFrameConfig": {"mainAppName": "webbas", "microAppPre": "/yundian/osm", "isNeedAppPre": true, "devPort": 8010}, "noAuthMicroApps": [{"microAppName": "login", "microAppPre": "/yundian/osm/login/", "microAppEnter": "/yundian/osm/login/", "isMicroApp": false, "singleOnly": true, "devPort": 8111}, {"microAppName": "sso", "microAppPre": "/yundian/osm/sso/", "microAppEnter": "/yundian/osm/subapp/sso/", "isMicroApp": true, "devPort": 8011}], "authMicroApps": [{"microAppName": "system", "microAppPre": "/yundian/osm/system/", "microAppEnter": "/yundian/osm/subapp/system/", "microFirstUrl": "/", "isMicroApp": true, "devPort": 8012}], "apiConfig": {"proxyConfig": [{"localProxy": "/proxy_webbas", "nginxProxy": "/yundian/osm/api"}], "routerConfig": [{"microAppName": "system", "localRouter": "/operator/", "nginxRouter": "/yundian/osm/system/"}], "unUsableStatusUrlList": ["/web/wf/bpm/processDef/getDefaultNodeBtns", "/web/wf/sys/tools/getInterFaceImpls", "/web/wf/bpm/processDef/variablesTree"], "authPathPrefix": "/proxy_webbas/web/auth/v1", "authUaSimPrefix": "/proxy_webbas/web/ua/sim/it/v1", "supportPathPrefix": "/proxy_webbas/web/support/v1", "managerPathPrefix": "/proxy_webbas/web/partner/v1", "safePathPrefix": "/proxy_webbas/web/security/v1", "authUaPrefix": "/proxy_webbas/web/partner/v1", "idaPathPrefix": "/proxy_webbas/web/ida/bus", "dealtsPathPrefix": "/proxy_webbas/web/partner", "level1cloudstorePathPreFix": "/yundian/osm/api"}, "projectConfig": {"systemName": "osmweb", "isMicroServer": true, "projectNavMemoryKey": "webbas_navMemory", "multiMenu": false, "pageHelpConfig": "pageHelpConfig", "defaultNodeMenuId": "多系统根级菜单节点Id", "domain": "admin", "operator": "domain", "themeColor": "#3498DB", "themeSwitch": false, "customLogout": true, "breadcrumbHeight": 0, "footerHeight": 0, "defaultNavigatorCode": "1", "haveMenuSpan": {"logoTitleSpan": 7, "multiSysBtnSpan": 0, "topMenuSpan": 10, "personalSpan": 7}, "noMenuSpan": {"logoTitleSpan": 12, "multiSysBtnSpan": 0, "topMenuSpan": 0, "personalSpan": 12}, "whiteList": ["/web/security/v1/encrypt/getPublicKey", "/web/security/v1/encrypt/receiveSecurityKey", "/web/security/v1/encrypt/getIgnoreUrl", "/web/auth/v1/captcha/image"], "smartDataPage": "/yundian/osm/previewPages/index.html#/preview/{code}/all"}, "excludeAssetList": ["api.map.baidu"]}