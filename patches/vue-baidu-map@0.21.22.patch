diff --git a/components/map/Map.vue b/components/map/Map.vue
index 94a3a016e207b6b50c834b7bb740259ea3083092..3c96df11bc5ff9a0966461275b0dab8cc9eef216 100644
--- a/components/map/Map.vue
+++ b/components/map/Map.vue
@@ -259,8 +259,8 @@ export default {
             global._initBaiduMap = null
           }
           const $script = document.createElement('script')
-          global.document.body.appendChild($script)
           $script.src = `https://api.map.baidu.com/api?v=2.0&ak=${ak}&callback=_initBaiduMap`
+          global.document.body.appendChild($script)
         })
         return global.BMap._preloader
       } else if (!global.BMap._preloader) {
