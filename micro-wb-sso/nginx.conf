# 登陆鉴权模块
server {
  listen       8011;
  server_name  localhost;
  
  add_header   Access-Control-Allow-Origin *;
  add_header   Access-Control-Allow-Methods 'GET,POST';
  add_header   Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

  location / {
    root   /opt/aspire/product/liuyong/nginx/html/micro-wb-login;
    index  index.html index.htm;
  }
  location /nginx_webbas/ {
    proxy_pass http://************:7001/;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Cookie $http_cookie;
    client_max_body_size 1000m;
    fastcgi_buffers 8 256k;
    fastcgi_buffer_size 64k;
    gzip on;
    gzip_min_length 50k;
    gzip_comp_level 5;
    gzip_vary on;
  }

  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root   /usr/local/web/micro-wb-login;
  }
}
 