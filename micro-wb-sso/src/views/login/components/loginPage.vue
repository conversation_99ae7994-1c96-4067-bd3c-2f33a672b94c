/**
* 中间跳转页面
*/

<template>
  <div v-loading="true"
       style="height: 100%"
       element-loading-text="正在跳转中..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="hsla(0,0%,100%,.9)">
  </div>
</template>

<script>
import ssoLogin from '@/views/login/ssologin'
import util from '@/utils/util'
import mixins from './mixins.js'
export default {
  name: 'loginIndex',
  mixins: [mixins],
  components: {},
  data () {
    return {
      param: {}
    }
  },
  watch: {},
  created () { },
  async mounted () {
    this.param = {
      loginType: this.getUrlQueryString('loginType') // 登录方式
    }
    try {
      await this.initDataAPI()
      await this.ssoIndexLogin()
    } catch (e) {
      console.info(e)
      this.goNoPage()
    }
  },
  methods: {
    /** 获取query参数 */
    getUrlQueryString (name) {
      const query = window.location.hash.split('?')
      if (query && query.length === 2) {
        const vars = query[1].split('&')
        for (let i = 0; i < vars.length; i++) {
          var pair = vars[i].split('=')
          if (pair[0] === name) {
            return pair[1]
          }
        }
      }
      return (false)
    },
    /** sso单点登录 */
    async ssoIndexLogin () {
      let compose = {}
      const composeStr = this.$main_tools.sessionStorage[this.$projectConfig.operator]
      if (composeStr) {
        compose = JSON.parse(composeStr)
      }
      let menuTree = this.$main_tools.sessionStorage.menuTree
      menuTree = menuTree ? JSON.parse(menuTree) : ''
      if (compose && compose.ssoFrom && compose.ssoFrom === '$$project_name$$') {
        await this.goToTargetPage(menuTree)
      } else {
        // 单点登陆接口
        const requestParmas = {}
        const response = await this.$aspHttps.asp_Post(this.$apiConfig.authPathPrefix + '/login/getUserRight', requestParmas).catch((e) => {
          console.info(e)
          this.goNoPage()
        })
        if (Object.prototype.hasOwnProperty.call(response, 'data') && Object.prototype.hasOwnProperty.call(response, 'status') && response.status.toString() === '200') {
          // 单点登录方式，默认为空，用于控制菜单显隐，默认展示菜单，隐藏菜单传递：1
          const loginType = this.param.loginType || 'password'
          ssoLogin.saveInfo(response, loginType, undefined, undefined, undefined, undefined, this)
          const data = response.data.data ? response.data.data : response.data // 兼容平台侧返回的数据格式
          const { menuTree } = data
          await this.goToTargetPage(menuTree)
        } else {
          this.$message.error(response.message)
          util.relogin()
        }
      }
    },
    goNoPage () {
      this.$router.push({ path: '/404' })
    },
    /**
     * 跳转至目标页面
     * ssoType:
     * 0-显示菜单（默认—）；
     * 1-不显示菜单列表；
     * 2-标识单点登录完成后会个性化独立跳转方式（window.location.href）；
     * 3-访问页面为嵌套页面（经iframe嵌套的，且需要菜单的；若不需要菜单可设置为1）
     */
    async goToTargetPage (menuTree) {
      // 先执行初始化
      await this.getCodeData()
      await this.initDivision()
      await this.postThemeData()
      let path = util.getFirstMenuUrl(menuTree) || ('/' + this.$mainFrameConfig.microAppPre + '/')
      path = this.$main_tools.menu.currentRouterPrefix(path)
      const firstUrl = this.$main_tools.sessionStorage.getItem('firstUrl')
      if (firstUrl) {
        path = firstUrl
        this.$main_tools.sessionStorage.removeItem('firstUrl')
      }
      window.history.pushState({}, 'title', path)
      window.location.reload()
    }
  }
}
</script>
