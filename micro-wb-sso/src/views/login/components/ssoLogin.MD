单点登录场景及验证场景；

1、账号登录：（4A登录）【需要后端启动4A服务组件、模拟验证桩】
 1.1、登录页面加载处理，页面地址：micro-wb-login\src\views\login\components\login\SSO4A.vue
 1.2、此处输入的是主账号的用户名和密码；
    说明：
      1)、单点登录分：主账号和从账号；
        从账号，就是我们正常的账号登录入口输入的用户名和密码，例如：账号B；
        主账号，就是要登录到我们系统来的三方平台的用户名和密码，例如：账号A；
      2）、三方平台的用户名和密码（账号A）不再我们的管理平台，需要在后端登录鉴权模块设置一个主从账号映射关系；
        然后，账号A就可以拥有账号B的所有权限，并且以账号B的身份访问我们自己的管理平台；
 1.3、登录过程（完整/全过程）：
  1.3.1、先输入主账号用户名/密码，再输入图形验证码（需要图形验证码时）；
  1.3.2、（当开启短信验证功能时）点击获取短信验证码，此时会向后端发起获取短信验证码接口请求：/web/ua/v1/login/valid，
      同时将1.3.1输入的信息发送给后端进行校验；
      参数：
      params = {
                  mainUserName: asp_RSAKey.RSAEncrypt(this.ssoMsg.username),
                  mainPwd: asp_RSAKey.RSAEncrypt(this.ssoMsg.password),
                  captchaCode: asp_RSAKey.RSAEncrypt(this.ssoMsg.checkCode),
                  contextPath: this.getIpHost().contextPath,
                  ip:  this.getIpHost().ip
                }
      PS：此时会额外传递本地IP和端口号给到后端，方便后端进行url拼接；

      仅当，输入信息都成功就会发送短信验证码到用手机上，用户输入短信验证码，发起登录步骤1.3.3操作；
      否则会清空图形验证码，需要重新输入；
  1.3.3、（当未开启短信验证功能时，忽略1.3.2的步骤）发起登录接口请求：/web/ua/v1/login/validConfirm
      参数：
      params = {
                  mainUserName: asp_RSAKey.RSAEncrypt(this.ssoMsg.username), // 登录名
                  mainPwd: asp_RSAKey.RSAEncrypt(this.ssoMsg.password), // 密码
                  captchaCode: asp_RSAKey.RSAEncrypt(this.ssoMsg.checkCode), // 图片验证码
                  contextPath: this.getIpHost().contextPath, // 上下文路径
                  ip: this.getIpHost().ip, // 上下文路径ip
                  domain: this.$projectConfig.domain, // 用户所属域
                  confirmCode: asp_RSAKey.RSAEncrypt(this.ssoMsg.phoneCode), // 二次确认密钥，短信验证码
                  mobile: this.ssoPage.mobile, // 手机号码
                  subUserName: this.ssoPage.subUserName // 从帐号
              }
      PS：此时比1.3.2的请求多了，domain、confirmCode、mobile、subUserName这些参数，若没有1.3.2这一步骤，confirmCode、mobile、subUserName这些参数可以传空；
  1.3.4、以上登录步骤完成后，我们仅完成了主账号（账号A）的登录，此时获取的token是4A组件服务的token并非webbas的token，因此并非webbas的
      账号B（从账号）的登录；因此，我们需要完成对webbas上从账号的登录；
    *******、通过1.3.3的接口响应，我们能获取到主要信息如下：
            ssoPage = {
                        indexUrl, // 单点登录后跳转的中间页面地址（已完成ip端口的拼接），默认是：/operator/#/ssoIndex，ssoIndex不可变
                        rUrl, // 单点登录完成后项目侧需要访问的目标地址
                        subUserName, // 用于完成单点登录最后一步从账号登录的，从账号用户名
                        domain, // 单点登录所访问的从账号的所属域
                        loginType, // 单点登录方式（2：自动跳转式单点登录（不传值时默认）；3：不自动跳转单点登录）
                        ssoType, // 单点登录标记（1：默认单点登录）
                        division, // 从账号所属的地域
                        userType, // 4A登录方式：（1:自登陆；2:sso单点登录；3:嵌套登录）其中，1-表示我们现在的登录方式
                        encryptSubUserName, // 加密后的从账号用户名（貌似后端进行加密的）
                        token // 4A服务侧token信息，（主账号的登录标识token）
                      }
    1.3.4.2、对如上参数进行拼接组装，如下：（注意大小写）
        const indexUrl = this.ssoPage.indexUrl + '?url=' + this.ssoPage.rUrl + '&username=' + this.ssoPage.subUserName +
                '&domain=' + this.ssoPage.domain + '&loginType=' + this.ssoPage.loginType +
                '&sso=' + this.ssoPage.ssoType + '&division=' + this.ssoPage.division +
                '&4AUserType=' + this.ssoPage.userType
        this.ssoPage.indexUrl = encodeURI(indexUrl) // 注意，这里要对indexUrl进行一次encodeURI的url编码：

        const params = {
                redirectUrl: this.ssoPage.indexUrl, // 单点登录跳转至从账号所属平台项目地址（本项目webbas地址）
                username: this.ssoPage.encryptSubUserName, // 加密的从账号用户名（注意大小写）
                domain:this.ssoPage.domain, // 从账号所属域
                token: this.ssoPage.token, // 
                '4AUserType': this.ssoPage.loginType
            }
            let url = '/web/auth/v1/sso/verify'
            if (params !== null) { // 注意此处请求参数key和值都需要做URI编码
                params = Object.keys(params).map(function (key) {
                    return encodeURIComponent(key) + "=" + encodeURIComponent(params[key])
                }).join("&")
                url = url + '?' + params
            }
            window.location.href = url
      然后对verifyUrl进行window.location.href的跳转；（此时请求的是webbas服务）
    1.3.4.3、后端接收到'/web/auth/v1/sso/verify'这个接口的请求后，会验证token和从账号用户信息，当接口请求失败时，会统一提示用户名密码错误（安全机制）；当接口请求成功时，后端会直接重定向我们传递的redirectUrl参数的地址（同时把token等信息写在请求头上）；
    1.3.4.4、后面操作步骤同：3、单点登录跳转逻辑（从账号登录到webabs）

  1.4、 4A登录的其他登录方式：2-sso单点登录；3-嵌套登录；
      说明4A登录方式：
      1-自登陆；--------- webbas（项目）平台侧提供登录入口，使用三方平台主账号登录，访问；
      2-sso单点登录；---- 三方平台自己的登录入口，使用三方平台主账号登录，访问webbas（项目）平台；
      3-嵌套登录；------- 三方平台自己的项目平台，通过点击摸个方案入口按钮（或者通过url）访问webbas（项目）平台；

      上述，2、3两种登录方式，前期动作无需关注，有三方平台和4A服务组件完成，后续动作与步骤：1.3.4.2和1.3.4.3大同小异；最终均会执行：1.3.4.4 这一步。

2、单点登录跳转逻辑--webbas单点登录模拟
    --也可配合知识库：【http://10.12.12.254:8090/pages/viewpage.action?pageId=12358930 】的第3点一起看，没有权限可以看如下操作；
  2.1、请求auth服务，http://10.1.202.4:30347/web/auth/v1/sso/getToken，获取token令牌
        请求方法POST {"Content-Type":"application/json"}
        参数：用户名必须加密（示例参数为webbas的加密）
        
      注意：此处username为主账号的用户名（需要加密，加密方式可以利用登录界面协助完成：http://10.1.202.102:30340/operator/#/login）；（注意username大小写）
      {"username":"76c4ed81e2ff21e0aed0b14c1490b8241d1caa3ef0561770e90561d53683baacdc0812b5ae01372e93dab63f2f5e4c9d239bb9b3ee71863e54c701a0fbc8bb45fcd86d132bc357f26afec02dc6c0b20a282a33e6892d2d90d4fd97d8bcd92623661091189cb1496a01ffc5b0c8709d4471d4b4cd23b8013a63162b233eb126a8",
      "domain":"admin"}
      返回结果，获取token
      {
        "status": "200",
        "data": { "token": "359320923741880320" }
      }
  2.2、拼接参数，浏览器访问nginx、模拟单点登录页面跳转
      模拟调整到中间页面再跳转目标机构管理页面  
      http://10.1.202.102:30340/web/auth/v1/sso/verify?token=359376785982357504&loginType=2&username=76c4ed81e2ff21e0aed0b14c1490b8241d1caa3ef0561770e90561d53683baacdc0812b5ae01372e93dab63f2f5e4c9d239bb9b3ee71863e54c701a0fbc8bb45fcd86d132bc357f26afec02dc6c0b20a282a33e6892d2d90d4fd97d8bcd92623661091189cb1496a01ffc5b0c8709d4471d4b4cd23b8013a63162b233eb126a8&domain=admin&redirectUrl=http%3a%2f%2f10.1.202.102%3a30340%2foperator%2f%23%2fssoIndex%3fsso%3d1%26username%3dwebbas%26domain%3dadmin%26url%3d%2fsystem%2fdept

      将步骤：2.1获取的token粘贴到上述url的token位置，然后再浏览器上新开一个页签输入地址，回车即可完成跳转和登录；
      后端成功后，会重定向到redirectUrl的地址页面去；

      补充说明（参数说明，及拼接方式说明，与1.3.4基本一致）：
        1)、参数：this.ssoPage = {
                        indexUrl, // 单点登录后跳转的中间页面地址（已完成ip端口的拼接），默认是：/operator/#/ssoIndex，ssoIndex不可变
                        rUrl, // 单点登录完成后项目侧需要访问的目标地址
                        subUserName, // 用于完成单点登录最后一步从账号登录的，从账号用户名
                        domain, // 单点登录所访问的从账号的所属域
                        loginType, // 单点登录方式（2：自动跳转式单点登录（不传值时默认）；3：不自动跳转单点登录）
                        ssoType, // 单点登录标记（1：默认单点登录）
                        division, // 从账号所属的地域
                        userType, // 4A登录方式：（1:自登陆；2:sso单点登录；3:嵌套登录）其中，1-表示我们现在的登录方式
                        encryptSubUserName, // 加密后的从账号用户名（貌似后端进行加密的）
                        token // 4A服务侧token信息，（主账号的登录标识token）
                      }
        2）、对如上参数进行拼接组装，如下：（注意大小写）
          const indexUrl = this.ssoPage.indexUrl + '?url=' + this.ssoPage.rUrl + '&username=' + this.ssoPage.subUserName +
                  '&domain=' + this.ssoPage.domain + '&loginType=' + this.ssoPage.loginType +
                  '&sso=' + this.ssoPage.ssoType + '&division=' + this.ssoPage.division +
                  '&4AUserType=' + this.ssoPage.userType
          this.ssoPage.indexUrl = encodeURI(indexUrl) // 注意，这里要对indexUrl进行一次encodeURI的url编码：

          const params = {
                  redirectUrl: this.ssoPage.indexUrl, // 单点登录跳转至从账号所属平台项目地址（本项目webbas地址）
                  username: this.ssoPage.encryptSubUserName, // 加密的从账号用户名（注意大小写）
                  domain:this.ssoPage.domain, // 从账号所属域
                  token: this.ssoPage.token, // 
                  '4AUserType': this.ssoPage.loginType
              }
              let url = '/web/auth/v1/sso/verify'
              if (params !== null) { // 注意此处请求参数key和值都需要做URI编码
                  params = Object.keys(params).map(function (key) {
                      return encodeURIComponent(key) + "=" + encodeURIComponent(params[key])
                  }).join("&")
                  url = url + '?' + params
              }
          window.location.href = url


3、单点登录跳转逻辑（从账号登录到webabs）

    3.1、前端项目路由拦截地址栏，会拦截到redirectUrl重定向的url及其参数信息；此处会自动判断，当前地址是否符合单点登录的协议标准；
      必要参数：to.patch/toRouterName，必须是/ssoIndex或者ssoIndex；
      若不是，则url参数里必须有如下参数：ss=1，username存在，domain存在，url存在；
      则此时会跳转值ssoPage页面；micro-wb-login\src\views\login\components\ssoPage.vue
    3.2、进入ssoPage，会自动从url上获取如下参数；
    
      this.param = {
          sso: this.getUrlQueryString('sso'), // 单点登录标识
          domain: this.getUrlQueryString('domain'), // 从账号所属域
          userType: this.getUrlQueryString('4AUserType'), // 4A登录方式：（1:自登陆；2:sso单点登录；3:嵌套登录）
          userName: this.getUrlQueryString('username') || this.getUrlQueryString('userName'),// 从账户用户名
          loginType4A: this.getUrlQueryString('loginType'), // 源自4A登录使用 1:自登陆；2:sso单点登录；3:嵌套登录
          ssoType: this.getUrlQueryString('ssoType') // 单点登录方式，是否展示菜单，ssoType=1（屏蔽菜单）,ssoType=2单点登录完成后跳转直接进行window.location.href
      }
    3.3、url上的参数url进行反编译，
      let urlStr = this.getUrlQueryString('url')
      if (urlStr) this.targetUrl = decodeURIComponent(urlStr)
      以下参数(ss=1，username存在，domain存在，url存在；)判断均存在，且之前未登录过即可，请求：http://10.1.202.102:30340/web/auth/v1/login/getUserRight 获取登录信息，并完成登录跳转；【注意，此接口是否正常发起请求，是单点登录是否成功的主要判断依据】

    3.4、单点登录附加功能：
      3.4.1、开启接口加密功能，同正常账号登录方式；
      3.4.2、获取登录初始化数据字典操作；获取地域属性信息；
      3.4.3、登录校验逻辑判断，登录失败，直接重定向至404页面，而非login页面；
      3.4.4、登录跳转逻辑；goToTargetPage（）

