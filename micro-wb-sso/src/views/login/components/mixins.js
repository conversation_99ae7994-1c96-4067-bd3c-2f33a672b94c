/**
 * 混合配置-通用模块
 */
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
export default {
  methods: {
    async getCodeData () {
      const response = await this.$aspHttps.asp_Post(this.$apiConfig.supportPathPrefix + '/dict/listAll?domain=' + this.$projectConfig.domain)
      if (this.$reponseStatus(response)) {
        this.$main_tools.sessionStorage.codeData = JSON.stringify(response.data)
      }
    },
    // 本地存储地域信息
    async initDivision () {
      // 获取地域属性并缓存
      const response = await this.$aspHttps.asp_Post(this.$apiConfig.supportPathPrefix + '/division/listDivisionTree')
      if (this.$reponseStatus(response)) {
        // 存储地域属性树形结构
        localStorage.division = JSON.stringify(response.data)
        // 初始化地域数据
        this.$main_tools.areas.initAreasData(response.data)
      }
    },
    async postThemeData () {
      if (this.$main_tools.store.state.app.themeSwitch) {
        const url = this.$apiConfig.managerPathPrefix + '/userSettings/get'
        const response = await this.$aspHttps.asp_Post(url)
        this.themeData = false
        if (this.$reponseStatus(response)) {
          this.themeData = true
          const themeConfig = {
            curSkinCode: response.data.skinCode,
            navigatorCode: response.data.navigatorCode,
            userSettings: response.data.userSettings
          }
          // 存储主题信息
          this.$main_tools.store.commit('app/setNavigatorCode', response.data.navigatorCode)
          this.$main_tools.store.commit('app/setUserSettings', response.data.userSettings)
          this.$main_tools.sessionStorage.setItem('themeConfig', JSON.stringify(themeConfig))
        }
      }
    },
    // 获取主题数据并缓存

    // 保存界面显示数据--初始化展示
    configDataApi (res) {
      const { platformConfig, domainConfig } = res.data
      // this.platformConfig = platformConfig
      // this.domainConfig = domainConfig
      document.title = domainConfig.title || platformConfig['portal-name']
    },
    getRamNumber () {
      var result = ''
      for (var i = 0; i < 16; i++) {
        result += Math.floor(Math.random() * 16).toString(16) // 获取0-15并通过toString转16进制
      }
      // 默认字母小写，手动转大写
      return result.toUpperCase()
    },
    // 数据加解密
    async initDataAPI () {
      // console.log('ssoPage init /listAll')
      const securityObject = {}
      this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
      let response = await this.$aspHttps.asp_Post(this.$apiConfig.safePathPrefix + '/encrypt/getPublicKey', {})
      if (this.$reponseStatus(response) && Object.prototype.hasOwnProperty.call(response, 'data') && Object.prototype.hasOwnProperty.call(response.data, 'encrypt')) {
        if (response.data.encrypt === '1') {
          // 保存数据
          securityObject.on = 1
          securityObject.aesKeyValue = this.getRamNumber()
          this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          // 启动加密需要sessionStorage中的securityObject的秘钥
          securityObject.whiteList = this.$aspHttps.asp_EncryptPlus(this.$projectConfig.whiteList)
          // 存储白名单
          this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          // 发送AES密钥给后端
          asp_RSAKey.RSASetPublic(response.data.modulus, response.data.exponent)
          const sendKey = asp_RSAKey.RSAEncrypt(securityObject.aesKeyValue)
          response = await this.$aspHttps.asp_PostForm(this.$apiConfig.safePathPrefix + '/encrypt/receiveSecurityKey', { security: sendKey })
          // 保存数据
          if (!(this.$reponseStatus(response))) {
            securityObject.on = 0
            this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          } else if (this.$reponseStatus(response) && response.data) {
            // 更新old
            securityObject.aesKeyValue = response.data.old || securityObject.aesKeyValue
            this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
            // 重新存储白名单
            securityObject.whiteList = this.$aspHttps.asp_EncryptPlus(this.$projectConfig.whiteList)
            this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          }
          // 请求白名单
          response = await this.$aspHttps.asp_Post(this.$apiConfig.safePathPrefix + '/encrypt/getIgnoreUrl', {})
          if (this.$reponseStatus(response) &&
            Object.prototype.hasOwnProperty.call(response, 'data') &&
            Object.prototype.hasOwnProperty.call(response.data, 'whiteList')) {
            securityObject.whiteList = this.$aspHttps.asp_EncryptPlus(response.data.whiteList)
            this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          }
          // 请求配置列表
          response = await this.$aspHttps.asp_Post(this.$apiConfig.supportPathPrefix + '/domainConfig/listAll', { domain: this.$projectConfig.domain })
          if (this.$reponseStatus(response)) {
            this.configDataApi(response)
            this.$main_tools.sessionStorage.setItem('LISTALL', JSON.stringify(response.data))
          }
        } else {
          // 保存数据
          securityObject.on = 0
          this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          // 请求配置列表
          response = await this.$aspHttps.asp_Post(this.$apiConfig.supportPathPrefix + '/domainConfig/listAll', { domain: this.$projectConfig.domain })
          if (this.$reponseStatus(response)) {
            this.configDataApi(response)
            this.$main_tools.sessionStorage.setItem('LISTALL', JSON.stringify(response.data))
          }
        }
      }
    }
  }
}
