/**
* 中间跳转页面
*/

<template>
  <div v-loading="true"
       style="height: 100%"
       element-loading-text="正在跳转中..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="hsla(0,0%,100%,.9)">
  </div>
</template>

<script>
import ssoLogin from '@/views/login/ssologin'
import mixins from './mixins.js'
export default {
  name: 'SsoIndex',
  mixins: [mixins],
  components: {},
  data () {
    return {
      param: {},
      targetName: '/home', // 目标页面地址,与元数据地址一致
      targetUrl: '/home' // 目标页面地址，与路由地址一致
    }
  },
  watch: {},
  created () { },
  async mounted () {
    const { microAppPre, routerPath, routerName } = this.$pageConfig?.loginPage
    if (microAppPre && routerPath && routerName) {
      this.targetUrl = `${microAppPre}#${routerPath}`
      this.targetName = routerName
    }
    this.param = {
      sso: this.getUrlQueryString('sso'),
      ssoType: this.getUrlQueryString('ssoType'), // 单点登录方式，是否展示菜单，ssoType=1
      domain: this.getUrlQueryString('domain'),
      userType: this.getUrlQueryString('4AUserType'), // userType
      userName: this.getUrlQueryString('username') || this.getUrlQueryString('userName'), // 从账户用户名
      loginType4A: this.getUrlQueryString('loginType') // 源自4A登录使用 1:自登陆；2:sso单点登录；3:嵌套登录
    }
    // 4A 登录，嵌套登录就可以开启菜单屏蔽，ssoType = 1
    if (this.param.userType && this.param.loginType4A && this.param.loginType4A === '3' && this.param.userType === '4A') this.param.ssoType = '1'
    const urlStr = this.getUrlQueryString('url')
    if (urlStr) {
      this.targetUrl = decodeURIComponent(urlStr)
      this.targetName = this.targetUrl.split('/')[(this.targetUrl.split('/').length - 1)]
    }
    try {
      await this.initDataAPI()
      await this.ssoIndexLogin()
    } catch {
      this.goNoPage()
    }
  },
  methods: {
    /** 获取query参数 */
    getUrlQueryString (name) {
      const query = window.location.hash.split('?')
      if (query && query.length === 2) {
        const vars = query[1].split('&')
        for (let i = 0; i < vars.length; i++) {
          var pair = vars[i].split('=')
          if (pair[0] === name) {
            return pair[1]
          }
        }
      }
      return (false)
    },
    /** sso单点登录 */
    async ssoIndexLogin () {
      let compose = {}
      const composeStr = this.$main_tools.sessionStorage[this.$projectConfig.operator]
      if (composeStr) {
        compose = JSON.parse(composeStr)
      }
      if (this.param.sso && this.param.sso === '1' && this.param.userName && this.param.domain) { // 判断是否已经单点登录
        if (compose && compose.ssoFrom && compose.ssoFrom === '$$project_name$$') { // 判断单点登录来自何处
          await this.goToTargetPage()
        } else {
          // 单点登陆接口
          const requestParmas = { username: this.param.userName, domain: this.param.domain }
          const response = await this.$aspHttps.asp_Post(this.$apiConfig.authPathPrefix + '/login/getUserRight', requestParmas).catch(() => {
            this.goNoPage()
          })
          if (Object.prototype.hasOwnProperty.call(response, 'data') && Object.prototype.hasOwnProperty.call(response, 'status') && response.status.toString() === '200') {
            // 单点登录方式，默认为空，用于控制菜单显隐，默认展示菜单，隐藏菜单传递：1
            const ssoType = this.param.ssoType ? this.param.ssoType : ''
            // 登录方式：自登陆-login，4A单点登录-sso_4A，权益平台单点登录-sso_right，UPC单点登录-sso_upc，其他方式单点登录-sso_XXX
            const loginType = this.param.loginType ? this.param.loginType : ''
            ssoLogin.saveInfo(response, 'sso', '', loginType, this.param.loginType4A, ssoType, this)
            // 过滤4A自登陆的操作按钮（屏蔽相关按钮权限）
            if (this.param.loginType4A && (this.param.loginType4A === '1' || this.param.loginType4A === '2')) {
              this.$main_tools.sessionStorage.LOGINTYPE = '4A'
              this.$main_tools.menu.operateFilter4AButton('4A', this)
            }
            await this.goToTargetPage()
          } else {
            this.$message.error(response.message)
            this.goNoPage()
          }
        }
      } else {
        this.goNoPage()
      }
    },
    goNoPage () {
      this.$router.push({ path: '/404' })
    },
    /**
     * 跳转至目标页面
     * ssoType:
     * 0-显示菜单（默认—）；
     * 1-不显示菜单列表；
     * 2-标识单点登录完成后会个性化独立跳转方式（window.location.href）；
     * 3-访问页面为嵌套页面（经iframe嵌套的，且需要菜单的；若不需要菜单可设置为1）
     */
    async goToTargetPage () {
      // 先执行初始化
      await this.getCodeData()
      await this.initDivision()
      await this.postThemeData()
      // 跳转入
      const ssoType = this.param.ssoType
      let routeUrl = this.targetUrl ? this.targetUrl.replace('sso=1', `sso=${new Date().getTime()}`) : '/microsystem/#/home'
      // console.log('this.toIframe', toIframe)
      // 初始化Iframe 加载的url信息
      // this.$store.commit('app/setIFrameUrl', '')
      if (ssoType === '3') {
        // 满足Iframe加载切换路由信息--TODO-iframe
        const microList = sessionStorage.microAppList ? JSON.parse(sessionStorage.microAppList) : []
        let microAppPre = this.$pageConfig.iframePage.microAppPre
        microList.forEach(microItem => {
          if (microItem.name === this.$pageConfig.iframePage.microAppName) {
            microAppPre = microItem.routeName
          }
        })
        routeUrl = microAppPre + '#' + this.$pageConfig.iframePage.routerPath + '?' + 'iframe=' + routeUrl
      } else if (ssoType === '2') {
        window.location.href = `${routeUrl}${routeUrl.indexOf('?') > 0 ? '&' : '?'}ssoType=${this.param.ssoType}`
        return
      } else {
        this.$main_tools.store.commit('app/setNavMemory', { router: { path: routeUrl } })
      }
      // 跨应用跳转
      window.history.pushState({}, 'title', `${routeUrl}${routeUrl.indexOf('?') > 0 ? '&' : '?'}ssoType=${this.param.ssoType}`)
      window.location.reload()
    }
  }
}
</script>
