/**
* Created by TurboC on 2019/08/8.
* sso
*/
// import router from '@/router'
// import store from '@/store'
import Cookies from 'js-cookie'

const ssoLogin = {}

// loginAction: 'pssword'-账号登录，'msg'-手机验证码登录 'sso'-sso登陆
// loginType: 登录方式：自登陆-login，4A单点登录-sso_4A，权益平台单点登录-sso_right，UPC单点登录-sso_upc，其他方式单点登录-sso_XXX
// loginType4A: 源自4A登录使用 1:自登陆；2:sso单点登录；3:嵌套登录
// ssoType：单点登录类型，主要用于传递菜单显隐
ssoLogin.saveInfo = (response, loginAction, loginName, loginType, loginType4A, ssoType, _t) => {
  const data = response.data.data ? response.data.data : response.data // 兼容平台侧返回的数据格式
  let { menuTree, curUserInfo, buttonRight, platformConfig, domainConfig } = data
  // 初始化动态路由信息
  _t.$main_tools.store.commit('app/setAddRouter', [])
  // 导航菜单记录 (非数据初始化使用)
  _t.$main_tools.sessionStorage[_t.$projectConfig.projectNavMemoryKey] = JSON.stringify({ navIndex: 0, router: { path: '' }, topNavIndex: _t.$pageConfig.homePage.pageId })

  /* 构建菜单信息,过滤无用菜单 */
  menuTree = _t.$main_tools.menuTreeUtil.filterInvalidMenu(menuTree)
  // if (menuTree.length === 0 || (menuTree[0].children && menuTree[0].children.length === 0)) {
  //   data.menuTree = []
  // }
  // 鉴权按钮转换
  if (buttonRight && Object.keys(buttonRight).length > 0) {
    _t.$main_tools.menu.transformButtonInfo(buttonRight)
  }
  // 对菜单进行封装处理(存储全量菜单)
  menuTree = _t.$main_tools.menuTreeUtil.getMenuTree(menuTree)
  window.menu && window.menu.forEach(item => {
    menuTree.push(item)
  })
  // 存储左侧菜单（一级菜单在顶部、二三级菜单在左侧）
  const currLeftMenuList = _t.$main_tools.menuTreeUtil.getMenuList(menuTree, _t.$pageConfig.homePage.pageId)
  _t.$main_tools.sessionStorage.setItem('setLeftForTopMenuList', JSON.stringify(currLeftMenuList))
  // 保存菜单信息
  _t.$main_tools.sessionStorage.setItem('menuTree', JSON.stringify(menuTree))
  // _t.$main_tools.sessionStorage.themeColor = JSON.stringify(menuTree)
  const leftMenuTree = _t.$main_tools.sessionStorage.menuTree ? JSON.parse(_t.$main_tools.sessionStorage.menuTree) : []
  const topMenuList = _t.$main_tools.sessionStorage.topMenuList ? JSON.parse(_t.$main_tools.sessionStorage.topMenuList) : []
  const topAndLeftMenuList = topMenuList
  _t.$main_tools.sessionStorage.setItem('topAndLeftMenuList', JSON.stringify(topAndLeftMenuList.concat(leftMenuTree)))
  // 保存主题色
  _t.$main_tools.sessionStorage.setItem('theme_color', JSON.stringify(platformConfig['user-default-skin']))
  // 是否开启主题功能
  if (!platformConfig['user-default-skin'] && !platformConfig['user-default-navigator']) {
    _t.$main_tools.store.commit('app/setThemeSwitch', false)
  } else {
    _t.$main_tools.store.commit('app/setThemeSwitch', _t.$projectConfig.themeSwitch)
  }
  /* 构建登陆信息 */
  let headerData = {}
  const userName = curUserInfo.username || ''
  const mobilePhone = curUserInfo.mobilePhone || ''
  if (loginAction !== 'sso') {
    headerData = response.headers ? { X_AUTH_HEADER_TOKEN: response.headers.x_auth_header_token || response.headers.X_AUTH_HEADER_TOKEN } : {}
  } else {
    headerData = {}
  }
  // 同平台 跨平台 同域 交互信息
  const compose = {
    type: 'login', // 状态类型
    sourceUrl: window.location.href, // 访问地址信息
    userInfo: { // 用户信息
      id: curUserInfo.id,
      username: userName,
      mobilePhone: mobilePhone,
      name: curUserInfo.name,
      organizationId: curUserInfo.organizationId,
      organizationName: curUserInfo.organizationName,
      organizationDivision: curUserInfo.organizationDivision,
      domain: curUserInfo.domain,
      divisionList: curUserInfo.divisionList,
      organizationType: curUserInfo.organizationType
    },
    authInfo: buttonRight, // 鉴权信息
    dictInfo: [], // 字典表信息
    platformConfig: platformConfig, // 平台配置信息
    domainConfig: domainConfig, // 域信息
    ssoFrom: loginAction !== 'sso' ? '$$project_name$$' : '$$project_name_sso$$',
    loginType: loginType || '',
    loginType4A: loginType4A || '',
    ssoType: ssoType || '',
    headers: headerData
  }
  const operator = _t.$projectConfig.operator
  _t.$main_tools.sessionStorage.setItem(operator, JSON.stringify(compose))
  /* 构建bootstrap页面登陆时，后端负责清除Cookies信息 */
  const lmsg = `${compose.userInfo.id},${operator}`
  Cookies.set('LMSG', lmsg)

  /* 构建微用户信息 */
  if (data && data.uiAppInfo) {
    _t.$main_tools.sessionStorage.setItem('microAppList', JSON.stringify(data.uiAppInfo))
  }
}

export default ssoLogin
