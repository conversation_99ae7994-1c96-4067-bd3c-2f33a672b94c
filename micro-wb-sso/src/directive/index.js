/**
 * Created by TurboC on 2021/03/12
 *
 * @directiveEvent 自定义指令
 *
 */
const directiveEvent = {}

/*
 * @focusEvent 自动聚焦
 * @focus {Object}  vue
 *
 * el-input
 * 焦点配置
 * v-focus="{
 *   cls: 'el-input',
 *   tag: 'input',
 *   foc: {boolean}
 * }"
 * 移除配置
 * @blur= {boolean}   ### 移除false
 *
 * input 配置
 * v-focus = {boolean}
 *
 * --------------------------------------------------------------- */
directiveEvent.focusEvent = (vm) => {
  vm.directive('focus', function (el, option) {
    const [defClass, defTag] = ['el-input', 'input']
    let value = option.value || true
    if (typeof value === 'boolean') {
      value = {
        cls: defClass,
        tag: defTag,
        foc: value
      }
    } else {
      value = {
        cls: value.cls || defClass,
        tag: value.tag || defTag,
        foc: value.foc || false
      }
    }
    if (el.classList.contains(value.cls) && value.foc) {
      el.getElementsByTagName(value.tag)[0].focus()
    }
  })
}

/*
 *
 * @hasAuth 按钮鉴权操作
 * btnRight {Object}
 * source: auth/v1/login/verify
 *
 * 非鉴权按钮删除
 * --------------------------------------------------------------- */
directiveEvent.hasAuth = (vm, main_tools, projectConfig) => {
  vm.directive('hasAuth', function (el, option) {
    // 获取按钮权限
    const domain = main_tools.sessionStorage[projectConfig.operator]
    const btnRight = domain && JSON.parse(domain).authInfo
    let authStatus = false
    for (const key in btnRight) {
      if (Object.prototype.hasOwnProperty.call(btnRight, key)) {
        if (btnRight[key].some(item => Object.is(item, option.value.btnCode))) {
          authStatus = !authStatus
        }
        // 增加数组类型
        if (option.value.btnCodeArr) {
          option.value.btnCodeArr.map(item => {
            if (btnRight[key].some(iitem => Object.is(iitem, item))) {
              authStatus = true
            }
          })
        }
      }
    }
    async function removeElement (el) {
      return el
    }
    if (!authStatus) {
      if (!!window.ActiveXObject || 'ActiveXObject' in window) {
        removeElement(el).then(() => {
          el.parentNode.removeChild(el)
        })
      } else {
        removeElement(el).then(() => {
          el.remove()
        })
      }
    }
  })
}

directiveEvent.init = (vm, main_tools, projectConfig) => {
  const eventGather = ['focusEvent', 'hasAuth']
  eventGather.forEach(item => {
    directiveEvent[item](vm, main_tools, projectConfig)
  })
}

export default directiveEvent
