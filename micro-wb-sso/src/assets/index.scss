@import "./mixins/mixins";
@import "./font/iconfont.css";

// 全局重置elementUI样式
@import './element/reset-breadcrumb.scss';
@import './element/reset-button.scss';
@import './element/reset-checkbox.scss';
@import './element/reset-collapse.scss';
@import './element/reset-date-editor.scss';
@import './element/reset-dialog.scss';
@import './element/reset-form.scss';
@import './element/reset-input.scss';
@import './element/reset-label.scss';
@import './element/reset-menu.scss';
@import './element/reset-message-box.scss';
@import './element/reset-pagination.scss';
@import './element/reset-radio.scss';
@import './element/reset-select.scss';
@import './element/reset-table.scss';
@import './element/reset-tabs.scss';
@import './element/reset-tag.scss';
@import './element/reset-textarea.scss';
@import './element/reset-tree.scss';
@import './element/reset-upload.scss';
@import './element/reset-cascader.scss';
@import './element/reset-transfer.scss';

// 自定义控件样式
@import './controls/asp-button.scss';

// 框架样式
@import './frame/login.scss';


// 统一设置
html,
body,
#app,
#main {
    height: 100%;
}

h1,
h2,
h3,
h4,
h5 {
    margin: 0;
}
ul {
    margin: 0;
}

ul li {
    list-style: none
}

body {
    font-family: $font-family, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
}

.webbas {
    /* 系统结构 */
    /*重置地域tree节点展示样式*/
    .areas-css {
        margin-bottom: 20px;
        width: 100%;
        min-height:50px;
        max-height:200px;
        overflow:auto;
        border: 1px solid gainsboro;
        padding: 10px 0px 5px;
    }
    .check-areas-change > .el-tree-node > .el-tree-node__children > .el-tree-node .el-tree-node {
        float: left;
    }
    /*修复角色跳转框火狐浏览器显红色版本兼容问题*/
    .fix-fireFox-error input[type=number] {
        box-shadow: none;
    }
    /*手动添加必填按钮*/
    .check-areas > .el-form-item__label:before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
    }
    .overflow-areas-css {
        max-height:320px;
        overflow: hidden;
        overflow-y: scroll
    }
    /*优化角色详情排版布局*/
    .role-info {
        width: 100%;
        .el-form-item__content {
            width: 78%;
        }
    }
    ::-webkit-scrollbar {
        width: 6px;
    }
    /*定义滚动条的轨道颜色、内阴影及圆角*/
    ::-webkit-scrollbar-track {
        background-color: ghostwhite;
        border-radius: 3px;
    }
    /*定义滑块颜色、内阴影及圆角*/
    ::-webkit-scrollbar-thumb {
        border-radius: 7px;
        height: 60px;
        background-color: #cccccc;
    }
}

// .transfer-dialog {
//     .el-dialog__header {
//         border-bottom: 1px solid #dee8f8;
//     }
//     .el-dialog__footer {
//         border-top: 1px solid #dfe9fc;
//         height: 54px;
//         background: #f1f5fa;
//     }
// }
// /*定义穿梭框宽度*/
// .el-transfer {
//     .el-transfer-panel {
//         width: 40%;
//     }
//     .el-transfer__buttons {
//         width: 10%;
//         text-align: center;
//         .el-button {
//             padding: 6px 15px;
//         }
//         .el-button:nth-child(2) {
//             margin: 0px;
//         }
//         .el-button [class*=el-icon-]+span {
//             margin: 0px;
//         }
//     }
// }

