/*
 * @Author: yuxuan
 * @Date: 2024-06-07 16:27:51
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-06-17 16:41:15
 * @Description: file content
 */
/**
 * *内置路由
 * Created by TurboC on 2021/03/12
 */
export const login = [
  {
    path: '/',
    redirect: '/loginIndex'
  },
  {
    path: '/ssoIndex',
    title: '单点登录跳转',
    name: 'ssoIndex',
    component: () => import(/* webpackChunkName: "loginConfig" */ '@/views/login/components/ssoPage')
  },
  {
    path: '/loginIndex',
    title: '单点登录跳转',
    name: 'loginIndex',
    component: () => import(/* webpackChunkName: "loginConfig" */ '@/views/login/components/loginPage')
  },
  {
    path: '/404',
    title: '404',
    name: 'noPage',
    component: () => import(/* webpackChunkName: "loginConfig" */ '@/views/error/404')
  }
]

export default login
