/**
 * @author: TurboC
 * @date: 2020-12-16 20:18:14
 */
import Vue from 'vue'
import App from '@/App'
import store from '@/store'
import VueRouter from 'vue-router'
import routes from '@/router'

// element-ui
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import '../assets/index.scss'

// 全局设置
import directiveEvent from '@/directive'

// 微前端配置
import actions from './actions'
import './public-path'
import './permission'

Vue.config.productionTip = true
Vue.config.devtools = true
Vue.config.debug = true
Vue.use(VueRouter)
Vue.use(ElementUI)

var router = null
let instance = null
/**
 * @name 导出生命周期函数
 */
const lifeCycle = () => {
  return {
    /**
     * @name 微应用初始化
     * @param {Object} props 主应用下发的props
     * @description  bootstrap 只会在微应用初始化的时候调用一次，下次微应用重新进入时会直接调用 mount 钩子，不会再重复触发
     * @description 通常我们可以在这里做一些全局变量的初始化，比如不会在 unmount 阶段被销毁的应用级别的缓存等
     */
    async bootstrap (props) {
      // console.log('micro-wb-login bootstrap props', props)
      // 保存信息
      saveInfo(props)
    },

    /**
     * @name 实例化微应用
     * @param {Object} props 主应用下发的props
     * @description 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
     */
    async mount (props) {
      // console.log('micro-wb-login mount props', props)
      // 保存信息
      saveInfo(props)
      // 注册应用间通信
      registerActions(props)
      // 注册微应用实例化函数
      render(props)
    },

    /**
     * @name 微应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
     */
    async unmount (props) {
      // console.log('micro-wb-login unmount props', props)
      // 注销应用
      instance.$destroy()
      instance.$el.innerHTML = ''
      instance = null
      router = null
    },

    /**
     * @name 手动加载微应用触发的生命周期
     * @param {Object} props 主应用下发的props
     * @description 可选生命周期钩子，仅使用 loadMicroApp 方式手动加载微应用时生效
     */
    async update (props) {
      // console.log('micro-wb-login update props', props)
      // 保存信息
      saveInfo(props)
    }
  }
}

/**
 * @name 注册应用间通信（注入 actions 实例）
 */
const registerActions = (props) => {
  if (!props) { return }
  actions.setActions(props)
  actions.onGlobalStateChange((state, prevState) => {
    // console.log('micro-wb-login微应用观察者, 改变前的值为 ', prevState)
    // console.log('micro-wb-login微应用观察者, 改变后的值为 ', state)
    if (state && (state.msg_type === 'contentH' || state.msg_type === 'router')) {
      Vue.prototype.$contentH = state.contentH
    }
  })
}

const saveInfo = (props) => {
  // 微应用路由基地址
  if (props && props.microAppPre && !Vue.prototype.$microAppPre) {
    Vue.prototype.$microAppPre = props.microAppPre
  }
  // 微应用访问目录
  if (props && props.microAppEnter && !Vue.prototype.$microAppEnter) {
    Vue.prototype.$microAppEnter = props.microAppEnter
  }
  // mainFrameConfig
  if (props && props.mainFrameConfig && !Vue.prototype.$mainFrameConfig) {
    Vue.prototype.$mainFrameConfig = props.mainFrameConfig
  }
  // pageConfig
  if (props && props.pageConfig && !Vue.prototype.$pageConfig) {
    Vue.prototype.$pageConfig = props.pageConfig
  }
  // apiConfig
  if (props && props.apiConfig && !Vue.prototype.$apiConfig) {
    Vue.prototype.$apiConfig = props.apiConfig
  }
  // projectConfig
  if (props && props.projectConfig && !Vue.prototype.$projectConfig) {
    Vue.prototype.$projectConfig = props.projectConfig
  }
  // main_tools
  if (props && props.main_tools && !Vue.prototype.$main_tools) {
    Vue.prototype.$main_tools = props.main_tools
    Vue.prototype.$aspHttps = props.main_tools.aspHttps
    Vue.prototype.$reponseStatus = props.main_tools.reponseStatus
    directiveEvent.init(Vue, props.main_tools, props.projectConfig)
  }
}

/**
 * @name 子应用实例化函数
 * @param {Object} props param0 qiankun将用户添加信息和自带信息整合，通过props传给子应用
 * @description {Array} routes 主应用请求获取注册表后，从服务端拿到路由数据
 * @description {String} 子应用路由前缀 主应用请求获取注册表后，从服务端拿到路由数据
 */
const render = (props) => {
  // 创建路由
  router = new VueRouter({
    // base: window.__POWERED_BY_QIANKUN__ ? '/login/' : '/subapp/login/',
    // mode: 'history',
    routes: routes || []
  })

  // 路由传给主应用
  actions.setGlobalState && actions.setGlobalState({
  })

  // 创建Vue实例
  instance = new Vue({
    name: 'Login',
    router,
    store,
    render: h => h(App)
  }).$mount(props && props.container ? props.container.querySelector('#app') : '#app')

  // https://github.com/umijs/qiankun/issues/601
  // 解决devtools检测不到子应用的问题，开发环境开启
  if (window.__POWERED_BY_QIANKUN__ && process.env.NODE_ENV === 'development') {
    const subDiv = document.createElement('div')
    subDiv.__vue__ = instance
    document.body.appendChild(subDiv)
  }
}

export { lifeCycle, render, router }
