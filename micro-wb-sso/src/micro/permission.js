/**
 * @author: <PERSON>C
 * @date: 2020-12-16 20:18:14
 */
import {
  router
} from './life-cycle'
import qs from 'qs'

router && router.afterEach((to, from) => {
  // console.log('micro-wb-login router.afterEach(), to: ', to)
  // console.log('micro-wb-login router.afterEach(), from: ', from)
})

router && router.beforeEach((to, from, next) => {
  // console.log('micro-wb-login router.beforeEach(), to: ', to)
  // console.log('micro-wb-login router.beforeEach(), from: ', from)
  let toRouterName = null
  if (!to.name) {
    const name = to.fullPath.substr(to.fullPath.lastIndexOf('/') + 1)
    toRouterName = (name && name.length > 0) ? name : to.name
  }
  // let fromRouterName = null
  // if (!from.name) {
  //   const name = from.fullPath.substr(to.fullPath.lastIndexOf('/') + 1)
  //   fromRouterName = (name && name.length > 0) ? name : to.name
  // }
  // 单点登录分流
  let urlParam = {}
  if (toRouterName !== 'ssoIndex') {
    urlParam = !window.location.hash.split('?')[1] ? {} : qs.parse(window.location.hash.split('?')[1])
    urlParam.username = urlParam.username ? urlParam.username : urlParam.userName ? urlParam.userName : ''
    urlParam = window.location.hash.indexOf('/ssoIndex?') > 0 ? {} : urlParam
  }
  if (Object.keys(urlParam).length > 0 && urlParam.sso === '1' &&
      urlParam.username && urlParam.domain && toRouterName !== 'login') { // 判断是否单点登录
    urlParam.indexUrl = urlParam.indexUrl ? urlParam.indexUrl : '/ssoIndex'
    urlParam.url = to.fullPath
    const fullPath = urlParam.indexUrl + '?' + qs.stringify(urlParam)
    next({
      name: 'ssoIndex',
      path: urlParam.indexUrl,
      params: urlParam,
      query: urlParam,
      fullPath: fullPath
    })
    return false
  }
  next()
})
