{"formList": [{"label": "栅格布局", "type": "row", "columnName": "row_1611737317526", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "rowTypeFlex": true, "formFields": [{"span": 24, "childList": [{"label": "", "type": "text", "columnName": "startDesc", "defaultValue": "", "isModel": false, "icon-dev": "iconfont icontext", "isLabelWidth": true, "span": 24, "width": "100%", "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false, "text": ""}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "name": "basic", "labelWidth": 0, "weak-tips": "", "class": "technicalSupportClass", "copyNewVal": "", "defValueType": "userDefined", "closeFormItemBorder": true}]}], "name": "layout", "labelWidth": 160}, {"label": "子表单", "type": "normalChildList", "button": false, "isLabelWidth": false, "columnName": "contactList", "defaultValue": [], "isModel": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "formFields": [{"label": "姓名", "type": "text", "columnName": "name", "defaultValue": "", "isModel": false, "icon-dev": "iconfont icontext", "isLabelWidth": false, "span": 24, "width": "", "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "name": "basic", "parentName": "contactList", "is-table-column": true, "labelWidth": 160, "copyNewVal": ""}, {"label": "手机号码", "type": "text", "columnName": "mobile", "defaultValue": "", "isModel": false, "icon-dev": "iconfont icontext", "isLabelWidth": false, "span": 24, "width": "", "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false, "text": ""}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "name": "basic", "parentName": "contactList", "is-table-column": true, "labelWidth": 160}, {"label": "电子邮箱", "type": "text", "columnName": "email", "defaultValue": "", "isModel": false, "icon-dev": "iconfont icontext", "isLabelWidth": false, "span": 24, "width": "", "operation": [], "rules": [], "hidden": false, "props": {"disabled": false, "readonly": false, "text": ""}, "style": {"word-break": "break-all"}, "isCompare": false, "isNumberType": false, "name": "basic", "parentName": "contactList", "is-table-column": true, "labelWidth": 160}], "hidden": false, "span": 24, "expand": false, "isTitle": false, "isVirtualTable": false, "operation": [], "toolList": [], "show-index": false, "show-single-selection": false, "show-add": false, "show-top-add": false, "show-operation": false, "operation-width": 100, "show-row-number": true, "row-number": 10, "row-number-message": "表格最大行数为10条", "props": {"default-expand-all": true}, "default-data": false, "childList": [], "validate-config": {"validate": false, "defaultText": "请输入唯一值", "type": false, "value": [], "object": []}, "compareProps": [], "selection": false, "isNullRowValidate": true, "nullRowValidateTip": "不允许为空行!", "isCompare": false, "isAlterColumn": false, "tooltipList": [], "name": "layout", "labelWidth": 160, "compCompareStatus": [], "alterColumnStatus": []}, {"label": "", "type": "buttonGroup", "isLabelWidth": false, "columnName": "buttonGroup_1637754183093", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "cacel", "type": "primary", "icon": "el-icon-close", "label": "关闭", "interactive": "", "validateProp": "", "apiName": "", "class": "hollow-with-icon-btn", "default": "show", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": false, "confirmStatus": [], "activeType": "button_group_close_dialog"}], "name": "layout", "labelWidth": 160, "isDialogFooterBtns": true}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "starPostion": "left", "class": "", "exportName": "technicalSupport", "defaultClass": "webbas", "size": "small", "statusList": [], "serverProps": {"localProxy": "/proxy_pcc", "nigxProxy": "/pcc-bof", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "titleName": "系统技术支持"}, "dataConfig": {}, "virtual_model": {"contactList": [{"id": 1611736535337, "new-operate": true, "name": ""}]}, "model": {"text_1611736542429": "", "text_1611736543253": "", "image_1611737220856": "", "contactList": [], "startDesc": ""}, "pre_loading_request_list": [{"apiId": 1637656115237, "isAuto": 1, "apiType": "post+json", "sendTime": "mounted", "apiName": "/web/business/v1/base/user/getSystemTechSupport", "apiParam": "{}", "status": [], "responseParam": "{mapRules:\"data\"}"}]}