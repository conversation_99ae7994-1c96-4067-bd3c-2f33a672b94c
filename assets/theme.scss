$mainColor: $wb-theme-color;
// 复选框
.el-radio__input.is-checked .el-radio__inner,
.el-checkbox__input.is-checked .el-checkbox__inner, 
.el-checkbox__input.is-indeterminate .el-checkbox__inner{
  background-color: $mainColor;
  border-color: $mainColor;
}
.el-select-dropdown__item.selected,
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected,
.el-button--text,
.el-link.el-link--primary,
.el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active,
.el-checkbox__input.is-checked+.el-checkbox__label,
.el-radio__input.is-checked+.el-radio__label,
.el-tabs__item.is-active,
.el-tabs__item:hover,
.el-date-table td.today span,
.aspFildDetail .file{
  color: $mainColor;
}
.el-transfer-panel__item:hover,
.el-date-table td.available:hover,
.el-tabs--border-card>.el-tabs__header .el-tabs__item:not(.is-disabled):hover{
  color: $mainColor;
}
.el-date-table td.current:not(.disabled) span,
.el-tabs__active-bar{
  background-color: $mainColor;
}
.el-radio-button__orig-radio:checked+.el-radio-button__inner {
  background-color: $mainColor;
}
.el-table .descending .sort-caret.descending{
  border-top-color:$mainColor
}
.el-table .ascending .sort-caret.ascending{
  border-bottom-color: $mainColor
}
.el-select .el-input.is-focus .el-input__inner{
  border-color: $mainColor
}
.el-button--primary{
  background: -webkit-gradient(linear, left bottom, left top, from(#4576e4), to(#6f9ef0));
  background: linear-gradient(360deg, #4576e4 0%, #6f9ef0 100%);
  border-color: $mainColor;
  &:hover{
    background: -webkit-gradient(linear, left bottom, left top, from(#4576e4), to(#6f9ef0));
    background: linear-gradient(360deg, #4576e4 0%, #6f9ef0 100%);
    border-color: $mainColor;
    opacity: 0.9;
  }
}
.hollow-no-icon-btn-white {
  font-size: 14px;
  padding: 7px 25px;
  color: rgba(70, 118, 229, 1);
  border-color: #419FFF;
  background: #fff;
}
.hollow-no-icon-btn-white:hover {
  border-color: #419FFF;
  background: #fff;
}

// /* 改变主题色变量 */
// $--color-primary: $wb-theme-color;

// /* 改变 icon 字体路径变量，必需 */
// $--font-path: '~element-ui/lib/theme-chalk/fonts';

// @import "~element-ui/packages/theme-chalk/src/index";
