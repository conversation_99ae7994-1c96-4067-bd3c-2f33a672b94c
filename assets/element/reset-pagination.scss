/**
* Created by TurboC on 2019/08/01.
* 重写分页样式
*/
@import "../mixins/mixins";
.webbas {
    .el-pagination {
        margin-top: 5px;
        .btn-prev {
            background: rgba(255, 255, 255, 1);
        }
        .btn-next {
            background: rgba(255, 255, 255, 1);
        }
        .el-pager {
            .number {
                color: rgba(111, 126, 162, 1);
                background: rgba(255, 255, 255, 1);
            }
            .active {
                background: linear-gradient(360deg, rgba(69, 118, 228, 0.2) 0%, rgba(111, 158, 240, 0.2) 100%);
                border-radius: 2px;
                color: rgba(71, 119, 229, 1);
            }
        }
    }
    .el-pagination .el-select .el-input .el-input__inner {
        padding: 0 15px;
        height: 28px;
        border: unset;
    }
    .el-pagination__editor.el-input .el-input__inner {
        height: 28px;
        box-shadow: 0 0 1px aliceblue;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: $wb-theme-color;
    }
}
