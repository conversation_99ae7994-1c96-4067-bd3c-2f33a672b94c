/**
* Created by TurboC on 2019/08/01.
* 重写折叠框样式
*/
.webbas {
    .el-collapse {
        border-top: none;
        border-bottom: none;
        .el-row {
            border-bottom: 1px solid #d9d9d9;
            padding-top: 4px;
            padding-bottom: 4px;
            padding-left: 5px;
            padding-right: 5px;
        }
    }
    .el-collapse-item {
        margin-bottom: 15px;
        border-left: 1px solid #d9d9d9;
        border-right: 1px solid #d9d9d9;
    }
    .el-collapse-item__header {
        background: #f9f9f9;
        border-top: 1px solid #d9d9d9;
        border-bottom: 1px solid #d9d9d9;
        font-size: 14px;
        font-weight: bold;
        color: #555;
        padding: 5px 15px;
        height: 22px;
    }
    .el-collapse-item__content {
        padding-bottom: 0;
        line-height: 1;
    }
    .el-collapse-item__wrap {
        border-bottom: none;
    }
    .el-collapse-item__header > i:first-child {
        margin-right: 5px;
    }
    .el-collapse-item__header > span > i:first-child {
        margin-right: 5px;
    }
    .el-collapse-item__header .el-button {
        margin-left: 5px;
    }
    .el-collapse-item__header a {
        margin-left: 10px;
    }
}


