/**
* Created by TurboC on 2019/08/01.
* 一级菜单高度、一级菜单背景
*/
// @import "../../../mixins/mixins";

.el-menu--vertical {

  // 修改滚动条颜色---设置滚动条的样式--start
  ::-webkit-scrollbar {
    width: 5px;
  }

  /* 滚动槽 */
  ::-webkit-scrollbar-track {}

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background: #DCDFE6;
  }

  .el-menu--popup {
    max-height: 500px;
    overflow-y: scroll;

    .el-menu-item {
      .el-submenu__icon-arrow {
        right: 10px;
      }
    }
  }
}

.frame-main-menu {
  @include main-layout;
  background: #f9f9f9;
  background-color: #f0f4f8;
  overflow: hidden;
  overflow-y: auto;
  border: 1px solid #dee8f8;
  z-index: 99;

  .el-menu {
    background: #f0f4f8;
  }

  .el-submenu .el-menu {
    background: #f1f1f1;
  }

  .el-menu-item,
  .el-submenu__title {
    height: 40px;
    line-height: 40px;
    font-size: 13px;
    color: #787878;
  }

  .el-submenu__title {
    font-weight: 600;
  }

  .el-menu-item.is-active {
    background-color: #e8eff2;
    color: $wb-theme-color;
    border-right: 4px solid #3878d2;

    .el-submenu__icon-arrow {
      right: 16px;
    }
  }

  .el-submenu__title:hover,
  .el-menu-item:hover {
    background-color: #e8eff2;
  }

  .el-menu-item:focus,
  .el-submenu__title:focus {
    background-color: #fdfdfd;
  }

  ul li {
    border-bottom: 1px solid #ebebeb;
  }

  .el-menu--collapse .hiddeName {
    display: none;
  }

  .menu-item-title-ellipsis,
  .submenu-title-ellipsis .el-submenu__title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    padding-right: 30px;
    //padding-left: 20px;
    //padding-right: 20px;
  }
}

.frame-main-menu>.el-menu>div>.el-menu-item {
  font-weight: 600;
}

// webbas项目菜单
.wb_01 {
  background: #4fc2f9
}

.wb_02 {
  background: #d5a817;
}

.wb_01,
.wb_02 {
  width: 21px;
  height: 21px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  overflow: hidden;
  vertical-align: middle;
  color: #FFF;
  font-size: 1.4rem;
  display: inline-block;
  line-height: 15px;
  text-align: center;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  margin-right: 3px;

  i:before {
    width: 21px;
    height: 21px;
    color: #fff;
  }
}

// CPCC项目个性化：CPCC项目菜单
.CPCC_00,
.CPCC_01,
.CPCC_02,
.CPCC_03,
.CPCC_commodity_01,
.CPCC_order_01,
.cpccWf_02 {
  width: 21px;
  height: 21px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  overflow: hidden;
  vertical-align: middle;
  color: #FFF;
  font-size: 1.4rem;
  display: inline-block;
  line-height: 15px;
  text-align: center;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  margin-right: 3px;
  i:before {
    width: 21px;
    height: 21px;
    color: #fff;
  }
}
.CPCC_00 {
  background: #4fc2f9
}
.CPCC_01 {
  background: #63d3ba;
}
.CPCC_02 {
  background: #ae8ce3;
}
.CPCC_03 {
  background: #f8b281;
}
.CPCC_order_01 {
  background: #FFD700;
}
.CPCC_commodity_01 {
  background: #bc5b68
}
.cpccWf_02 {
  background: #a0d911;
}
.cpccWf_0201 {
  background: #a0cfff;
}
.cpccWf_0202 {
  background: #a77a94;
}
.cpccwf_0401 {
  background: #3c763d;
}
.cpccWf_020201 {
  background: #9E9E9E;
}
.cpccWf_020202 {
  background: #e2b78d;
}