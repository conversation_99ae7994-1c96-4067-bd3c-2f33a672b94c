/**
* Created by TurboC on 2021/09/25.
* 框架顶部菜单样式样式
*/
@import '../mixins/mixins';

.asp-header-menu,
.asp-user-info-menu {
  // @include main-layout;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  color: #fff;
  background: $wb-theme-color;
  background-color: $wb-theme-color;
  overflow: hidden;
  // overflow-y: auto;
  border: none;
  z-index: 99;
  .el-menu.el-menu--horizontal {
    border-bottom: none;
    .el-menu-item:not(.is-disabled):hover,
    .el-menu-item:not(.is-disabled):focus {
      background-color: $wb-theme-color;
      color: #fff;
      font-weight: 600;
      border-bottom: 2px solid #fff;
    }
  }
  .el-menu {
    background: $wb-theme-color;
  }
  .el-submenu .el-menu {
    background: $wb-theme-color;
  }
  .el-menu-item,
  .el-submenu__title {
    height: 40px;
    line-height: 40px;
    font-size: 13px;
    color: #fff;
  }
  .el-submenu__title {
    font-weight: 600;
    padding: 0;
  }
  .el-menu-item.is-active {
    background-color: $wb-theme-color;
    color: #fff;
    font-weight: 600;
    border-bottom: 2px solid #fff;
  }
  .el-submenu__title:hover,
  .el-menu-item:hover {
    background-color: $wb-theme-color;
    color: #fff;
    font-weight: 600;
    border-bottom: 2px solid #fff;
  }
  .el-menu-item:focus,
  .el-submenu__title:focus {
    background-color: $wb-theme-color;
    color: #fff;
    font-weight: 600;
    border-bottom: 2px solid #fff;
  }
  ul li {
    border-bottom: 1px solid #ebebeb;
  }
  .el-menu--collapse .hiddeName {
    display: none;
  }
  .el-menu > div > .el-menu-item {
    font-weight: 600;
    padding-top: 0;
    margin-top: 5px;
    font-size: 14px;
  }
  .el-submenu__title .el-menu-item {
    /* font-size: 16px; */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    /* white-space: normal; */
    word-break: break-all;
  }
  .el-menu--horizontal {
    .el-menu--popup-bottom-start {
      height: 350px;
      overflow: auto;
    }
    // 修改滚动条颜色---设置滚动条的样式--start
    ::-webkit-scrollbar {
      width: 7px;
    }
    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background-color: #cccccc;
    }
    ::-webkit-scrollbar-thumb:window-inactive {
      right: 0 !important;
    }
  }
}
.asp-user-info-menu {
  .el-menu--horizontal {
    height: inherit;
    line-height: inherit;
    .el-submenu {
      height: inherit;
      line-height: inherit;
      > .el-submenu__title {
        height: inherit;
        line-height: inherit;
        font-size: 14px;
        color: #fff;
        i {
          color: #fff;
        }
      }
    }
    .el-submenu__title:hover,
    .el-menu-item:hover {
      color: #fff;
      background: $wb-theme-color;
      background-color: $wb-theme-color;
    }
    .el-submenu__title {
      font-weight: 500;
    }
    .el-menu--popup-bottom-start {
      height: auto;
    }
    > .el-submenu .el-submenu__title:hover,
    > .el-submenu.is-active .el-submenu__title {
      font-weight: 500;
      color: #fff;
      background: $wb-theme-color;
      background-color: $wb-theme-color;
    }
    > .el-submenu:focus .el-submenu__title,
    > .el-submenu:hover .el-submenu__title {
      color: #fff;
    }
  }
}

.asp-user-info-submenu.el-menu--horizontal {
  min-width: 130px;
  max-width: 150px;
  width: auto;
  height: auto;
  .el-menu--popup {
    width: 100%;
    height: 100%;
    min-width: 0;
    border-radius: 4px;
  }
  .el-menu.el-menu--popup {
    padding: 10px 0;
    -webkit-box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    li.el-menu-item {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    li.el-menu-item,
    li.el-submenu {
      font-size: 14px;
      color: #606266;
      padding: 0 25px;
      .el-submenu__title {
        font-size: 14px;
        color: #606266;
        padding: 0;
        .el-submenu__icon-arrow {
          font-size: 14px;
          color: #606266;
          right: -15px;
        }
      }
    }
    li.el-menu-item.is-active,
    li.el-submenu.is-active > .el-submenu__title {
      font-size: 14px;
      color: #606266;
    }
    li.el-menu-item:focus,
    li.el-menu-item:not(.is-disabled):hover,
    li.el-submenu:focus,
    li.el-submenu:not(.is-disabled):hover {
      background-color: #ecf5ff;
      color: #66b1ff;
    }
    li.el-submenu .el-submenu__title:focus,
    li.el-submenu .el-submenu__title:not(.is-disabled):hover,
    li.el-submenu .el-submenu__icon-arrow:focus,
    li.el-submenu .el-submenu__icon-arrow:not(.is-disabled):hover {
      color: #66b1ff;
    }
    li.el-submenu .el-submenu__title {
      background-color: transparent;
    }
  }
  .el-menu--popup .popper__arrow {
    border-width: 6px;
    -webkit-filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
    filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  }
  .el-menu--popup .popper__arrow,
  .el-menu--popup .popper__arrow::after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
  }
  .el-menu--popup .popper__arrow::after {
    content: ' ';
    border-width: 6px;
  }
}

.asp-user-info-submenu[x-placement^='bottom-start'] {
  .el-menu--popup {
    overflow: unset;
  }
  .el-menu--popup > .popper__arrow {
    top: -6px;
    left: 50%;
    margin-right: 3px;
    border-top-width: 0;
    border-bottom-color: #ebeef5;
  }
  .el-menu--popup > .popper__arrow::after {
    top: 1px;
    margin-left: -6px;
    border-top-width: 0;
    border-bottom-color: #fff;
  }
}
