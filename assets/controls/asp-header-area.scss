/**
* Created by TurboC on 2019/08/01.
* 框架顶部样式
*/
@import '../mixins/mixins';

.asp-header-area {
  color: #fff;
  background: $wb-theme-color;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
  padding-left: 0;
  padding-right: 0;
  box-shadow: none;
  cursor: pointer;
  .el-link {
    color: #ffffff !important;
    font-size: 16px;
    margin-right: 10px;
    margin-bottom: 3px;
    text-decoration: underline;
  }
  .el-link:hover {
    color: #ffffff !important;
  }
}

.asp-header-area-left {
  img {
    height: auto;
    max-width: 100%;
    vertical-align: middle;
    margin-left: 15px;
    border: 0;
  }
  strong {
    margin-left: 25px;
    font-size: 18px;
  }
  i {
    font-size: 22px;
  }
}

.asp-header-area-middle {
  display: inline-flex;
  li {
    float: right;
    font-size: 14px;
    padding: 12px 8px 0px;
    margin-right: 5px;
    line-height: 24px;
  }
  li.active {
    border-bottom: 1px solid #fff;
  }
  .classifyScroll {
    padding: 0;
    width: 95%;
    overflow: hidden;
    transition: all 0.3s;
    &.active {
      transition: all 0.3s;
      width: 90%;
      margin: 0 auto;
    }
    .classifys {
      // 注意一下，这里没有用position: relative，，而图标用了position:absolute。因为这个东西是准备做组件的，我把相对定位写在父级的css标签上了。
      transition: all 0.3s;
      display: flex;
      align-items: center;
      &-item {
        white-space: nowrap;
      }
    }
  }
  .rightIcon {
    display: inline;
    bottom: 75%;
    transform: translate(0, 50%);
    z-index: 9;
  }
  .leftIcon {
    display: inline;
    bottom: 75%;
    transform: translate(0, 50%);
    z-index: 9;
  }
  .el-icon-arrow-left,
  .el-icon-arrow-right {
    font-size: 16px;
    margin-bottom: 16px;
  }
  .dataNavList {
    .el-menu--horizontal {
      div {
        display: flex;
      }
      .el-icon-arrow-right {
        font-size: 12px;
      }
    }
    .classifys-item {
      div {
        display: inline-block;
      }
    }
  }
}

.asp-header-area-middle-menu {
  height: 48px;
  line-height: 48px;
  // overflow-x: scroll;
  overflow: hidden;
  .dataNavList {
    li {
      float: left;
      padding: 0 8px;
      border-bottom: 0;
      .wb-menu-withicon {
        line-height: 21px;
      }
    }
    .el-menu {
      background-color: transparent;
      div {
        .el-menu-item {
          margin-top: 0;
        }
      }
    }
    .el-menu-item * {
      color: #fff;
    }
    .el-menu-item,
    .el-submenu__title {
      height: 42px;
      line-height: 42px;
      font-size: 14px;
    }
    .el-menu-item:focus,
    .el-menu-item:hover,
    .el-submenu__title:hover {
      background-color: transparent;
    }
    .el-menu-item.is-active {
      border-bottom: 1px solid #fff;
    }
    .el-submenu.is-active {
      .el-submenu__title {
        border-bottom: 1px solid #fff;
      }
    }
    .el-submenu__title {
      color: #fff;
      i {
        color: #fff;
      }
    }
    .el-submenu__icon-arrow {
      right: 1px;
      top: 26px;
    }
    :focus {
      outline: 0;
    }
  }
}

.asp-header-area-right {
  text-align: right;
  padding-right: 25px;
  .el-dropdown {
    color: #fff;
  }
}

.asp-header-department-css {
  // z-index: 0(个人中心修改个人信息，默认不可修改)
  position: absolute;
  z-index: 0;
  right: 0;
}

// 多系统动态切换菜单标题展示样式
.asp-header-area {
  .multi_menu_title.asp-header-area-left {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: row;
    flex-wrap: nowrap;
    .header_menu_title {
      display: flex;
      height: 48px;
      /* padding: 0 10px; */
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      .header_main_title {
        flex: 1;
        height: 28px;
        line-height: 28px;
        font-size: 18px;
      }
      .header_second_title {
        flex: 1;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
      }
    }
  }
}
