{"name": "asp-micro-app", "version": " 3.6.2-p4", "description": "微前端开发框架", "author": "TurboC", "license": "ISC", "main": "index.js", "scripts": {"clean:app": "cd micro-proxy && rimraf node_modules", "clean:login": "cd micro-wb-login && rimraf node_modules", "clean:sso": "cd micro-wb-sso && rimraf node_modules", "clean:system": "cd micro-wb-system && rimraf node_modules", "clean:main": "cd micro-wb-main && rimraf node_modules", "clean:parent": "rimraf node_modules", "clean-all": "npm-run-all clean:*", "install:login": "pnpm -C micro-wb-login install", "install:sso": "pnpm -C micro-wb-sso install", "install:system": "pnpm -C micro-wb-system install", "install:main": "pnpm -C micro-wb-main install", "install:app": "pnpm -C micro-proxy install", "install-all": "pnpm install", "start:login": "pnpm -C micro-wb-login run serve ", "start:sso": "pnpm -C micro-wb-sso run serve ", "start:system": "pnpm -C micro-wb-system run serve", "start:main": "pnpm -C micro-wb-main run serve", "start:app": "pnpm -C micro-proxy run serve", "part": "pnpm -C micro-proxy run part", "start-all": "npm-run-all --parallel start:*", "serve-all": "npm-run-all --parallel start:*", "build:main": "pnpm -C micro-wb-main run build", "build:login": "pnpm -C micro-wb-login run build", "build:sso": "pnpm -C micro-wb-sso run build", "build:system": "pnpm -C micro-wb-system run build", "build-all": "npm-run-all --parallel build:*"}, "devDependencies": {"npm-run-all": "^4.1.5", "rimraf": "^6.0.1"}, "rules": {"parser": "babel-es<PERSON>"}, "pnpm": {"patchedDependencies": {"vue-baidu-map@0.21.22": "patches/<EMAIL>"}, "overrides": {"vue-template-compiler": "2.6.14"}}}