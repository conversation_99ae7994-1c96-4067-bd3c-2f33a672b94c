<!doctype html>
<html lang="en">
  <head>
    <title>登录中，请稍后</title>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  </head>
  <body>
    <script>
      const url = new URL(location.href)
      const certificate = /^[\d\w]+$/.exec(url.searchParams.get('artifact'))
      const systemId = url.searchParams.get('systemId') === "2" ? 2 : 1
      const data = { systemId:systemId, certificate:'-1' }
      if( systemId === 2 || (systemId === 1 && certificate !== null) ) {
        if(systemId === 1 && certificate !== null){
          data.certificate = certificate[0]
        }
        fetch('/yundian/csm/api/login/sso', {
          method: 'post',
          headers: {
            'content-type': 'application/json'
          },
          body: JSON.stringify(data)
        })
        .then(res=> res.json())
        .then(res=> {
          if(res && res.data && res.data.secretEnable === 1) {
            sessionStorage.setItem('needEncrypt',1)
          }else{
            sessionStorage.removeItem('needEncrypt')
          }
          location.href = "./"
        })
        .catch(e=> {
          console.log(e)
          location.href = "./"
        })
      }else {
        location.href = './'
      }

    </script>
  </body>
</html>