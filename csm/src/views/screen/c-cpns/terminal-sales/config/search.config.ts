// 获取前一天时间
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
const lastMonth = dayjs().add(-1, 'month').format('YYYY-MM')
const preDate = getPreDay()
const searchConfig = {
  headerTitle: {
    title: '终端销售分析',
    mb20: true,
    right: false
  },

  formItems: [
    {
      // 时间
      type: 'date',
      prop: [dayjs(preDate).format('YYYYMMDD'), dayjs(preDate).format('YYYYMMDD')],
      propMonth: [lastMonth, lastMonth],
      key: 'date',
      // 是否支持月
      isSwitch: true
    },

    {
      // 省选择
      type: 'prov',
      key: 'provinceCode',
      prop: ''
    },
    {
      // 市选择
      type: 'city',
      key: 'cityCode',
      prop: ''
    },
    {
      // 营业厅选择
      type: 'select',
      key: 'storeName',
      prop: '',
    },
    {
      // 日期类型
      type: 'dateType',
      key: 'dateType',
      prop: '',
    },
    {
      // 终端选择
      type: 'select',
      prop: '',
      key: 'brand',
      placeholder: '全部终端品牌',
      options: [
        { value: '', label: '终端品牌' },
        { value: 0, label: '其他' },
        { value: 1, label: 'VIVO' },
        { value: 2, label: 'OPPO' },
        { value: 3, label: '华为' },
        { value: 4, label: '荣耀' },
        { value: 5, label: '小米' },
        { value: 6, label: '苹果' },
        { value: 7, label: 'Realme' },
        { value: 8, label: '三星' },
      ]
    },
    // {
    //   // 下单方式
    //   type: 'select',
    //   prop: '',
    //   placeholder: '终端选择',
    //   options: [
    //     { value: 1, label: '全部' },
    //     { value: 2, label: '扫码购' },
    //     { value: 3, label: '一键下单' },
    //   ]
    // },
    // {
    //   // 输入框
    //   type: 'input',
    //   prop: '',
    //   key: 'exportFileName',
    //   placeholder: '请输入订单编号'
    // }
  ]
}

export default searchConfig
