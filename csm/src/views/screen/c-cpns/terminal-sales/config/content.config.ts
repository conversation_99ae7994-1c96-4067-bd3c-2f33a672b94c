// 获取前一天时间
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
const preDate = getPreDay()
// 大屏总数明细列表查询
const contentConfig = {
  // 接口名称
  pageName: 'content1694176869163',

  // 接口名称
  interface: 'salesOrderDetails',

  // 表格参数
  tableColumn: [
    { type: 'index', width: '100', label: '序号', align: 'center' },
    { type: 'date', prop: 'statTime', label: '日期', align: 'center' },
    { prop: 'provinceName', label: '归属省', align: 'center' },
    { prop: 'cityName', label: '归属地市', align: 'center' },
    { prop: 'storeName', label: '归属营业厅', align: 'center' },
    { prop: 'screenId', label: '设备编号', align: 'center' },
    { prop: 'orderId', label: '订单编号', align: 'center' },
    { prop: 'purchaseApproachShow', label: '购机方式', align: 'center' },
    { prop: 'goodsCategoryShow', label: '分类', align: 'center' },
    { prop: 'brandShow', label: '商品品牌', align: 'center' },
    { prop: 'goodsTypeShow', label: '终端销售类型', align: 'center' },
    { prop: 'goodsNum', label: '购买数量', align: 'center' },
    { prop: 'billChargeShow', label: '订单总价', align: 'center' },
  ],

  // 表格高度
  tableHeight: 'auto',

  // 是否显示导出
  isExport: true,

  // 表格上方标题
  title: '售出终端明细数据表',

  // state的数据
  storeState: {
    pageList: [],
    pageTotal: 0,
    pageNum: 1,
    pageSize: 10,
    // date: [],
    tableLoading: false,
    brand: null,
    // 填写导出文件名称的props
    inputProp: '',
    startDate: dayjs(preDate).format('YYYY-MM-DD'),
    endDate: dayjs(preDate).format('YYYY-MM-DD'),
    dateType: '',
  }
}

export default contentConfig
