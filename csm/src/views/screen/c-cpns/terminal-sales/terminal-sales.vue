<template>
  <section>
    <page-search ref="searchRef" :content-config="contentConfig" @handleQueryClicks="handleQueryClicks"  @handleQueryClick="handleQueryClick" :search-config="searchConfig" />

    <other-block v-if="allData.zdsppjdj" :sAnalyseDatas="sAnalyseDatas" :allData="allData"/>

    <page-content ref="contentRef" :content-config="contentConfig" @handle-export-down-load="handleExport">
      <!-- <template #date>
        <span v-if="storeState?.date?.length > 0">{{ storeState?.date[0] }} - {{ storeState?.date[1] }}</span>
      </template> -->
    </page-content>
  </section>
</template>
<script lang="ts" setup>
import { salesAnalyse, singleMetrics } from '@/api/query'
import searchConfig from './config/search.config'
import contentConfig from './config/content.config'
import PageSearch from '@/components/page-search/page-search.vue'
import PageContent from '@/components/page-content/page-content.vue'
import usePageContent from '@/hooks/usePageContent'
// import useSystemStore from '@/store/system/system'
// import { storeToRefs } from 'pinia'
import OtherBlock from '@/views/screen/c-cpns/other-block/other-block.vue'
import { ref } from 'vue'

const handleQueryClicks = (e: any, dateOrMonth: string): void => {
  const data = {
    startDate: e.startDate,
    endDate: e.endDate,
    dateType: dateOrMonth,
    provinceCode: e.provinceCode,
    cityCode: e.cityCode,
    storeName: e.storeName,
    metricCodes: [
      'zdsppjdj'
    ]
  }
  const sAnalyseData = {
    startDate: e.startDate,
    endDate: e.endDate,
    dateType: dateOrMonth,
    provinceCode: e.provinceCode,
    cityCode: e.cityCode,
    storeName: e.storeName,
    brand: e.brand
  }
  void singleMetrics(data).then(res => {
    allData.value = res.data.metricsMap
  })
  void salesAnalyse(sAnalyseData).then(res => {
    sAnalyseDatas.value = res.data
  })
}
const allData: any = ref({})
const sAnalyseDatas = ref({})
const { contentRef, handleQueryClick } = usePageContent()
// const systemStore = useSystemStore()
// const storeState: Record<string, any> = storeToRefs(systemStore)[contentConfig.pageName]
const searchRef = ref()
function handleExport (): any {
  const queryInfo = searchRef.value?.getherQuery(false)
  return contentRef.value?.exportDownLoad(queryInfo)
}
</script>
<style lang="scss" scoped></style>
