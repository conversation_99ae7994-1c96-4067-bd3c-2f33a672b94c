<template>
  <section>
    <page-search ref="searchRef" :content-config="contentConfig" @handleQueryClicks="handleQueryClicks" @handleQueryClick="handleQueryClick" :search-config="searchConfig" />
    <!-- 分块显示的内容 -->
    <block-content v-if="allData.ddxse" :data="[{
        title:'订单销售额',
        span:allData.ddxse.metricValue ? allData.ddxse.metricValueShow : '0',
        desc:'统计口径：统计订单的实付金额总数（仅统计泛终端商品）',
      }, {
        title:'订单成交量： ',
        span:allData.ddcjl.metricValue ? allData.ddcjl.metricValueShow : '0',
        desc:'统计口径：成功下单的订单数量（仅统计泛终端商品）'
      }, {
        title:'订单转化率： ',
        span:allData.ddzhl.metricValue ? allData.ddzhl.metricValueShow : '0',
        desc:'统计口径：成交的订单数占扫码量+登录人数的百分比'
      }]" />

    <page-content ref="contentRef" :content-config="contentConfig" @handle-export-down-load="handleExport">
      <template #date>
        <span v-if="storeState?.date?.length > 0 && storeState.dateType === 'day'">统计时间：{{ storeState?.date[0] }} - {{ storeState?.date[1] }}</span>
        <span v-if="storeState?.date?.length > 0 && storeState.dateType === 'month'">统计时间：{{ handleDate(storeState?.date[0]) }} - {{ handleDate(storeState?.date[1]) }}</span>
      </template>
    </page-content>
  </section>
</template>
<script lang="ts" setup>
import { singleMetrics } from '@/api/query'
import searchConfig from './config/search.config'
import contentConfig from './config/content.config'
import PageSearch from '@/components/page-search/page-search.vue'
import PageContent from '@/components/page-content/page-content.vue'
import BlockContent from '@/components/common/block-content/index.vue'
import usePageContent from '@/hooks/usePageContent'
import useSystemStore from '@/store/system/system'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import { handleDate } from '@/utils/utils'

const handleQueryClicks = (e: any, dateOrMonth: string): void => {
  const data = {
    startDate: e.startDate,
    endDate: e.endDate,
    dateType: dateOrMonth,
    provinceCode: e.provinceCode,
    cityCode: e.cityCode,
    storeName: e.storeName,
    screenId: e.screenId,
    metricCodes: [
      'ddxse',
      'ddcjl',
      'ddzhl'
    ]
  }
  void singleMetrics(data).then(res => {
    allData.value = res.data.metricsMap
  })
}

const allData: any = ref({})
const { contentRef, handleQueryClick } = usePageContent()
const systemStore = useSystemStore()
const storeState: Record<string, any> = storeToRefs(systemStore)[contentConfig.pageName]
const searchRef = ref()
function handleExport (): any {
  const queryInfo = searchRef.value?.getherQuery(false)
  return contentRef.value?.exportDownLoad(queryInfo)
}
</script>
<style lang="scss" scoped></style>
