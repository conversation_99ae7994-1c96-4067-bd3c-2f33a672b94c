// 获取前一天时间
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
const preDate = getPreDay()
const contentConfig = {
  // 接口名称
  pageName: 'content1694176903203',

  // 接口名称
  interface: 'orderDetails',

  // 表格参数
  tableColumn: [
    { type: 'index', width: '100', label: '序号', align: 'center' },
    { type: 'date', prop: 'statTime', label: '日期', align: 'center' },
    { prop: 'provinceName', label: '归属省', align: 'center' },
    { prop: 'cityName', label: '归属地市', align: 'center' },
    { prop: 'storeName', label: '归属营业厅', align: 'center' },
    { prop: 'screenId', label: '设备编号', align: 'center' },
    { prop: 'orderId', label: '订单编号', align: 'center' },
    { prop: 'purchaseApproachShow', label: '下单方式', align: 'center' },
    { prop: 'statusShow', label: '订单状态', align: 'center' },
    { prop: 'goodsTypeShow', label: '销售类型', align: 'center' },
    { prop: 'goodsNum', label: '购买数量', align: 'center' },
    { prop: 'billChargeShow', label: '订单销售金额（实付金额）', align: 'center' },
  ],

  // 表格高度
  tableHeight: 'auto',

  // 是否显示导出
  isExport: true,

  // 表格上方标题
  title: '订单明细数据表',

  // state的数据
  storeState: {
    pageList: [],
    pageTotal: 0,
    pageNum: 1,
    pageSize: 10,
    // date: [],
    tableLoading: false,
    purchaseApproach: '',
    goodsType: '',
    screenId: '',
    startDate: dayjs(preDate).format('YYYY-MM-DD'),
    endDate: dayjs(preDate).format('YYYY-MM-DD'),
    // 填写导出文件名称的props
    inputProp: '',
    dateType: '',
  }
}

export default contentConfig
