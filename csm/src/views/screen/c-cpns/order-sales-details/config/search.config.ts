// 获取前一天时间
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
const lastMonth = dayjs().add(-1, 'month').format('YYYY-MM')
const preDate = getPreDay()
const searchConfig = {
  headerTitle: {
    title: '订单销售明细数据（仅泛终端）：',
    mb20: true,
    right: false
  },

  formItems: [
    {
      // 时间
      type: 'date',
      prop: [dayjs(preDate).format('YYYYMMDD'), dayjs(preDate).format('YYYYMMDD')],
      propMonth: [lastMonth, lastMonth],
      key: 'date',
      // 是否支持月
      isSwitch: true
    },

    {
      // 省选择
      type: 'prov',
      key: 'provinceCode',
      prop: ''
    },
    {
      // 市选择
      type: 'city',
      key: 'cityCode',
      prop: ''
    },
    {
      // 营业厅选择
      type: 'select',
      key: 'storeName',
      prop: '',
    },
    {
      // 日期类型
      type: 'dateType',
      key: 'dateType',
      prop: '',
    },
    {
      // 终端选择
      type: 'select',
      prop: '',
      placeholder: '全部泛终端',
      key: 'goodsType',
      options: [
        { value: '', label: '全部泛终端' },
        { value: 5, label: '裸机' },
        { value: 6, label: '信用购' },
        { value: 7, label: '顺差让利' },
        { value: 8, label: '5G金币' },
        { value: 10, label: '其他合约' },
        { value: 11, label: '其他' },
      ]
    },
    {
      // 下单方式
      type: 'select',
      prop: '',
      placeholder: '全部下单方式',
      key: 'purchaseApproach',
      options: [
        { value: '', label: '全部下单方式' },
        { value: 1, label: '扫码购' },
        { value: 2, label: '一站式下单' },
      ]
    },
    {
      // 输入框
      type: 'input',
      prop: '',
      key: 'screenId',
      placeholder: '请输入设备编号'
    }
  ]
}

export default searchConfig
