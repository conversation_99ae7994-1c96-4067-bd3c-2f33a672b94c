<template>
  <main>

    <section class="sale">
      <!--   终端商品平均单价   -->
      <div class="terminalSales">
        <!--    标题    -->
        <div class="title">
          <div class="tit" v-if="props.allData">终端商品平均单价： <span>{{ props.allData.zdsppjdj.metricValue ? props.allData.zdsppjdj.metricValueShow : '0' }}</span></div>
          <div class="desc">统计口径：售出终端商品的平均价格</div>
        </div>

        <!--    售出商品购机方式占比    -->
        <div class="block1">
          <ul>
            <title-alone-possess :desc="'统计口径：统计商品通过不同购机方式售出的数量之比（仅统计泛终端商品）'" :title="'售出商品购机方式占比'" class="mb10" />
            <my-table :columns="tableColumn"
              :options="{ size: 'small', defaultSort: { prop: 'name', order: 'ascending' }, stripe: true, border: true, showPagination: false }"
              :table-data="tableData" />

            <title-alone-possess :desc="'统计口径：售出的各类终端商品数量之比（仅统计泛终端商品）'" :title="'售出商品类型占比'" class="mb10 mt10" />
            <my-table :columns="tableColumn2"
              :options="{ size: 'small', defaultSort: { prop: 'name', order: 'ascending' }, stripe: true, border: true, showPagination: false }"
              :table-data="tableData2" />
          </ul>
        </div>

      </div>

      <!--   商品品牌销量排名   -->
      <div class="brandSales">
        <title-alone-possess :desc="'统计口径：对于售出的终端商品数量，按照品牌进行排名'" :title="'商品品牌销量排名'" class="mb10" />
        <my-table :columns="tableColumn3"
          :options="{ size: 'small', defaultSort: { prop: 'name', order: 'ascending' }, stripe: true, border: true, showPagination: false }"
          :table-data="tableData3" />
      </div>
    </section>

  </main>
</template>
<script lang="ts" setup>
import MyTable from '@/components/table/my-table.vue'
import TitleAlonePossess from '@/components/common/title-alone-possess/index.vue'
import { watch, ref } from 'vue'
const props = defineProps({
  sAnalyseDatas: Object,
  allData: Object,
})
watch(() => props.sAnalyseDatas, item => {
  if (item !== null && item !== undefined) {
    if (item.purchasePercentage != null && item.purchasePercentage.length > 0) {
      console.log(item.purchasePercentage);

      const purPerColum: any = []; const purPerTable: any = {}
      item.purchasePercentage.forEach((o: { percentage: any, purchaseApproachShow: any }, index: number) => {
        purPerColum.push({
          label: o.purchaseApproachShow,
          prop: 'a' + index,
          align: 'center'
        })
        purPerTable['a' + index] = o.percentage
      })
      tableColumn.value = purPerColum
      tableData.value = [purPerTable]
    }else{
      tableColumn.value = []
      tableData.value = []
    }
    if (item.goodsTypePercentage != null && item.goodsTypePercentage.length > 0) {
      const goodsColum: any = []; const goodsTable: any = {}
      item.goodsTypePercentage.forEach((o: { percentage: any, goodsTypeShow: any }, index: number) => {
        goodsColum.push({
          label: o.goodsTypeShow,
          prop: 'a' + index,
          align: 'center'
        })
        goodsTable['a' + index] = o.percentage
      })
      tableColumn2.value = goodsColum
      tableData2.value = [goodsTable]
    }else{
      tableColumn2.value = []
      tableData2.value = []
    }
    if (item.top != null && item.top.length > 0) {
      tableData3.value = item.top
    }else{
      tableData3.value = []
    }
  }
})

const tableColumn: any = ref([])

const tableData: any = ref([])

const tableColumn2: any = ref([])

const tableData2: any = ref([])

const tableColumn3: any = [
  { type: 'index', width: '100', label: '序号', align: 'center' },
  { prop: 'brandShow', label: '商品品牌', align: 'center' },
  { prop: 'goodsNumShow', label: '商品销量', align: 'center' },
]

const tableData3: any = ref([])
</script>
<style lang="scss" scoped>
.sale {
  margin-bottom: 20px;

  ul {
    padding: 0;
    margin: 0;
  }

  display: flex;
  width: 100%;

  .terminalSales {
    flex: 1.5;
    margin-right: 20px;
    text-align: center;
    .title {
      margin-bottom: 10px;
      .tit {
        font-weight: bold;

        span {
          color: red;
        }
      }

      .desc {
        color: #8C8C8C;
        font-size: 13px;
      }
    }

    // .block1 {}
  }

  .brandSales {
    flex: 1;
  }

}
</style>
