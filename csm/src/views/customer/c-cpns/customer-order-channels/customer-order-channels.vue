<template>
  <section>
    <page-search ref="searchRef" :content-config="[logOnUserContentConfig, productAccessContentConfig]"
      :search-config="searchConfig" @handleQueryClick="handleQueryClick" @handleQueryClicks="handleQueryClicks" />

    <el-row>
      <el-col :span="12">
        <title-alone-possess
          v-if="allData.spsmgtzl"
          :desc="'（统计口径：扫码购二维码的扫码次数）'"
          :mb20="true"
          :redTitle="allData.spsmgtzl.metricValue ? allData.spsmgtzl.metricValueShow : '0'"
          :lastTitle="'人次'"
          :title="'商品扫码购跳转量 : '"
        />
      </el-col>
    </el-row>

    <!--  商品访问明细表  -->
    <page-content :ref="el => { contentRef[0] = el }" :content-config="logOnUserContentConfig"
      @handle-export-down-load="handleExport(0)">
      <template #date>
        <span v-if="logOnUserStoreState?.date?.length > 0 && logOnUserStoreState.dateType === 'day'">统计时间：{{ logOnUserStoreState?.date[0] }} - {{ logOnUserStoreState?.date[1] }}</span>
        <span v-if="logOnUserStoreState?.date?.length > 0 && logOnUserStoreState.dateType === 'month'">统计时间：{{ handleDate(logOnUserStoreState?.date[0]) }} - {{ handleDate(logOnUserStoreState?.date[1]) }}</span>
      </template>
    </page-content>

    <el-row class="mt40">
      <el-col :span="12">
        <title-alone-possess v-if="allData.yzsxddjl" :desc="'（统计口径：一站式下单按钮的点击次数）'" :mb20="true"
          :redTitle="allData.yzsxddjl.metricValue ? allData.yzsxddjl.metricValueShow : '0'" :lastTitle="'次'" :title="'一站式下单点击量 : '" />
      </el-col>
    </el-row>

    <!--  一站式下单明细表  -->
    <page-content :ref="el => { contentRef[1] = el }" :content-config="productAccessContentConfig"
      @handle-export-down-load="handleExport(1)">
      <template #date>
        <span v-if="logOnUserStoreState?.date?.length > 0 && logOnUserStoreState.dateType === 'day'">统计时间：{{ productAccessStoreState?.date[0] }} - {{ productAccessStoreState?.date[1] }}</span>
        <span v-if="logOnUserStoreState?.date?.length > 0 && logOnUserStoreState.dateType === 'month'">统计时间：{{ handleDate(productAccessStoreState?.date[0]) }} - {{ handleDate(productAccessStoreState?.date[1]) }}</span>
      </template>
    </page-content>

  </section>
</template>
<script lang="ts" setup>
import { singleMetrics } from '@/api/query'
import logOnUserContentConfig from './config/logOnUser.content.config'
import productAccessContentConfig from './config/productAccess.content.config'
import searchConfig from './config/search.config'
import usePageContent from '@/hooks/usePageContent'
import PageSearch from '@/components/page-search/page-search.vue'
import PageContent from '@/components/page-content/page-content.vue'
import TitleAlonePossess from '@/components/common/title-alone-possess/index.vue'
import useSystemStore from '@/store/system/system'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import { handleDate } from '@/utils/utils'

const handleQueryClicks = (e: any, dateOrMonth: string): void => {
  const data = {
    startDate: e.startDate,
    endDate: e.endDate,
    dateType: dateOrMonth,
    provinceCode: e.provinceCode,
    cityCode: e.cityCode,
    storeName: e.storeName,
    screenId: e.screenId,
    metricCodes: [
      'spsmgtzl',
      'yzsxddjl',
    ]
  }
  void singleMetrics(data).then(res => {
    allData.value = res.data.metricsMap
  })
}
const allData: any = ref({})
const { contentRef, handleQueryClick } = usePageContent()
const systemStore = useSystemStore()
const logOnUserStoreState: Record<string, any> = storeToRefs(systemStore)[logOnUserContentConfig.pageName]
const productAccessStoreState: Record<string, any> = storeToRefs(systemStore)[productAccessContentConfig.pageName]
const searchRef = ref()
function handleExport (index: number): any {
  const queryInfo = searchRef.value?.getherQuery(false)
  return contentRef.value[index]?.exportDownLoad(queryInfo)
}
</script>
