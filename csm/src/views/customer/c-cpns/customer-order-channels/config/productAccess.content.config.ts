// 获取前一天时间
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
const preDate = getPreDay()
const logOnUserContentConfig = {
  // 组件名
  pageName: 'productAccess1694176655791',

  // 接口名称
  interface: 'customerOrderClickDetails',

  // 表格参数
  tableColumn: [
    { type: 'index', width: '100', label: '序号', align: 'center' },
    { type: 'date', prop: 'statTime', label: '日期', align: 'center' },
    { prop: 'provinceName', label: '归属省', align: 'center' },
    { prop: 'cityName', label: '归属地市', align: 'center' },
    { prop: 'storeName', label: '归属营业厅', align: 'center' },
    { prop: 'screenId', label: '设备编号', align: 'center' },
    { prop: 'goodsTypeShow', label: '商品类型', align: 'center' },
    { prop: 'goodsBrandShow', label: '商品品牌', align: 'center' },
    { prop: 'goodsName', label: '商品名称', align: 'center' },
    { prop: 'clickNum', label: '点击次数', align: 'center' },
  ],

  // 表格高度
  tableHeight: 'auto',

  // 是否显示导出
  isExport: true,

  // 表格上方标题
  title: '一站式下单点击量明细表',

  // state的数据
  storeState: {
    pageList: [],
    pageTotal: 0,
    pageNum: 1,
    pageSize: 10,
    // 填写导出文件名称的props
    inputProp: '',
    tableLoading: false,
    startDate: dayjs(preDate).format('YYYY-MM-DD'),
    endDate: dayjs(preDate).format('YYYY-MM-DD'),

    // 下单
    placeAnOrder: 0,
    dateType: '',
  }
}

export default logOnUserContentConfig
