// 获取前一天时间
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
const preDate = getPreDay()
const logOnUserContentConfig = {
  // 组件名
  pageName: 'logOnUser1694176683607',

  // 接口名称
  interface: 'customerTrafficLoginUserDetails',

  // 表格参数
  tableColumn: [
    { type: 'index', width: '100', label: '序号', align: 'center' },
    { type: 'date', prop: 'statTime', label: '日期', align: 'center' },
    { prop: 'provinceName', label: '归属省', align: 'center' },
    { prop: 'cityName', label: '归属地市', align: 'center' },
    { prop: 'screenId', label: '设备编号', align: 'center' },
    { prop: 'customerId', label: '登录手机号', align: 'center' },
    { prop: 'loginTypeShow', label: '鉴权方式', align: 'center' },
  ],

  // 表格高度
  tableHeight: 'auto',

  // 是否显示导出
  isExport: true,

  // 表格上方标题
  title: '登录用户明细表',

  // state的数据
  storeState: {
    pageList: [],
    pageTotal: 0,
    pageNum: 1,
    pageSize: 10,
    startDate: dayjs(preDate).format('YYYY-MM-DD'),
    endDate: dayjs(preDate).format('YYYY-MM-DD'),
    // 填写导出文件名称的props
    inputProp: '',
    tableLoading: false,

    // 登录用户数
    loginUser: 0,
    // 平均时长
    browseDuration: 0,
    dateType: '',
  }
}

export default logOnUserContentConfig
