<template>
  <section>
    <page-search ref="searchRef"
      :content-config="[logOnUserContentConfig, productAccessContentConfig, accessTrafficContentConfig, rotationalAdvertisingContentConfig]"
      :search-config="searchConfig" @handleQueryClick="handleQueryClick" @handleQueryClicks="handleQueryClicks" />

    <el-row>
      <el-col :span="12">
        <title-alone-possess v-if="allData.dlyhs" :desc="'（统计口径：在大屏登录的用户数量）'" :mb20="true"
          :redTitle="allData.dlyhs.metricValue ? allData.dlyhs.metricValueShow : '0'" :lastTitle="'人'"  :title="'登录用户数 : '" />
      </el-col>
      <el-col :span="12">
        <title-alone-possess v-if="allData.yhpjllsc" :desc="'（统计口径：用户单次浏览的平均时长）'" :mb20="true"
          :redTitle="allData.yhpjllsc.metricValue ? allData.yhpjllsc.metricValue : '0'" :lastTitle="'分钟'" :right="false"
          :title="'用户平均浏览时长 : '" />
      </el-col>
    </el-row>

    <!--  登录用户明细表  -->
    <page-content :ref="el => { contentRef[0] = el }" :content-config="logOnUserContentConfig"
      @handle-export-down-load="handleExport(0)">
      <template #date>
        <span v-if="logOnUserStoreState.date && logOnUserStoreState.dateType === 'day'">统计时间：{{ logOnUserStoreState?.date[0] }} - {{ logOnUserStoreState?.date[1]}}</span>
        <span v-if="logOnUserStoreState.date && logOnUserStoreState.dateType === 'month'">查询周期：{{ handleDate(logOnUserStoreState?.date[0]) }} - {{ handleDate(logOnUserStoreState?.date[1]) }}</span>
      </template>
    </page-content>

    <el-row class="mt40">
      <el-col :span="12">
        <title-alone-possess v-if="allData.spfwzcs" :desc="'（统计口径：商品详情页的浏览次数）'" :mb20="true"
          :redTitle="allData.spfwzcs.metricValue ? allData.spfwzcs.metricValueShow : 0" :lastTitle="'次'" :title="'商品访问总次数 : '" />
      </el-col>
    </el-row>

    <!--  商品访问明细表  -->
    <page-content :ref="el => { contentRef[1] = el }" :content-config="productAccessContentConfig"
      @handle-export-down-load="handleExport(1)">
      <template #date>
        <span v-if="logOnUserStoreState.date && logOnUserStoreState.dateType === 'day'">统计时间：{{ logOnUserStoreState?.date[0] }} - {{ logOnUserStoreState?.date[1]}}</span>
        <span v-if="logOnUserStoreState.date && logOnUserStoreState.dateType === 'month'">查询周期：{{ handleDate(logOnUserStoreState?.date[0]) }} - {{ handleDate(logOnUserStoreState?.date[1]) }}</span>
      </template>
    </page-content>

    <el-row class="mt40">
      <el-col :span="12">
        <title-alone-possess v-if="allData.yjcddjl" :desc="'（统计口径：一级菜单区域的用户点击量）'" :mb20="true"
          :redTitle="allData.yjcddjl.metricValue ? allData.yjcddjl.metricValueShow : '0'" :lastTitle="'次'" :title="'一级菜单点击量 : '" />
      </el-col>
    </el-row>

    <!--  一级菜单点击量明细表  -->
    <page-content :ref="el => { contentRef[2] = el }" :content-config="accessTrafficContentConfig"
      @handle-export-down-load="handleExport(2)">
      <template #date>
        <span v-if="logOnUserStoreState.date && logOnUserStoreState.dateType === 'day'">统计时间：{{ logOnUserStoreState?.date[0] }} - {{ logOnUserStoreState?.date[1]}}</span>
        <span v-if="logOnUserStoreState.date && logOnUserStoreState.dateType === 'month'">查询周期：{{ handleDate(logOnUserStoreState?.date[0]) }} - {{ handleDate(logOnUserStoreState?.date[1]) }}</span>
      </template>
    </page-content>

    <el-row class="mt40">
      <el-col :span="12">
        <title-alone-possess v-if="allData.lbggdjl" :desc="'（统计口径：轮播广告区域的用户点击量）'" :mb20="true"
          :redTitle="allData.lbggdjl.metricValue ? allData.lbggdjl.metricValueShow : '0'" :lastTitle="'次'" :title="'轮播广告点击量'" />
      </el-col>
    </el-row>

    <!--  轮播广告点击量明细表  -->
    <page-content :ref="el => { contentRef[3] = el }" :content-config="rotationalAdvertisingContentConfig"
      @handle-export-down-load="handleExport(3)">
      <template #date>
        <span v-if="logOnUserStoreState.date && logOnUserStoreState.dateType === 'day'">统计时间：{{ logOnUserStoreState?.date[0] }} - {{ logOnUserStoreState?.date[1]}}</span>
        <span v-if="logOnUserStoreState.date && logOnUserStoreState.dateType === 'month'">查询周期：{{ handleDate(logOnUserStoreState?.date[0]) }} - {{ handleDate(logOnUserStoreState?.date[1]) }}</span>
      </template>
    </page-content>
  </section>
</template>
<script lang="ts" setup>
import { singleMetrics } from '@/api/query'
import logOnUserContentConfig from './config/logOnUser.content.config'
import accessTrafficContentConfig from './config/accessTraffic.content.config'
import productAccessContentConfig from './config/productAccess.content.config'
import rotationalAdvertisingContentConfig from './config/rotationalAdvertising.content.config'
import searchConfig from './config/search.config'
import usePageContent from '@/hooks/usePageContent'
import PageSearch from '@/components/page-search/page-search.vue'
import PageContent from '@/components/page-content/page-content.vue'
import TitleAlonePossess from '@/components/common/title-alone-possess/index.vue'
import useSystemStore from '@/store/system/system'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import { handleDate } from '@/utils/utils'
const handleQueryClicks = (e: any, dateOrMonth: string): void => {
  // console.log(e,'ppoo');

  const data = {
    startDate: e.startDate,
    endDate: e.endDate,
    dateType: dateOrMonth,
    provinceCode: e.provinceCode,
    cityCode: e.cityCode,
    storeName: e.storeName,
    screenId: e.screenId,
    metricCodes: [
      'dlyhs',
      'yhpjllsc',
      'spfwzcs',
      'yjcddjl',
      'lbggdjl'
    ]
  }
  void singleMetrics(data).then(res => {
    allData.value = res.data.metricsMap
  })
}
const allData: any = ref({})

const { contentRef, handleQueryClick } = usePageContent()
const systemStore = useSystemStore()
const logOnUserStoreState: Record<string, any> = storeToRefs(systemStore)[logOnUserContentConfig.pageName]
// const accessTrafficStoreState: Record<string, any> = storeToRefs(systemStore)[accessTrafficContentConfig.pageName]
// const productAccessStoreState: Record<string, any> = storeToRefs(systemStore)[productAccessContentConfig.pageName]
// const rotationalAdvertisingStoreState: Record<string, any> = storeToRefs(systemStore)[rotationalAdvertisingContentConfig.pageName]
const searchRef = ref()
function handleExport (index: number): any {
  const queryInfo = searchRef.value?.getherQuery(false)
  return contentRef.value[index]?.exportDownLoad(queryInfo)
}
</script>
