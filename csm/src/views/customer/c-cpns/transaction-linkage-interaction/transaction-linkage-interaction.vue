<template>
  <section>
    <page-search ref="searchRef" :content-config="contentConfig" @handleQueryClicks="handleQueryClicks" @handleQueryClick="handleQueryClick" :search-config="searchConfig" />

    <el-row>
      <el-col :span="10">
        <title-alone-possess v-if="allData.swldhdcs" :mb20="true" :title="'实物联动互动次数 : '" :redTitle="allData.swldhdcs.metricValue ? allData.swldhdcs.metricValueShow : '0'" :lastTitle="'人次'" :desc="'（统计口径：统计拿起实物联动专区样机的次数）'"  />
      </el-col>
      <el-col :span="10">
        <title-alone-possess v-if="allData.swdlyhs" :right="true" :mb20="true" :title="'登录用户数 : '" :redTitle="allData.swdlyhs.metricValue ? allData.swdlyhs.metricValueShow : '0'" :lastTitle="'人'" :desc="'（统计口径:参与互动并登录的用户数）'"  />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="10">
        <title-alone-possess v-if="allData.hdcjl" :mb20="true" :title="'互动成交量 : '" :redTitle="allData.hdcjl.metricValue ? allData.hdcjl.metricValueShow : '0'" :lastTitle="'笔'" :desc="'（统计口径:参与互动并成交的订单数）'"  />
      </el-col>
      <el-col :span="10">
        <title-alone-possess v-if="allData.zhl" :right="true" :mb20="true" :title="'转化率 : '" :redTitle="allData.zhl.metricValue ? allData.zhl.metricValueShow : '0'" :desc="'（统计口径: 互动成交量/实物联动互动次数）'"  />
      </el-col>
    </el-row>

    <page-content ref="contentRef" :content-config="contentConfig" @handle-export-down-load="handleExport">
      <template #date>
        <span v-if="storeState?.date?.length > 0 && storeState.dateType === 'day'">当前时间：{{ storeState?.date[0] }} - {{ storeState?.date[1] }}</span>
        <span v-if="storeState?.date?.length > 0 && storeState.dateType === 'month'">当前时间：{{ handleDate(storeState?.date[0]) }} - {{ handleDate(storeState?.date[1]) }}</span>
      </template>
    </page-content>
  </section>
</template>
<script lang="ts" setup>
import { singleMetrics } from '@/api/query'
import searchConfig from './config/search.config'
import contentConfig from './config/content.config'
import PageSearch from '@/components/page-search/page-search.vue'
import PageContent from '@/components/page-content/page-content.vue'
import usePageContent from '@/hooks/usePageContent'
import TitleAlonePossess from '@/components/common/title-alone-possess/index.vue'
import useSystemStore from '@/store/system/system'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import { handleDate } from '@/utils/utils'

const handleQueryClicks = (e: any, dateOrMonth: string): void => {
  const data = {
    startDate: e.startDate,
    endDate: e.endDate,
    dateType: dateOrMonth,
    provinceCode: e.provinceCode,
    cityCode: e.cityCode,
    storeName: e.storeName,
    screenId: e.screenId,
    metricCodes: [
      'swldhdcs',
      'swdlyhs',
      'hdcjl',
      'zhl',
    ]
  }
  void singleMetrics(data).then(res => {
    allData.value = res.data.metricsMap
  })
}
const allData: any = ref({})
const { contentRef, handleQueryClick } = usePageContent()
const systemStore = useSystemStore()
const storeState: Record<string, any> = storeToRefs(systemStore)[contentConfig.pageName]
const searchRef = ref()
function handleExport (): any {
  const queryInfo = searchRef.value?.getherQuery(false)
  contentRef.value?.exportDownLoad(queryInfo)
}
</script>
<style lang="scss" scoped></style>
