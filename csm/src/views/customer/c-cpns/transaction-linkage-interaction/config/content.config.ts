// 获取前一天时间
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
const preDate = getPreDay()
const contentConfig = {
  // 接口名称
  pageName: 'content1694176809819',

  // 接口名称
  interface: 'physicalInteractionDetails',

  // 表格参数
  tableColumn: [
    { type: 'index', width: '100', label: '序号', align: 'center' },
    { type: 'date', prop: 'statTime', label: '日期', align: 'center' },
    { prop: 'provinceName', label: '归属省', align: 'center' },
    { prop: 'cityName', label: '归属地市', align: 'center' },
    { prop: 'storeName', label: '归属营业厅', align: 'center' },
    { prop: 'screenId', label: '设备编号', align: 'center' },
    { prop: 'goodsTypeShow', label: '联动商品类型', align: 'center' },
    { prop: 'goodsBrandShow', label: '联动商品品牌', align: 'center' },
    { prop: 'goodsName', label: '联动商品名称', align: 'center' },
    { prop: 'experienceDurationShow', label: '联动体验时长', align: 'center' },
    { prop: 'placeOrderShow', label: '是否下单', align: 'center' },
  ],

  // 表格高度
  tableHeight: 'auto',

  // 是否显示导出
  isExport: true,

  // 表格上方标题
  title: '实物联动互动明细表',

  // state的数据
  storeState: {
    startDate: dayjs(preDate).format('YYYY-MM-DD'),
    endDate: dayjs(preDate).format('YYYY-MM-DD'),
    pageList: [],
    pageTotal: 0,
    pageNum: 1,
    pageSize: 10,
    // date: [],
    tableLoading: false,
    // 填写导出文件名称的props
    inputProp: '',
    // koa: '99',
    dateType: '',
  }
}

export default contentConfig
