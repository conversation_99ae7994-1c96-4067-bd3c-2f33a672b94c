<template>
  <section>
    <page-search ref="searchRef" :content-config="contentConfig" @handleQueryClicks="handleQueryClicks"  @handleQueryClick="handleQueryClick" :search-config="searchConfig">
      <template #redTitle>
        {{ allData.metricValueShow }}
      </template>
    </page-search>

    <page-content ref="contentRef" :content-config="contentConfig" @handle-export-down-load="handleExport">
      <template #date>
        <span v-if="storeState?.date?.length > 0">截止到：{{ storeState?.date[0] }}</span>
      </template>
    </page-content>
  </section>
</template>
<script lang="ts" setup>
import { singleMetrics } from '@/api/query'
import searchConfig from './config/search.config'
import contentConfig from './config/content.config'
import PageSearch from '@/components/page-search/page-search.vue'
import PageContent from '@/components/page-content/page-content.vue'
import usePageContent from '@/hooks/usePageContent'
import useSystemStore from '@/store/system/system'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'

const handleQueryClicks = (e: any, dateOrMonth: string): void => {
  const data = {
    startDate: e.startDate,
    endDate: e.endDate,
    dateType: dateOrMonth,
    provinceCode: e.provinceCode,
    cityCode: e.cityCode,
    storeName: e.storeName,
    screenId: e.screenId,
    metricCodes: [
      'dpzs'
    ]
  }
  void singleMetrics(data).then(res => {
    allData.value = res.data.metricsMap.dpzs
  })
}

const allData: any = ref({})
const systemStore = useSystemStore()
const { contentRef, handleQueryClick } = usePageContent()
const storeState: Record<string, any> = storeToRefs(systemStore)[contentConfig.pageName]
const searchRef = ref()

function handleExport (): any {
  const queryInfo = searchRef.value?.getherQuery(false)
  return contentRef.value?.exportDownLoad(queryInfo)
}
</script>
<style lang="scss" scoped></style>
