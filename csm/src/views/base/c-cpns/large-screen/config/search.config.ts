// 获取前一天时间
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
const preDate = getPreDay()
const searchConfig = {
  headerTitle: {
    title: '大屏总数：',
    desc: '（统计口径：以大屏设备编号为准，统计编号数量）',
    mb20: true,
    right: false
  },

  formItems: [
    {
      // 省选择
      type: 'prov',
      key: 'provinceCode',
      prop: ''
    },
    {
      // 市选择
      type: 'city',
      key: 'cityCode',
      prop: ''
    },
    {
      // 营业厅选择
      type: 'select',
      key: 'storeName',
      prop: '',
    },
    {
      // 时间
      type: 'date',
      types: 'onlydate',
      prop: dayjs(preDate).format('YYYYMMDD 00:00:00'),
      key: 'date',
      // 是否支持月
      isSwitch: false
    },
    {
      // 日期类型选择
      type: 'dateType',
      key: 'dateType',
      prop: '',
    },
    // {
    //   // 时间
    //   type: 'date',
    //   types:'onlydate',
    //   prop: [dayjs(preDate).format('YYYYMMDD'), dayjs(preDate).format('YYYYMMDD')],
    //   key: 'date',
    //   // 是否支持月
    //   isSwitch: false
    // },
  ]
}

export default searchConfig
