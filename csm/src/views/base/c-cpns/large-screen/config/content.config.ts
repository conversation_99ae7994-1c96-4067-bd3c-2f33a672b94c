// 获取前一天时间
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
const preDate = getPreDay()
// 大屏总数明细列表查询
const contentConfig = {
  // 名称
  pageName: 'content1694176986507',

  // 接口名称
  interface: 'deviceStatusTotalDetails',

  // 表格参数
  tableColumn: [
    { type: 'index', width: '100', label: '序号', align: 'center' },
    { prop: 'provinceName', label: '归属省', align: 'center' },
    { prop: 'cityName', label: '归属地市', align: 'center' },
    { prop: 'storeName', label: '归属营业厅', align: 'center' },
    { prop: 'screenId', label: '设备编号', align: 'center' },
    { type: 'date', prop: 'registerTime', label: '注册时间', align: 'center' },
    { prop: 'statusShow', label: '是否有效设备', align: 'center' }
  ],

  // 表格高度
  tableHeight: '345px',

  // 是否显示导出
  isExport: true,

  // 表格上方标题
  title: '大屏总数明细信息表',

  // state的数据
  storeState: {
    pageSize: 10,
    pageNum: 1,
    provinceCode: '000',
    cityCode: '',
    storeName: '',
    startDate: dayjs(preDate).format('YYYY-MM-DD 00:00:00'),
    endDate: dayjs(preDate).format('YYYY-MM-DD 00:00:00'),
    dateType: '',
  }
}

export default contentConfig
