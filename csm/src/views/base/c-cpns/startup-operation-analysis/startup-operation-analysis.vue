<template>
  <section class="sectionStyle">
    <page-search ref="searchRef" :content-config="contentConfig" @handleQueryClick="handleQueryClick" @handleQueryClicks="handleQueryClicks" :search-config="searchConfig" />

    <el-row>
      <el-col :span="12">
        <title-alone-possess v-if="allData.kjzs" :mb20="true" :title="'开机总数 : '" :redTitle="allData.kjzs.metricValue ? allData.kjzs.metricValueShow : '0'"
          :desc="'（统计口径：当日8:00-20:00，每日开机时长超1小时的大屏数量）'" />
      </el-col>
      <el-col :span="12">
        <title-alone-possess v-if="allData.kjl" :right="true" :mb20="true" :title="'开机率 : '" :redTitle="allData.kjl.metricValue ? allData.kjl.metricValueShow : '0'"
          :desc="'（统计口径：每日开机时长超过1小时的大屏数量占大屏总数的百分比）'" />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <title-alone-possess v-if="allData.pjyxsc" :mb20="true" :title="'平均运行时长 : '" :redTitle="allData.pjyxsc.metricValue ? allData.pjyxsc.metricValueShow : '0'" :desc="'（统计口径：大屏开机的平均时长）'" />
      </el-col>
    </el-row>

    <page-content ref="contentRef" :content-config="contentConfig" @handle-export-down-load="handleExport">
      <template #date>
        <span v-if="storeState?.date?.length > 0 && storeState.dateType === 'day'">查询周期：{{ storeState?.date[0] }} - {{ storeState?.date[1] }}</span>
        <span v-if="storeState?.date?.length > 0 && storeState.dateType === 'month'">查询周期：{{ handleDate(storeState?.date[0]) }} - {{ handleDate(storeState?.date[1]) }}</span>
      </template>
    </page-content>
  </section>
</template>
<script lang="ts" setup>
import { singleMetrics } from '@/api/query'
import searchConfig from './config/search.config'
import contentConfig from './config/content.config'
import PageSearch from '@/components/page-search/page-search.vue'
import PageContent from '@/components/page-content/page-content.vue'
import usePageContent from '@/hooks/usePageContent'
import TitleAlonePossess from '@/components/common/title-alone-possess/index.vue'
import useSystemStore from '@/store/system/system'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import { handleDate } from '@/utils/utils'
const handleQueryClicks = (e: any, dateOrMonth: string): void => {
  const data = {
    startDate: e.startDate,
    endDate: e.endDate,
    dateType: dateOrMonth,
    provinceCode: e.provinceCode,
    cityCode: e.cityCode,
    storeName: e.storeName,
    screenId: e.screenId,
    metricCodes: [
      'kjzs',
      'kjl',
      'pjyxsc'
    ]
  }
  void singleMetrics(data).then(res => {
    allData.value = res.data.metricsMap
  })
}
const allData: any = ref({})
const { contentRef, handleQueryClick } = usePageContent()
const systemStore = useSystemStore()
const storeState: Record<string, any> = storeToRefs(systemStore)[contentConfig.pageName]
const searchRef: any = ref()
function handleExport (): any {
  const queryInfo = searchRef.value?.getherQuery(false)
  return contentRef.value?.exportDownLoad(queryInfo)
}
</script>
<style lang="scss" scoped></style>
