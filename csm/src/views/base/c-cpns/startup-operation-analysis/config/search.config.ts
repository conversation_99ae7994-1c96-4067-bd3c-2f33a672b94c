import dayjs from 'dayjs'
// 获取前一天时间
import { getPreDay } from '@/utils/utils'
const lastMonth = dayjs().add(-1, 'month').format('YYYY-MM')
const preDate = getPreDay()
const searchConfig = {
  headerTitle: {
    title: '大屏开机运行分析：',
    mb20: true,
    right: false
  },

  formItems: [
    {
      // 时间 日
      type: 'date',
      prop: [dayjs(preDate).format('YYYYMMDD'), dayjs(preDate).format('YYYYMMDD')],
      propMonth: [lastMonth, lastMonth],
      key: 'date',

      // 是否支持月
      isSwitch: true
    },
    {
      // 省选择
      type: 'prov',
      key: 'provinceCode',
      prop: ''
    },
    {
      // 市选择
      type: 'city',
      key: 'cityCode',
      prop: ''
    },
    {
      // 营业厅选择
      type: 'select',
      key: 'storeName',
      prop: '',
    },

    {
      // 输入框
      type: 'input',
      prop: '',
      key: 'screenId',
      placeholder: '请输入设备编号'
    },
    {
      // 日期类型
      type: 'dateType',
      key: 'dateType',
      prop: '',
    }
    // {
    //     // 终端选择
    //     type:'select',
    //     prop:'',
    //     placeholder: '终端选择',
    //     options:[
    //         {value:1, label:'全部'},
    //         {value:2, label:'裸机'},
    //         {value:3, label:'信用购机'},
    //         {value:4, label:'优惠购机'},
    //         {value:5, label:'金币购机'},
    //         {value:6, label:'智能设备'},
    //         {value:7, label:'家庭宽带'},
    //     ]
    // },
    // {
    //     // 下单方式
    //     type:'select',
    //     prop:'',
    //     placeholder: '终端选择',
    //     options:[
    //         {value:1, label:'全部'},
    //         {value:2, label:'扫码购'},
    //         {value:3, label:'一键下单'},
    //     ]
    // },

  ]
}

export default searchConfig
