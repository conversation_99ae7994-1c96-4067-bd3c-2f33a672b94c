// 获取前一天时间
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
const preDate = getPreDay()
// 开机明细列表查询
const contentConfig = {
  // 接口名称
  pageName: 'content1694176809819',

  // 接口名称
  interface: 'deviceStatusOnline',

  // 表格参数
  tableColumn: [
    { type: 'index', width: '100', label: '序号', align: 'center' },
    { prop: 'provinceName', label: '归属省', align: 'center' },
    { prop: 'cityName', label: '归属地市', align: 'center' },
    { prop: 'storeName', label: '归属营业厅', align: 'center' },
    { prop: 'screenId', label: '设备编号', align: 'center' },
    { prop: 'queryTimes', label: '查询周期', align: 'center' },
    { prop: 'totalOnlineTime', label: '总在线时长', align: 'center' },
    { prop: 'avgDayOnlineTime', label: '日均在线时长', align: 'center' },
  ],

  // 表格高度
  tableHeight: 'auto',

  // 是否显示导出
  isExport: true,

  // 表格上方标题
  title: '开机明细表',

  // state的数据
  storeState: {
    pageList: [],
    pageTotal: 0,
    pageNum: 1,
    pageSize: 10,
    startDate: dayjs(preDate).format('YYYY-MM-DD'),
    endDate: dayjs(preDate).format('YYYY-MM-DD'),
    tableLoading: false,
    // 填写导出文件名称的props
    inputProp: '',
    dateType: '',
  }
}

export default contentConfig
