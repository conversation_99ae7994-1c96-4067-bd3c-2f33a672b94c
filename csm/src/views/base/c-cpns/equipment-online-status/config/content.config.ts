// 获取前一天时间
import dayjs from 'dayjs'
// import { getPreDay } from '@/utils/utils'
// const preDate = getPreDay()
const contentConfig = {
  // 接口名称
  pageName: 'content1694176968035',

  // 接口名称
  interface: 'deviceOnlineLiveDetails',

  // 表格参数
  tableColumn: [
    { type: 'index', width: '100', label: '序号', align: 'center' },
    { prop: 'provinceName', label: '归属省', align: 'center' },
    { prop: 'cityName', label: '归属地市', align: 'center' },
    { prop: 'storeName', label: '归属营业厅', align: 'center' },
    { prop: 'screenId', label: '设备编号', align: 'center' },
    { prop: 'statusShow', label: '当前在线状态', align: 'center' },
    { prop: 'firstLoginTime', label: '当日首次登录时间', align: 'center' },
  ],

  // 表格高度
  tableHeight: '345px',

  // 是否显示导出
  isExport: true,

  // 表格上方标题
  title: '当前在线大屏明细信息表',

  // state的数据
  storeState: {
    pageList: [],
    pageTotal: 0,
    pageNum: 1,
    pageSize: 10,
    // 填写导出文件名称的props
    inputProp: '',
    startDate: dayjs().format('YYYY-MM-DD'),
    endDate: dayjs().format('YYYY-MM-DD'),
    dateType: '',
  }
}

export default contentConfig
