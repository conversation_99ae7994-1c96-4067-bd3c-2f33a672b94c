// import dayjs from 'dayjs'
// 获取前一天时间
// const preDate = new Date(new Date().getTime() - 24 * 60 * 60 * 1000)
const searchConfig = {
  headerTitle: {
    title: '设备在线实况',
    desc: '',
    mb20: true,
    right: false
  },

  formItems: [
    {
      // 省选择
      type: 'prov',
      key: 'provinceCode',
      prop: ''
    },
    {
      // 市选择
      type: 'city',
      key: 'cityCode',
      prop: ''
    },
    {
      // 营业厅选择
      type: 'select',
      key: 'storeName',
      prop: '',
    },
    {
      // 日期类型
      type: 'dateType',
      key: 'dateType',
      prop: 'month',
    },
    // {
    //   // 时间
    //   type: 'date',
    //   prop: [dayjs(preDate).format('YYYYMMDD'),dayjs(preDate).format('YYYYMMDD')],
    //   key: 'date',

    //   // 是否支持月
    //   isSwitch: false
    // },
  ]
}

export default searchConfig
