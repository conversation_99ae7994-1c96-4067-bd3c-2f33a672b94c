<template>
    <!-- 设备在线情况 -->
    <section>
      <page-search
        :content-config="contentConfig"
        :search-config="searchConfig"
        @handleQueryClick="handleQueryClick"
        @handleQueryClicks="handleQueryClicks"
        ref="searchRef"
      >
      <template #date>
        <span>统计时间：{{ startTime }}</span>
      </template>
      </page-search>

      <!-- 分块显示的内容 -->
      <block-content v-if="allData.dqzxdps && allData.dqzxdpzxl" :data="[{
        title:'当前在线大屏数',
        span: allData.dqzxdps.metricValueShow ? allData.dqzxdps.metricValueShow : '0',
        desc:'（统计口径：当前状态为 “在线” 的大屏数量）',
      }, {
        title:'当前在线大屏在线率： ',
        span:allData.dqzxdpzxl.metricValueShow ? allData.dqzxdpzxl.metricValueShow : '0',
        desc:'（统计口径：当前状态为“在线”的大屏数量占大屏总数的百分比）'
      }]" />

      <page-content ref="contentRef" :content-config="contentConfig" @handle-export-down-load="handleExport">
        <template #date v-if="startTime">
          <span>统计时间：{{ startTime }}</span>
        </template>
      </page-content>
    </section>
</template>
<script setup lang="ts">
import { singleMetrics } from '@/api/query'
import BlockContent from '@/components/common/block-content/index.vue'
import searchConfig from './config/search.config'
import contentConfig from './config/content.config'
import PageSearch from '@/components/page-search/page-search.vue'
import PageContent from '@/components/page-content/page-content.vue'
import usePageContent from '@/hooks/usePageContent'
import dayjs from 'dayjs'
// import useSystemStore from '@/store/system/system'
// import { storeToRefs } from 'pinia'
import { ref } from 'vue'
const handleQueryClicks = (e: any, dateOrMonth: string): void => {
  const data = {
    startDate: e.startDate,
    endDate: e.endDate,
    dateType: dateOrMonth,
    provinceCode: e.provinceCode,
    cityCode: e.cityCode,
    storeName: e.storeName,
    metricCodes: [
      'dqzxdps',
      'dqzxdpzxl',
    ]
  }
  void singleMetrics(data).then(res => {
    allData.value = res.data.metricsMap
    startTime.value = res.data.startDate
    startTime.value = dayjs(res.data.startDate).format('YYYY年MM月DD日 HH时')
  })
}
const allData: any = ref({})
const startTime: any = ref({})
const { contentRef, handleQueryClick } = usePageContent()
// const systemStore = useSystemStore()
// const storeState: Record<string, any> = storeToRefs(systemStore)[contentConfig.pageName]
const searchRef = ref()
function handleExport (): any {
  const queryInfo = searchRef.value?.getherQuery(false)
  return contentRef.value?.exportDownLoad(queryInfo)
}
</script>
<style scoped lang="scss">
</style>
