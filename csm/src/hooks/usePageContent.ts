import { ref } from 'vue'
import type PageContent from '@/components/page-content/page-content.vue'

function usePageContent (): { contentRef: any, handleQueryClick: any } {
  const contentRef: any = ref<Array<InstanceType<typeof PageContent>> | InstanceType<typeof PageContent>>([])
  function handleQueryClick (queryInfo: any): void {
    if (typeof contentRef.value.forEach === 'function') {
      contentRef.value.forEach((item: any) => {
        item.fetchPageListData(queryInfo)
      })
    } else {
      contentRef.value?.fetchPageListData(queryInfo)
    }
  }

  return {
    contentRef,
    handleQueryClick,
  }
}

export default usePageContent
