import Login from '../api/login.ts'
import { useUserStore } from '../store/user'

export async function checkUser (): Promise<boolean> {
  return await new Promise(resolve => {
    const user = useUserStore()
    Login.info().then(infoRes => {
      if (infoRes.status === 0) {
        user.setUser(infoRes.data)
        resolve(infoRes.data)
      } else {
        user.setUser(null)
        resolve(user.isLogined)
      }
    }).catch(() => {
      user.setUser(null)
      resolve(user.isLogined)
    })
  })
}

export default {
  checkUser
}
