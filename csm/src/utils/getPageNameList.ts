const viewsContext: any = import.meta.glob('../views/**/*content.config.ts', {
  eager: true
})
const pageNames = []
for (const key in viewsContext) {
  pageNames.push({
    pageName: viewsContext[key].default.pageName,
    storeState: viewsContext[key].default.storeState
  })
}

const obj: any = {}
for (const item of pageNames) {
  obj[item.pageName] = item.storeState
}

sessionStorage.setItem('PageNameList', JSON.stringify(obj))
