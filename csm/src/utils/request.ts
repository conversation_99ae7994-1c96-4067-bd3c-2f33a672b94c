import axios from 'axios'
import { ElMessage } from 'element-plus'
import transitEncrypt from './transit-encrypt.ts'
import type { myAxiosConfig } from './types/index'

const BASE_URL = '/yundian/csm/api'
// 创建axios实例
const request = axios.create({
  baseURL: BASE_URL, // api 的 base_url
  timeout: 30000, // request timeout
  method: 'post',
  headers: {
    'Content-type': 'application/json',
  },
})

// request拦截器
request.interceptors.request.use(
  (config) => {
    const options = config as myAxiosConfig
    const needEncrypt = sessionStorage.getItem('needEncrypt') === '1' ? 1 : 0
    if (needEncrypt === 1) {
      options.authKey = transitEncrypt.getRandomAesKey()
      config.headers.leaf = transitEncrypt.encryptRsa(options.authKey)
      config.data = {
        encrypt: transitEncrypt.encryptAes(config.data, options.authKey),
      }
      // config.data = transitEncrypt.encryptAes(config.data, options.authKey)
    }
    return config
  },
  (error) => {
    // Do something with request error
    // console.log(error) // for debug
    void Promise.reject(error)
  }
)

// response 拦截器
request.interceptors.response.use(
  (response) => {
    /**
     * code为非20000是抛错 可结合自己业务进行修改
     */
    let res = response.data
    if (res.code === 0) {
      res.code = res.status === 0 ? 200 : res.status
    }
    if (response.config.responseType === 'arraybuffer') {
      if (response.data.byteLength < 1024) {
        res = ab2Json(response.data as ArrayBuffer)
      } else {
        res = {
          code: 0,
          status: 0,
          data: {
            headers: response.headers,
            body: response.data,
          },
        }
      }
    }
    if (res.status !== 0) {
      ElMessage.closeAll()
      const message = res.message
      ElMessage({
        message: (message !== null && message !== '') ? message : '操作失败，请重试',
        type: 'error',
        duration: 5 * 1000,
      })
      return response.data
    } else {
      const config: myAxiosConfig = response.config as myAxiosConfig
      if (response.config.responseType !== 'arraybuffer' &&
        config.authKey !== undefined &&
        config.authKey !== null &&
        config.authKey?.length > 1 &&
        typeof res.data === 'string') {
        res.data = JSON.parse(
          transitEncrypt.decryptAes(res.data, config.authKey)
        )
        // console.log(res)
      }
      return res
    }
  },
  async (error) => {
    ElMessage.closeAll()
    ElMessage({
      message: error.message,
      type: 'error',
      duration: 5 * 1000,
    })
    return await Promise.reject(error)
  }
)
function ab2Json (buf: ArrayBufferLike): any {
  if (buf !== null && buf.byteLength > 0) {
    const encodedString = String.fromCodePoint.apply(null, [...new Uint8Array(buf)])
    const decodedString = decodeURIComponent(escape(encodedString))
    // console.log(decodedString)
    return JSON.parse(decodedString)
  } else {
    return ''
  }
}
export default request
