import JSEncrypt from 'jsencrypt'
import CryptoJS from 'crypto-js'

const encryptor = new JSEncrypt() // 新建JSEncrypt对象
encryptor.setPublicKey('MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDTy220ZMFhFZPtAbyQKGaIYzv4S0jWjMn5/xuK2dczarHfEVaWJhtM+FQENcwMApevVwVaUjHb+JloNNBH1uYqDudKl/da/jgGwKnEuX/5YhlDfZ7lu4narQTSvCSb7pdXKksgJL+I0nNLI2VjxOkOC5RQvsr2UN3vWSwhuZ/eOQIDAQAB') // 设置公钥

export function encryptRsa (str: string): string | false {
  return encryptor.encrypt(str)
}

export function encryptAes (word: string, key: string): string {
  const keyHex = CryptoJS.enc.Utf8.parse(key)
  let srcs: CryptoJS.lib.WordArray
  if (typeof word === 'string') {
    srcs = CryptoJS.enc.Utf8.parse(word)
  } else {
    // if (typeof word == "object")
    // 对象格式的转成json字符串
    srcs = CryptoJS.enc.Utf8.parse(JSON.stringify(word))
  }
  return CryptoJS.AES.encrypt(srcs, keyHex, {
    // iv: iv,
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  }).toString()
}
export function decryptAes (word: string, key: string): string {
  const keyHex = CryptoJS.enc.Utf8.parse(key)
  const decrypted = CryptoJS.AES.decrypt(word, keyHex, {
    // iv: iv,
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })

  return decrypted.toString(CryptoJS.enc.Utf8)
}
function randomNumber (min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}
function randomNumArray (min: number, max: number, bitNumber: number): number[] {
  const result = []
  for (let i = 0; i < bitNumber; i++) {
    result.push(randomNumber(min, max))
  }
  // 默认字母小写，手动转大写
  return result
}
export function getRandomAesKey (): string {
  return randomNumArray(0, 15, 16).map(item => { return item.toString(16) }).join('')
}
export default {
  encryptRsa,
  encryptAes,
  decryptAes,
  getRandomAesKey,
}
