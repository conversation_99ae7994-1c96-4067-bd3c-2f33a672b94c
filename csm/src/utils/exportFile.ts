import dayjs from 'dayjs'

// 文件导出
export function ExportFile (data: { body: ArrayBuffer, headers: { 'content-disposition': string } }, name: any = ''): void {
  const date = String(dayjs(new Date()).format('YYYY-MM-DD'))
  let fileName
  if (data.headers['content-disposition']) {
    fileName = decodeURIComponent( // 获取文件名
      data.headers['content-disposition'].split('=')[1].replace( /\+/g, ' ' )
    )
  } else {
    fileName = name + date
  }
  const blob = new Blob([data.body])
  const link = document.createElement('a')
  link.download = fileName
  link.href = URL.createObjectURL(blob)
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  URL.revokeObjectURL(link.href)
  document.body.removeChild(link)
}

export default ExportFile
