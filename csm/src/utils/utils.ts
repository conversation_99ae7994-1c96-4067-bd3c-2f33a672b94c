import dayjs from 'dayjs'

export function isExternal (path: any): boolean {
  return /^(https?:|mailto:|tel:)/.test(path)
}
export function getPreDay (): Date {
  const date = new Date(dayjs(new Date()).format('YYYY-MM-DD'))
  date.setDate(date.getDate() - 1)
  return date
}
export function handleDate (value: any): string {
  const dateItem = value.split('-')
  return `${dateItem[0]}/${dateItem[1]}`
}
export default {
  isExternal,
  getPreDay,
  handleDate
}
