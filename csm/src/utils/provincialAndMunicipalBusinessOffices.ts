import { regionInfo } from '@/api/common'
import type { regionInfoResponse } from '@/api/types/regionInfo.d.ts'
import useCommonStore from '@/store/common/common'
import type { Item } from './types/index'

void regionInfo().then((res: regionInfoResponse) => {
  const { list } = res.data
  const arr: Item[] = []

  list.forEach(provide => {
    const curObj: Item = {
      label: provide.provinceName,
      value: provide.provinceCode,
      children: []
    }

    provide.cities.forEach((city: any) => {
      const obj: Item = {
        label: city.cityName,
        value: city.cityCode,
        children: []
      }

      city.stores.forEach((store: any) => {
        const obj2: Item = {
          label: store.storeName,
          value: store.storeName,
          children: []
        }
        obj.children.push(obj2)
      })

      curObj.children.push(obj)
    })

    arr.push(curObj)
  })

  const commonStore = useCommonStore()
  commonStore.provincesAndCitiesAction(arr)
})
