<template>
  <div>
    <el-table ref="tableRef" :data="tableData" v-bind="_options" @selection-change="handleSelectionChange"
      @row-click="handleRowClick" @cell-click="handleCellClick" @sort-change="handleSortChange">
      <template v-for="(col) in columns">
        <!---复选框, 序号 (START)-->
        <el-table-column v-if="col.type === 'index' || col.type === 'selection' || col.type === 'expand'"
          :index="indexMethod" v-bind="col"  v-bind:key="col.name">
          <!-- 当type等于expand时， 配置通过h函数渲染、txs语法或者插槽自定义内容 -->
          <template #default="{ row, $index }">
            <!-- render函数 (START) : 使用内置的component组件可以支持h函数渲染和txs语法 -->
            <component v-if="col.render" :is="col.render" :row="row" :index="$index" />
            <!-- render函数 (END) -->
            <!-- 自定义slot (START) -->
            <slot v-else-if="col.slot" name="expand" :row="row" :index="$index"></slot>
            <!-- 自定义slot (END) -->
          </template>
        </el-table-column>
        <!---复选框, 序号 (END)-->
        <!-- 渲染插槽 START -->
        <TableColumn  v-else :col="col" v-bind:key="col.name+'-else'" @command="handleAction">
          <template v-for="slot in  Object.keys($slots) " #[slot]="scope: Record<string, any>">
            <slot :name=" slot " v-bind="scope"></slot>
          </template>
        </TableColumn>
        <!-- 渲染插槽 END -->
      </template>
    </el-table>
    <!-- 分页器 -->
    <div v-if=" _options.showPagination " class="mt20">
      <el-pagination
        v-bind=" _paginationConfig "
        @size-change="pageSizeChange"
        class="el-pagination"
        :page-sizes=" [10] "
        :total="_options.paginationConfig.total || 0"
        small
        @current-change=" currentPageChange "
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { type ComputedRef, computed, ref } from 'vue'
import TableColumn from './table-column.vue'
import { ElTable } from 'element-plus'
import type { SortParams, TableProps } from './types/index'

const props = defineProps<TableProps>()

const tableRef = ref<InstanceType<typeof ElTable>>()
// 设置option默认值，如果传入自定义的配置则合并option配置项
const _options: ComputedRef<any> = computed(() => {
  const option = {
    stripe: false,
    tooltipEffect: 'dark',
    showHeader: true,
    showPagination: false,
    rowStyle: () => 'cursor:pointer' // 行样式
  }
  return Object.assign(option, props?.options)
})
// 合并分页配置
const _paginationConfig = computed(() => {
  const config = {
    total: 0,
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 30, 40, 50, 100],
    // layout: 'total, sizes, prev, pager, next, jumper',
    layout: 'total,sizes, prev, pager, next'
  }
  return Object.assign(config, _options.value.paginationConfig)
})
const emit = defineEmits([
  'selection-change', // 当选择项发生变化时会触发该事件
  'row-click', // 当某一行被点击时会触发该事件
  'cell-click', // 当某个单元格被点击时会触发该事件
  'command', // 按钮组事件
  'size-change', // pageSize事件
  'current-change', // currentPage按钮组事件
  'pagination-change', // currentPage或者pageSize改变触发
  'sort-change', // 列排序发生改变触发
])
// 自定义索引
function indexMethod (index: number): number {
  const tabIndex = index + (_paginationConfig.value.currentPage - 1) * _paginationConfig.value.pageSize + 1
  return tabIndex
}
// 切换pageSize
function pageSizeChange (pageSize: number): void {
  emit('size-change', pageSize)
  emit('pagination-change', 1, pageSize)
}
// 切换currentPage
function currentPageChange (currentPage: number): void {
  emit('current-change', currentPage)
  emit('pagination-change', currentPage, _paginationConfig.value.pageSize)
}
// 按钮组事件
function handleAction (command: any, row: any, index: number): void {
  emit('command', command, row, index)
}
// 多选事件
function handleSelectionChange (val: any): void {
  emit('selection-change', val)
}
// 当某一行被点击时会触发该事件
function handleRowClick (row: any, column: any, event: MouseEvent): void {
  emit('row-click', row, column, event)
}
// 当某个单元格被点击时会触发该事件
function handleCellClick (row: any, column: any, cell: any, event: MouseEvent): void {
  emit('cell-click', row, column, cell, event)
}
/**
 *  当表格的排序条件发生变化的时候会触发该事件
 * 在列中设置 sortable 属性即可实现以该列为基准的排序， 接受一个 Boolean，默认为 false。
 * 可以通过 Table 的 default-sort 属性设置默认的排序列和排序顺序。
 * 如果需要后端排序，需将 sortable 设置为 custom，同时在 Table 上监听 sort-change 事件，
 * 在事件回调中可以获取当前排序的字段名和排序顺序，从而向接口请求排序后的表格数据。
 */
function handleSortChange ({ column, prop, order }: SortParams<any>): void {
  emit('sort-change', { column, prop, order })
}
// 暴露给父组件参数和方法，如果外部需要更多的参数或者方法，都可以从这里暴露出去。
defineExpose({ element: tableRef })
</script>
<style lang="scss" scoped>
:deep(.el-pagination) {
  margin-top: 10px;

  .el-pagination__sizes {
    margin-right: auto;
  }
}

:deep(.el-image__inner) {
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    transform: scale(1.2);
  }
}
</style>
