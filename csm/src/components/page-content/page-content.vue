<template>
  <div class="page-search">
    <!--  表格导出以及标题  -->
    <section class="table-paging-header">
      <el-row>
        <el-col :span="12">
          <!-- 标题 -->
          <div class="left">
            <div class="title">{{ props.contentConfig.title }}</div>
            <div class="date">
              <slot name="date"></slot>
            </div>
          </div>
        </el-col>

        <el-col :span="12">
          <!-- 导出文件名称 -->
          <div class="right">
            <div v-show="props.contentConfig.isExport" class="export">
              <el-input v-model.trim="exportFileName" class="iptStyle" clearable placeholder="请输入导出文件名称"
                size="small" />
              <el-button class="searchStyle" size="small" type="primary" @click.stop="handleExportDownLoad"> 导出明细表 </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </section>

    <!-- 表格 -->
    <section class="table">
      <my-table :columns="props.contentConfig.tableColumn" v-loading="currentStoreData.tableLoading" :options="{
        height: props.contentConfig.tableHeight,
        maxHeight: props.contentConfig.tableHeight,
        size: 'small',
        defaultSort: { prop: 'name', order: 'ascending' },
        stripe: true,
        border: true,
        showPagination: true,
        paginationConfig: {
          total: currentStoreData.pageTotal,
          currentPage: currentStoreData.pageNum
        }
      }" :table-data="currentStoreData.pageList" @pagination-change="handleSelectionChange">
      </my-table>
    </section>
  </div>
</template>

<script lang="ts" setup>
import useSystemStore from '@/store/system/system'
import { storeToRefs } from 'pinia'
import MyTable from '@/components/table/my-table.vue'
import { fileDownLoad } from '@/api/fileDownLoad'
import exportFile from '@/utils/exportFile'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'
// import type { IProps } from './types/index'

interface IProps {
  contentConfig: {
    pageName: string
    tableColumn: any[]
    tableHeight: number | string
    isExport?: boolean
    inputProp?: string
    title: string
    storeState: object
    interface: string
  }
}
const emit = defineEmits(['handleExportDownLoad'])
// 获取store中对应的数据
const props = defineProps<IProps>()
const systemStore = useSystemStore()
const systemStoreRef = storeToRefs<any>(systemStore)
const currentStoreData = systemStoreRef[props.contentConfig.pageName]
const exportFileName = ref('')
const from = ref({})
fetchPageListData()

// 请求数据
function fetchPageListData (formData: any = {}): void {
  systemStore[props.contentConfig.pageName].tableLoading = true
  // 拼接store中的请求参数
  const pageInfo: any = {}
  const storeState = Object.keys(props.contentConfig.storeState || {})
  storeState.forEach((item: any) => {
    pageInfo[item] = currentStoreData.value[item]
  })
  // 存请求数据
  from.value = formData
  const queryInfo = { ...pageInfo, ...formData }

  systemStore.postPageListAction(
    {
      interface: props.contentConfig.interface,
      pageName: props.contentConfig.pageName
    },
    queryInfo
  )
}

// 分页改变
function handleSelectionChange (pageNum: number, pageSize: number): void {
  fetchPageListData({
    ...from.value,
    pageNum,
    pageSize: pageSize || currentStoreData.value.pageSize,
  })
}

// 导出文件
async function handleExportDownLoad (): Promise<boolean> {
  const inputProp = exportFileName.value
  emit('handleExportDownLoad', { name: inputProp })
  return true
}
// 导出文件
async function exportDownLoad (formData: any = {}): Promise<boolean> {
  const inputProp = exportFileName.value
  const res: {
    message: string
    code: number
    status: number
    data: { body: ArrayBuffer, headers: any }
  } = await fileDownLoad(props.contentConfig.interface, { ...formData, exportFileName: inputProp, })
  if (res.status !== 0) {
    ElMessage({
      message: (res.message !== null && res.message !== '') ? res.message : '操作失败，请重试',
      type: 'error',
      duration: 5 * 1000,
    })
  }
  exportFile(res.data, '')
  return true
}

defineExpose({ fetchPageListData, exportDownLoad })
</script>

<style lang="scss" scoped>
.page-search {
  color: #333;
  font-size: 14px;

  .table-paging-header {
    margin-bottom: 10px;
  }

  .left {
    display: flex;
    align-items: center;

    .date {
      font-size: 12px;
      margin-left: 10px;
      color: #b6b6b6;
    }
  }

  .right {
    .export {
      display: flex;
      justify-content: flex-end;

      .iptStyle {
        width: 150px;
        margin-right: 10px;
      }
    }
  }
}

.table {
  margin: 10px 0;
}
</style>
