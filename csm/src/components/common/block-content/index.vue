<template>
  <main>
    <!-- 分块显示的内容 -->
    <div class="blockList">
      <ul>
        <li v-for="(item,index) in props.data" v-bind:key="index">
          <div class="title">{{item.title}} <span>{{ item.span }}</span></div>
          <div class="desc">{{ item.desc}}</div>
        </li>
      </ul>
    </div>
  </main>
</template>
<script setup lang="ts">
import type { ItemType } from './types/type'

const props = defineProps({
  data: {
    type: Array as () => ItemType[],
    default () { return [] }
  }
})
</script>
<style scoped lang="scss">
.blockList {
  border: 1px solid #e7e9ee;
  margin-bottom: 20px;

  ul {
    display: flex;
    align-items: center;
    justify-content: center;

    li {
      flex: 1;
      padding: 20px;
      list-style: none;
      position: relative;

      &:last-child {
        &::before {
          content: none;
        }
      }

      &::before {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 0.5px;
        height: 70px;
        margin: auto;
        background: #e7e9ee;
      }

      .title {
        font-weight: bold;
        font-size: 16px;
        text-align: center;

        span {
          color: red;
        }
      }

      .desc {
        text-align: center;
        font-size: 12px;
        margin-top: 20px;
      }
    }
  }
}
</style>
