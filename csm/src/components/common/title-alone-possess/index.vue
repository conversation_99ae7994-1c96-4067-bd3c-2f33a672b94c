<template>
  <section>
    <div
        v-if="Object.keys(props.contentConfig).length || Object.keys(props.searchConfig).length"
        :class="['title-alone-possess', props.searchConfig.headerTitle.mb20?'mb20':'', props.searchConfig.headerTitle.right?'right':'']">
      <!-- 标题 -->
      <div class="title">{{ props.searchConfig.headerTitle.title }}</div>

      <!-- 红色数字 -->
      <div class="redTitle">{{ deviceStatusTotalDetails.pageTotal }}</div>

      <!-- 其他说明 -->
      <div v-show="props.searchConfig.headerTitle.desc" class="desc">{{ props.searchConfig.headerTitle.desc }}</div>
    </div>

    <div
        v-else
        :class="['title-alone-possess', props.mb20?'mb20':'', props.right?'right':'']">
      <!-- 标题 -->
      <div class="title">{{ props.title }}</div>

      <!-- 红色数字 -->
      <div class="redTitle">{{ props.redTitle }}</div>
      <div class="titleLast">{{ props.lastTitle }}</div>
      <!-- 其他说明 -->
      <div v-show="props.desc" class="desc">{{ props.desc }}</div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import useSystemStore from '@/store/system/system'

const props = defineProps({
  contentConfig: {
    type: Object,
    default () { return {} }
  },
  searchConfig: {
    type: Object,
    default () { return {} }
  },
  title: {
    type: String,
    default: '标题'
  },
  redTitle: {
    type: String,
    default: ''
  },
  lastTitle: {
    type: String,
    default: ''
  },
  desc: {
    type: String,
    default: ''
  },
  mb20: {
    type: Boolean,
    default: false
  },
  right: {
    type: Boolean,
    default: false
  }
})

const { deviceStatusTotalDetails } = useSystemStore()
</script>
<style lang="scss" scoped>
.title-alone-possess {
  display: flex;
  align-items: center;

  &.mb20 {
    margin-bottom: 20px;
  }

  &.right {
    justify-content: flex-end;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }

  .redTitle {
    color: red;
    font-size: 14px;
    margin-left: 5px;
  }
  .titleLast{
    font-size: 12px;
    margin-left: 5px;
  }
  .desc {
    font-size: 12px;
    margin-left: 10px;
  }
}
</style>
