// 表单项接口
export interface FormItem {
  type: string // 表单项类型
  prop: string // 表单项值
  isSwitch?: boolean // 是否支持月，如果type为'placeAnOrder'，则为true，否则为undefined
  inputVal?: any // 输入框，类型为'input'
  placeholder?: string // 输入框类型的placeholder
}

// 搜索配置接口
export interface SearchConfig {
  headerTitle: {
    title: string // 标题
    mb20: boolean // marginBottom 20
    right: boolean // 右居中
    desc?: string // 描述
  }
  formItems?: FormItem[] // 表单项配置数组，可选
  [key: string]: any // 允许其他字段
}

// 内容配置接口
export interface ContentConfig {
  pageName: string // 页面名称
  searchConfig: SearchConfig // 搜索配置
  [key: string]: any // 允许其他字段
  storeState?: any
}

// 组件属性接口
export interface IProps {
  contentConfig: ContentConfig | ContentConfig[] // 内容配置
  searchConfig: SearchConfig // 搜索配置
  [key: string]: any // 允许其他属性
}
