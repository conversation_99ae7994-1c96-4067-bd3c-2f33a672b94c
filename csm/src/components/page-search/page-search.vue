<template>
  <div class="page-search">
    <div
      :class="[
        'title-alone-possess',
        props.searchConfig.headerTitle.mb20 ? 'mb20' : '',
        props.searchConfig.headerTitle.right ? 'right' : '',
      ]"
    >
      <!-- 标题 -->
      <div class="title">{{ props.searchConfig.headerTitle.title }}</div>
      <!-- 红色数字 -->
      <div class="redTitle">
        <slot name="redTitle"></slot>
      </div>
      <div class="date">
        <slot name="date"></slot>
      </div>
      <!-- 其他说明 -->
      <div v-show="props.searchConfig.headerTitle.desc" class="desc">
        {{ props.searchConfig.headerTitle.desc }}
      </div>
    </div>

    <div class="searchForm">
      <ul>
        <li v-for="(val, index) in searchForm" v-bind:key="index">
          <el-cascader
            size="small"
            v-if="val.type === 'prov'"
            v-model="cascaderValue"
            ref="cascaderRef"
            style="width: 200px; margin-left: 10px"
            @change="handleChangeProvCity"
            @visible-change='handleCascaderVisible'
            :options="commonStore.provincesAndCities"
            clearable
          />

          <!--   下拉选择框   -->
          <el-select
            v-if="val.type === 'select' && val.key !== 'storeName'"
            v-model="val.prop"
            :placeholder="val.placeholder"
            class="selectStyle"
            size="small"
          >
            <el-option
              v-for="item in val.options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

          <!--   时间   -->
          <el-radio-group
            v-if="val.type === 'date' && val.isSwitch"
            v-model="switchDateType"
            label="size control"
            size="small"
          >
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="day">日</el-radio-button>
          </el-radio-group>
          <div
            v-if="switchDateType === 'day' && val.type === 'date' && val.types !== 'onlydate'"
            class="dateStyle"
          >
            <el-date-picker
              v-model="val.prop"
              @change="changeDate"
              :size="'small'"
              class="dateStyle"
              end-placeholder="结束时间"
              :disabled-date="disabledDateFn"
              range-separator="-"
              start-placeholder="开始时间"
              type="daterange"
            />
          </div>
          <div
            v-if="switchDateType === 'month' && val.type === 'date' && val.types !== 'onlydate'"
            class="dateStyle"
          >
            <el-date-picker
              v-model="val.propMonth"
              @change="changeMonth"
              :size="'small'"
              class="dateStyle"
              end-placeholder="结束时间"
              :disabled-date="disabledMonthFn"
              range-separator="-"
              start-placeholder="开始时间"
              type="monthrange"
            />
          </div>
          <!-- 单日期展示 -->
          <div
            v-if="switchDateType === 'day' && val.type === 'date' && val.types === 'onlydate'"
            class="dateStyle"
          >
            <el-date-picker
            style="margin-top: -2.5px;"
              v-model="val.prop"
              :clearable="false"
              :editable="false"
              size="small"
              class="dateStyle"
              :disabled-date="disabledDateFn"
              placeholder="开始时间"
              type="date"
            />
          </div>
          <!--   输入框    -->
          <el-input
            v-if="val.type === 'input'"
            v-model="val.prop"
            :placeholder="val.placeholder"
            class="inputStyle"
            clearable
            size="small"
          />
        </li>
        <el-button
          class="submitStyle"
          size="small"
          type="primary"
          @click="handleQueryClick()"
          >查询</el-button
        >
      </ul>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, nextTick } from 'vue'
import dayjs from 'dayjs'
import { getPreDay } from '@/utils/utils'
import useSystemStore from '@/store/system/system'
import useCommonStore from '@/store/common/common'
import { ElMessage } from 'element-plus'
import _ from 'lodash'
import type { ContentConfig, IProps } from './types/index'
const preDate = getPreDay()
const lastMonth = dayjs().add(-1, 'month').format('YYYY-MM')
const emit = defineEmits(['handleQueryClick', 'handleQueryClicks'])
const props = defineProps<IProps>()
const switchDateType = ref<'day' | 'month'>('day')
const searchForm = reactive<Record<string, any>>(
  props.searchConfig.formItems ?? [],
)
const cascaderValue = ref()
const cascaderRef = ref()
const systemStore: Record<string, any> = useSystemStore()
const commonStore = useCommonStore()
initProvinceAndCity(commonStore.provincesAndCities)
watch(
  () => commonStore.provincesAndCities,
  (item) => {
    initProvinceAndCity(item)
  }
)

// 联级选择器展示问题
function handleCascaderVisible (flag: boolean): void {
  if (flag) {
    const value = [...cascaderValue.value]
    cascaderValue.value = []
    void nextTick(() => {
      cascaderValue.value = value
    })
  }
}
function initProvinceAndCity (item: any): void {
  const arr = []
  if (item != null && item.length > 0) {
    arr.push(item[0].value)
    let child = (item[0].children !== undefined && item[0].children[0] !== undefined) ? item[0].children[0] : null
    while (child !== null) {
      arr.push(child.value)
      child = (child.children !== undefined && child.children[0] !== undefined) ? child.children[0] : null
    }
    cascaderValue.value = arr
  }
  // 路由跳转/数据恢复默认值
  for (const searchFormKey in searchForm) {
    if (searchForm[searchFormKey].key === 'screenId') {
      searchForm[searchFormKey].prop = ''
    } else if (searchForm[searchFormKey].key === 'date' && searchForm[searchFormKey].types !== 'onlydate') {
      searchForm[searchFormKey].prop = [dayjs(preDate).format('YYYYMMDD'), dayjs(preDate).format('YYYYMMDD')]
      searchForm[searchFormKey].propMonth = [lastMonth, lastMonth]
    } else if (searchForm[searchFormKey].key === 'date' && searchForm[searchFormKey].types === 'onlydate') {
      searchForm[searchFormKey].prop = dayjs(preDate).format('YYYYMMDD 00:00:00')
    } else if (searchForm[searchFormKey].key === 'brand') {
      searchForm[searchFormKey].prop = ''
    } else if (searchForm[searchFormKey].key === 'goodsType') {
      searchForm[searchFormKey].prop = ''
    } else if (searchForm[searchFormKey].key === 'purchaseApproach') {
      searchForm[searchFormKey].prop = ''
    }
  }
  handleChangeProvCity(arr)
  handleQueryClick()
}
// 日期选择范围不能大于12个月处理
function changeDate (date: any): void {
  if (date[1].getTime() - date[0].getTime() > 31536000000) {
    ElMessage({
      message: '日期选择时间不能超过12个月',
      type: 'error',
      duration: 2000,
    })
    searchForm[0].prop[0] = new Date().getTime() - 8.64e7
    searchForm[0].prop[1] = new Date().getTime() - 8.64e7
  }
}
// 日期选择范围不能大于12个月处理
function changeMonth (month: any): void {
  // 将字符串转换为Day.js对象并计算时间差
  if (dayjs(month[1]).diff(month[0], 'month') > 12) {
    ElMessage({
      message: '日期选择时间不能超过12个月',
      type: 'error',
      duration: 2000,
    })
    // 创建当前时间的 Date 对象
    const currentDate = new Date();
    // 设置为上个月第一天
    currentDate.setMonth(currentDate.getMonth() - 1); // 注意这里需要先将月份减去1再进行操作
    currentDate.setDate(1); // 设置为每月的第一天
    searchForm[0].propMonth[0] = currentDate.toLocaleString()
    searchForm[0].propMonth[1] = currentDate.toLocaleString()
  }
}
function getherQuery (sync: boolean = true): Record<string, any> {
  // 处理options参数
  const options: any = {}
  for (const searchFormKey in searchForm) {
    if (searchForm[searchFormKey].key === 'dateType') {
      searchForm[searchFormKey].prop = switchDateType.value
      options.dateType = searchForm[searchFormKey].prop
    } else if (searchForm[searchFormKey].key === 'date') {
      // 月份选择 默认展示上个月
      if (searchForm[searchFormKey].propMonth && switchDateType.value === 'month') {
        const dateArr = JSON.parse(
          JSON.stringify(searchForm[searchFormKey].propMonth),
        )
        // 没有选择就不要处理时间
        if (dateArr?.map) {
          const newDate = dateArr.map((item: string) => {
            return String(dayjs(item).format('YYYY-MM-DD'))
          })
          options.startDate = newDate[0]
          options.endDate = newDate[1]
        }
      } else if (searchForm[searchFormKey].prop !== null) {
        // 日期选择 默认展示前一天
        const dateArr = JSON.parse(
          JSON.stringify(searchForm[searchFormKey].prop),
        )
        // 没有选择就不要处理时间
        if (dateArr?.map) {
          const newDate = dateArr.map((item: string) => {
            return String(dayjs(item).format('YYYY-MM-DD'))
          })
          options.startDate = newDate[0]
          options.endDate = newDate[1]
        }
        if (typeof dateArr === 'string') {
          const newDate = String(dayjs(dateArr).format('YYYY-MM-DD'))
          options.startDate = newDate
          options.endDate = newDate
        }
      }
    } else {
      options[searchForm[searchFormKey].key] = searchForm[searchFormKey].prop
    }
  }
  if (sync) {
    let contentConfigs: ContentConfig[]
    if (_.isArray(props.contentConfig)) {
      contentConfigs = props.contentConfig
    } else {
      contentConfigs = [props.contentConfig]
    }

    contentConfigs.forEach((item: ContentConfig) => {
      // 将开始结束时间存到store中
      if (options.startDate || options.endDate) {
        systemStore[item.pageName].date = [options.startDate, options.endDate]
      } else {
        systemStore[item.pageName].date = []
      }

      // 重置pageNum
      if (_.isArray(props.contentConfig)) {
        (props.contentConfig).forEach(
          (item: ContentConfig) => {
            systemStore[item.pageName].pageNum = 1
          },
        )
      } else {
        systemStore[(props.contentConfig).pageName].pageNum = 1
      }

      // 同步到store中
      const exclude = ['pageName']
      if (_.isArray(props.contentConfig)) {
        (props.contentConfig).forEach(
          (item: ContentConfig) => {
            const curStoreState = Object.keys(item.storeState)
            const curStore = systemStore[item.pageName]
            curStoreState.forEach((key) => {
              if (!exclude.includes(key)) {
                if (options[key] !== null) {
                  curStore[key] = options[key]
                }
              }
            })
          },
        )
      } else {
        const curStore =
          systemStore[(props.contentConfig).pageName]
        const curStoreState = Object.keys(
          (props.contentConfig).storeState,
        )
        curStoreState.forEach((key) => {
          if (!exclude.includes(key)) {
            // console.log([key, options[key]])
            curStore[key] = options[key]
          }
        })
      }
    })
  }
  // console.log(options, 'optionsoptions')

  return options
}

// 点击查询
function handleQueryClick (): void {
  // 发射事件
  const options = getherQuery()
  emit('handleQueryClick', options)
  emit('handleQueryClicks', options, switchDateType.value)
}

/* 省市选择回调 */
function handleChangeProvCity (val: string[]): void {
  // 将省市营业厅code添加进searchForm
  for (const searchFormKey in searchForm) {
    const item = searchForm[searchFormKey] as {
      type: string
      key: string
      prop: any
    }

    if (item.type === 'prov') {
      item.prop = val?.[0] ? val[0] : null
    } else if (item.type === 'city') {
      item.prop = val?.[1] ? val[1] : null
    } else if (item.key === 'storeName') {
      item.prop = val?.[2] ? val[2] : null
    }
  }
}
defineExpose({ getherQuery })
const disabledDateFn = (time: any): boolean => {
  const date = new Date()
  const nowYear = date.getFullYear()
  const changeYear = time.getFullYear()
  if (changeYear !== 2023 && changeYear !== nowYear) {
    return true
  } else if (time.getTime() > Date.now() - 8.64e7) {
    return true
  } else {
    return false
  }
}
// 月份限制 只能选择本年度 以及 本年上个月
const disabledMonthFn = (time: any): boolean => {
  const date = new Date()
  const nowYear = date.getFullYear()
  const changeYear = time.getFullYear()
  if (changeYear !== 2023 && changeYear !== nowYear) {
    return true
  } else if (time.getTime() > Date.now() - 30 * 24 * 60 * 60 * 1000) {
    return true
  } else {
    return false
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-range-editor.el-input__wrapper) {
  width: 200px;
}

:deep(.el-cascader-node__prefix) {
  left: 5px;
}

:deep(.el-radio-group) {
  height: 24px;
  margin-left: 10px;
  margin-right: 10px;
}
.page-search {
  display: flex;
  align-items: flex-start;

  .searchForm {
    flex: 1;
    min-width: 0;

    ul {
      padding: 0;
      margin: 0;
      display: flex;
      flex-flow: wrap;
      justify-content: flex-end;

      li {
        list-style: none;
        display: flex;

        .selectStyle {
          width: 110px;
          margin-left: 10px;
          margin-bottom: 10px;
        }

        .dateStyle {
          margin-bottom: 10px;
          margin-left: 10px;
        }

        .inputStyle {
          width: 120px;
          height: 24px;
          margin-left: 10px;
        }
      }

      .submitStyle {
        margin-left: 10px;
      }
    }
  }
}

.title-alone-possess {
  display: flex;
  align-items: center;

  &.mb20 {
    margin-bottom: 20px;
  }

  &.right {
    justify-content: flex-end;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }

  .redTitle {
    color: red;
    font-size: 14px;
    margin-left: 5px;
  }
  .date {
      font-size: 12px;
      margin-left: 10px;
      color: #b6b6b6;
    }
  .desc {
    font-size: 12px;
    margin-left: 10px;
  }
}
</style>
