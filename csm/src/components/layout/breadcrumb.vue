<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb" v-if="levelList && levelList.length">
      <template v-for="(item, index) in levelList">
        <el-breadcrumb-item v-if="item.meta && item.meta.title" :key="item.path">
          <span v-if="item.redirect === 'noredirect' || index == levelList.length - 1" class="no-redirect">{{ item.meta.title
          }}</span>
          <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
        </el-breadcrumb-item>
      </template>
    </transition-group>
  </el-breadcrumb>
</template>

<script setup lang="ts">
import { ElBreadcrumb, ElBreadcrumbItem } from 'element-plus'
import { ref, watch, nextTick } from 'vue'
import { type RouteLocationMatched, useRouter, useRoute } from 'vue-router'
import pathToRegexp from 'path-to-regexp/dist/index.js'
defineOptions({
  name: 'PageBreadcrumb'
})

const levelList = ref<Array<RouteLocationMatched | any> | null>()
const $router = useRouter()
const $route = useRoute()
// $router.afterEach((to)=>{
//   $route.value = to
//   getBreadcrumb()
// })
watch($route, () => {
  void nextTick(() => {
    getBreadcrumb()
  })
})
getBreadcrumb()

function getBreadcrumb (): void {
  const matched: Array<RouteLocationMatched | any> = $route.matched.filter((item: { name: any }) => {
    if (item.name !== null) {
      return true
    } else {
      return false
    }
  })
  // const first = matched[0]
  // if (first && first.name !== 'home') {
  //     matched = [{ path: '/home', meta: { title: '首页' }}].concat(matched)
  // }
  levelList.value = matched
}
function pathCompile (path: string): string {
  // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561
  const { params } = $route
  const toPath = pathToRegexp.compile(path)
  return toPath(params)
}
function handleLink (item: RouteLocationMatched): void {
  const { redirect, path } = item
  if (redirect !== null) {
    void $router.push(item)
  } else {
    void $router.push(pathCompile(path))
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 10px;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
