<template>
  <div>
    <a v-if="isExternalLink(props.to)" :href="props.to">
      <slot />
    </a>
    <RouterLink v-else :to="props.to">
      <slot />
    </RouterLink>
  </div>
</template>

<script setup lang="ts">
import { isExternal } from '../../../utils/utils.ts'

defineOptions({
  name: 'SidebarLink'
})
const props = defineProps({
  to: {
    type: String,
    required: true
  }
})
function isExternalLink (routePath: any): boolean {
  return isExternal(routePath)
}
// function linkProps(url:any) {
//     if (isExternalLink(url)) {
//         return {
//             is: 'a',
//             href: url,
//             target: '_blank',
//             rel: 'noopener'
//         }
//     }
//     return {
//         is: 'router-link',
//         to: url
//     }
// }
</script>
