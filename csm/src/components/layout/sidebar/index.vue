<template>
    <el-scrollbar wrap-class="scrollbar-wrapper">
        <el-menu
            v-if="showSidebar"
            :show-timeout="200"
            :default-active="current.path"
            :collapse="isCollapse"
            mode="vertical"
            text-color="#fff"
            active-text-color="#fff"
        >
            <SidebarItem v-for="route in routerPath" :key="route.path" :item="route" :base-path="route.path" />
        </el-menu>
    </el-scrollbar>
</template>

<script setup lang="ts">
import SidebarItem from './sidebar-item.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import { ElMenu, ElScrollbar } from 'element-plus'

const router = useRouter()
const current = router.currentRoute
const routerPath = router.options.routes
const showSidebar = ref(true)
const isCollapse = ref(false)
</script>

<style lang="scss" scoped>
    .el-menu{
        background: transparent!important;
    }
</style>
