<template>
  <div v-if="!item.hidden && item.children" class="menu-wrapper">
    <template
      v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
      <app-link :to="resolvePath(onlyOneChild.path)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{ 'submenu-title-noDropdown': !isNest }">
          <item v-if="onlyOneChild.meta" :icon="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"
            :title="onlyOneChild.meta.title" />
        </el-menu-item>
      </app-link>
    </template>

    <el-sub-menu v-else :index="resolvePath(item.path)">
      <template #title>
        <item v-if="item.meta" :icon="item.meta.icon" :title="item.meta.title" />
      </template>
      <template v-for="child in item.children">
        <template v-if="!(child.meta && child.meta.hidden)">
          <sidebar-item v-if="child.children && child.children.length > 0" :key="child.path" :is-nest="true" :item="child"
            :base-path="resolvePath(child.path)" class="nest-menu" />
          <app-link v-else :key="child.name" :to="resolvePath(child.path)">
            <el-menu-item :index="resolvePath(child.path)">
              <item v-if="child.meta" :icon="child.meta && child.meta.icon" :title="child.meta && child.meta.title" />
            </el-menu-item>
          </app-link>
        </template>
      </template>
    </el-sub-menu>
  </div>
</template>

<script>
import path from 'path-browserify'
import Item from './menu-item.vue'
import AppLink from './link.vue'
// import { ElSubMenu, ElMenu, ElMenuItem, ElScrollbar } from 'element-plus'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  data () {
    return {
      onlyOneChild: null,
      wisdomScreenShow: true,
      shops: ['8071']
    }
  },
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  created () {
  },
  methods: {
    hasOneShowingChild (children, parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden === 1) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath (routePath) {
      if (this.isExternalLink(routePath)) {
        return routePath
      }
      return path.resolve(this.basePath, routePath)
    },
    isExternalLink (routePath) {
      const reg = /^(https?:|mailto:|tel:)/
      return reg.test(routePath)
    }
  },
  watch: {
    item: {
      handler () {
        // classificationManagement.queryBigScreenStatus({
        //     shopId:this.$store.getters.shopId
        // }).then(res => {
        //     if(res.data.status == 1){
        //         this.wisdomScreenShow = true
        //         let dom = document.querySelectorAll('.sidebar-container .el-menu .menu-wrapper')
        //         let dom2 = Array.from(dom)
        //         dom2.map((item) => {
        //             item.style.display = 'block'
        //         })
        //     }else{
        //         this.wisdomScreenShow = false
        //         let dom = document.querySelectorAll('.sidebar-container .el-menu .menu-wrapper')
        //         let dom2 = Array.from(dom)
        //         dom2.map((item) => {
        //             if(item.getAttribute('isscreen') == 1){
        //                 item.style.display = 'none'
        //             }else{
        //                 item.style.display = 'block'
        //             }
        //         })
        //     }
        // }).catch(() => {
        //     this.wisdomScreenShow = false
        // })
      },
      immediate: true
    }
  },
}
</script>
<style lang="scss" scoped>
.hide {
  display: none;
}

.el-menu-item {
  color: rgb(50, 50, 50);
  cursor: pointer;
  font-weight: 700;
  transition: border-color .3s, background-color .3s, color .3s;

  &:focus,
  &:hover {
    background-color: #fafafa !important;
  }

  &.is-active {
    color: #007cff !important;
    background-color: #ebf2ff !important;
    font-weight: 700;
  }
}
</style>
<style lang="scss">
.el-sub-menu__title {
  color: rgb(50, 50, 50) !important;
  font-weight: 700 !important;
}

.el-menu .el-sub-menu.is-active .el-submenu__title {
  color: #007cff !important;
}
.app-breadcrumb.el-breadcrumb .no-redirect{
  font-weight: bold;
  color: #3E7CFF !important;
}
</style>
