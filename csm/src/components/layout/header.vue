<template>
  <div class="header">
    <div class="name">
      <img src="@/assets/header/logo.png" />
      <el-divider direction="vertical"></el-divider>
      社区云店智慧屏运营管理平台
    </div>
    <div v-if="user.isLogined" class="user">
      <el-dropdown>
        <span class="el-dropdown-link">
          您好,{{ user.info.userName }}
          <el-icon class="el-icon--right"><CaretBottom /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElDivider, ElDropdown, ElDropdownMenu, ElDropdownItem, ElIcon } from 'element-plus'
import { useUserStore } from '../../store/user'
import { CaretBottom } from '@element-plus/icons-vue'
import { loginOut } from '@/api/login'
import { useRouter } from 'vue-router'
const router = useRouter()
const user = useUserStore()
// 点击登出
async function logout (): Promise<void> {
  await loginOut().then(() => {
    router.go(0)
  })
}
</script>
<style lang="scss">
.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  color:#fff;
  align-items: center;
  width:100%;
  padding:0 16px;
  height:48px;
  background-image: linear-gradient(#007cff,#007cff),linear-gradient(#131532,#131532);
  .name {
    display: flex;
    align-items: center;
    margin-right:auto;
    font-size:18px;
  }
  .user{
    margin-left:auto;
    color:#fff;
    display: flex;
    justify-content: center;
    .el-dropdown-link{
      color:#fff;
      font-size:14px;
      padding-left:20px;
      background: url('@/assets/header/avatar.jpg') no-repeat left center;
      background-size: auto 20px ;
    }
  }

}
</style>
