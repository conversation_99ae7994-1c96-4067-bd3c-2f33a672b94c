<template>
  <section class="app-main">
    <router-view v-slot="{ Component }">
      <transition name="fade-transform" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </section>
</template>
<script setup lang="ts">
defineOptions({
  name: 'LayoutMain'
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .app-main {
      /*50 = navbar  */
      background: #ffffff;
      border-radius: 2px;
      overflow-y: auto;
      overflow-x: hidden;
      // @include scrollBar;
  }
</style>
