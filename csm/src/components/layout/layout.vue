<template>
  <div class="layout">
    <div class="layout-header">
      <Header></Header>
    </div>
    <div v-if="info.visible && user.isLogined" class="layout-body">
      <Sidebar class="layout-body-sidebar" />
      <div class="layout-body-mainbox">
        <Breadcrumb />
        <Main />
      </div>
    </div>
    <div v-else class="layout-body-norights">
      您无权访问该页面,请重新 <a :href="b2b">登录</a>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from '../../store/user'
import Main from './main.vue'
import Sidebar from './sidebar/index.vue'
import Header from './header.vue'
import Breadcrumb from './breadcrumb.vue'
defineOptions({
  name: 'PageLayout'
})
const b2b = import.meta.env.VITE_B2B_LINK // 登录链接

const user = useUserStore()
const info = user.info

</script>

<style lang="scss">
.layout {
  height: 100vh;
  width: 100%;

  &-header {
    display: flex;
    justify-content: center;

  }

  &-body {
    display: flex;
    height: calc(100vh - 48px);
    flex-direction: row;
    justify-content: space-around;
    background-color: #f6f6f6;

    &-sidebar {
      width: 200px;
      background: #fff;
    }

    &-mainbox {
      text-align: left;
      padding: 18px;
      width: calc(100vw - 200px);
    }

    &-norights {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 40%
    }
  }
}</style>
