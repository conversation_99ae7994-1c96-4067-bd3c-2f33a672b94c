import { defineStore } from 'pinia'

export class UserInfo {
  userName!: string | null
  loginNum!: string
  province!: string | null
  city!: string | null
  userId!: string | null
  status!: string | null
  roleNum!: string | null
  visible: boolean
  constructor () {
    this.visible = false
  }
}

export const useUserStore = defineStore('user', {
  state: () => {
    const data: { isLogined: boolean, info: UserInfo } = {
      isLogined: false,
      info: new UserInfo()
    }
    return data
  },
  // 也可以这样定义
  // state: () => ({ count: 0 })
  actions: {
    setUser (user: UserInfo | null) {
      this.info = user ?? new UserInfo()
      if ((user != null) && (user.loginNum !== '')) {
        this.isLogined = true
      }
    },
  },
})

export default {
  useUserStore
}
