import { defineStore } from 'pinia'
import { deviceStatusTotalDetails } from '@/api/query'
import type { State } from '@/store/system/type'

const state: State = (sessionStorage.getItem('PageNameList') != null) ? JSON.parse((sessionStorage.getItem('PageNameList')) as any) : {}

const systemStore: any = defineStore('system', {
  state: (): State => (state),
  actions: {
    async postPageListAction (contentConfig: { interface: string, pageName: string }, queryInfo: any) {
      const pageListResult = await deviceStatusTotalDetails(contentConfig.interface, queryInfo)
      const { total, list, pageNum, pageSize } = pageListResult.data

      // 处理枚举值
      const filterList = list.map((item: any) => {
        return {
          ...item,
          firstLoginTime: item.firstLoginTime ? this.timestampToTime(item.firstLoginTime) : '-',
        }
      })

      setTimeout(() => {
        this[contentConfig.pageName].pageList = filterList || []
        this[contentConfig.pageName].pageTotal = total || 0
        this[contentConfig.pageName].pageNum = pageNum || 1
        this[contentConfig.pageName].pageSize = pageSize || 10
        this[contentConfig.pageName].tableLoading = false
      }, 500)
    },
    // 时间戳转为时间
    timestampToTime (timestamp: any) {
      const date = new Date(timestamp)
      const Y = date.getFullYear() + '-'
      const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
      const D = date.getDate() < 10 ? '0' + date.getDate() + ' ' : date.getDate() + ' '
      const H = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
      const m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
      const S = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
      return Y + M + D + H + m + S
    },
  },
})

export default systemStore
