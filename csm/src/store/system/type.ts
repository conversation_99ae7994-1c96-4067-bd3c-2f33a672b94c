export interface IList {
  id: number
  name: string
  realname: string
  cellphone: number
  enable: number
  departmentId: number
  roleId: number
  createAt: string
  updateAt: string
}

export interface IDevicesState {
  pageList: any[]
  pageTotal: number
  pageNum: number
  pageSize: number
}

export interface StoreState {
  [key: string]: any
  storeState?: PageFormData
}

export interface PageFormData {
  pageList?: any[]
  pageTotal?: number
  pageNum?: number
  pageSize?: number
  tableLoading?: boolean

  // 填写导出文件名称的props
  inputProp?: string

  // 营业厅名称
  storeName?: string

  // 省id
  provinceCode?: string

  // 市id
  cityCode?: string

  // 市id
  dateType?: string

  // 开始时间
  startDate?: string

  // 结束时间
  endDate?: string

  // 开始结束时间数组
  date?: any[]
}

export type State = Record<string, PageFormData>
