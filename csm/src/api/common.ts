import request from '../utils/request.ts'
import type { regionInfoResponse } from '@/api/types/regionInfo.d.ts'

/**
 * @description 获取省,市,营业厅
 * @returns Promise<regionInfoResponse>
 */
export async function regionInfo (): Promise<regionInfoResponse> {
  return await request({
    url: '/common/regionInfo',
    method: 'post',
    data: {
      provinceCode: '',
      cityCode: '',
      storeName: ''
    }
  })
}

export default {
  regionInfo
}
