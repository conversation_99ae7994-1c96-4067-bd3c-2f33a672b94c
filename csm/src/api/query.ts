import request from '../utils/request.ts'
import { type deviceStatusTotalDetailsRequest, type deviceStatusTotalDetailsResponse } from '@/api/types/deviceStatusTotalDetails'

export async function deviceStatusTotalDetails (interfaceName: string, queryInfo: deviceStatusTotalDetailsRequest = {}): Promise<deviceStatusTotalDetailsResponse> {
  queryInfo.startDate = queryInfo.startDate ? queryInfo.startDate : null
  queryInfo.endDate = queryInfo.endDate ? queryInfo.endDate : null
  queryInfo.cityCode = queryInfo.cityCode ? queryInfo.cityCode : null
  queryInfo.dateType = queryInfo.dateType ? queryInfo.dateType : 'day'
  queryInfo.provinceCode = queryInfo.provinceCode ? queryInfo.provinceCode : null
  queryInfo.storeName = queryInfo.storeName ? queryInfo.storeName : null
  queryInfo.pageNum = queryInfo.pageNum ? queryInfo.pageNum : 1
  queryInfo.pageSize = queryInfo.pageSize ? queryInfo.pageSize : 10
  queryInfo.screenId = queryInfo.screenId ? queryInfo.screenId : ''

  return await request({
    url: `/query/${interfaceName}`,
    method: 'post',
    data: queryInfo,
  })
}

// 页面小指标接口
export async function singleMetrics (data: any): Promise<any> {
  return await request({
    url: '/query/singleMetrics',
    method: 'post',
    data
  })
}

// 终端销售分析-占比及排名类查询
export async function salesAnalyse (data: any): Promise<any> {
  return await request({
    url: '/query/salesAnalyse',
    method: 'post',
    data
  })
}
export default {
  deviceStatusTotalDetails,
  singleMetrics
}
