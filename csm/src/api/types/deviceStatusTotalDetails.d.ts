export interface deviceStatusTotalDetailsItem {
  statTime: string
  provinceCode: string
  provinceName: string
  cityCode: string
  cityName: string
  storeName: string
  screenId: string
  registerTime: number
  status: string
  statusShow: string
}

export interface deviceStatusTotalDetailsResponse {
  status?: number
  message?: string
  additions?: null
  data: {
    totalPages: number
    pageNum: number
    pageSize: number
    size: number
    total: number
    list: deviceStatusTotalDetailsItem[]
  }
}

export interface deviceStatusTotalDetailsRequest {
  pageNum?: number
  pageSize?: number
  startDate?: string | null
  endDate?: string | null
  provinceCode?: string | null
  cityCode?: string | null
  dateType?: string | null
  screenId?: string | null
  storeName?: string | null
}

export interface IResponse<T> {
  data: T
}
