import { createRouter, createWebHashHistory, type RouteRecordRaw } from 'vue-router'

import loginUtils from '../utils/login.ts'
import Layout from '../components/layout/layout.vue'
import { Histogram, UserFilled, TrendCharts } from '@element-plus/icons-vue'
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/base'
  },
  {
    path: '/base',
    name: 'Base',
    component: Layout,
    // 重定向
    redirect: '/base/devices',
    meta: {
      title: '智慧屏基础运营',
      icon: Histogram
    },
    children: [
      {
        path: '/base/devices',
        name: 'BaseDevices',
        meta: {
          title: '设备运行情况',
          hidden: false
        },
        component: async () => await import('../views/base/devices.vue') // component: import('../views/reg.vue')
      },
      {
        path: '/base/online',
        name: 'BaseOnline',
        meta: {
          title: '设备在线情况',
          hidden: false
        },
        component: async () => await import('../views/base/online.vue') // component: import('../views/reg.vue')
      }
    ]
  },
  {
    path: '/customer',
    name: 'Customer',
    component: Layout,
    // 重定向
    redirect: '/customer/traffic',
    meta: {
      title: '客户行为分析',
      icon: UserFilled
    },
    children: [
      {
        path: '/customer/traffic',
        name: 'CustomerTraffic',
        meta: {
          title: '客户流量分析',
          hidden: false
        },
        component: async () => await import('../views/customer/traffic.vue') // component: import('../views/reg.vue')
      },
      {
        path: '/customer/channel',
        name: 'CustomerChannel',
        meta: {
          title: '客户下单渠道',
          hidden: false
        },
        component: async () => await import('../views/customer/channel.vue') // component: import('../views/reg.vue')
      },
      {
        path: '/customer/interaction',
        name: 'CustomerInteraction',
        meta: {
          title: '实物互动分析',
          hidden: false
        },
        component: async () => await import('../views/customer/interaction.vue') // component: import('../views/reg.vue')
      }
    ]
  },
  {
    path: '/screen',
    name: 'Screen',
    component: Layout,
    // 重定向
    redirect: '/screen/order',
    meta: {
      title: '智慧屏销售分析',
      icon: TrendCharts
    },
    children: [
      {
        path: '/screen/order',
        name: 'ScreenOrder',
        meta: {
          title: '订单明细数据',
          hidden: false
        },
        component: async () => await import('../views/screen/order.vue') // component: import('../views/reg.vue')
      },
      {
        path: '/screen/sale',
        name: 'ScreenSale',
        meta: {
          title: '终端销售分析',
          hidden: false,
          refresh: true
        },
        component: async () => await import('../views/screen/sale.vue') // component: import('../views/reg.vue')
      }
    ]
  },
]

const router = createRouter({
  history: createWebHashHistory(), // createWebHistory(),
  routes
})

router.beforeEach(async (_to, _from, next) => {
  await loginUtils.checkUser()
  next()
})
export default router
