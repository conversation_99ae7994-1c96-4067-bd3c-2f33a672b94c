module.exports = {
  "env": {
    "browser": true,
    "es2021": true
  },
  "extends": [
    "standard-with-typescript",
    "plugin:vue/vue3-essential",
    'plugin:@typescript-eslint/recommended',
  ],
  "parser": 'vue-eslint-parser', // 解析vue
  "overrides": [
    {
      "env": {
        "node": true
      },
      "files": [
        ".eslintrc.{js,cjs}"
      ],
      "parserOptions": {
        "sourceType": "script"
      }
    }
  ],
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true,
    },
    "tsconfigRootDir": __dirname,
    "parser": '@typescript-eslint/parser', 
    "project": ["./tsconfig.json"],
    "extraFileExtensions":[
      ".vue"
    ]
  },
  "plugins": [
    "vue"
  ],
  "rules": {
    "@typescript-eslint/no-explicit-any": "off",
    "comma-dangle": "off",
    "@typescript-eslint/comma-dangle": "off",
    "vue/multi-word-component-names": "off",
    "@typescript-eslint/strict-boolean-expressions": "off"
  }
}
