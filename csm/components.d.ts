/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    BlockContent: typeof import('./src/components/common/block-content/index.vue')['default']
    Breadcrumb: typeof import('./src/components/layout/breadcrumb.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    Header: typeof import('./src/components/layout/header.vue')['default']
    Layout: typeof import('./src/components/layout/layout.vue')['default']
    Link: typeof import('./src/components/layout/sidebar/link.vue')['default']
    Main: typeof import('./src/components/layout/main.vue')['default']
    MenuItem: typeof import('./src/components/layout/sidebar/menu-item.vue')['default']
    MyTable: typeof import('./src/components/table/my-table.vue')['default']
    PageContent: typeof import('./src/components/page-content/page-content.vue')['default']
    PageSearch: typeof import('./src/components/page-search/page-search.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Sidebar: typeof import('./src/components/layout/sidebar/index.vue')['default']
    SidebarItem: typeof import('./src/components/layout/sidebar/sidebar-item.vue')['default']
    SvgIcon: typeof import('./src/components/svg-icon/index.vue')['default']
    TableColumn: typeof import('./src/components/table/table-column.vue')['default']
    TitleAlonePossess: typeof import('./src/components/common/title-alone-possess/index.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
