server {
    if ($request_method !~ ^(GET|HEAD|POST)$ ) {
        return 444;
    }
    listen  80  default;
    server_name  localhost;
    access_log off;
    location / {
        return 403;
    }
}
server {
    listen       80;
    listen  [::]:80;
    server_name  bd.shop.10086.cn grey.bd.shop.10086.cn bd.staff.ydsc.liuliangjia.cn;

    if ($request_method !~ ^(GET|HEAD|POST)$ ) {
        return 444;
    }
    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;
    if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})") {
        set $curtime $1$2$3$4;
    }
    access_log  /home/<USER>/logs/nginx/nginx.access.$curtime.log aspire;
    gzip on;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_http_version 1.1;
    gzip_comp_level 2;
    gzip_types text/plain application/x-javascript application/javascript text/css application/xml;
    gzip_vary on;
    etag off;
    root   /var/www/;
    add_header "x-content-type-options" "nosniff";
    add_header "x-frame-options" "SAMEORIGIN";
    add_header "x-xss-protection" "1; mode=block";
    add_header "Content-Security-Policy" "frame-ancestors 'self'";
    if ( $request_uri ~ (\./|%2[eE]%2[fF]|%00) ) {
      rewrite ^.*$ /yundian/csm/404.html? permanent;
    }
    location / {
        index  index.html index.htm;
    }
    location /yundian/csm/ {
        index index.html;
    }

    #error_page  404              /404.html;

    error_page 404 403 =200 /yundian/csm/404.html; #新增404定义
    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
