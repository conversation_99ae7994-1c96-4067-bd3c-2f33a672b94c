FROM aspirecn-euler-nginx:1.24-webkeeper

COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/default.conf /etc/nginx/conf.d/default.conf
COPY dist /var/www/yundian/csm

# ENV LD_PRELOAD=/usr/local/lib/libwrapperio.so
# ENV APP_ip=*************
# ENV APP_temp=1
# ENV APP_name=template1

EXPOSE 80
VOLUME [ "/home/<USER>/logs/nginx/" ]
# ENTRYPOINT ["/docker-entrypoint.sh"]
# STOPSIGNAL SIGQUIT
# CMD ["/usr/sbin/nginx", "-g", "daemon off;"]