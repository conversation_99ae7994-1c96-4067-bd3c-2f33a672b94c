{"name": "csm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode staff", "build": "vue-tsc && vite build", "build:staff": "vue-tsc && vite build --mode staff", "preview": "vite preview", "lint": "eslint --ext .ts,.jsx,.js,.vue src --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.7.7", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "element-plus": "^2.3.12", "elui-china-area-dht": "^2.0.0", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "path-browserify": "^1.0.1", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.4", "@types/crypto-js": "^4.1.1", "@types/lodash": "^4.14.198", "@types/node": "^20.5.7", "@typescript-eslint/eslint-plugin": "^6.6.0", "@vitejs/plugin-vue": "^4.2.3", "consola": "^3.2.3", "eslint": "^8.49.0", "eslint-config-standard-with-typescript": "^39.0.0", "eslint-plugin-vue": "^9.17.0", "path-to-regexp": "^6.2.1", "prettier": "^3.0.3", "sass": "^1.66.1", "typescript": "^5.0.2", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-style-import": "^2.0.0", "vue-eslint-parser": "^9.3.1", "vue-tsc": "^1.8.5"}}