import { defineConfig } from 'vite'
import path from 'path'
import vue from '@vitejs/plugin-vue'
// import eslint from 'vite-plugin-eslint
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

import {
  createStyleImportPlugin,
  ElementPlusResolve,
} from 'vite-plugin-style-import'

import terser from "@rollup/plugin-terser"
// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  build: {
    minify: 'terser',
    rollupOptions: {
      plugins: [
        terser([])
      ]
    }
  },
  plugins: [
    vue(),
    // eslint(),
    AutoImport({
      resolvers: [
        ElementPlusResolver()
      ],
    }),
    Components({
      resolvers: [
        ElementPlusResolver()
      ],
    }),
    createStyleImportPlugin({
      resolves: [
        ElementPlusResolve(),
      ],
    }),
  ],
  esbuild: {
    jsxFactory: 'h',
    jsxFragment: 'Fragment',
    jsxInject: "import { h } from 'vue';"
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    }
  },
  server: {
    proxy: {
      '/yundian/csm/api/': 'https://m.staff.ydsc.liuliangjia.cn'
    }
  },
  define: {
    'process.env': {
      BASE_API: 'https://m.staff.ydsc.liuliangjia.cn/yundian/csm/api'
    }
  }
})
