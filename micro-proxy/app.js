/*
 * @Author: yuxuan
 * @Date: 2024-06-05 19:28:38
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-08-16 17:32:44
 * @Description: file content
 */
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const { appServerMicroApps, mainFrameConfig, noAuthMicroApps } = require('./../config.json');
// const { proxyConfig } = require('./../configProxy.json');

const app = express();

// 增加请求体大小限制
// app.use(express.json({ limit: '500mb' }));
// app.use(express.urlencoded({ limit: '500mb', extended: true }));

// 代理到静态资源服务器 1
// app.use('/micrologin', createProxyMiddleware({
//   target: 'http://localhost:8111/micrologin',
//   changeOrigin: true
// }));

// 代理到静态资源服务器 2
// app.use('/', createProxyMiddleware({
//   target: 'http://localhost:8010',
//   changeOrigin: true
// }));

function setProxy (context, target, pathRewrite = undefined) {
  console.log(context, target, pathRewrite)
  if (!context || !target) return

  if (pathRewrite) {
    target = `${target}${pathRewrite['^' + context]}`
  }
  const proxy = createProxyMiddleware({
    target,
    changeOrigin: true
  })
  app.use(context, proxy)
}
// 代理到独立服务login应用
noAuthMicroApps.forEach(item => {
  if (item.singleOnly) {
    const pathRewrite = {}
    pathRewrite[`^${item.microAppPre}`] = item.microAppPre
    setProxy(item.microAppPre, `http://localhost:${item.devPort}`, pathRewrite)
  }
})
// 代理到微服务主应用-其他子应用均由主应用转发，无需额外配置代理；
setProxy('/', `http://localhost:${mainFrameConfig.devPort}`)

app.listen(appServerMicroApps.devPort, () => {
  console.log(`Server is running on http://localhost:${appServerMicroApps.devPort}`);
});