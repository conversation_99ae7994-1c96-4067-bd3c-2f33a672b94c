/*
 * @Author: yuxuan
 * @Date: 2024-06-05 19:28:38
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-08-16 17:32:44
 * @Description: file content
 */
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const { appServerMicroApps, authMicroApps, noAuthMicroApps } = require('../config.json');
const { proxyConfig, port } = require('./../configPart.json');



const app = express();
console.log(process.argv)

// 代理到独立服务

noAuthMicroApps.concat(authMicroApps).forEach(item => {
  if (process.argv.indexOf(item.microAppName) > -1) {
    const proxyConfig = {
      target: `http://localhost:${item.devPort}`,
      changeOrigin: true,
      pathRewrite: {
        ["^"+ item.microAppEnter] : "/"
      }
    }
    const proxy = createProxyMiddleware(proxyConfig)
    console.log([item.microAppEnter, proxyConfig])
    app.use(item.microAppEnter, proxy)
  }
})
Object.keys(proxyConfig).forEach(key => {
  const item = proxyConfig[key]
  console.log([key,item])
  const proxy = createProxyMiddleware(item)
  app.use(key, proxy)
})

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});
