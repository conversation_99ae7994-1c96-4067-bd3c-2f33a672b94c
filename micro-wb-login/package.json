{"name": "login", "author": "TurboC", "description": "webbas鉴权模块", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"asp-smart-ui": "^1.1.4", "axios": "^1.7.7", "concurrently": "^5.0.2", "core-js": "^3.6.4", "element-ui": "^2.13.2", "es6-promise": "^4.2.8", "js-base64": "^3.7.2", "js-cookie": "^2.2.1", "mockjs": "^1.1.0", "qs": "^6.13.0", "rxjs": "^6.5.4", "terser-webpack-plugin": "^4.2.3", "vue": "2.6.14", "vue-router": "^3.1.6", "vuex": "^3.1.3", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^5.0.1", "eslint-plugin-import": "2.27.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "2.6.14", "webpack": "4.47"}}