/**
 * @author: TurboC
 * @date: 2020-12-16 20:18:14
 */
const path = require('path')
const { name } = require('./package')
const { mainFrameConfig, noAuthMicroApps } = require('./../config.json')
const { proxyConfig } = require('./../configProxy.json')
const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV)
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const TerserPlugin = require('terser-webpack-plugin') // 清除打包后代码注释
const ReplaceStringPlugin = require('../plugins/replace-string-plugin')
// const CompressionWebpackPlugin = require("compression-webpack-plugin") // gzip压缩

let port
let enter
let pathName = `/${name}`
noAuthMicroApps && noAuthMicroApps.forEach(item => {
  if (item.microAppName.toLowerCase() === name.toLowerCase()) {
    port = item.devPort
    enter = item.microAppEnter
    pathName = item.microAppPre
  }
})

function resolve (dir) {
  return path.join(__dirname, dir)
}
const cacheGroups = {
  elementui: {
    name: 'elementui', // split elementUI into a single package
    test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
    priority: 20 // the weight needs to be larger than libs and app or it will be packaged into libs or app
  },
  vendors: {
    name: 'chunk-vendors',
    test: /[\\/]node_modules[\\/]/,
    chunks: 'initial',
    priority: 2,
    reuseExistingChunk: true,
    enforce: true
  }
}

module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: `${pathName}`,
  outputDir: `../${mainFrameConfig.mainAppName}${enter}`,
  assetsDir: 'static',
  filenameHashing: true,
  productionSourceMap: false, // 生产环境的 source map【修复360漏洞，默认false】
  // tweak internal webpack configuration.
  // see https://github.com/vuejs/vue-cli/blob/dev/docs/webpack.md
  devServer: {
    hot: true,
    disableHostCheck: true,
    port,
    overlay: {
      warnings: false,
      errors: true
    },
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Cache-Control': 'no-cache',
      pragma: 'no-cache',
      Expires: 0
    },
    proxy: proxyConfig
  },
  chainWebpack: config => {
    config.when(IS_PROD, config => {
      // 打包分析
      // config.plugin("webpack-report").use(BundleAnalyzerPlugin, [{
      //   analyzerMode: "static"
      // }])
      // 拆分文件
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: cacheGroups
      })
      config.plugin('replace-specific-string')
        .use(ReplaceStringPlugin, [{
          rules: [
            {
              test: [
                /\/opt\/aspire\/product\/rancher([\w\-/.]+)/g,
                /http:\/\/10.[\w./:-]+/g
              ]
            }]
        }])
      // config.optimization.runtimeChunk({
      //   name: entryPoint => `manifest.${entryPoint.name}`
      // })
    })
  },
  // configureWebpack: config => {
  //   const plugins = [];
  //   if (IS_PROD) {
  //     plugins.push(
  //       new CompressionWebpackPlugin({
  //         filename: "[path].gz[query]",
  //         algorithm: "gzip",
  //         test: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i, // gzip压缩文件类型
  //         threshold: 10240,
  //         minRatio: 0.8,
  //         deleteOriginalAssets: false // 删除原文件
  //       })
  //     );
  //   }
  //   config.plugins = [...config.plugins, ...plugins];
  // },
  // 自定义webpack配置
  configureWebpack: {
    optimization: {
      minimize: IS_PROD,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            format: {
              comments: false
            },
            output: {
              comments: false
            }
          },
          extractComments: false
        })
      ]
    },
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    // 配置微应用的打包, 把微应用打包成 umd 库格式
    output: {
      library: `${name}-[name]`,
      libraryTarget: 'umd',
      jsonpFunction: `webpackJsonp_${name}`
    }
  }
}
