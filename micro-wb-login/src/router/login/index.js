/*
 * @Author: yuxuan
 * @Date: 2024-01-12 11:36:34
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-06-07 21:06:47
 * @Description: file content
 */
/**
 * *内置路由
 * Created by TurboC on 2021/03/12
 */
export const login = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录'
    },
    component: () => import(/* webpackChunkName: "loginConfig" */ '@/views/login/index')
  },
  {
    path: '/404',
    title: '404',
    name: 'noPage',
    component: () => import(/* webpackChunkName: "loginConfig" */ '@/views/error/404')
  },
  // {
  //     path: '/modifyPwd',
  //     name: 'modifyPwd',
  //     meta: {
  //         title: '修改密码'
  //     },
  //     component: () => import('@/views/webbas/setting/modifyPwd')
  // },
  {
    path: '/forgetPWD',
    name: 'forgetPWD',
    meta: {
      title: '忘记密码'
    },
    component: () => import(/* webpackChunkName: "loginConfig" */ '@/views/login/components/pwd/forgetPwd')
  },
  {
    path: '/forgetPWD4A',
    name: 'forgetPWD4A',
    meta: {
      title: '忘记4A密码'
    },
    component: () => import(/* webpackChunkName: "loginConfig" */ '@/views/login/components/pwd/forgetPWD4A')
  }
]

export default login
