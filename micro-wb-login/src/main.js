/*
 * @Author: yuxuan <EMAIL>
 * @Date: 2024-05-14 17:18:27
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-09-11 13:47:42
 * @Description: file content
 */
import Vue from 'vue'
import App from './App.vue'
import store from './store'
import router from './router'

// element-ui
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
// 引入全局样式
import './assets/index.scss'
// 全局设置
import aspHttps from '../../micro-wb-main/src/utils/http.js'
import validation from '@/utils/validation.js'
import util from '@/utils/util.js'

// 全局设置
import directiveEvent from '@/directive'

const {
  apiConfig,
  projectConfig,
  pageConfig
} = require('./../../config.json')

Vue.use(ElementUI)

Vue.prototype.$main_tools = {
  sessionStorage,
  validation,
  util
}
Vue.prototype.$apiConfig = apiConfig
Vue.prototype.$projectConfig = projectConfig
Vue.prototype.$pageConfig = pageConfig
Vue.prototype.$aspHttps = aspHttps
Vue.prototype.$reponseStatus = aspHttps.reponseStatus
Vue.prototype.$aspHttps.$store = store
directiveEvent.init(Vue)

new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#app')
