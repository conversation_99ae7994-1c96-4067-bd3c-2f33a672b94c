/**
 * Created by TurboC on 2019/08/01.
 * webbbas框架的store
 */
// import Vue from 'vue'

const state = {
  secretWord: '',
  secretHeader: '',
  secretType: ''
}

const mutations = {
  setData: (state, data) => {
    state.secretWord = data.word
    state.secretType = data.type
    state.secretHeader = data.headerWord
  }
}

const actions = {}

const getters = {}


export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
