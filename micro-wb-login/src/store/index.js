/** Created by TurboC on 2021/03/12 **/
import Vue from 'vue'
import Vuex from 'vuex'
import VuexPersistence from 'vuex-persistedstate'
// import { debounce } from 'throttle-debounce'

Vue.use(Vuex)

const webbasModulesFiles = require.context('./modules', true, /\.js$/)
const modules = webbasModulesFiles.keys().reduce((modules, modulePath) => {
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = webbasModulesFiles(modulePath)
  const moduleNames = moduleName.split('/')[moduleName.split('/').length - 1]
  modules[moduleNames] = value.default
  return modules
}, {})

const plugins = [
  VuexPersistence({
    storage: window.sessionStorage,
    key: 'secretConf',
    modules: [modules.vuexStorage],
    reducer: (state) => {
      // console.log('loin -》state', state, modules.vuexStorage)
      return { ...{ vuexStorage: state.vuexStorage } }
    }
  })
]

const store = new Vuex.Store({
  modules,
  plugins
})

export default store
