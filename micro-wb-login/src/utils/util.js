import { asp_Encrypt } from 'asp-smart-ui/lib/utils'
const util = {}
const { pageConfig } = require('./../../../config.json')
const encrypt_kei = '0'
/**
 * 跳转至登录页面重新登录
 */
util.relogin = () => {
  const path = pageConfig.loginPage.microAppPre + '#' + pageConfig.loginPage.routerPath
  const title = pageConfig.loginPage.titleName
  // 若当前页面就是登陆页面则不做任何操作
  if (window.location.pathname !== pageConfig.loginPage.microAppPre) {
    sessionStorage.clear()
    window.history.pushState({}, title, path)
    window.location.reload()
  }
}

/**
 * 单向加密菜单ID
 * @param {*} str 菜单ID
 * @returns 加密后的菜单ID
 */
util.encryptCode = (str) => {
  if (!str) {
    return ''
  }
  const encryptStr = asp_Encrypt(str, encrypt_kei)
  // 转义除以下字符, 只保留不会被encodeUriComponent的字符
  const regex = /[^A-Za-z0-9-_.!~*'()]/g
  return encryptStr.replace(regex, 'z')
}

function findFirstLeafWithUrl(node) {
  // 检查当前节点是否是叶子节点并且url不为空
  if (node.leaf === true && node.url) {
    return node.url
  }

  // 如果当前节点有子节点，递归遍历子节点
  if (node.children && node.children.length > 0) {
    for (const child of node.children) {
      const result = findFirstLeafWithUrl(child)
      if (result) {
        return result // 一旦找到符合条件的节点，立即返回其url
      }
    }
  }

  return null // 如果没有找到符合条件的节点，返回null
}

function findUrlInTree(tree) {
  if (!Array.isArray(tree)) {
    return null
  }
  for (const rootNode of tree) {
    let url = findFirstLeafWithUrl(rootNode)
    if (url) {
      if (rootNode.id) {
        url += '?_s=' + util.encryptCode(rootNode.id)
      }
      return { url, rootNode } // 找到第一个符合条件的url后返回
    }
  }
  return null // 如果所有根节点都没有符合条件的节点，返回null
}

function getFirstMenuUrl(tree) {
  try {
    const obj = findUrlInTree(tree)
    return obj?.url || ''
  } catch (e) {
    return ''
  }
}
util.getFirstMenuUrl = getFirstMenuUrl

export default util
