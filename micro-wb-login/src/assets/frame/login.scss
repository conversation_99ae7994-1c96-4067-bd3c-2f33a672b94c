/**
* Created by TurboC on 2019/08/01.
* 登陆页面样式
*/

.wblogin {
  position: relative;
  height: 100%;
  background: #fff;
  -webkit-background-size: cover;
  background-size: cover;
  overflow: hidden;
  .login-head {
    display: inline-block;
    padding: 10px;
    h1 {
      float: right;
      color: #00a3ff;
      font-size: 25px;
      margin-left: 15px;
      margin-top: 3px;
      letter-spacing: 3px;
    }
  }
  .login-wrap {
    position: fixed;
    right: 160px;
    top: 320px;
    width: 395px;
    -webkit-transform: translateY(-60%);
    transform: translateY(-60%);

    .code-img {
      .el-input-group__append {
        border: none;
        background: none;
        padding-right: 0;
        #imgObj {
          min-width: 100px;
          height: 32px;
        }
      }
    }
    .el-card__header {
      padding: 10px 0 5px 10px;
      .el-radio.is-bordered {
        padding: 10px;
        border: none;
        height: auto;
      }
      .el-radio.is-bordered + .el-radio.is-bordered {
        margin-left: 0px;
      }
    }
    .el-input__inner {
      height: 34px;
      line-height: 34px;
    }
    .iconfont {
      font-size: 20px;
    }
    /*-- 清除必填符 --*/
    .el-form-item.is-required > .el-form-item__label:before {
      content: ' ';
    }
    .el-form-item {
      .el-form-item__content {
        .code-img {
          .el-input-group__append {
            padding: 0 0 0 14px;
          }
        }
      }
    }
    .captcha-btn {
      border: none;
      background: #f5f7fa;
      outline: none;
    }
    // 忘记密码
    .forgetPassword-wrap {
      position: absolute;
      bottom: -35px;
      right: 0;
      a {
        cursor: pointer;
        &:hover {
          color: #419fff;
        }
      }
    }
    .forgetPWD-wrap {
      padding: 30px 20px;
    }
  }
  .forget-wrap {
    padding: 20px;
    border-radius: 10px;
    background-color: #ffffff;
  }
  .login-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: center;
    line-height: 25px;
    font-size: 12px;
  }
  .login-ele-icon {
    position: absolute;
    left: 0;
    width: 100%;
    img {
      width: 100%;
    }
  }
  .login-type-header-css {
    // 登录框头部登录类型栏样式
    display: inline-block;
    width: calc(100% - 65px);
    vertical-align: top;
    .el-radio {
      margin-right: 0;
    }
    label {
      width: 150px;
    }
  }
  .cursor-pointer-css {
    cursor: pointer;
  }
  .check-img-css {
    float: left;
    height: 90%;
  }
  .padding-css {
    padding-left: 4px;
    padding-right: 4px;
  }
  .main-nav {
    font-size: 16px;
    width: 50%;
    margin: 0 auto;
    ul {
      padding-top: 18px;
    }
    li {
      float: left;
      padding: 0 8px 15px;
      text-align: center;
      margin-right: 5px;
      // border-bottom: 1px solid $color-theme;
      cursor: pointer;
    }
  }
  // 以下样式后续优化代码之后删除
  .label-right-css {
    text-align: right;
    vertical-align: middle;
  }
  .width-200-css {
    width: 200px;
  }
  .top-10-css {
    top: 10px;
  }
  .margin-top-30-css {
    margin-top: 30px;
  }
  .check-code-row-css {
    margin-left: -50px;
    margin-right: -30px;
    margin-bottom: 5px;
  }
  .reset-password-title-css {
    padding: 10px 0 20px 0;
  }
}

.verify-info {
  // 忘记密码、修改密码样式
  position: relative;
  height: 100%;
  background: ghostwhite;
  -webkit-background-size: cover;
  background-size: cover;
  overflow: hidden;
  .head {
    background: #fff;
    width: 100%;
    .logo {
      display: inline-block;
      padding: 10px;
      h1 {
        float: right;
        color: #00a3ff;
        font-size: 25px;
        margin-left: 15px;
        margin-top: 3px;
        letter-spacing: 3px;
      }
    }
  }
  .grid-content {
    height: 30px;
    line-height: 40px;
  }
  .login-ele-icon {
    position: absolute;
    left: 13%;
    top: 50%;
    -webkit-transform: translateY(-55%);
    transform: translateY(-55%);
  }
  .login-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: center;
    line-height: 25px;
    font-size: 12px;
  }
}
