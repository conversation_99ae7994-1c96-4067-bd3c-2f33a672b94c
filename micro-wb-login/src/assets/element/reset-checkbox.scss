/**
* Created by TurboC on 2019/08/01.
* 重写按钮框样式
*/
@import "../mixins/mixins";
.webbas {
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: $wb-theme-color;
        border-color: $wb-theme-color;
    }
    // 禁用且选中的多选框，重置背景色边框色，防止被以上样式覆盖
    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
        background-color: #f2f6fc;
        border-color: #dcdfe6;
    }

    .el-checkbox__input.is-focus .el-checkbox__inner {
        border-color: $wb-theme-color;
    }

    .el-checkbox__inner:hover {
        border-color: $wb-theme-color;
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
        color: $wb-theme-color;
    }
    // 穿梭框里的多选框换行展示
    .el-transfer-panel__list .el-checkbox {
        display: block;
    }
}
