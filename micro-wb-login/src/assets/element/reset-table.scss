/**
* Created by TurboC on 2019/08/01.
* 重写表格样式
*/
.webbas {
    .el-table {
        //height: 100%;
        border: 1px solid rgba(223, 233, 252, 1);
        .cell {
            padding: 0; // 去掉element-ui自带的值
        }
        tr {
            height: 34px;
        }
        td {
            padding: 3px; // 去掉element-ui自带的值
            box-sizing: border-box; // 统一各浏览器盒模型的计算
        }
        .cell.el-tooltip {
            //min-width: 20px;
            min-width: 30px;
        }
        th {
            border-right: 1px solid rgba(195, 213, 255, 0.5);
            color: rgba(83, 96, 126, 1);
            background-color: #E8F0FC;
            font-size: 14px;
            padding: 0px 3px 0px 3px; // 去掉element-ui自带的值
        }
        .sort-caret {
            left: 1px;
        }
        .caret-wrapper {
            width: 14px;
        }
        .el-form-item__label {
            width: auto;
        }
        .el-form-item__content {
            width: 100%;
            line-height: 30px;
        }
    }
    .el-table__body-wrapper::-webkit-scrollbar {
        width: 5px; // 横向滚动条
        height: 5px; // 纵向滚动条 必写
    }
    ::-webkit-scrollbar-thumb {
        background-color: rgba(53, 113, 230, 0.3);
        border-radius: 10px;
    }
    .el-table--border td:first-child .cell, .el-table--border th:first-child .cell {
        padding-left: 0;
    }
    .el-table .caret-wrapper {
        height: auto;
    }
    .el-table .sort-caret.ascending {
        top: -12px;
    }
    .el-table .sort-caret.descending {
        bottom: auto;
    }
    .el-table::before{
        height: auto;
    }

}
// 处理表格悬浮弹框的样式
.el-tooltip__popper.is-dark {
    max-width: 50%;
}
