/**
 * 混合配置-通用模块
 */
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
import { asp_randomNumString } from 'asp-smart-ui/lib/utils/common'
export default {
  methods: {
    // 数据加解密
    async initDataAPI () {
      const securityObject = {}
      this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
      let response = await this.$aspHttps.asp_Post(this.$apiConfig.safePathPrefix + '/encrypt/getPublicKey', {})
      if (this.$reponseStatus(response) && Object.prototype.hasOwnProperty.call(response, 'data') && Object.prototype.hasOwnProperty.call(response.data, 'encrypt')) {
        if (response.data.encrypt === '1') {
          // 保存数据
          securityObject.on = 1
          securityObject.aesKeyValue = asp_randomNumString(0, 15, 16, 16)
          this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          // 启动加密需要sessionStorage中的securityObject的秘钥
          securityObject.whiteList = this.$aspHttps.asp_EncryptPlus(this.$projectConfig.whiteList)
          // 存储白名单
          this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          // 发送AES密钥给后端
          asp_RSAKey.RSASetPublic(response.data.modulus, response.data.exponent)
          const sendKey = asp_RSAKey.RSAEncrypt(securityObject.aesKeyValue)
          response = await this.$aspHttps.asp_PostForm(this.$apiConfig.safePathPrefix + '/encrypt/receiveSecurityKey', { security: sendKey })
          // 保存数据
          if (!(this.$reponseStatus(response))) {
            securityObject.on = 0
            delete securityObject.aesKeyValue
            delete securityObject.whiteList
            this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          } else if (this.$reponseStatus(response) && response.data) {
            // 更新old
            securityObject.aesKeyValue = response.data.old || securityObject.aesKeyValue
            this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
            // 重新存储白名单
            securityObject.whiteList = this.$aspHttps.asp_EncryptPlus(this.$projectConfig.whiteList)
            this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          }
          // 请求白名单
          // response = await this.$aspHttps.asp_Post(this.$apiConfig.safePathPrefix + '/encrypt/getIgnoreUrl', {})
          // if (this.$reponseStatus(response) &&
          //   Object.prototype.hasOwnProperty.call(response, 'data') &&
          //   Object.prototype.hasOwnProperty.call(response.data, 'whiteList')) {
          //   securityObject.whiteList = this.$aspHttps.asp_EncryptPlus(response.data.whiteList)
          //   this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          // }
          // 请求配置列表
          response = await this.$aspHttps.asp_Post(this.$apiConfig.supportPathPrefix + '/domainConfig/listAll', { domain: this.$projectConfig.domain })
          if (this.$reponseStatus(response)) {
            this.configDataApi(response)
            const { domainConfig, platformConfig } = response.data
            // 控制logo 和 copyRight
            if (platformConfig.showLogo) this.showLogo = platformConfig.showLogo === 'true'
            if (domainConfig.showLogo) this.showLogo = domainConfig.showLogo === 'true'
            if (platformConfig.showCopyRight) this.showCopyRight = platformConfig.showCopyRight === 'true'
            if (domainConfig.showCopyRight) this.showCopyRight = domainConfig.showCopyRight === 'true'
            this.$main_tools.sessionStorage.setItem('LISTALL', JSON.stringify(response.data))
          }
        } else {
          // 保存数据
          securityObject.on = 0
          this.$main_tools.sessionStorage.setItem('securityObject', JSON.stringify(securityObject))
          // 请求配置列表
          response = await this.$aspHttps.asp_Post(this.$apiConfig.supportPathPrefix + '/domainConfig/listAll', { domain: this.$projectConfig.domain })
          if (this.$reponseStatus(response)) {
            this.configDataApi(response)
            const { domainConfig, platformConfig } = response.data
            // 控制logo 和 copyRight
            if (platformConfig.showLogo) this.showLogo = platformConfig.showLogo === 'true'
            if (domainConfig.showLogo) this.showLogo = domainConfig.showLogo === 'true'
            if (platformConfig.showCopyRight) this.showCopyRight = platformConfig.showCopyRight === 'true'
            if (domainConfig.showCopyRight) this.showCopyRight = domainConfig.showCopyRight === 'true'
            this.$main_tools.sessionStorage.setItem('LISTALL', JSON.stringify(response.data))
          }
        }
      }
    }
  }
}
