<!--
 * @Author: yuxuan <EMAIL>
 * @Date: 2022-11-11 19:12:39
 * @LastEditors: yuxuan
 * @LastEditTime: 2022-11-11 20:06:16
 * @Description: 当从账号有多个时，自动提供从账号选择弹窗，选择指定从账号进行登录
-->
<template>
  <div id='edit-pwd' class='webbas'>
    <el-dialog title='请选择您要登录的账号'
              :close-on-click-modal='false'
              :visible.sync='dialogParam.visible'
              width='35%'
              @closed="submitLogin('cancel')">
      <el-form ref='subacctForm'
              :model='subacctForm'
              :inline='true'
              label-width='100px'>
        <el-row>
          <el-col>
            <template>
              <el-form-item label=''
                            prop='subbacct'>
                <el-select v-model="subacctForm.subbacct" placeholder="请选择">
                  <el-option
                    v-for="item in getSubaccts"
                    :key="item.subbacct"
                    :label="item.subbacct"
                    :value="item.subbacct">
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-col>
        </el-row>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <div style="text-align: center">
          <el-button class='hollow-with-icon-btn'
                    icon='el-icon-close'
                    @click="submitLogin('cancel')">取消登录</el-button>
          <el-button v-loading='dialogParam.submitStatus'
                    icon='el-icon-check'
                    class='solid-with-icon-btn'
                    @click="submitLogin('save')">登录</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'multAccounts',
  components: {},
  props: {
    dialogParam: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      subacctForm: {
        subbacct: ''
      }
    }
  },
  computed: {
    getSubaccts () {
      let subaccts = []
      if (this.dialogParam && this.dialogParam.subaccts) {
        subaccts = this.dialogParam.subaccts
      }
      return subaccts
    }
  },
  watch: {
    'dialogParam.visible' (val) {
      if (val) {
        // 初始化从账号
        this.subacctForm.subbacct = this.dialogParam.subaccts[0].subbacct
      }
    }
  },
  created () {},
  methods: {
    submitLogin (action) {
      const { response, next } = this.dialogParam
      const subbacct = this.subacctForm.subbacct
      if (action === 'save') {
        next(true, response, subbacct)
      } else {
        next(false, response, subbacct)
      }
      this.$emit('simSubLogin', action)
    }
  }
}
</script>
