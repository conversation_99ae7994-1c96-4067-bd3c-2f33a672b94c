/*
 * @Author: yuxuan <EMAIL>
 * @Date: 2024-01-12 11:36:34
 * @LastEditors: yuxuan
 * @LastEditTime: 2024-04-07 16:50:15
 * @Description: file content
 */
/**
 * 登录模块 混合配置
 * 验证码刷新 获取 状态监听
 */
export default {
  props: {
    imgUrl: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      imageUrl: '', // 图形验证码
      mixinsName: '',
      // 验证码配置项
      validationMap: ''
    }
  },
  methods: {
    // 更新图形验证码
    updateValidationCode (type) {
      this.validationMap = Math.random() + '' + new Date().getTime()
      this.$aspHttps.asp_Fetch(this.$apiConfig.authPathPrefix + '/captcha/image?x=' + this.validationMap).then(response => {
        if (this.$aspHttps.reponseStatus(response, !type) && response?.data?.img) {
          const imageUrl = `data:image/jpg;base64,${response.data.img}`
          this.imageUrl = imageUrl
          // 这样分发，是因为切换tab时初始化也会触发异常，需要多tabs共享一个imageUrl
          this.$emit('updateImage', imageUrl)
          sessionStorage.removeItem('imgUrl')
        } else if (this.imgUrl) {
          // 备份最后一次图形验证码
          sessionStorage.setItem('imgUrl', this.imgUrl)
          this.imageUrl = this.imgUrl
        } else if (!this.imgUrl) {
          // 提取最后一次备份的图形验证码
          const imgUrl = sessionStorage.getItem('imgUrl') || ''
          this.imageUrl = imgUrl
          this.$emit('updateImage', imgUrl)
          sessionStorage.removeItem('imgUrl')
        }
      })
    }
  },
  computed: {
    // imgUrl() {
    //   let url = this.$apiConfig.authPathPrefix + '/captcha/image?x=' + this.validationMap
    //   if (process.env.NODE_ENV !== 'development') {
    //     this.$apiConfig && this.$apiConfig.proxyConfig && this.$apiConfig.proxyConfig.forEach(item => {
    //       url = url.replace(item.localProxy, item.nginxProxy)
    //     })
    //   }
    //   return url
    // },
    accountInfo_watch () {
      return this.mixinsName !== '' && this[this.mixinsName + 'Info'].msg
    }
  },
  watch: {
    accountInfo_watch () {
      this.updateValidationCode('changeTab')
      this.$refs[this.mixinsName].resetFields()
      this.focus.count = true
    }
  }
}
