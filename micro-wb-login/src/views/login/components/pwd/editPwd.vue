/**
 * 密码过期，修改密码
**/
<template>
  <div id='edit-pwd'
       class='webbas'>
    <el-dialog title='修改密码'
               :close-on-click-modal='false'
               :visible.sync='dialogParams.editPwdModelVisible'
               width='35%'
               @closed="operateEvent('cancel')">
      <el-form ref='editPwd'
               :model='editPwd'
               :rules='rules'
               :inline='true'
               label-width='100px'>
        <el-row>
          <el-col>
            <template>
              <el-form-item label='旧密码:'
                            prop='oldPassword'>
                <el-input v-model='editPwd.oldPassword'
                          type='password'
                          auto-complete='off'
                          @input='change'></el-input>
              </el-form-item>
            </template>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <template>
              <el-form-item label='新密码:'
                            prop='newPassword'>
                <el-input v-model='editPwd.newPassword'
                          type='password'
                          auto-complete='off'
                          @input='change'></el-input>
              </el-form-item>
            </template>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <template>
              <el-form-item label='密码确认:'
                            prop='pwdConfirm'>
                <el-input v-model='editPwd.pwdConfirm'
                          type='password'
                          auto-complete='off'
                          @input='change'></el-input>
              </el-form-item>
            </template>
          </el-col>
        </el-row>
      </el-form>
      <div slot='footer'
           class='dialog-footer'>
        <el-button v-if='!dialogParams.isExpired'
                   class='hollow-with-icon-btn'
                   icon='el-icon-close'
                   @click="operateEvent('cancel')">取消</el-button>
        <el-button v-loading='submitStatus'
                   icon='el-icon-check'
                   class='solid-with-icon-btn'
                   @click="operateEvent('save')">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
const VAR_INIT_STR = ''
export default {
  name: 'EditPwd',
  components: {},
  props: {
    dialogParams: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      dialogFormVisible: false,
      userDomain: '', // 当前登录用所属域
      userName: '',
      submitStatus: false, // 提交按钮限制
      // domain: this.$projectConfig.domain, // 获取秘钥默认admin
      modulus: '', // 用于密码加密操作
      exponent: '', // 用于密码加密操作
      editPwd: {
        oldPassword: VAR_INIT_STR,
        newPassword: VAR_INIT_STR,
        pwdConfirm: VAR_INIT_STR
      },
      operateStatusCode: '', // 操作状态参数
      rules: {
        oldPassword: [
          { required: true, message: '旧密码不能为空', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '新密码不能为空', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkPassword, trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === this.editPwd.oldPassword) {
                callback(new Error('新密码不能与旧密码相同！'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        pwdConfirm: [
          { required: true, message: '确认密码不能为空', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkPassword, trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== this.editPwd.newPassword) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () { },
  methods: {
    // 重新渲染数据
    change () {
      this.$forceUpdate()
    },
    // 获取资源信息进行加密操作
    getConfig () {
      const { platformConfig } = this.$main_tools.sessionStorage.getItem('LISTALL')
        ? JSON.parse(this.$main_tools.sessionStorage.getItem('LISTALL'))
        : { platformConfig: {} }
      this.modulus = platformConfig.modulus
      this.exponent = platformConfig.exponent
      this.submitOperStaff()
    },
    addRSA (password) {
      // RSA
      asp_RSAKey.RSASetPublic(this.modulus, this.exponent)
      return asp_RSAKey.RSAEncrypt(password)
    },
    // 提交操作
    submitOperStaff () {
      this.submitStatus = true
      let operUrl = ''
      let param = {}
      const domain = this.$main_tools.sessionStorage[this.$projectConfig.operator]
        ? JSON.parse(this.$main_tools.sessionStorage[this.$projectConfig.operator])
        : null
      if (!this.dialogParams.username && domain === null) {
        return
      }
      const username = this.dialogParams.username
        ? this.dialogParams.username
        : domain.userInfo.username
      operUrl = this.$apiConfig.managerPathPrefix + '/user/modifyPwd'
      // 该操作后台自动获取当前登录用户的Id，不用传userId
      param = {
        domain: this.$projectConfig.domain,
        oldPassword: this.addRSA(this.editPwd.oldPassword),
        newPassword: this.addRSA(this.editPwd.newPassword),
        userName: username
      }
      // 通过选择的部门或机构获取当前用户所属的域
      this.$aspHttps.asp_Post(operUrl, param).then((response) => {
        if (this.$reponseStatus(response)) {
          this.$message.success('修改成功！')
          // 调取关闭操作
          this.operateEvent('cancel')
          this.submitStatus = false
        } else {
          this.submitStatus = false
        }
      })
    },
    // 编辑提交
    operateEvent (param) {
      // 关闭弹窗 初始化信息
      const resetDialog = () => {
        // 点击取消 数据重置
        this.$refs.editPwd.resetFields()
        this.dialogParams.editPwdModelVisible = false
      }
      if (param === 'save') {
        this.$refs.editPwd.validate((valid) => {
          if (valid) {
            // 提交前发起获取秘钥操作
            this.getConfig()
          }
        })
      } else {
        // 取消
        resetDialog()
        this.$emit(
          'resetButtonLoading',
          Object.assign(
            {},
            {
              nextHome: !this.dialogParams.isExpired, // 密码是否已过期，已过期的不能nextHome
              submitLoading: false
            }
          )
        )
      }
    }
  }
}
</script>
