/**
 * 登录模块 系统入口
 * 功能：账户登录、短信验证码、密码过期修改密码、忘记密码
 * 获取获取用户、鉴权、字典表信息
**/
<template>
  <div class='wblogin'>
    <!--  logo 系统名称配置信息 -->
    <div class='login-head'>
      <img :style="[{visibility: showLogo ?'unset':'hidden'}]"
           src='../../../../../../assets/img/loginlogo.png' />
      <h1>{{ domainConfig.title || platformConfig['portal-name'] }}</h1>
    </div>
    <!-- 登录页装饰图片 -->
    <div class='login-ele-icon'>
      <img src='../../../../../../assets/img/loginbg.png' />
    </div>

    <!-- 登录模块 -->
    <div class='login-wrap forget-wrap'>
      <div slot='header'
           class='reset-password-title-css'>
        <span>重置密码</span>
      </div>

      <div>
        <el-form ref='forgetForm'
                 :rules='ruleList'
                 :model='forgetForm'>
          <el-form-item label=''
                        prop='username'>
            <el-input v-focus="{ cls: 'el-input', tag: 'input', foc: focus.count }"
                      :disabled='formDisableMap.username'
                      v-model.trim='forgetForm.username'
                      autocomplete='off'
                      placeholder='请输入用户名'
                      name='username'
                      @blur='focus.count = false'>
              <template slot='prepend'>
                <i class='iconfont el-icon-mine'></i>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-if='isCheckOn'
                        label=''
                        prop='checkCode'>
            <div class="grid-content bg-purple code-content code-img">
              <el-input v-model="forgetForm.checkCode"
                        placeholder="请输入验证码">
                <template slot="append">
                  <a class="cursor-pointer-css"
                     title
                     @click="updateImageCode()">
                    <img v-if="imageUrl"
                         id="imgObj"
                         :src="imageUrl"
                         alt
                         class="check-img-css" />
                  </a>
                </template>
              </el-input>
            </div>
          </el-form-item>
          <el-form-item label=''
                        prop='phoneCode'>
            <div class='code-content'
                 @keyup.enter='handleSubmit'>
              <el-input v-model.trim='forgetForm.phoneCode'
                        autocomplete='off'
                        placeholder='请输入短信验证码'>
                <template slot='append'>
                  <el-button v-if='show'
                             :disabled='isCheckOn && !forgetForm.checkCode'
                             type='primary'
                             plain
                             size='small'
                             @click='getMessageCode'>获取验证码</el-button>
                  <el-button v-if='!show'
                             type='info'
                             round
                             disabled
                             size='small'
                             class='padding-css'>{{ count }} s</el-button>
                </template>
              </el-input>
            </div>
          </el-form-item>
          <el-form-item>
            <div style="text-align: center">
              <el-button :loading='submitStatus'
                         :disabled='submitStatus'
                         type='primary'
                         size='medium'
                         @click='handleSubmit'>
                确定
              </el-button>
              <el-button type='primary'
                         size='medium'
                         @click='goBack'>取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- copyright -->
    <div :style="[{visibility: showCopyRight ?'unset':'hidden'}]"
         class='login-footer'>
      {{ domainConfig.copyright || platformConfig.copyright || '中国移动·版权所有' }}
    </div>
  </div>
</template>
<script>
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
import mixins from '../mixins'
import initApi from './../../initApi'
export default {
  name: 'ForgetPwd4A',
  components: {},
  mixins: [mixins, initApi],
  props: {},
  data () {
    return {
      forgetForm: {
        username: '',
        checkCode: '',
        phoneCode: ''
      },
      formDisableMap: {
        username: false,
        checkCode: false
      },
      // userDomain: this.$projectConfig.domain, // 当前登录用户所属的域
      showLogo: true,
      showCopyRight: true,
      isCheckOn: true, // 是否需要图形验证码，默认开启
      ssoMsgNextStatus: false, // 控制校验规则
      show: true, // 切换短信验证展示和按钮
      count: '', // 展示短信验证码倒计时
      timer: null, // 短信验证码倒计时计时器
      validationMap: '',
      platformConfig: {},
      domainConfig: {},
      submitStatus: false,
      focus: { count: true },
      rule1: {
        username: [
          { validator: this.$main_tools.validation.checkUsername, trigger: 'blur' }
        ],
        checkCode: [
          { required: true, message: '请输验证码', trigger: 'blur' }
        ]
      },
      rule2: {
        username: [
          { validator: this.$main_tools.validation.checkUsername, trigger: 'blur' }
        ],
        checkCode: [
          { required: true, message: '请输验证码', trigger: 'blur' }
        ],
        phoneCode: [
          { required: true, message: '请输入短信验证码', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ruleList () {
      if (this.ssoMsgNextStatus) {
        return this.rule2
      }
      return this.rule1
    }
  },
  created () {
    // 初始化、退出刷新
    this.updateValidationCode('changeTab')
    this.initDataAPI()
  },
  // 清除定时器
  beforeDestroy () {
    this.reSetTimer()
  },
  methods: {
    /**
     * 获取资源信息进行加密操作
     * listAll 单独请求；
     * 忘记密码是白名单路由 可不经过登录访问
     */
    configDataApi (response) {
      const { platformConfig, domainConfig } = response.data
      this.platformConfig = platformConfig
      this.domainConfig = domainConfig
      this.modulus = platformConfig.modulus
      this.exponent = platformConfig.exponent
      this.isCheckOn = domainConfig.checkCodeFor4A ? domainConfig.checkCodeFor4A === 'true' : true
      asp_RSAKey.RSASetPublic(platformConfig.modulus, platformConfig.exponent)
    },
    // 返回登录页
    goBack () {
      window.history.back()
    },
    // 提交
    handleSubmit () {
      this.$refs.forgetForm.validate((valid) => {
        if (!valid) {
          return false
        }
        // 提交状态
        this.submitStatus = true
        const url = this.$apiConfig.authUaPrefix + '/login/resetPassword'
        const params = {
          userName: asp_RSAKey.RSAEncrypt(this.forgetForm.username),
          phoneCode: asp_RSAKey.RSAEncrypt(this.forgetForm.phoneCode)
        }
        this.$aspHttps.asp_Post(url, params).then((response) => {
          if (this.$reponseStatus(response)) {
            this.$message.success('密码重置成功！')
            window.history.back()
            this.submitStatus = false
          } else {
            this.submitStatus = false
            // 登录失败，校验前两个
            this.ssoMsgNextStatus = false
            // 清空短信验证码
            this.forgetForm.phoneCode = ''
            this.reSetTimer()
            this.updateImageCode()
            // 放开禁用
            for (const key in this.formDisableMap) {
              this.formDisableMap[key] = false
            }
          }
        })
      })
    },
    /**
     * 先验证用户名密码是否正确，再获取短信验证码
     */
    getMessageCode () {
      this.ssoMsgNextStatus = false
      this.$refs.forgetForm.validate((valid) => {
        if (!valid) {
          return false
        }
        const url = this.$apiConfig.authUaPrefix + '/login/sendSmsCodePassword'
        const params = {
          userName: asp_RSAKey.RSAEncrypt(this.forgetForm.username),
          checkCode: asp_RSAKey.RSAEncrypt(this.forgetForm.checkCode)
        }
        // 验证用户名、图形验证码，并发送短信
        this.$aspHttps.asp_Post(url, params).then((response) => {
          if (this.$reponseStatus(response)) {
            // 短信发送成功，校验所有
            this.ssoMsgNextStatus = true
            // 设置username,pwd,checkCode为不可修改
            for (const key in this.formDisableMap) {
              this.formDisableMap[key] = true
            }
            // 设置页面短信验证刷新时间
            const TIME_COUNT = 60
            if (!this.timer) {
              this.count = TIME_COUNT
              this.show = false
              this.timer = setInterval(() => {
                if (this.count > 0 && this.count <= TIME_COUNT) {
                  this.count--
                } else {
                  this.show = true
                  clearInterval(this.timer)
                  this.timer = null
                }
              }, 1000)
            }
          } else {
            this.reSetTimer()
            this.updateImageCode()
            // 刷新图形验证，重新校验前两项
            this.ssoMsgNextStatus = false
          }
        })
      })
    },
    /**
     * 清除定时器，重置短信验证码按钮
     */
    reSetTimer () {
      clearInterval(this.timer)
      this.timer = null
      this.show = true
    },
    // 刷新图形验证码
    updateImageCode () {
      this.updateValidationCode()
      this.forgetForm.checkCode = ''
    }
  }
}
</script>
