/**
* 账号登录忘记密码，修改密码
*/
<template>
  <div data-v="2019-3-7 10:49"
       class="wblogin">
    <div class="login-head">
      <img :style="[{visibility: showLogo ?'unset':'hidden'}]"
           src='../../../../../../assets/img/loginlogo.png' />
      <h1>{{ domainConfig.title || platformConfig["portal-name"] }}</h1>
    </div>
    <div class="main-header">
      <div class="main-nav">
        <el-steps :active="active"
                  finish-status="success">
          <el-step title="输入手机号"></el-step>
          <el-step title="验证身份"></el-step>
          <el-step title="设置新密码"></el-step>
          <el-step title="完成"></el-step>
        </el-steps>
      </div>
    </div>
    <div class="login-ele-icon">
      <img src='../../../../../../assets/img/loginbg.png' />
    </div>
    <div class="login-wrap">
      <div v-if="active === 0"
           class="forgetPWD-wrap el-card is-always-shadow">
        <el-row :gutter="24">
          <el-col :span="9">
            <div class="grid-content bg-purple label-right-css">
              <span>登陆账号：</span>
            </div>
          </el-col>
          <el-col :span="15">
            <div class="grid-content bg-purple">
              <el-input v-model.trim="userName"
                        placeholder="请输入您的登陆账号"
                        class="width-200-css"></el-input>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24"
                class="top-10-css">
          <el-col :span="9">
            <div class="grid-content bg-purple label-right-css">
              <span>输入手机号：</span>
            </div>
          </el-col>
          <el-col :span="15">
            <div class="grid-content bg-purple">
              <el-input v-model.trim="phoneNum"
                        placeholder="请输入您账户关联的手机号"
                        class="width-200-css">
              </el-input>
            </div>
          </el-col>
        </el-row>
        <div class="margin-top-30-css">
          <div style="text-align: center">
            <el-button type="primary"
                       size="medium"
                       @click="mobile">下一步</el-button>
            <el-button type="primary"
                       size="medium"
                       @click="loginOut">取消</el-button>
          </div>
        </div>
      </div>
      <div v-if="active === 1"
           class="forgetPWD-wrap el-card is-always-shadow">
        <el-row :gutter="24"
                class="check-code-row-css">
          <el-col :span="6">
            <div class="grid-content bg-purple label-right-css">
              <span>验证码:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="grid-content bg-purple code-content code-img">
              <el-input v-model="checkCode"
                        placeholder="请输入验证码">
                <template slot="append">
                  <a class="cursor-pointer-css"
                     title
                     @click="updateImageCode()">
                    <img v-if="imageUrl"
                         id="imgObj"
                         :src="imageUrl"
                         alt
                         class="check-img-css" />
                  </a>
                </template>
              </el-input>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24"
                class="check-code-row-css">
          <el-col :span="6">
            <div class="grid-content bg-purple label-right-css">
              <span>验证身份:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="grid-content bg-purple">
              <el-input v-model="phoneCode"
                        placeholder="请输入验证码">
                <template slot="append">
                  <el-button v-if="show"
                             :disabled="!checkCode"
                             type="primary"
                             plain
                             size="small"
                             class="padding-css"
                             @click="getCode">获取验证码</el-button>
                  <el-button v-if="!show"
                             type="info"
                             round
                             disabled
                             size="small"
                             class="padding-css">
                    {{ count }} s</el-button>
                </template>
              </el-input>
            </div>
          </el-col>
        </el-row>
        <div class="margin-top-30-css">
          <div style="text-align: center">
            <el-button type="primary"
                       size="medium"
                       @click="verifyCode">下一步</el-button>
            <el-button type="primary"
                       size="medium"
                       @click="loginOut">取消</el-button>
          </div>
        </div>
      </div>
      <div v-if="active === 2"
           class="forgetPWD-wrap el-card is-always-shadow">
        <el-row :gutter="24">
          <el-col :span="9">
            <div class="grid-content bg-purple label-right-css">新密码：</div>
          </el-col>
          <el-col :span="15">
            <div class="grid-content bg-purple">
              <el-input v-model="newPassword"
                        type="password"
                        class="width-200-css"
                        auto-complete="off">
              </el-input>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24"
                class="top-10-css">
          <el-col :span="9">
            <div class="grid-content bg-purple label-right-css">
              确认新密码：
            </div>
          </el-col>
          <el-col :span="15">
            <div class="grid-content bg-purple">
              <el-input v-model="pwdConfirm"
                        type="password"
                        class="width-200-css"
                        auto-complete="off">
              </el-input>
            </div>
          </el-col>
        </el-row>
        <div class="margin-top-30-css">
          <div style="text-align: center">
            <el-button type="primary"
                       size="medium"
                       @click="passwordCheck">提交</el-button>
            <el-button type="primary"
                       size="medium"
                       @click="loginOut">取消</el-button>
          </div>
        </div>
      </div>
      <div v-if="active === 3">
        <div style="text-align: center">
          <h2>修改密码成功，正在返回登录页...{{ timeNum }}</h2>
          <el-button type="success"
                     size="medium"
                     class="margin-top-30-css"
                     @click="loginOut">返回登录页
          </el-button>
        </div>
      </div>
    </div>
    <div :style="[{visibility: showCopyRight ?'unset':'hidden'}]"
         class="login-footer">{{ domainConfig.copyright || platformConfig.copyright || '中国移动·版权所有' }}
    </div>
  </div>
</template>

<script>
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
import mixins from '../mixins'
import initApi from './../../initApi'
const VAR_INIT_STR = ''
export default {
  name: 'ForgetPwd',
  components: {},
  mixins: [mixins, initApi],
  data () {
    return {
      phoneNum: '',
      userName: '',
      phoneCode: '',
      platformConfig: {},
      domainConfig: {},
      showLogo: true,
      showCopyRight: true,
      modulus: '', // 用于密码加密操作
      exponent: '', // 用于密码加密操作
      active: 0,
      show: true,
      count: '',
      timer: null,
      timeNum: 0,
      newPassword: VAR_INIT_STR,
      pwdConfirm: VAR_INIT_STR,
      domain: '',
      checkCode: '' // 图形验证吗
    }
  },
  created () {
    this.updateValidationCode('reset')
    this.initDataAPI()
    /* this.domain = this.$projectConfig.domain || (() => {
      let arry = ['supplier', 'channel']
      return arry.filter(e => {
      return this.$main_tools.sessionStorage.getItem(e)
      })[0]
    })() */
  },
  // 清除定时器
  beforeDestroy () {
    // clearInterval(this.timer)
    // this.timer = null
    this.reSetTimer()
  },
  methods: {
    /**
     * 获取资源信息进行加密操作
     * listAll 单独请求；
     * 忘记密码是白名单路由 可不经过登录访问
     */
    configDataApi (response) {
      const { platformConfig, domainConfig } = response.data
      this.platformConfig = platformConfig
      this.domainConfig = domainConfig
      this.modulus = platformConfig.modulus
      this.exponent = platformConfig.exponent
    },
    addRSA (password) {
      asp_RSAKey.RSASetPublic(this.modulus, this.exponent)
      return asp_RSAKey.RSAEncrypt(password)
    },
    /**
     * 限制手机号的格式
     */
    mobile () {
      if (!this.userName) {
        this.$message.error('登陆账号不能为空！')
        return
      }
      const { platformConfig } = JSON.parse(this.$main_tools.sessionStorage.getItem('LISTALL'))
      const reg = new RegExp(platformConfig.mobileComplex.replace(/\//g, ''))
      // let reg = /^(1[3|4|5|6|7|8|9]\d{9})$/
      if (this.phoneNum.trim() === '') {
        this.$message.error('手机号码不能为空！')
        return
      }
      if (!reg.test(this.phoneNum)) {
        // this.$message.error('请输入正确的手机号码格式！')
        this.$message.error(platformConfig.mobileComplexTip)
      } else {
        this.active++
      }
    },
    /**
     * 获取验证码
     */
    getCode () {
      const param = {
        mobile: this.addRSA(this.phoneNum),
        userName: this.addRSA(this.userName),
        domain: this.$projectConfig.domain,
        checkCode: this.addRSA(this.checkCode)
      }
      this.$aspHttps.asp_Post(this.$apiConfig.managerPathPrefix + '/user/findPwdSmsCode', param).then((response) => {
        if (this.$reponseStatus(response)) {
          const TIME_COUNT = 60
          if (!this.timer) {
            this.count = TIME_COUNT
            this.show = false
            this.setTimerOut(TIME_COUNT)
          }
        } else {
          this.reSetTimer()
          if (response.status === '400') {
            // this.checkCode = ''
            this.updateImageCode()
            this.$alert(`${response.message}！`, '提示信息', {
              confirmButtonText: '确定',
              showClose: false
            })
              .then(() => {
                this.loginOut()
              })
              .catch(() => {
                this.loginOut()
              })
          }
        }
      })
    },
    /**
     * 限制验证码格式，并验证验证码
     */
    verifyCode () {
      if (!this.checkCode) {
        this.$message.error('请输入图形验证码')
        return
      }
      if (!this.phoneCode) {
        this.$message.error('请输入身份验证码')
        return
      }
      const reg = /(\d{6})/
      if (!reg.test(this.phoneCode)) {
        this.$message.error('请输入正确的验证码格式')
        return
      }
      const param = {
        smsCode: this.addRSA(this.phoneCode),
        mobile: this.addRSA(this.phoneNum),
        domain: this.$projectConfig.domain
      }
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.managerPathPrefix + '/user/validateSmsCode',
          param
        )
        .then((response) => {
          if (this.$reponseStatus(response)) {
            if (response.data.result === 1) {
              this.active++
            } else {
              this.$message.error('请输入正确的验证码')
            }
          }
        })
    },
    /**
     * 验证两次密码的一致性，限制密码位数，并修改密码
     * 成功：data.result == 1
     * 失败：data.result == 0
     */
    passwordCheck () {
      const num = 3
      // 非空验证
      if (this.newPassword === '') {
        this.$message.error('新密码不能为空')
        return
      }
      if (this.pwdConfirm === '') {
        this.$message.error('确认密码不能为空')
        return
      }
      // 字数验证
      const { platformConfig } = JSON.parse(this.$main_tools.sessionStorage.getItem('LISTALL'))
      const reg = new RegExp(
        platformConfig['password-complex'].replace(/\//g, '')
      )
      if (!reg.test(this.newPassword) || !reg.test(this.pwdConfirm)) {
        this.$message.error(platformConfig['password-complex-tip'])
        return
      }
      if (this.newPassword === this.pwdConfirm) {
        const param = {
          newPwd: this.addRSA(this.newPassword),
          smsCode: this.addRSA(this.phoneCode),
          mobile: this.addRSA(this.phoneNum),
          domain: this.$projectConfig.domain
        }
        this.$aspHttps
          .asp_Post(
            this.$apiConfig.managerPathPrefix + '/user/modifyLostedPwd',
            param
          )
          .then((response) => {
            if (this.$reponseStatus(response)) {
              if (response.data.result === 1) {
                // 两次密码输入相同
                this.active++
                this.show = false
                this.timeNum = num
                // 设置自动跳转
                this.setTimerActive()
              } else {
                this.$message.error('修改密码失败，请稍后重试！')
              }
            }
          })
      } else {
        this.$message.error('两次输入的密码不一致')
      }
    },
    /** 自动跳转倒计时 */
    setTimerActive () {
      this.timer = setInterval(() => {
        if (this.active === 3 && this.timeNum > 0) {
          this.timeNum--
        } else {
          this.reSetTimer()
          this.loginOut()
        }
      }, 1000)
    },
    /** 短信验证码倒计时 */
    setTimerOut (TIME_COUNT) {
      this.timer = setInterval(() => {
        if (this.count > 0 && this.count <= TIME_COUNT) {
          this.count--
        } else {
          this.reSetTimer()
        }
      }, 1000)
    },
    /**
     * 清除定时器，重置短信验证码按钮
     */
    reSetTimer () {
      clearInterval(this.timer)
      this.timer = null
      this.show = true
    },
    // 刷新图形验证码
    updateImageCode () {
      this.updateValidationCode('reset')
      this.checkCode = ''
    },
    // 退出登录
    loginOut () {
      window.history.back()
    }
  }
}
</script>
