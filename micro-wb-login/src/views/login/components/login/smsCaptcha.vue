/**
* 短信验证码登录
*/
<template>
  <div>
    <el-form ref='smsCaptcha'
             :rules='rule'
             :model='smsCaptcha'>
      <el-form-item label
                    prop='mobilePhone'>
        <el-input v-focus="{ cls: 'el-input', tag: 'input', foc: focus.count }"
                  v-model.trim='smsCaptcha.mobilePhone'
                  placeholder='请输入手机号'
                  name='mobilePhone'
                  autocomplete='off'
                  @blur='focus.count = false'>
          <template slot='prepend'>
            <i class='el-icon-mobile-phone'></i>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item v-if='isCheckOn'
                    label
                    prop='checkCode'>
        <div class='code-content code-img'
             @keyup.enter='submit'>
          <el-input v-model.trim='smsCaptcha.checkCode'
                    placeholder='请输入验证码'>
            <template slot='append'>
              <a class='cursor-pointer-css'
                 title
                 @click='updateValidationCode()'>
                <img id='imgObj'
                     :src='imgUrl'
                     alt />
              </a>
            </template>
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label
                    prop='OTPToken'>
        <div class='code-content'>
          <el-input v-model.trim='smsCaptcha.OTPToken'
                    placeholder='请输入短信验证码'>
            <template slot='append'>
              <el-button :style='captchaDisable'
                         :disabled='captcha.disabled'
                         class='getCaptchaBtn'
                         type='button'
                         @click="sendMessage()">{{ captcha.btn }}</el-button>
            </template>
          </el-input>
        </div>
      </el-form-item>
      <el-form-item>
        <el-row>
          <el-col>
            <el-button :disabled='smsCaptchaInfo.submitLoading'
                       :loading='smsCaptchaInfo.submitLoading'
                       type='primary'
                       size='small'
                       class='login-button-width'
                       @click='submit'>登录</el-button>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixins from '../mixins'
export default {
  name: 'SmsCaptcha',
  mixins: [mixins],
  props: {
    smsCaptchaInfo: {
      type: Object,
      default: null
    }
  },
  data () {
    const { platformConfig, domainConfig } = JSON.parse(
      this.$main_tools.sessionStorage.getItem('LISTALL')
    )
    return {
      smsCaptcha: {
        mobilePhone: '',
        OTPToken: '',
        checkCode: '',
        username: ''
      },
      isCheckOn: platformConfig['is-check-on'] === 'true',
      rule: {
        mobilePhone: [
          { validator: this.$main_tools.validation.checkMobile, trigger: ['submit', 'blur'] }
        ],
        OTPToken: [
          { required: true, message: '请输入短信验证码', trigger: ['submit', 'blur'] }
        ],
        checkCode: [
          { required: true, message: '请输验证码', trigger: ['submit', 'blur'] }
        ]
      },
      platformConfig: platformConfig,
      domainConfig: domainConfig,
      focus: { count: true },
      captcha: {
        btn: '获取验证码',
        disabled: false
      }
    }
  },
  computed: {
    captchaDisable () {
      return this.captcha.disabled
        ? { cursor: 'not-allowed' }
        : { cursor: 'pointer' }
    }
  },
  created () {
    this.mixinsName = 'smsCaptcha'
    // 初始化、退出刷新
    // this.updateValidationCode()
  },
  methods: {
    // 获取验证码
    sendMessage () {
      let timeLen = 60
      this.captcha.disabled = true
      const param = {
        mobile: this.smsCaptcha.mobilePhone,
        domain: this.$projectConfig.domain,
        checkCode: this.smsCaptcha.checkCode
      }
      // 发短信验证
      this.$aspHttps.asp_Post(this.$apiConfig.authPathPrefix + '/assist/dynamicPassword', param).then((response) => {
        if (this.$reponseStatus(response)) {
          // 读取接口定时器 时间
          timeLen = response.data?.period || timeLen
          // 设置页面短信验证刷新时间
          const timer = setInterval(() => {
            --timeLen
            if (timeLen === 0) {
              clearInterval(timer)
              this.captcha.btn = '获取验证码'
              this.captcha.disabled = false
            } else {
              this.captcha.btn = `${timeLen}s后再获取`
            }
          }, 1000)
        } else {
          this.captcha.disabled = false
          // 情况图形验证码，并刷新图形验证码
          this.smsCaptcha.checkCode = ''
          this.updateValidationCode()
        }
      })
    },
    // 提交
    submit () {
      this.$refs.smsCaptcha.validate((valid) => {
        if (!valid) {
          return false
        }
        // username
        this.smsCaptcha.username = this.smsCaptcha.mobilePhone
        delete this.smsCaptcha.mobilePhone
        this.$emit(
          'loginSubmit',
          Object.assign(this.smsCaptcha, {
            domain: this.$projectConfig.domain,
            loginType: 'msg'
          })
        )
        this.smsCaptcha = {
          mobilePhone: '',
          OTPToken: '',
          checkCode: '',
          username: ''
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.login-button-width {
  width: 100%;
}
</style>