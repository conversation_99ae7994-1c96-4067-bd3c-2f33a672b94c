/**
*
* 账户登录
*/
<template>
  <el-form ref='account'
           :rules='rule'
           :model='account'>
    <el-form-item label
                  prop='username'>
      <el-input v-focus="{ cls: 'el-input', tag: 'input', foc: focus.count }"
                v-model.trim='account.username'
                placeholder='请输入用户名'
                name='username'
                autocomplete='off'
                @blur='focus.count = false'>
        <template slot='prepend'>
          <i class='iconfont el-icon-mine'></i>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label
                  prop='password'>
      <div @keyup.enter='submit'>
        <el-input v-model.trim='account.password'
                  placeholder='请输入密码'
                  type='password'
                  name='password'
                  autocomplete='off'
                  @keyup.enter='submit'>
          <template slot='prepend'>
            <i class='iconfont el-icon-lock'></i>
          </template>
        </el-input>
      </div>
    </el-form-item>
    <el-form-item v-if='isCheckOn'
                  label
                  prop='checkCode'>
      <div class='code-content code-img'
           @keyup.enter='submit'>
        <el-input v-model.trim='account.checkCode'
                  placeholder='请输入验证码'>
          <template slot='append'>
            <a class='cursor-pointer-css'
               title
               @click='updateValidationCode()'>
              <img id='imgObj'
                   :src='imgUrl'
                   alt='验证码' />
            </a>
          </template>
        </el-input>
      </div>
    </el-form-item>
    <el-form-item>
      <el-row>
        <el-col>
          <el-button :disabled='accountInfo.submitLoading'
                     :loading='accountInfo.submitLoading'
                     type='primary'
                     size='small'
                     class='login-button-width'
                     @click='submit'>登录</el-button>
          <div v-if='showForgetPwd'
               class='forgetPassword-wrap'>
            <a @click='forgetPassword'>忘记密码？</a>
          </div>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script>
import mixins from '../mixins'
const VAR_MAIN_ONE = '1'
const VAR_INIT_STR = ''
export default {
  name: 'Account',
  mixins: [mixins],
  props: {
    accountInfo: {
      type: Object,
      default: null
    }
  },
  data () {
    const { platformConfig, domainConfig } = JSON.parse(
      this.$main_tools.sessionStorage.getItem('LISTALL')
    )
    return {
      account: {
        username: '',
        password: VAR_INIT_STR,
        checkCode: ''
      },
      isCheckOn: platformConfig['is-check-on']
        ? platformConfig['is-check-on'] === 'true'
        : true, // 控制图形验证码显隐，默认展示
      showForgetPwd: platformConfig.showForgetPwd
        ? platformConfig.showForgetPwd === VAR_MAIN_ONE
        : true, // 控制忘记密码显隐，默认展示
      rule: {
        username: [
          { validator: this.$main_tools.validation.checkUsername, trigger: 'submit' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'submit' },
          {
            validator: this.$main_tools.validation.checkPasswordLogin,
            message: platformConfig['password-complex-tip'],
            trigger: 'submit'
          }
        ],
        checkCode: [
          { required: true, message: '请输验证码', trigger: 'submit' }
        ]
      },
      platformConfig: platformConfig,
      domainConfig: domainConfig,
      focus: {
        count: true
      }
    }
  },
  created () {
    this.mixinsName = 'account'
    // 初始化、退出刷新
    // this.updateValidationCode()
  },
  methods: {
    // 忘记密码
    forgetPassword () {
      this.$router.push({ path: '/forgetPWD' })
    },
    submit () {
      this.$refs.account.validate((valid) => {
        if (!valid) {
          return false
        }
        this.$emit(
          'loginSubmit',
          Object.assign(this.account, {
            domain: this.$projectConfig.domain,
            loginType: 'password'
          })
        )
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.login-button-width {
  width: 100%;
}
</style>
