/**
* SIM超级卡登录
*/
<template>
  <div>
    <el-form ref='simSmart'
             :rules='rule'
             :model='simSmart'>
      <el-form-item label
                    prop='mobilePhone'>
        <el-input v-focus="{ cls: 'el-input', tag: 'input', foc: focus.count }"
                  v-model.trim='simSmart.mobilePhone'
                  placeholder='请输入移动手机号'
                  name='mobilePhone'
                  autocomplete='off'
                  @blur='focus.count = false'>
          <template slot='prepend'>
            <!-- <i class='el-icon-mobile-phone'></i> -->
            <div style="font-size: 16px;font-weight: 500;color: #000">+86</div>
          </template>
        </el-input>
        <div class="sim-tip-css">
          温馨提示：登录时，您的手机将会收到登录请求弹窗，请留意手机弹窗并进行登录授权确认。
          <el-popover placement="bottom-end"
                      width="300"
                      trigger="hover">
            <div>
              <p>1.如您需了解更多手机SIM登录，请前往手机端“移动数据网络→SIM卡应用程序”或前往手机自带APP“SIM卡应用”。</p>
              <p>2.若您手机卡不具备手机SIM登录能力，为让您能更快捷登录，请到移动营业厅免费更换SIM卡。</p>
            </div>
            <i class="el-icon-question"
               style="cursor: pointer;"
               slot="reference"></i>
          </el-popover>
        </div>
      </el-form-item>
      <el-form-item v-if='isCheckOn'
                    label
                    prop='checkCode'>
        <div class='code-content code-img'
             @keyup.enter='submit'>
          <el-input v-model.trim='simSmart.checkCode'
                    placeholder='请输入验证码'>
            <template slot='append'>
              <a class='cursor-pointer-css'
                 title
                 @click='updateValidationCode()'>
                <img id='imgObj'
                     :src='imgUrl'
                     alt />
              </a>
            </template>
          </el-input>
        </div>
      </el-form-item>
      <el-form-item>
        <el-row>
          <el-col>
            <el-button :disabled='simSmartInfo.submitLoading'
                       :loading='simSmartInfo.submitLoading'
                       type='primary'
                       size='small'
                       class='login-button-width'
                       @click='submit'>{{loginBtnText}}</el-button>
            <div v-if='showForgetPwd'
                 class='forgetPassword-wrap'>
              <a @click='forgetPassword'>忘记SIM密码？</a>
            </div>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixins from '../mixins'
const VAR_MAIN_ONE = '1'
export default {
  name: 'SIMSmart',
  mixins: [mixins],
  props: {
    simSmartInfo: {
      type: Object,
      default: null
    },
    loginBtnText: {
      type: String,
      default: '登录'
    }
  },
  data () {
    const { platformConfig, domainConfig } = JSON.parse(
      this.$main_tools.sessionStorage.getItem('LISTALL')
    )
    return {
      simSmart: {
        mobilePhone: '',
        checkCode: '',
        username: ''
      },
      showForgetPwd: platformConfig.showForgetPwd
        ? platformConfig.showForgetPwd === VAR_MAIN_ONE
        : true, // 控制忘记密码显隐，默认展示
      isCheckOn: platformConfig['is-check-on'] === 'true',
      rule: {
        mobilePhone: [
          { validator: this.$main_tools.validation.checkMobile, trigger: 'submit' }
        ],
        checkCode: [
          { required: true, message: '请输验证码', trigger: 'submit' }
        ]
      },
      platformConfig: platformConfig,
      domainConfig: domainConfig,
      focus: { count: true }
    }
  },
  computed: {},
  created () {
    this.mixinsName = 'simSmart'
    // 初始化、退出刷新
    // this.updateValidationCode()
  },
  methods: {
    // 忘记SIM卡密码
    forgetPassword () {
      // window.open('https://yzhk.cmpassport.com/simpub/simquick/index.html', '_blank')
      const openUrl = 'https://yzhk.cmpassport.com/simpub/simquick/index.html'
      window['op' + 'en'](openUrl, '_blank')
    },
    // 提交
    submit () {
      this.$refs.simSmart.validate(async (valid) => {
        if (!valid) {
          return false
        }
        const requestParmas = {
          mobile: this.simSmart.mobilePhone,
          domain: this.$projectConfig.domain
        }
        await this.$aspHttps.asp_Post(this.$apiConfig.authPathPrefix + '/login/simAuth', requestParmas).then((response) => {
          if (this.$reponseStatus(response)) {
            this.simSmart.username = this.simSmart.mobilePhone
            delete this.simSmart.mobilePhone
            this.$emit(
              'loginSubmit',
              {
                username: this.simSmart.username,
                domain: this.$projectConfig.domain,
                loginType: 'sim',
                password: response.data.transactionId
              }
            )
            this.simSmart = {
              mobilePhone: this.simSmart.username,
              checkCode: '',
              username: ''
            }
          } else {
            this.$message.error(response.message || '验证失败！')
          }
        })
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.login-button-width {
  width: 100%;
}
.sim-tip-css {
  color: #606266;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
}
</style>
