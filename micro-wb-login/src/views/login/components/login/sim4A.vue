/**
* SIM超级卡登录，对接4A平台的SIM卡登陆（sim4A）
*/
<template>
  <div>
    <el-form ref='sim4A'
             :rules='rule'
             :model='sim4A'>
      <el-form-item label
                    prop='loginName'>
        <el-input v-focus="{ cls: 'el-input', tag: 'input', foc: focus.count }"
                  v-model.trim='sim4A.loginName'
                  :disabled='loginNameDisabled'
                  placeholder='请输入移动手机号或者4A账号'
                  name='loginName'
                  autocomplete='off'
                  @blur='focus.count = false'>
          <template slot='prepend'>
            <!-- <i class='el-icon-mobile-phone'></i> -->
            <div style="font-size: 16px;font-weight: 500;color: #000">+86</div>
          </template>
        </el-input>
        <div class="sim-tip-css">
          温馨提示：登录时，您的手机将会收到登录请求弹窗，请留意手机弹窗并进行登录授权确认。
          <el-popover placement="bottom-end"
                      width="300"
                      trigger="hover">
            <div>
              <p>1.如您需了解更多手机SIM登录，请前往手机端“移动数据网络→SIM卡应用程序”或前往手机自带APP“SIM卡应用”。</p>
              <p>2.若您手机卡不具备手机SIM登录能力，为让您能更快捷登录，请到移动营业厅免费更换SIM卡。</p>
            </div>
            <i class="el-icon-question"
               style="cursor: pointer;"
               slot="reference"></i>
          </el-popover>
        </div>
      </el-form-item>
      <el-form-item v-if='isCheckOn'
                    label
                    prop='checkCode'>
        <div class='code-content'
             @keyup.enter='submit'>
          <el-input v-model.trim='sim4A.checkCode'
                    placeholder='请输入验证码'>
            <template slot='append'>
              <a class='cursor-pointer-css'
                 title
                 @click='updateImageCode()'>
                <img id='imgObj'
                     :src='imgUrl'
                     alt />
              </a>
            </template>
          </el-input>
        </div>
      </el-form-item>
      <el-form-item>
        <el-row>
          <el-col>
            <el-button :disabled='submitLoading'
                       type='primary'
                       size='small'
                       class='login-button-width'
                       @click='submit'>{{loginBtnText}}</el-button>
            <div v-if='showForgetPwd'
                 class='forgetPassword-wrap'>
              <a @click='forgetPassword'>忘记SIM密码？</a>
            </div>
            <div v-if="showAccountRegister"
                 class="registerAccount-wrap">
              <a @click='registerAccount'>用户注册</a>
            </div>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixins from '../mixins'
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
// const VAR_MAIN_ONE = '1'
const VAR_INIT_STR = ''
export default {
  name: 'sim4A',
  mixins: [mixins],
  props: {
    sim4AInfo: {
      type: Object,
      default: null
    }
  },
  data () {
    const { platformConfig, domainConfig } = JSON.parse(
      this.$main_tools.sessionStorage.getItem('LISTALL')
    )
    return {
      timer: null, // 短信验证码倒计时计时器
      isStartLogin: false, // 是否开始登陆了，默认是false，当为true时，表示登录不再进行后续登录操作
      loginBtnText: '登录',
      sim4A: {
        loginName: '',
        checkCode: '',
        username: ''
      },
      submitLoading: false,
      loginNameDisabled: false,
      // showForgetPwd: platformConfig.showForgetPwd
      //   ? platformConfig.showForgetPwd === VAR_MAIN_ONE
      //   : true, // 控制忘记密码显隐，默认展示
      showForgetPwd: false,
      showAccountRegister: false,
      // showAccountRegister: platformConfig.showAccountRegister
      //   ? platformConfig.showAccountRegister === '1'
      //   : true, // 控制用户注册显隐，默认展示
      isCheckOn: false, // && platformConfig['is-check-on'] === 'true', // 暂不开启
      simLoginMultAccount: platformConfig.simLoginMultAccount,
      rule: {
        loginName: [
          { required: true, message: '请输入移动手机号或者4A账号', trigger: ['submit', 'blur'] }
        ],
        checkCode: [
          { required: true, message: '请输验证码', trigger: 'submit' }
        ]
      },
      platformConfig: platformConfig,
      domainConfig: domainConfig,
      focus: { count: true },
      subacctForm: { subbacct: '' }
    }
  },
  computed: {},
  created () {
    this.mixinsName = 'sim4A'
    this.isStartLogin = false
    // 初始化、退出刷新
    this.reSetTimer()
    // this.updateImageCode()
  },
  methods: {
    // 忘记SIM卡密码
    forgetPassword () {
      // window.open('https://yzhk.cmpassport.com/simpub/simquick/index.html', '_blank')
      this.$alert('尊敬的用户您好，如您忘记密码或需要修改密码，您可用注册4A账号的手机号直接发送“czmm”至“**************”进行4A账号密码重置，谢谢！', '温馨提示：', {
        confirmButtonText: '确定',
        callback: action => { }
      })
    },
    // 用户注册
    registerAccount () {
      this.$router.push({ path: '/registerAccount' })
    },
    // 提交
    submit () {
      this.$refs.sim4A.validate(async (valid) => {
        if (!valid) return false
        this.submitLoading = true // 按钮禁用
        const requestParmas = {
          loginName: this.sim4A.loginName,
          domain: this.$projectConfig.domain
        }
        await this.$aspHttps.asp_Post(this.$apiConfig.authUaSimPrefix + '/simAuth', requestParmas).then((response) => {
          if (this.$reponseStatus(response)) {
            const { taskId } = response.data
            // 触发轮询请求验证接口，直至超时/或者接口响应
            this.pollingLogin(taskId)
          } else {
            this.$message.error('验证失败！')
            // 清除轮询操作
            this.reSetTimer()
          }
        })
      })
    },
    /**
     * @description: 轮询验证登录
     * @param {*} taskId
     * @return {*}
     * @author: yuxuan <EMAIL>
     */
    pollingLogin (taskId) {
      // 设置loginName为不可修改
      this.loginNameDisabled = true
      // 设置页面下次登录操作时间
      let TIME_COUNT = 180 // 默认180秒
      if (this.platformConfig.simLoginValidMin && !isNaN(this.platformConfig.simLoginValidMin)) {
        const num = parseInt(this.platformConfig.simLoginValidMin)
        TIME_COUNT = num * 60
      }
      if (!this.timer) {
        this.loginBtnText = TIME_COUNT
        this.show = false
        this.timer = setInterval(() => {
          if (this.loginBtnText > 0 && this.loginBtnText <= TIME_COUNT) {
            this.loginBtnText--
            this.confirmSimLogin(taskId)
          } else {
            clearInterval(this.timer)
            this.timer = null
            this.loginBtnText = '登录'
            this.loginNameDisabled = false
            this.submitLoading = false
          }
        }, 1000)
      }
    },
    /**
     * @description: 验证sim卡是否登陆成功
     * @param {*} taskId
     * @return {*}
     * @author: yuxuan <EMAIL>
     */
    confirmSimLogin (taskId) {
      const requestParmas = {
        loginName: this.sim4A.loginName,
        taskId
      }
      this.$aspHttps.asp_Post(this.$apiConfig.authUaSimPrefix + '/simAuthStatusConfirm', requestParmas).then((response) => {
        if (this.$reponseStatus(response) && !this.isStartLogin) {
          const code = response.data.code // 判断登录验证状态，当code为0时登录成功，其他为登录失败
          if (code !== '0') return
          this.isStartLogin = true
          // 清除轮询操作
          this.reSetTimer()
          // 请求验证接口通过，进行4A单点登录
          this.beforeLoginFor4A(response.data)
        }
      })
    },
    /**
     * simLoginMultAccount- 1-sim卡登录默认选中从账号列表第一位；2-sim卡登录用户主动选从账号(只有一个时默认选中)；
     * @description: 发起4A单点登录前的验证动作
     * @param {*} response 轮询sim卡登录验证结果
     * @return {*}
     * @author: yuxuan <EMAIL>
     */
    beforeLoginFor4A (response) {
      const { subaccts } = response
      if (this.platformConfig.simLoginMultAccount === '2' && subaccts && subaccts.length > 1) {
        this.chooseSubbacct(response, subaccts, (iscontinue, response, subbacct) => {
          if (iscontinue) this.loginFor4A(response, subbacct)
          else this.isStartLogin = false
        })
      } else {
        // 请求验证接口通过，进行4A单点登录
        this.loginFor4A(response, subaccts[0].subbacct)
      }
    },
    loginFor4A (confirmResponse, subbacct) {
      const { mainAcct, mobile, taskId } = confirmResponse
      // 单点登陆接口
      // const url = this.$apiConfig.authUaPrefix + '/login/validConfirm'
      const url = this.$apiConfig.level1cloudstorePathPreFix + '/oua/sim4A/verifySimConfirm'
      const params = {
        mainUserName: asp_RSAKey.RSAEncrypt(mainAcct), // 登录名
        taskId: asp_RSAKey.RSAEncrypt(taskId), // sim认证任务ID
        mainPwd: VAR_INIT_STR, // 密码
        contextPath: '', // this.getIpHost().contextPath, // 上下文路径
        ip: '', // this.getIpHost().ip, // 上下文路径ip
        domain: this.$projectConfig.domain, // 用户所属域
        // confirmCode: asp_RSAKey.RSAEncrypt(taskId), // 二次确认密钥
        mobile: asp_RSAKey.RSAEncrypt(mobile), // 手机号码
        subUserName: subbacct // 从帐号 ？？
      }
      if (this.sim4A.checkCode) {
        params.checkCode = asp_RSAKey.RSAEncrypt(this.sim4A.checkCode) // 图片验证码
      }
      this.$aspHttps.asp_Post(url, params).then((response) => {
        if (this.$reponseStatus(response)) {
          // ssoPage = response.data
          this.vSsoVerify(response.data)
        } else {
          // 登录失败，重置计时器，刷新图形验证码
          this.reSetTimer()
          this.updateImageCode()
        }
      })
    },
    /**
     * 清除定时器，重置短信验证码按钮
     */
    reSetTimer () {
      clearInterval(this.timer)
      this.timer = null
      this.loginBtnText = '登录'
      this.loginNameDisabled = false
      this.submitLoading = false
    },
    // 刷新图形验证码
    updateImageCode () {
      if (!this.isCheckOn) return
      this.updateValidationCode()
      this.ssoMsg.checkCode = ''
      this.ssoMsgDisableMap.checkCode = false // 清空验证码后 需要将验证码变为可输入
    },
    // 校验从账户token---V
    vSsoVerify (ssoPage) {
      // 重建indexURL
      this.rebuildIndexUrl(ssoPage)
      // redirectUrl = 'ip:port/NG/#/ssoIndex?username=name&url=目标Url'
      let params = {
        redirectUrl: ssoPage.indexUrl,
        username: ssoPage.encryptSubUserName,
        domain: ssoPage.domain,
        token: ssoPage.token,
        '4AUserType': ssoPage.loginType
      }
      let url = this.$apiConfig.authPathPrefix + '/sso/verify'
      if (params !== null) {
        params = Object.keys(params)
          .map(function (key) {
            return (
              encodeURIComponent(key) + '=' + encodeURIComponent(params[key])
            )
          })
          .join('&')
        url = url + '?' + params
      }
      window.location.href = this.getRealUrl(url)
    },
    // 过滤代理url（通过location.href形式请求）
    getRealUrl (url) {
      if (process.env.NODE_ENV !== 'development') {
        this.$apiConfig && this.$apiConfig.proxyConfig && this.$apiConfig.proxyConfig.forEach(item => {
          url = url.replace(item.localProxy, item.nginxProxy)
        })
      }
      return url
    },
    /**
     * 实际登录--C--跳转中间页面操作 --/ssoIndex
     * sso，domain，username，loginType4A 源自4A登录使用 1:自登陆；2:sso单点登录；3:嵌套登录
     * url 登陆地址
     */
    rebuildIndexUrl (ssoPage) {
      let indexUrl = ''
      // 转换ip前缀
      indexUrl = ssoPage.indexUrl + '?url=' + ssoPage.rurl +
        '&username=' + ssoPage.subUserName + '&domain=' + ssoPage.domain +
        '&loginType=' + ssoPage.loginType + '&sso=' + ssoPage.ssoType +
        '&division=' + ssoPage.division + '&4AUserType=' + ssoPage.userType
      ssoPage.indexUrl = encodeURI(indexUrl)
    },
    // 获取当前浏览器的ip及端口号
    getIpHost () {
      const localUrl = window.location.href
      const ip = localUrl.split(window.location.host)[0]
      const contextPath =
        localUrl.split(window.location.host)[0] + window.location.host
      return { contextPath: contextPath, ip: ip }
    },
    // 选择指定从账号进行登录
    chooseSubbacct (response, subaccts, next) {
      this.$emit('loginSubmit', { loginType: 'sim4A', response, subaccts, next })
    },
    submitLogin () { }
  },
  destroyed () {
    this.reSetTimer()
  }
}
</script>

<style lang='scss' scoped>
.login-button-width {
  width: 100%;
}
.sim-tip-css {
  color: #606266;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
}
</style>
