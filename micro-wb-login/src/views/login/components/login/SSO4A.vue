/**
* 4A单点登录（包含免密登录）（4A、nopwd4A）
*/
<template>
  <el-form ref='ssoMsg'
           :rules='ruleList'
           :model='ssoMsg'>
    <el-form-item label
                  prop='username'>
      <el-input v-focus="{ cls:'el-input', tag:'input', foc:focus.count }"
                v-model.trim='ssoMsg.username'
                :disabled='ssoMsgDisableMap.username'
                placeholder='请输入用户名'
                name='username'
                autocomplete='off'
                @blur='focus.count = false'
                @keyup.enter='submit'>
        <template slot='prepend'>
          <i class='iconfont el-icon-mine'></i>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item v-if="showPWD"
                  label
                  prop='password'>
      <div @keyup.enter='submit'>
        <el-input v-model.trim='ssoMsg.password'
                  :disabled='ssoMsgDisableMap.password'
                  placeholder='请输入密码'
                  type='password'
                  name='password'
                  autocomplete='off'
                  @keyup.enter='submit'>
          <template slot='prepend'>
            <i class='iconfont el-icon-lock'></i>
          </template>
        </el-input>
      </div>
    </el-form-item>
    <el-form-item v-if='isCheckOn'
                  label
                  prop='checkCode'>
      <div @keyup.enter='submit'
           class='code-content code-img'>
        <el-input v-model.trim='ssoMsg.checkCode'
                  :disabled='ssoMsgDisableMap.checkCode'
                  placeholder='请输入验证码'>
          <template slot='append'>
            <a :disabled='ssoMsgDisableMap.checkCode'
               class='cursor-pointer-css'
               title
               @click='updateImageCode()'>
              <img id='imgObj'
                   :src='imgUrl'
                   alt
                   class='check-img-css' />
            </a>
          </template>
        </el-input>
      </div>
    </el-form-item>
    <el-form-item v-if='isSendPhoneCode'
                  label
                  prop='phoneCode'>
      <div>
        <el-input v-model.trim='ssoMsg.phoneCode'
                  placeholder='请输入短信验证码'>
          <template slot='append'>
            <el-button v-if='show'
                       :disabled='isCheckOn && !ssoMsg.checkCode'
                       type='primary'
                       plain
                       size='small'
                       @click='getMessageCode'>获取验证码</el-button>
            <el-button v-if='!show'
                       type='info'
                       round
                       disabled
                       size='small'
                       class='padding-css'>{{ count }} s</el-button>
          </template>
        </el-input>
      </div>
    </el-form-item>
    <el-form-item>
      <el-button
        :disabled="ssoMsgInfo.submitLoading"
        :loading="ssoMsgInfo.submitLoading"
        type="primary"
        size="small"
        class="login-button-width"
        @click="submit"
        >登录</el-button
      >
      <div v-if="showForgetPwd" class="forgetPassword-wrap">
        <el-tooltip class="item" effect="dark"  placement="bottom">
          <div slot="content">
            <div style="line-height:200%">
            尊敬的用户您好，如您忘记密码或需要修改密码，<br/>
            您可用注册4A账号的手机号直接发送<br/>
            “czmm”至“10658966587388”<br/>
            进行4A账号密码重置，谢谢！
            </div>
          </div>
          <a @click="forgetPassword">忘记密码？</a>
        </el-tooltip>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
import mixins from '../mixins'
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
const VAR_MAIN_ONE = '1'
const VAR_INIT_STR = ''
export default {
  name: 'SSO4A',
  mixins: [mixins],
  props: {
    ssoMsgInfo: {
      type: Object,
      default: null
    },
    mode: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      ssoMsg: {
        username: '',
        password: VAR_INIT_STR,
        checkCode: '',
        phoneCode: ''
      },
      ssoMsgDisableMap: {
        // 动态控制锁定图形验证码和短信验证码不可编辑
        username: false,
        password: false,
        checkCode: false
      },
      ssoPage: {
        // 跳转所需参数
        loginMode: '', // 登陆方式loginMode=1时不验证短信验证码 为2时需要
        // vUrl: '', // 登陆地址--sso/vid?=hidenUfrl
        rUrl: '', // 登陆地址--/home  目标页面地址
        indexUrl: '', // 登陆地址--==>/ssoIndex?url=/e 中间页面
        subUserName: '', // 从帐号登陆名
        encryptSubUserName: '', // 从帐号登陆名
        domain: '',
        token: '',
        userType: '',
        loginType: '',
        ssoType: '',
        division: '',
        mobile: ''
      },
      ssoMsgNextStatus: false, // 控制校验规则
      show: true, // 切换短信验证展示和按钮
      count: '', // 展示短信验证码倒计时
      timer: null, // 短信验证码倒计时计时器
      isCheckOn: true, // 是否展示图形验证码，默认展示
      showForgetPwd: true, // 是否展示忘记密码，默认展示
      isSendPhoneCode: true, // 非中央不需要短信验证
      rule1: {
        username: [
          { validator: this.$main_tools.validation.checkUsername4A, trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkPassword4A, trigger: 'blur' }
        ],
        checkCode: [
          { required: true, message: '请输入图形验证码', trigger: 'blur' }
        ]
      },
      rule2: {
        username: [
          { validator: this.$main_tools.validation.checkUsername4A, trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { validator: this.$main_tools.validation.checkPassword4A, trigger: 'blur' }
        ],
        checkCode: [
          { required: true, message: '请输入图形验证码', trigger: 'blur' }
        ],
        phoneCode: [
          { required: true, message: '请输入短信验证码', trigger: 'blur' }
        ]
      },
      focus: { count: true }
    }
  },
  computed: {
    ruleList () {
      if (this.ssoMsgNextStatus) {
        return this.rule2
      }
      return this.rule1
    },
    showPWD () {
      // 是否展示密码功能
      if (this.mode && this.mode.activeType === 'nopwd4A') return false
      return true
    }
  },
  created () {
    this.mixinsName = 'ssoMsg'
    // 初始化、退出刷新
    // this.updateImageCode()
    this.getConfig()
  },
  // 清除定时器
  beforeDestroy () {
    this.reSetTimer()
  },
  methods: {
    /**
     * 获取资源信息进行加密操作
     */
    getConfig () {
      this.$aspHttps
        .asp_Post(
          this.$apiConfig.supportPathPrefix + '/domainConfig/listAll',
          { domain: this.$projectConfig.domain }
        )
        .then((response) => {
          if (this.$reponseStatus(response)) {
            const { platformConfig, domainConfig } = response.data
            asp_RSAKey.RSASetPublic(
              platformConfig.modulus,
              platformConfig.exponent
            )
            this.$main_tools.sessionStorage.setItem('LISTALL', JSON.stringify(response.data))
            this.isCheckOn = domainConfig.checkCodeFor4A
              ? domainConfig.checkCodeFor4A === 'true'
              : true
            this.showForgetPwd = domainConfig.show4AForgetPwd
              ? domainConfig.show4AForgetPwd === 'true'
              : true
            this.isSendPhoneCode = domainConfig.systemId
              ? domainConfig.systemId === '000'
              : true
          }
        })
    },
    // 忘记密码
    forgetPassword () {
      return false
      // this.$router.push({ path: '/forgetPWD4A' })
      // this.$alert('尊敬的用户您好，如您忘记密码或需要修改密码，您可用注册4A账号的手机号直接发送“czmm”至“10658966587388”进行4A账号密码重置，谢谢！', '温馨提示：', {
      //   confirmButtonText: '确定',
      //   callback: action => {
      //   }
      // })
    },
    /**
     * 先验证用户名密码是否正确，并获取短信验证码【或者】
     */
    getMessageCode () {
      // 只校验前三个
      this.ssoMsgNextStatus = false
      this.$refs.ssoMsg.validate((valid) => {
        if (!valid) {
          this.ssoMsgInfo.submitLoading = false
          return false
        }
        const url = this.$apiConfig.authUaPrefix + '/login/valid'
        const params = {
          mainUserName: asp_RSAKey.RSAEncrypt(this.ssoMsg.username),
          mainPwd: asp_RSAKey.RSAEncrypt(this.ssoMsg.password),
          captchaCode: asp_RSAKey.RSAEncrypt(this.ssoMsg.checkCode),
          contextPath: '', // this.getIpHost().contextPath,
          ip: '' // this.getIpHost().ip
        }
        if (!this.showPWD) {
          params.noPwd = VAR_MAIN_ONE
          params.mainPwd = VAR_INIT_STR
        }
        // 验证用户名、密码、图形验证码，并获取短信验证码【或者】
        this.$aspHttps.asp_Post(url, params).then((response) => {
          if (this.$reponseStatus(response)) {
            this.ssoPage = response.data
            if (this.isSendPhoneCode) {
              // 短信发送成功，开启校验所有
              // this.ssoMsgNextStatus = true
              // 设置倒计时
              this.setTimer()
            } else {
              // 不发送短信直接登录
              this.vSsoVerify()
            }
          } else {
            this.reSetTimer()
            this.updateImageCode()
            this.ssoMsgInfo.submitLoading = false
            this.ssoMsg.phoneCode = ''
            // 刷新图形验证，重新校验前三项
            this.ssoMsgNextStatus = false
          }
        })
      })
    },
    /**
     * 提交短信验证码，进行登录--B
     * 或者直接提交用户名密码进行登录--B
     * 登录说明：
     * 有短信验证码：'验证用户名密码，发送短信验证码' A->B（验证短信码） ==>> '登录跳转' C
     * 没有短信验证码：'验证用户名密码' A  ==>> '登录跳转' C
     */
    submit () {
      // 提交登录，校验所有
      this.ssoMsgNextStatus = true
      this.$refs.ssoMsg.validate((valid) => {
        if (!valid) {
          return false
        }
        // 禁用提交按钮,预防多次点击提交
        this.ssoMsgInfo.submitLoading = true
        // 中央走短信验证码，发送短信验证码
        if (this.isSendPhoneCode) {
          const url = this.$apiConfig.authUaPrefix + '/login/validConfirm'
          const params = {
            mainUserName: asp_RSAKey.RSAEncrypt(this.ssoMsg.username), // 登录名
            mainPwd: asp_RSAKey.RSAEncrypt(this.ssoMsg.password), // 密码
            captchaCode: asp_RSAKey.RSAEncrypt(this.ssoMsg.checkCode), // 图片验证码
            contextPath: '', // this.getIpHost().contextPath, // 上下文路径
            ip: '', // this.getIpHost().ip, // 上下文路径ip
            domain: this.$projectConfig.domain, // 用户所属域
            confirmCode: asp_RSAKey.RSAEncrypt(this.ssoMsg.phoneCode), // 二次确认密钥
            mobile: this.ssoPage.mobile, // 手机号码
            subUserName: this.ssoPage.subUserName // 从帐号
          }
          if (!this.showPWD) {
            params.noPwd = VAR_MAIN_ONE
            params.mainPwd = VAR_INIT_STR
          }
          this.$aspHttps.asp_Post(url, params).then((response) => {
            if (this.$reponseStatus(response)) {
              this.ssoPage = response.data
              this.vSsoVerify()
            } else {
              // 登录失败，重置计时器，刷新图形验证码
              this.reSetTimer()
              this.updateImageCode()
              // 重启校验前三个
              this.ssoMsgNextStatus = false
              // 启动提交按钮
              this.ssoMsgInfo.submitLoading = false
              this.ssoMsg.phoneCode = ''
              // 放开禁用
              for (const key in this.ssoMsgDisableMap) {
                this.ssoMsgDisableMap[key] = false
              }
            }
          })
        } else {
          this.getMessageCode()
        }
      })
    },
    // 校验从账户token---V
    vSsoVerify () {
      // 重建indexURL
      this.rebuildIndexUrl()
      // redirectUrl = 'ip:port/NG/#/ssoIndex?username=name&url=目标Url'
      let params = {
        redirectUrl: this.ssoPage.indexUrl,
        username: this.ssoPage.encryptSubUserName,
        domain: this.ssoPage.domain,
        token: this.ssoPage.token,
        '4AUserType': this.ssoPage.loginType
      }
      let url = this.$apiConfig.authPathPrefix + '/sso/verify'
      if (params !== null) {
        params = Object.keys(params)
          .map(function (key) {
            return (
              encodeURIComponent(key) + '=' + encodeURIComponent(params[key])
            )
          })
          .join('&')
        url = url + '?' + params
      }
      window.location.href = this.getRealUrl(url)
    },
    // 过滤代理url（通过location.href形式请求）
    getRealUrl (url) {
      if (process.env.NODE_ENV !== 'development') {
        this.$apiConfig && this.$apiConfig.proxyConfig && this.$apiConfig.proxyConfig.forEach(item => {
          url = url.replace(item.localProxy, item.nginxProxy)
        })
      }
      return url
    },
    /**
     * 实际登录--C--跳转中间页面操作 --/ssoIndex
     * sso，domain，username，loginType4A 源自4A登录使用 1:自登陆；2:sso单点登录；3:嵌套登录
     * url 登陆地址
     */
    rebuildIndexUrl () {
      let indexUrl = ''
      // 转换ip前缀
      indexUrl =
        this.ssoPage.indexUrl +
        '?url=' +
        this.ssoPage.rurl +
        '&username=' +
        this.ssoPage.subUserName +
        '&domain=' +
        this.ssoPage.domain +
        '&loginType=' +
        this.ssoPage.loginType +
        '&sso=' +
        this.ssoPage.ssoType +
        '&division=' +
        this.ssoPage.division +
        '&4AUserType=' +
        this.ssoPage.userType
      this.ssoPage.indexUrl = encodeURI(indexUrl)
    },
    // 获取当前浏览器的ip及端口号
    getIpHost () {
      const localUrl = window.location.href
      const ip = localUrl.split(window.location.host)[0]
      const contextPath =
        localUrl.split(window.location.host)[0] + window.location.host
      return { contextPath: contextPath, ip: ip }
    },
    /**
     * 设置定时器，重置短信验证码按钮
     */
    setTimer () {
      // 设置username,pwd,checkCode为不可修改
      for (const key in this.ssoMsgDisableMap) {
        this.ssoMsgDisableMap[key] = true
      }
      // 设置页面短信验证刷新时间
      const TIME_COUNT = 60
      if (!this.timer) {
        this.count = TIME_COUNT
        this.show = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.show = true
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    },
    /**
     * 清除定时器，重置短信验证码按钮
     */
    reSetTimer () {
      clearInterval(this.timer)
      this.timer = null
      this.show = true
    },
    // 刷新图形验证码
    updateImageCode () {
      this.updateValidationCode()
      this.ssoMsg.checkCode = ''
      this.ssoMsgDisableMap.checkCode = false // 清空验证码后 需要将验证码变为可输入
    }
  }
}
</script>


<style lang='scss' scoped>
.login-button-width {
  width: 100%;
}
</style>
