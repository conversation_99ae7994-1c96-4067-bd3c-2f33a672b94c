/**
* 登录模块 系统入口
* 功能：账户登录、短信验证码、密码过期修改密码、忘记密码、4A登录，4A免密登录、超级SIM卡登录，4A超级SIM卡登录
* 获取获取用户、鉴权、字典表信息
*/
<template>
  <div class="wblogin">
    <div class="login-head">
      <img :style="[{visibility: showLogo ?'unset':'hidden'}]"
           src="../../../../assets/img/loginlogo.png" />
      <h1>{{ domainConfig.title || platformConfig['portal-name'] }}</h1>
    </div>
    <div class="login-ele-icon"
         style="height: 100%;">
      <img src="../../../../assets/img/loginbg.png" />
    </div>
    <div class="login-wrap">
      <div v-if="loginAction">
        <el-card v-if="loginAction">
          <div slot="header">
            <div class="login-type-header-css">
              <el-radio v-for="(item, index) in loginTypesCompute"
                        v-show="index < loginTypesNum"
                        :key="item.id"
                        :label="item.id"
                        v-model="mode.activeType"
                        border
                        @change="changeTab(item.id)">
                {{ item.name }}
              </el-radio>
            </div>
            <el-button v-if="loginTypesCompute.length > 2"
                       :icon="icon"
                       type="primary"
                       size="mini"
                       @click="buttonChange()"></el-button>
          </div>
          <div>
            <!-- 账户登录类型 -->
            <Account v-if="mode.activeType === 'password'"
                     :account-info="mode.account"
                     :imgUrl="imgUrl"
                     @updateImage="updateImage"
                     @loginSubmit="loginSubmit" />
            <!-- 验证码登录 -->
            <SmsCaptcha v-if="mode.activeType === 'msg'"
                        :sms-captcha-info="mode.smsCaptcha"
                        :imgUrl="imgUrl"
                        @updateImage="updateImage"
                        @loginSubmit="loginSubmit" />
            <!-- 4A登录 -->
            <SSO4A v-if="['4A', 'nopwd4A'].includes(mode.activeType)"
                   :sso-msg-info="mode.ssoMsg"
                   :mode="mode"
                   :imgUrl="imgUrl"
                   @updateImage="updateImage"
                   @loginSubmit="loginSubmit" />
             <!-- 明天高软登录 -->
             <SSO4A2 v-if="mode.activeType === '4A2'"
                   :sso-msg-info="mode.ssoMsg"
                   :mode="mode"
                   :imgUrl="imgUrl"
                   @updateImage="updateImage"
                   @loginSubmit="loginSubmit" />
            <!-- SIM超级卡登录 -->
            <SIMSmart v-if="mode.activeType === 'sim'"
                      :sim-smart-info="mode.simSmartInfo"
                      :loginBtnText="simLoginBtnText"
                      :imgUrl="imgUrl"
                      @updateImage="updateImage"
                      @loginSubmit="loginSubmit" />
            <!-- 4ASIM超级卡登录 -->
            <SIM4A v-if="mode.activeType === 'sim4A'"
                   ref="sim4A"
                   :sim4AInfo="mode.sim4AInfo"
                   :imgUrl="imgUrl"
                   @updateImage="updateImage"
                   @loginSubmit="loginSubmit" />
          </div>
        </el-card>
      </div>
    </div>
    <!-- copyright -->
    <div :style="[{visibility: showCopyRight ?'unset':'hidden'}]"
         class="login-footer">{{ domainConfig.copyright || platformConfig.copyright || '中国移动·版权所有' }}
    </div>
    <!-- 修改密码 -->
    <EditPwd :dialog-params="editPwdParam"
             @resetButtonLoading="resetButtonLoading">
    </EditPwd>
    <!-- 选择指定从账号进行sim卡登录 -->
    <multAccounts :dialog-param="multAccountParam"
                  @simSubLogin="simSubLogin">
    </multAccounts>
  </div>
</template>

<script>
// import qs from 'qs'
import SIMSmart from './components/login/simSmart.vue'
import SIM4A from './components/login/sim4A.vue'
import Account from './components/login/account'
import SmsCaptcha from './components/login/smsCaptcha'
import SSO4A from './components/login/SSO4A'
import SSO4A2 from './components/login/SSO4A2'
import EditPwd from './components/pwd/editPwd'
import multAccounts from './components/simMultAccounts/multAccounts'
import { asp_RSAKey } from 'asp-smart-ui/lib/utils/ras'
import initApi from './initApi'
export default {
  name: 'Login',
  components: {
    Account,
    SmsCaptcha,
    SSO4A,
    SSO4A2,
    EditPwd,
    SIMSmart,
    SIM4A,
    multAccounts
  },
  mixins: [initApi],
  data () {
    return {
      mode: {
        activeType: '',
        loginTypes: [
          {
            id: 'password',
            name: '账号登录',
            state: false
          },
          {
            id: 'msg',
            name: '手机验证码登录',
            state: false
          },
          {
            id: '4A2',
            name: '4A账号登录',
            state: false
          },
          {
            id: '4A',
            name: '4A主账号登录',
            state: false
          },
          {
            id: 'nopwd4A',
            name: '4A短信验证码登录', // 4A登陆拓展免密登录，复用4A登录功能
            state: false
          },
          {
            id: 'sim',
            name: '手机SIM登录', // webbas对接的SIM卡登陆，与4ASIM卡登陆互斥
            state: true
          },
          {
            id: 'sim4A',
            name: '4A手机SIM登录', // 对接4A的手机SIM卡登陆，与webbasSIM卡登陆互斥
            state: false
          }
        ],
        account: { submitLoading: false, msg: '' },
        smsCaptcha: { submitLoading: false, msg: '' },
        ssoMsg: { submitLoading: false, msg: '' },
        simSmartInfo: { submitLoading: false, msg: '' },
        sim4AInfo: { submitLoading: false, msg: '' }
      },
      // 请求失败登录模块不展示
      loginAction: true,
      showLogo: true,
      showCopyRight: false,
      formParams: {
        focus: {
          count: true
        },
        loginType: 0,
        submitStatus: false
      },
      // param: {
      //   domain: this.$projectConfig.domain
      // },
      multAccountParam: { visible: false },
      // 密码过期打开修改界面
      editPwdParam: {
        editPwdModelVisible: false,
        isExpired: false
      },
      currentDate: '', // 当前日期
      day: '', // 天数
      platformConfig: { 'login-types': '', copyright: '', 'portal-name': '' }, // 域配置
      // 平台配置信息
      domainConfig: {},
      buttonChangeFlag: false,
      loginTypesNum: 2,
      icon: 'el-icon-arrow-up',
      timeCount: 10000, // 轮询时间间隔
      simCount: 1, // 记录请求次数
      timer: null, // 计时
      simLoginSuccess: true,
      simLoginBtnText: '登录',
      authRedirectUrl: '', // 新单点登录重定向地址
      imgUrl: '', // 公共图形验证码
      subLogType: ''
    }
  },
  computed: {
    loginTypesCompute: function () {
      const loginTypesNew = []
      const _t = this
      if (_t.mode.loginTypes && Object.prototype.hasOwnProperty.call(_t.platformConfig, 'login-types') && _t.platformConfig['login-types']) {
        const loginTypes = _t.platformConfig['login-types'].split(',')
        const loginTypeObj = {}
        _t.mode.loginTypes.forEach(item => {
          loginTypeObj[item.id] = item
        })
        loginTypes.forEach(type => {
          if (loginTypeObj[type]) {
            loginTypesNew.push(loginTypeObj[type])
          }
        })
      }
      return loginTypesNew
    }
  },
  created () {
    this.initDataAPI()
  },
  methods: {
    // 按钮切换
    buttonChange () {
      this.buttonChangeFlag = !this.buttonChangeFlag
      if (this.buttonChangeFlag) {
        this.icon = 'el-icon-arrow-down'
        this.loginTypesNum = 4
      } else {
        this.icon = 'el-icon-arrow-up'
        this.loginTypesNum = 2
      }
    },
    /** 更新图片信息 */
    updateImage (imageData) {
      if (imageData) this.imgUrl = imageData
    },
    // 登录提交
    loginSubmit (params) {
      const { modulus, exponent } = this.platformConfig
      const { loginType, password, username, OTPToken } = params
      // RSA
      asp_RSAKey.RSASetPublic(modulus, exponent)
      let requestParmas = {}
      const newParams = Object.assign({}, params)
      // 判断多种登录方式
      switch (loginType) {
        case 'password':
          this.mode.account.submitLoading = true
          requestParmas = Object.assign(newParams, {
            username: asp_RSAKey.RSAEncrypt(username),
            password: asp_RSAKey.RSAEncrypt(password),
            loginType: loginType === 'password' ? '0' : '1'
          })
          break
        case 'msg':
          this.mode.smsCaptcha.submitLoading = true
          requestParmas = Object.assign(newParams, {
            username: asp_RSAKey.RSAEncrypt(username),
            password: asp_RSAKey.RSAEncrypt(OTPToken),
            loginType: loginType === 'password' ? '0' : '1'
          })
          delete requestParmas.OTPToken
          break
        case 'sim':
          this.mode.simSmartInfo.submitLoading = true
          requestParmas = Object.assign(newParams, {
            username: asp_RSAKey.RSAEncrypt(username),
            password: asp_RSAKey.RSAEncrypt(password),
            domain: this.$projectConfig.domain,
            loginType: '4'
          })
          break
      }
      if (loginType === 'sim') {
        const _this = this
        this.timer = null
        // 暂时保留，sim卡超级后续可删
        // setTimeout(() => {
        //   _this.handleLoginAction(params, requestParmas)
        //   if (this.simLoginSuccess) {
        //     this.timer = setInterval(() => {
        //       _this.timeCount = _this.simCount > 1 ? 5000 : _this.timeCount
        //       if (_this.simCount <= 9) {
        //         _this.handleLoginAction(params, requestParmas)
        //         ++_this.simCount
        //       } else {
        //         _this.$message.error('请求超时，请重新登录！')
        //         _this.clearSimLogin()
        //       }
        //     }, 5000)
        //   }
        // }, 10000)
        let t1 = this.platformConfig.simLoginPeriodSecond || 180
        let t2 = t1
        let varValue = 10
        this.timer = setInterval(() => {
          t2--
          _this.simLoginBtnText = `${t2}s`
          if ((t1 - t2) === varValue && _this.simLoginSuccess && t2 > 0) {
            t1 = t1 - varValue
            varValue = 5
            _this.handleLoginAction(params, requestParmas)
          } else if (t2 === 0) {
            _this.$message.error('请求超时，请重新登录！')
            _this.clearSimLogin()
          }
        }, 1000)
      } else if (loginType === 'sim4A') {
        this.multAccountParam = { visible: true, ...params }
      } else {
        this.handleLoginAction(params, requestParmas)
      }
    },
    /**
     * @description: 实施登录配置
     * @param {*} params
     * @param {*} requestParmas
     * @return {*}
     * @author: yuxuan <EMAIL>
     */
    handleLoginAction (params, requestParmas) {
      const { loginType } = params
      this.subLogType = ''
      const _t = this
      this.$aspHttps.asp_PostForm(this.$apiConfig.authPathPrefix + '/login/verify', requestParmas, true).then((response) => {
        _t.subLogType = loginType
        if (_t.$reponseStatus(response.data)) {
          _t.simLoginSuccess = false
          loginType === 'sim' && _t.clearSimLogin()
          const { token, type, key } = response.data.data
          if (type === 'header' && key) {
            this.$store.commit('vuexStorage/setData', {
              type,
              word: this.$aspHttps.asp_EncryptPlus(token),
              headerWord: this.$aspHttps.asp_EncryptPlus(key)
            })
          }
          // 密码有效日期
          _t.passwordValidity(response.data.data)
          // 过滤按钮
          // _t.$main_tools.menu.operateFilter4AButton('', this)
          // 获取秘钥token + 操作类型
        } else if (response.data.status === '901' && loginType !== 'sim') {
          if (loginType === 'password') {
            _t.mode.account = {
              submitLoading: false,
              msg: 'err' + Math.random()
            }
          } else {
            _t.mode.smsCaptcha = {
              submitLoading: false,
              msg: 'err' + Math.random()
            }
          }
          _t.$confirm('密码复杂度不符合安全规范，修改密码后才能登录', '提示', {
            confirmButtonText: '修改密码',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            _t.modifyPwd()
          }).catch(() => {
            _t.$message({
              type: 'info',
              message: '已取消修改'
            })
          })
        } else if (response.data.status === '903' && loginType !== 'sim') {
          // 修改接口存在暴力破解风险（处于免鉴权暴露阶段），目前已暂时屏蔽该接口，前端屏蔽修改界面
          // 清空登录信息，重新登录
          this.$main_tools.util.relogin()
        } else if (loginType !== 'sim') {
          console.log('222')
          debugger
          _t.initDataAPI()
          if (loginType === 'password') {
            _t.mode.account = {
              submitLoading: false,
              msg: 'err' + Math.random()
            }
          } else {
            _t.mode.smsCaptcha = {
              submitLoading: false,
              msg: 'err' + Math.random()
            }
          }
          // err message
          _t.$message.error(response.data.message)
        }
        // 刷新验证码
        _t.temp = new Date().getTime()
      })
    },
    // 重置SIM卡超时登录
    clearSimLogin () {
      clearInterval(this.timer)
      this.timer = null
      this.simCount = 1
      this.timeCount = 10000
      this.mode.simSmartInfo.submitLoading = false
      this.simLoginBtnText = '登录'
    },
    // 密码有效期
    passwordValidity (data) {
      const { curUserInfo, authRedirectUrl } = data
      const { platformConfig } = JSON.parse(this.$main_tools.sessionStorage.getItem('LISTALL'))
      const expireRemind = platformConfig.passwordExpireRemind
      const passwordWillExpireDays = curUserInfo.passwordWillExpireDays
      if (authRedirectUrl) this.authRedirectUrl = authRedirectUrl // 追加重定向url
      // if (passwordWillExpireDays <= expireRemind && this.validLoginData({ data })) { // 判断条件未知不清楚为什么要判断菜单menuTree
      if (passwordWillExpireDays <= expireRemind) {
        this.confirmUpdate(passwordWillExpireDays)
      } else {
        this.toHome()
        // 防止退出
        this.loginAction = false
      }
    },
    // 跳转入口
    async toHome () {
      if (this.authRedirectUrl) {
        window.location.replace('/oauth2/success')
      } else {
        const { microAppPre, routerPath, titleName } = this.$pageConfig.loginIndex
        const path = `${microAppPre}#${routerPath}?loginType=${this.subLogType}`
        window.history.pushState({}, titleName, path)
        window.location.reload()
      }
    },
    // 密码即将过期提醒
    confirmUpdate (dates) {
      this.$confirm('您的密码' + dates + '天后过期，建议现在修改', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '修改',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            this.editPwdParam = {
              editPwdModelVisible: true, // 打开修改界面
              isExpired: false // 密码还未过期，可以点取消按钮进入主页
            }
            done()
          }
          if (action === 'cancel') {
            done()
          }
        }
      }).then(() => {
        this.formParams.submitStatus = false
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消修改' })
        this.formParams.submitStatus = false
        this.toHome()
      })
    },
    // 密码过期修改后 登录
    confirmUpdateNoCancel () {
      this.$confirm('您的密码已过期，需修改密码才能登录', '提示', {
        closeOnClickModal: false,
        showCancelButton: false,
        confirmButtonText: '修改',
        type: 'warning',
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            this.editPwdParam = {
              editPwdModelVisible: true, // 打开修改界面
              isExpired: true // 密码已过期
            }
            done()
          }
        }
      }).then(() => {
        this.formParams.submitStatus = false
      })
    },
    // 切换登陆方式
    changeTab (idCode) {
      this.mode.activeType = idCode
    },
    // 修改密码
    modifyPwd () {
      this.$router.push({ path: '/forgetPWD' })
    },
    /** 添加校验拦截，避免强制修改接口响应，打开修改密码界面 */
    validLoginData (response) {
      // 添加校验拦截，避免强制修改接口响应，打开修改密码界面
      const data = response.data.data ? response.data.data : response.data // 兼容平台侧返回的数据格式
      return data.menuTree
    },
    /** 修改完密码后重新发起登录跳转 */
    resetButtonLoading (data) {
      if (data) {
        this.mode.account.submitLoading = data.submitLoading
        if (data.nextHome) { // 密码未过期的nextHome为true，密码已过期的nextHome为false
          this.toHome()
        }
      }
    },
    simSubLogin (data) {
      this.multAccountParam = { visible: false }
    },
    // 保存界面显示数据
    configDataApi (res) {
      const { platformConfig, domainConfig } = res.data
      this.platformConfig = platformConfig
      this.domainConfig = domainConfig
      document.title = domainConfig.title || platformConfig['portal-name']
      // 无登录类型 不展示登录入口
      if (platformConfig['login-types'] !== '') {
        this.loginAction = true
        // 构建登录方式
        platformConfig['login-types'].split(',').forEach((item, index) => {
          const one = this.mode.loginTypes.find(x => x.id === item)
          if (this.mode.activeType === '' && one) {
            this.mode.activeType = one.id
          }
        })
      }
    }
  },
  destroyed () {
    this.clearSimLogin()
    this.authRedirectUrl = ''
  }
}
</script>
